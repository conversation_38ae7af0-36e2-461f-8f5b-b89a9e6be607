import { Album } from "@/generated/graphql/graphql";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

interface ListAlbumItemProps {
  album: PartialDeep<Album, { recurseIntoArrays: true }>;
}

export default function ListAlbumItem({ album }: ListAlbumItemProps) {
  const { images, url, title, categories, photoCount, videoCount, mediaCount } = album;
  const image = images?.ratio_3x2 ?? null;
  const [category] = categories ?? [];

  return (
    <article className={styles.albumItem}>
      <div className={styles.details}>
        {mediaCount && (
          <p className={styles.counts}>
            {`${(photoCount ?? 0) > 0 ? `${photoCount} photo${(photoCount ?? 0) > 1 ? "s" : ""}` : ""} 
    ${(photoCount ?? 0) > 0 && (videoCount ?? 0) > 0 ? " - " : ""}
    ${(videoCount ?? 0) > 0 ? `${videoCount} vidéo${(videoCount ?? 0) > 1 ? "s" : ""}` : ""}`}
          </p>
        )}

        <h3 className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </h3>
      </div>
      {image?.url && (
        <div className={styles.imageWrapper}>
          <Image
            src={image.url}
            width={200}
            height={200}
            alt={image?.alt ?? ""}
            sizes="(max-width: 767px) 74px, (max-width: 1301px) 224px, 384px"
          />
        </div>
      )}
    </article>
  );
}
