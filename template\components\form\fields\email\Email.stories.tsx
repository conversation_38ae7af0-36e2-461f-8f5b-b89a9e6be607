import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Email from "./Email";

const meta: Meta<typeof Email> = {
  title: "Form/Email",
  component: Email,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Email>;

export const Default: Story = {
  args: {
    name: "input_1",
    label: "E-mail",
    description: "Texte de description additionel",
    placeholder: "Please enter your e-mail address",
  },
};
