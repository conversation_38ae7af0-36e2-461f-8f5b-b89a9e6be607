"use client";

import styles from "@/styles/ErrorPage.module.scss";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function Error() {
  const router = useRouter();

  return (
    <div className="container">
      <div className={styles.errorPage}>
        <div className={styles.content}>
          <h1>Erreur 500</h1>
          <h2>
            Une erreur est survenue sur <em>notre serveur !</em>
          </h2>
          <p>
            Une erreur interne s'est produite sur notre serveur. Notre équipe technique a été notifiée et travaille à
            résoudre le problème.
          </p>
          <button onClick={() => router.refresh()} className={styles.returnLink}>
            <i className="far fa-refresh" aria-hidden="true"></i>
            Rafraîchir la page
          </button>
        </div>
        <Image className={styles.illustration} src="/images/500.svg" width={538} height={559} alt="" />
      </div>
    </div>
  );
}
