"use client";

import { AsChildProps } from "@/components/utils/asChild";
import { Slot, Slottable } from "@radix-ui/react-slot";
import clsx from "clsx";
import React, { KeyboardEvent, MouseEvent } from "react";
import styles from "./Button.module.scss";

export type ButtonProps = AsChildProps<React.ButtonHTMLAttributes<HTMLButtonElement>> & {
  variant?: "contained" | "outlined" | "text";
  color?:
    | "primary"
    | "secondary"
    | "tertiary"
    | "primary-inverted"
    | "secondary-inverted"
    | "tertiary-inverted"
    | "danger-inverted";
  size?: "xs" | "sm" | "md" | "lg";
  startIcon?: React.ReactNode | string;
  endIcon?: React.ReactNode | string;
  className?: string;
  disabled?: boolean;
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  onKeyDown?: (event: KeyboardEvent<HTMLButtonElement>) => void;
};

// TODO: Add "only icon" option
// TODO: Add "inverted colors" option, for dark bg (use a specific global class? e.g. dark)
// TODO: Add "neutral" color
export default function Button({
  ref,
  variant = "contained",
  size = "md",
  color = "primary",
  className,
  startIcon,
  endIcon,
  children,
  asChild,
  disabled = false,
  onClick,
  onKeyDown,
  ...restProps
}: React.PropsWithChildren<ButtonProps> & { ref?: React.RefObject<HTMLButtonElement | null> }) {
  const Comp = asChild ? Slot : "button";

  function handleClick(event: MouseEvent<HTMLButtonElement>) {
    if (event.currentTarget instanceof HTMLButtonElement) {
      if (disabled) {
        event.preventDefault();
        event.stopPropagation();
      } else {
        onClick?.(event);
      }
    }
  }

  function handleKeyDown(event: KeyboardEvent<HTMLButtonElement>) {
    if (event.key !== " " && event.key !== "Enter") {
      return;
    }

    if (event.currentTarget instanceof HTMLButtonElement) {
      if (disabled) {
        event.preventDefault();
        event.stopPropagation();
      } else {
        onKeyDown?.(event);
      }
    }
  }

  const { type, tabIndex } = restProps as React.ButtonHTMLAttributes<HTMLButtonElement>;

  return (
    <Comp
      ref={ref}
      aria-disabled={disabled ? true : undefined}
      tabIndex={disabled && type !== "submit" && type !== "reset" ? -1 : tabIndex}
      className={clsx(
        styles.button,
        styles[`variant-${variant}`],
        styles[`color-${color}`],
        styles[`size-${size}`],
        className
      )}
      {...restProps}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
    >
      {startIcon && <ButtonIconSlot icon={startIcon} className={styles.startIcon} />}
      <Slottable>{children}</Slottable>
      {endIcon && <ButtonIconSlot icon={endIcon} className={styles.endIcon} />}
    </Comp>
  );
}

function ButtonIconSlot({ icon, className }: { icon: React.ReactNode; className: string }) {
  if (typeof icon === "string") {
    return <i className={clsx(icon, className)} aria-hidden="true"></i>;
  }

  return <span className={className}>{icon}</span>;
}
