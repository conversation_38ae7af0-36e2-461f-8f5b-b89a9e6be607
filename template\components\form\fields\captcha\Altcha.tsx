"use client";

import useFormCondition from "@/components/form/useFormCondition";
import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import { AltchaField } from "@/generated/graphql/graphql";
import "altcha";
import { useId, useRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import styles from "./captcha.module.scss";

type AltchaProps = Omit<AltchaField, "__typename">;

const captchaErrorMessage = "Please complete the captcha verification";

export default function RHFAltcha({ name, columnSpan, condition, challengeurl }: AltchaProps) {
  const { control, setValue, getValues } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const captchaRef = useRef<AltchaWidget & AltchaWidgetMethods & HTMLElement>(null);
  const errorId = useId();

  const handleOnLoad = () => {
    const payload = getValues(name);

    if (payload) {
      captchaRef.current?.setState("verified");
    }
  };

  const handleOnChange = (event: CustomEvent) => {
    if ("detail" in event) {
      const payload = event.detail.payload;
      const state = event.detail.state;

      if (state === "verified" && payload) {
        setValue(name, payload);
      } else if (state === "unverified" || state === "error") {
        setValue(name, null);
      }
    }
  };

  return (
    visible && (
      <FormControl columnSpan={columnSpan} className={styles.captcha}>
        <Controller
          name={name}
          control={control}
          rules={{ required: captchaErrorMessage }}
          render={({ fieldState }) => (
            <>
              <altcha-widget
                ref={captchaRef}
                challengeurl={challengeurl}
                onload={handleOnLoad}
                onstatechange={(event) => handleOnChange(event)}
              />

              {fieldState?.error && (
                <FormHelper id={errorId} variant="error">
                  {fieldState.error.message?.toString()}
                </FormHelper>
              )}
            </>
          )}
        />
      </FormControl>
    )
  );
}
