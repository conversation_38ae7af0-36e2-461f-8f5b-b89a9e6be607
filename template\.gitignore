# IDE
.idea

# Dependencies
node_modules

# Typescript
*.tsbuildinfo
next-env.d.ts

# Env files (can opt-in for committing if needed)
.env*
!.env.example

# Next.js
/.next/
/out/
/build
/certificates/

# GraphQL
/generated

# Package managers
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

*storybook.log
