import FormControl from "@/components/ui/form-control/FormControl";
import Label from "@/components/ui/label/Label";
import SelectElement from "@/components/ui/select/Select";
import { SelectFilter } from "@/generated/graphql/graphql";
import { useId } from "react";

interface SelectProps {
  filter: Partial<Omit<SelectFilter, "__typename">>;
  selectedOptions: string[];
}

export default function Select({
  filter: { attribute, label, options, placeholder },
  selectedOptions = [],
}: SelectProps) {
  const selectId = useId();

  if (!options || options.length === 0) {
    return null;
  }

  return (
    <FormControl>
      <Label htmlFor={selectId}>{label}</Label>
      <SelectElement id={selectId} name={attribute} placeholder={placeholder ?? ""} defaultValue={selectedOptions[0]}>
        {options.map((opt, index) => (
          <option key={index} value={opt.value}>
            {opt.label}
          </option>
        ))}
      </SelectElement>
    </FormControl>
  );
}
