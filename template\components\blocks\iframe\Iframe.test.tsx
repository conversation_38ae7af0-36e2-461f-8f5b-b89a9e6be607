import { fireEvent, render } from "@testing-library/react";
import { describe, expect, test, vi } from "vitest";
import Iframe from "./Iframe";

describe("IframeBlock", () => {
  test("focus the inner iframe when pressing enter on the block", async () => {
    const { container } = render(
      <Iframe
        title="Lorem ipsum dolor sit amet, consectetur adipiscing elit."
        url="https://react.dev/"
        height={500}
        outlined={true}
      />
    );

    const iframeBlock = container.querySelector("section");

    expect(iframeBlock).toBeTruthy();
    iframeBlock!.focus();

    const iframe = iframeBlock!.querySelector("iframe");

    expect(iframe).toBeTruthy();

    // Mock iframe.contentWindow if not defined
    Object.defineProperty(iframe!, "contentWindow", {
      writable: true,
      value: {
        focus: vi.fn(),
      },
    });

    fireEvent.keyDown(iframeBlock!, { key: "Enter", code: "Enter", charCode: 13 });

    expect(iframe!.contentWindow!.focus).toHaveBeenCalledOnce();
  });
});
