@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.footer {
  display: flex;
  flex-direction: column;
  align-self: stretch;

  .wrapper {
    padding: 56px 72px;
    background: $color-neutral-100;

    @include breakpoint(medium down) {
      padding: 48px;
    }

    @include breakpoint(small down) {
      padding: 16px;
    }
  }

  .container {
    display: flex;
    flex-flow: row wrap;
    align-self: stretch;
    justify-content: space-between;
    width: 100%;
    max-width: 1216px;
    margin-inline: auto;

    @include breakpoint(medium down) {
      gap: 32px;
    }
  }

  .footerLeft {
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-content: center;
    align-items: center;
    width: 100%;

    @include breakpoint(medium up) {
      flex-direction: row;
      gap: 0 32px;
      justify-content: space-between;
      width: 100%;
    }

    @include breakpoint(large up) {
      align-content: flex-start;
      align-items: flex-start;
      width: auto;
    }
  }

  .logo {
    .image {
      display: flex;
      flex-direction: column;
      align-items: center;
      max-width: 120px;
      height: 140px;
    }
  }

  .details {
    display: flex;
    flex-direction: column;
    gap: 0 32px;
    align-items: center;
    max-width: 100%;

    @include breakpoint(medium up) {
      align-items: flex-start;
    }

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: $color-primary-500;
      border: 1px solid $color-primary-500;
      border-radius: 50%;
      transition: background 0.3s ease;

      &:hover {
        color: $color-white;
        background: $color-primary-500;
      }
    }
  }

  .siteName {
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
  }

  .address {
    margin-top: 8px;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 110%;
    text-align: center;

    @include breakpoint(medium up) {
      text-align: left;
    }
  }

  .socials {
    display: flex;
    gap: 0 2px;
    align-items: flex-start;
    justify-content: center;
    margin-top: 16px;

    @include breakpoint(large up) {
      justify-content: flex-start;
    }

    a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      color: $color-primary-500;
      text-decoration: none;
      border: 1px solid $color-primary-500;
      border-radius: 1500px;
      transition: background 0.3s ease;

      &:hover {
        color: $color-white;
        background: $color-primary-500;
      }
    }
  }

  .buttons {
    display: flex;
    flex-shrink: 0;
    flex-direction: column;
    gap: 8px 0;
    width: 100%;

    @include breakpoint(medium up) {
      width: auto;
    }

    li {
      a {
        width: 100%;
        font-size: 14px;
        font-weight: 400;
        text-align: center;
      }
    }
  }

  .nav {
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
    width: 100%;

    @include breakpoint(medium up) {
      flex-flow: row wrap;
      gap: 32px;
      align-items: flex-start;
      width: auto;
    }

    a {
      display: flex;
      gap: 2px;
      align-items: center;
      align-self: stretch;
      padding: 4px 0;
      font-style: normal;
      line-height: 120%;
      color: $color-black;
      text-align: start;
      text-transform: uppercase;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .leftList {
    display: flex;
    flex-flow: row wrap;
    gap: 2px 24px;
    justify-content: center;
    width: 100%;
    max-width: 176px;
    max-width: fit-content;
    font-size: 14px;
    font-weight: 700;

    @include breakpoint(large up) {
      flex-direction: column;
    }
  }

  .listRight {
    display: flex;
    flex-flow: row wrap;
    gap: 2px 24px;
    justify-content: center;
    width: 100%;
    max-width: 176px;
    max-width: fit-content;
    font-size: 12px;
    font-weight: 400;

    @include breakpoint(large up) {
      flex-direction: column;
    }
  }
}

.footerInfo {
  display: flex;
  align-self: stretch;
  justify-content: center;
  min-width: 1216px;
  margin-top: 32px;
  background: $color-neutral-100;

  @include breakpoint(medium down) {
    min-width: 100%;
  }

  .separator {
    gap: 8px 0;
    align-self: stretch;
    width: 100%;
    max-width: 1216px;
    height: 1px;
    margin: 11.5px 0;
    background: $color-neutral-300;
    border: 0;

    @include breakpoint(medium down) {
      margin: 18px 0;
    }
  }

  .cover {
    display: flex;
    flex-flow: column wrap;
    gap: 8px 0;
    align-self: stretch;
    justify-content: center;
    width: 100%;
    max-width: 1216px;
  }
}

.bottom {
  align-self: stretch;
  height: 22px;
  min-height: 22px;
  padding-bottom: 16px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  color: $color-neutral-500;

  .infoLeft,
  .infoRight {
    display: flex;
    gap: 0 32px;
    align-items: center;
    justify-content: center;

    @include breakpoint(small down) {
      flex-direction: column;
    }

    a {
      text-decoration: none;
    }
  }

  .infoRight {
    @include breakpoint(small down) {
      gap: 24px;
    }
  }

  @include breakpoint(small down) {
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    height: auto;
  }

  .infoTop {
    display: flex;
    gap: 0 8px;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    p {
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .brand {
    padding: 4px 8px;
    font-weight: bold;
    color: $color-white;
    background: $color-black;
  }
}
