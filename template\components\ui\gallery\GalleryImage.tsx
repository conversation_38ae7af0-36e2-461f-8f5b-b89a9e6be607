import clsx from "clsx";
import NextImage from "next/image";
import { useId } from "react";
import striptags from "striptags";
import styles from "./GalleryImage.module.scss";

interface GalleryImageProps {
  src: string;
  poster?: string | null;
  caption?: string | null;
  alt?: string | null;
  onClick?: () => void;
  playable?: boolean;
}

// FIXME: A11y, handle the case with no poster and a caption
export default function GalleryImage({ src, poster, caption, alt, onClick, playable }: GalleryImageProps) {
  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault();
    onClick?.();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === " " || event.key === "Enter") {
      event.preventDefault();
      onClick?.();
    }
  };

  return (
    <a
      href={src}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      aria-haspopup="dialog"
      className={styles.imageLink}
      data-disable-progress={true}
    >
      <CaptionOverlay caption={caption}>
        {poster && (
          <NextImage
            className={styles.image}
            src={poster}
            fill={true}
            // TODO: Handle videos as well
            alt={alt ? `Agrandir l'image : ${alt}` : "Agrandir l'image décorative"}
            sizes="(max-width: 480px) 100vw, (max-width: 768px) 75vw, 33vw"
            loading="lazy"
            quality={85}
          />
        )}
        {playable && <i className={clsx("far fa-play", styles.playIcon)} aria-hidden="true"></i>}
      </CaptionOverlay>
    </a>
  );
}

/**
 * Wrap the image with an accessible overlay.
 */
function CaptionOverlay({ caption, children }: React.PropsWithChildren<{ caption?: string | null }>) {
  const captionId = useId();

  return caption ? (
    <figure role="figure" aria-labelledby={captionId}>
      {children}

      <figcaption id={captionId} className={styles.overlay}>
        <i className={clsx("far fa-expand", styles.expandIcon)} aria-hidden="true"></i>

        <span dangerouslySetInnerHTML={{ __html: striptags(caption, ["strong", "b", "em", "i"]) }}></span>
      </figcaption>
    </figure>
  ) : (
    <>
      {children}
      <div className={styles.overlay}>
        <i className={clsx("far fa-expand", styles.expandIcon)} aria-hidden="true"></i>
      </div>
    </>
  );
}
