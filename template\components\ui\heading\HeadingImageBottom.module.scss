@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heading {
  position: relative;
  padding-top: 56px;

  &:not(:has(.image)) {
    padding-bottom: 56px;
  }

  @include breakpoint(large up) {
    padding-block: 72px;
  }

  &::before {
    @include size(100%);
    @include absolute(0, null, null, 0);

    z-index: -1;
    content: "";
    background-color: $color-primary-50;
  }

  &:has(.image) {
    &::before {
      @include size(100%, 90%);

      @include breakpoint(medium up) {
        height: 75%;
      }

      @include breakpoint(large up) {
        height: 70%;
      }
    }
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 56px;
  max-width: 1248px;
  margin-inline: auto;

  @include breakpoint(medium up) {
    gap: 72px;
  }
}

.content,
.title {
  display: flex;
  flex-direction: column;
}

.content {
  display: flex;
  gap: 24px;
  padding-inline: 16px;

  @include breakpoint(medium up) {
    padding-inline: 24px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    width: 100%;
    max-width: 1216px;
    padding-inline: 0;
    margin-inline: auto;
  }
}

.surtitle {
  margin-bottom: 12px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    margin-bottom: 16px;
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.title {
  font-size: 3.2rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 4rem;
  }

  @include breakpoint(large up) {
    font-size: 5.6rem;
  }
}

.teaser {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 160%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.dates {
  font-size: 1.2rem;
  line-height: 100%;
  color: $color-neutral-700;
  text-transform: uppercase;

  &::before {
    display: block;
    width: 57px;
    margin-bottom: 12px;
    content: "";
    border-bottom: 1px solid $color-neutral-300;

    @include breakpoint(large up) {
      margin-bottom: 16px;
    }
  }

  span ~ span {
    &::before {
      margin-inline: 1ch;
      content: "-";
    }
  }
}

.image {
  margin-inline: auto;
  object-fit: cover;

  @include breakpoint(small only) {
    @include min-size(100%, 162px);
  }

  @include breakpoint(large up) {
    @include max-size(1216px, 521px);
  }
}
