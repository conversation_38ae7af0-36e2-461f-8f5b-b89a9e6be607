"use client";

import Hx from "@/components/ui/title/Hx";
import type { Publication } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import formatSize from "@/utils/formatSize";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { useId } from "react";
import styles from "./PublicationsItem.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));

interface PublicationItemProps {
  publication: Publication;
}

export default function PublicationItem({
  publication: { images, url, title, files, leadText, categories },
}: PublicationItemProps) {
  const [category] = categories ?? [];
  const [{ downloadUrl, viewUrl, size, extname } = {}] = files ?? [];
  const image = images?.ratio_A4_portrait ?? null;
  const titleId = useId();
  const fileMetaId = useId();
  const titleLevel = useTitleLevel();

  return (
    <article className={styles.publicationsItem}>
      <div className={styles.titleWrapper}>
        <Hx level={titleLevel} className={styles.title} id={titleId}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </Hx>
      </div>
      {leadText && <p className={styles.teaser}>{leadText}</p>}
      <div className={styles.imageWrapper}>
        {image?.url && (
          <Image
            src={image.url}
            width={image.width}
            height={image.height}
            alt={image.alt ?? ""}
            sizes="(max-width: 1301px) 152px, 240px"
          />
        )}
      </div>
      <div className={styles.actions}>
        {files.length === 1 ? (
          extname &&
          size &&
          downloadUrl && (
            <p id={fileMetaId} className={styles.documentsInfo} aria-roledescription="Type et poids du fichier">
              Document {extname.toUpperCase()} - {formatSize(size)}
            </p>
          )
        ) : (
          <>
            <p id={fileMetaId} className={styles.documentsInfo} aria-roledescription="Nombre de documents">
              {files.length} document{files.length > 1 ? "s" : ""}
            </p>
            <div className={styles.buttons}>
              <Button
                asChild
                variant="outlined"
                startIcon="far fa-arrow-right"
                aria-describedby={`${titleId} ${fileMetaId}`}
              >
                {url ? (
                  <Link href={url} className={styles.itemLink}>
                    Consulter
                  </Link>
                ) : (
                  <span>Consulter</span>
                )}
              </Button>
            </div>
          </>
        )}
        {files.length === 1 && (downloadUrl || viewUrl) && (
          <div className={styles.buttons}>
            {downloadUrl && (
              <Button asChild variant="outlined" startIcon="far fa-arrow-to-bottom">
                <Link
                  href={downloadUrl}
                  download
                  className={styles.itemLink}
                  aria-describedby={`${titleId} ${fileMetaId}`}
                >
                  Télécharger
                </Link>
              </Button>
            )}
            {viewUrl && (
              <Button asChild variant="outlined" startIcon="far fa-book-open">
                <Link href={viewUrl} className={styles.itemLink} aria-describedby={titleId}>
                  Feuilleter
                </Link>
              </Button>
            )}
          </div>
        )}
      </div>
    </article>
  );
}
