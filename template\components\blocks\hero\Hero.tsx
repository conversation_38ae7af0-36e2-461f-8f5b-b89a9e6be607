import type { HeroBlock } from "@/generated/graphql/graphql";
import dynamic from "next/dynamic";
import "./Hero.scss";

const HeroImageCarousel = dynamic(() => import("./HeroImageCarousel"));
const HeroImage = dynamic(() => import("./HeroImage"));
const HeroVideo = dynamic(() => import("./HeroVideo"));

type HeroProps = Omit<HeroBlock, "__typename" | "innerBlocks">;

export default function Hero({ slides }: HeroProps) {
  const isVideoOnly = slides?.length === 1 && !!slides[0]?.videoSrc && !slides[0]?.imageSrc;
  const isCarousel = !!slides && slides.length > 1;

  if (isVideoOnly) {
    return <HeroVideo slides={slides} />;
  }

  return isCarousel ? <HeroImageCarousel slides={slides} /> : <HeroImage slides={slides} />;
}
