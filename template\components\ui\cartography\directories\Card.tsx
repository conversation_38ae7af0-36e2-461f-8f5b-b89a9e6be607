import { Image as ImageType } from "@/generated/graphql/graphql";
import Image from "next/image";
import styles from "./Card.module.scss";

interface CardProps {
  id: number;
  title?: string;
  category?: string;
  image?: ImageType;
  handleClick: () => void;
}

export default function Card({ title, category, image, handleClick }: CardProps) {
  return (
    <button type="button" className={styles.button} onClick={handleClick}>
      <h3 className={styles.title}>
        {category && (
          <span className={styles.category}>
            {category}
            <span className="sr-only">:</span>
          </span>
        )}
        {title && <p className={styles.titleText}>{title}</p>}
      </h3>

      {image?.url && (
        <div className={styles.thumbnailWrapper}>
          <Image
            className={styles.thumbnail}
            src={image.url}
            width={image?.width}
            height={image?.height}
            alt={image?.alt ?? "Location image"}
          />
        </div>
      )}
    </button>
  );
}
