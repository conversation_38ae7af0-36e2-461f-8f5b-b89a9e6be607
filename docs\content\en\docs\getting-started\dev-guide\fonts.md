# Step 1: Prepare the Font Files

## Create a Fonts Folder:
Inside your project (e.g., in the `citeopolis` directory), create a folder named `fonts`.
This folder will store all the font files for your chosen font family (e.g., Poppins).

## Add Font Files:
Place all the font styles (e.g., `Poppins-Regular.woff2`, `Poppins-Bold.woff2`, `Poppins-Light.woff2`, etc.) into the `fonts` folder.
Ensure the font files are in a supported format (e.g., `.woff2`, `.woff`, `.ttf`).

---

# Step 2: Configure Local Fonts in `layout.tsx`

## Import Required Modules:
In your `layout.tsx` file (inside the `app` folder), import the necessary modules:

```typescript
import type { Metadata } from "next";
import localFont from "next/font/local";
```

## Define Metadata:
Set the metadata for your page (e.g., title):

```typescript
export const metadata: Metadata = {
  title: "Citeopolis",
};
```

## Configure Local Fonts:
Use the `localFont` function to load the font files from the `fonts` folder.
Add each font file to the `src` array, specifying its path, weight, and style:

```typescript
const fonts = localFont({
  src: [
    {
      path: "../fonts/Poppins-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../fonts/Poppins-Black.woff2",
      weight: "900",
      style: "normal",
    },
    {
      path: "../fonts/Poppins-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../fonts/Poppins-BoldItalic.woff2",
      weight: "700",
      style: "italic",
    },
    {
      path: "../fonts/Poppins-RegularItalic.woff2",
      weight: "400",
      style: "italic",
    },
    {
      path: "../fonts/Poppins-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    // Add other weights and styles as needed
  ],
});
```

---

# Step 3: Apply the Font to the Layout

## Wrap the Layout with the Font:
In the `RootLayout` function, apply the font to the `<body>` tag by setting its `className` to `fonts.className`:

```typescript
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={fonts.className}>{children}</body>
    </html>
  );
}
```

This ensures that the font is applied globally to all text within the body of your application.

---

# Step 4: Verify the Font Change

## Run the Application:
Start your Next.js development server:

```bash
npm run dev
```

## Open your Application:
Visit your application in the browser and inspect the text to ensure the new font is applied.

## Check the Font Family:
Use browser developer tools (e.g., Chrome DevTools) to inspect an element and verify that the correct font family is being used.

---

# Step 5: (Optional) Switch to a Different Font

## If you want to change to a different font (e.g., Roboto):

### Replace Font Files:
Delete the existing font files from the `fonts` folder and add the new font files (e.g., `Roboto-Regular.woff2`, `Roboto-Bold.woff2`, etc.).

### Update the `localFont` Configuration:
Modify the `src` array in the `localFont` configuration to point to the new font files:

```typescript
const fonts = localFont({
  src: [
    {
      path: "../fonts/Roboto-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../fonts/Roboto-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    // Add other weights and styles as needed
  ],
});
```

### Restart the Development Server:
Restart your Next.js server to ensure the changes take effect.

