@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.dateInterval {
  display: flex;
  gap: 24px;
  align-items: center;
  font-weight: 700;
  line-height: 110%;
  color: $color-primary-500;

  @include breakpoint(large up) {
    gap: 32px;
  }

  &.size-md {
    font-size: 1.6rem;

    @include breakpoint(medium up) {
      font-size: 1.8rem;
    }

    @include breakpoint(large up) {
      font-size: 2.4rem;
    }
  }

  &.size-lg {
    font-size: 1.8rem;

    @include breakpoint(medium up) {
      font-size: 2rem;
    }

    @include breakpoint(large up) {
      font-size: 3.2rem;
    }
  }
}

.arrowIcon {
  font-size: 1.6rem;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}
