"use client";

import useControllableState from "@/lib/hooks/useControllableState";
import clsx from "clsx";
import maplibre, { FullscreenControl, GeoJSONSource, NavigationControl } from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";
import { useEffect, useMemo, useRef, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";
import { consolidateMarkers } from "./consolidateMarkers";
import blueMarker from "./icons/marker-blue.png";
import redMarker from "./icons/marker-red.png";
import styles from "./Map.module.scss";
import type { MapProps, Marker } from "./types";

/**
 * Renders an interactive map with markers.
 */
export default function MapComponent({
  className,
  clustering,
  controls,
  defaultCenter = [2.3522, 48.8566],
  defaultZoom = 10,
  fitBoundsToMarkers = true,
  interactive = true,
  markers = [],
  maxZoom = 22,
  minZoom = 0,
  onMapLoad,
  onSelectionChange,
  selectedMarkerIds,
  scrollOverlay,
  ...restProps
}: React.HTMLAttributes<HTMLDivElement> & MapProps) {
  const [selectedMarkers, setSelectedMarkers] = useControllableState<Marker[]>({
    prop: markers.filter((m) => selectedMarkerIds?.includes(m.id)),
    defaultProp: [],
    onChange: onSelectionChange,
  });

  // Let the markers be selected if it has selection props defined.
  const selectable = useMemo(
    () => Boolean(onSelectionChange || selectedMarkerIds),
    [onSelectionChange, selectedMarkerIds]
  );

  const mapElement = useRef<HTMLDivElement>(null);
  const map = useRef<maplibre.Map | null>(null);

  const [overlayMessage, setOverlayMessage] = useState<string>("");
  const clearOverlayDebounced = useDebounceCallback(() => {
    setOverlayMessage("");
  }, 1000);

  useEffect(
    () => {
      map.current = new maplibre.Map({
        center: defaultCenter,
        container: mapElement.current!,
        interactive,
        maxZoom,
        minZoom,
        style: "https://tiles.stadiamaps.com/styles/osm_bright.json",
        zoom: defaultZoom,
        attributionControl: false,
      });

      map.current.on("load", async () => {
        const { data } = await map.current!.loadImage(blueMarker.src);

        map.current!.addImage("marker-icon", data);

        const { data: data2 } = await map.current!.loadImage(redMarker.src);

        map.current!.addImage("selected-marker-icon", data2);

        map.current?.setProjection({
          type: "globe",
        });

        map.current?.addSource("markers", {
          type: "geojson",
          data: {
            type: "FeatureCollection",
            features: consolidateMarkers(markers).map((marker) => {
              const selected =
                "point_ids" in (marker.data ?? {})
                  ? selectedMarkers.some((s) => marker.data.point_ids.includes(s.id))
                  : selectedMarkers.some((s) => s.id === marker.id);

              return {
                type: "Feature",
                geometry: {
                  type: "Point",
                  coordinates: marker.coordinates,
                },
                properties: {
                  id: marker.id,
                  icon: marker.icon ?? "",
                  selected: selectable ? selected : false,
                  ...marker.data,
                },
              };
            }),
          },
          generateId: true,
          cluster: clustering?.enabled ?? true,
          clusterMaxZoom: clustering?.maxZoom ?? 14,
          clusterRadius: clustering?.radius ?? 50,
        });

        map.current?.setProjection({
          type: "globe",
        });

        // TODO: Add as configuration
        map.current?.addLayer({
          id: "markers-outline",
          type: "circle",
          source: "markers",
          filter: ["all", ["!", ["has", "point_count"]], ["get", "selected"]],
          paint: {
            "circle-color": "transparent",
            "circle-radius": 37,
            "circle-stroke-color": "transparent",
            "circle-stroke-width": 8,
            "circle-stroke-opacity": 0.65,
          },
        });

        map.current?.addLayer({
          id: "markers",
          type: "symbol",
          source: "markers",
          filter: ["!", ["has", "point_count"]],
          layout: {
            "icon-image": [
              "case",
              ["get", "selected"],
              ["image", "selected-marker-icon"],
              ["coalesce", ["image", ["get", "icon"]], ["image", "marker-icon"]],
            ],
            "icon-allow-overlap": true,
            "text-size": 14,
            "text-line-height": 1.2,
            "text-anchor": "bottom",
            "text-allow-overlap": true,
            "text-field": ["case", [">", ["get", "density_count"], 1], ["to-string", ["get", "density_count"]], ""],
          },
          paint: {
            "text-color": "white",
          },
        });

        map.current?.addLayer({
          id: "clusters",
          type: "circle",
          source: "markers",
          filter: ["has", "point_count"],
          paint: {
            "circle-color": "#214FAB",
            "circle-radius": 28,
            "circle-stroke-color": "#214FAB",
            "circle-stroke-width": 11,
            "circle-stroke-opacity": 0.5,
          },
        });

        map.current?.addLayer({
          id: "cluster-count",
          type: "symbol",
          source: "markers",
          filter: ["has", "point_count"],
          layout: {
            "text-field": ["get", "point_count"],
            "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"],
            "text-size": 16,
            "text-line-height": 1.3,
            "text-allow-overlap": true,
          },
          paint: {
            "text-color": "#FFFFFF",
          },
        });

        // Zoom the cluster to the extent
        map.current?.on("click", "clusters", async (event) => {
          const features = map.current!.queryRenderedFeatures(event.point, {
            layers: ["clusters"],
          });
          const clusterId = features[0].properties.cluster_id;
          const zoom = await map.current!.getSource<GeoJSONSource>("markers")!.getClusterExpansionZoom(clusterId);

          if (features[0].geometry.type === "Point") {
            map.current!.easeTo({
              center: features[0].geometry.coordinates as [number, number],
              zoom,
            });
          }
        });

        // Trigger the marker click
        map.current?.on("click", "markers", (event) => {
          if (!selectable) {
            return;
          }

          event.preventDefault();

          // If the marker is dense, trigger the special onClick fn
          if (event.features && "density_count" in event.features?.[0].properties) {
            const pointIds: (string | number)[] = JSON.parse(event.features[0].properties.point_ids);
            const groupedMarkers = markers.filter((m) => pointIds.includes(m.id));

            setSelectedMarkers(groupedMarkers);
            return;
          }

          const marker = markers.find((m) => m.id === event.features?.[0].properties.id);

          if (marker) {
            setSelectedMarkers([marker]);
          }
        });

        // Deselect the markers when clicking on non markers elements
        map.current?.on("click", (event) => {
          clearOverlayDebounced.flush();

          if (event._defaultPrevented || !selectable) {
            return;
          }

          setSelectedMarkers([]);
        });

        // On mouse wheel, prevent scrolling if overlay is enabled
        map.current?.on("wheel", (event) => {
          if (scrollOverlay && !event.originalEvent.ctrlKey) {
            event.preventDefault();
            setOverlayMessage("Utilisez CTRL + molette pour zoomer la carte");
            clearOverlayDebounced();
          } else {
            clearOverlayDebounced.flush();
          }
        });

        // On single touch (pan start), prevent gesture if overlay is enabled
        map.current?.on("touchstart", (event) => {
          if (scrollOverlay && event.points.length < 2) {
            event.preventDefault();
            setOverlayMessage("Utilisez deux doigts pour déplacer la carte");
            clearOverlayDebounced();
          } else {
            clearOverlayDebounced.flush();
          }
        });

        // If the map is moved, hide the overlay
        map.current?.on("move", () => {
          clearOverlayDebounced.flush();
        });

        // Fit the bounds to the contained markers
        if (fitBoundsToMarkers && markers.length > 0) {
          const bounds = new maplibre.LngLatBounds();

          for (const marker of markers) bounds.extend(marker.coordinates);
          map.current?.fitBounds(bounds, {
            padding: 50,
            duration: 1000,
          });
        }

        onMapLoad?.(map.current!);
      });

      return () => {
        if (map.current) {
          map.current.remove();
          map.current = null;
        }

        clearOverlayDebounced.cancel();
      };
    },
    /** eslint-disable-next-line react-hooks/exhaustive-deps -- Run once */
    []
  );

  const fullscreenControlRef = useRef<FullscreenControl>(null);
  const navigationControlRef = useRef<NavigationControl>(null);

  // Update map settings
  useEffect(() => {
    if (!map.current) return;

    map.current.setMaxZoom(maxZoom);
    map.current.setMinZoom(minZoom);

    if (interactive) {
      map.current.scrollZoom.enable();
      map.current.boxZoom.enable();
      map.current.dragRotate.enable();
      map.current.dragPan.enable();
      map.current.keyboard.enable();
      map.current.doubleClickZoom.enable();
      map.current.touchZoomRotate.enable();
    } else {
      map.current.scrollZoom.disable();
      map.current.boxZoom.disable();
      map.current.dragRotate.disable();
      map.current.dragPan.disable();
      map.current.keyboard.disable();
      map.current.doubleClickZoom.disable();
      map.current.touchZoomRotate.disable();
    }

    const currentFullscreenControl = fullscreenControlRef.current;
    const currentNavigationControl = navigationControlRef.current;

    if (currentFullscreenControl) {
      try {
        map.current.removeControl(currentFullscreenControl);
      } catch (error) {
        console.warn("Error removing fullscreen control", error);
      }
    }

    if (currentNavigationControl) {
      try {
        map.current.removeControl(currentNavigationControl);
      } catch (error) {
        console.warn("Error removing navigation control", error);
      }
    }

    if (interactive && (controls?.fullscreen ?? true)) {
      fullscreenControlRef.current = new FullscreenControl();
      map.current.addControl(fullscreenControlRef.current);
    }

    if (interactive && (controls?.zoom ?? true)) {
      navigationControlRef.current = new NavigationControl();
      map.current.addControl(navigationControlRef.current, controls?.position ?? "top-right");
    }

    // Cleanup function
    return () => {
      if (!map.current) return;

      if (fullscreenControlRef.current) {
        try {
          map.current.removeControl(fullscreenControlRef.current);
        } catch (error) {
          console.warn("Cleanup error removing fullscreen control", error);
        }

        fullscreenControlRef.current = null;
      }

      if (navigationControlRef.current) {
        try {
          map.current.removeControl(navigationControlRef.current);
        } catch (error) {
          console.warn("Cleanup error removing navigation control", error);
        }

        navigationControlRef.current = null;
      }
    };
  }, [maxZoom, minZoom, interactive, controls, map]);

  // Update markers source if it has changed
  // TODO: Test this
  useEffect(() => {
    const markersSource = map.current?.getSource<GeoJSONSource>("markers");

    if (!markersSource) {
      return;
    }

    // FIXME: Use `updateData` for faster diffs
    markersSource.setData({
      type: "FeatureCollection",
      features: consolidateMarkers(markers).map((marker) => {
        const selected =
          "point_ids" in (marker.data ?? {})
            ? selectedMarkers.some((s) => marker.data.point_ids.includes(s.id))
            : selectedMarkers.some((s) => s.id === marker.id);

        return {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: marker.coordinates,
          },
          properties: {
            id: marker.id,
            icon: marker.icon ?? "",
            selected: selectable ? selected : false,
            ...marker.data,
          },
        };
      }),
    });
  }, [markers, selectedMarkers, selectable]);

  return (
    <div ref={mapElement} className={clsx(styles.map, className)} {...restProps}>
      <div className={styles.overlay} aria-hidden="true" data-visible={String(overlayMessage.length > 0)}>
        {overlayMessage}
      </div>
    </div>
  );
}
