import clsx from "clsx";
import styles from "./Checkbox.module.scss";

interface CheckboxProps {
  error?: boolean;
  size?: "small" | "medium";
}

export default function Checkbox({
  error,
  size,
  className,
  ...restProps
}: CheckboxProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, "size" | "type">) {
  return (
    <input
      type="checkbox"
      className={clsx(styles.input, error && styles.error, size && styles[`size-${size}`], "far fa-check", className)}
      {...restProps}
    />
  );
}
