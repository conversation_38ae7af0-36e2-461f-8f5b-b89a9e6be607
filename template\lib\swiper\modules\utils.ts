export function makeElementsArray<T extends Element>(element: T | undefined | null | (T | undefined | null)[]): T[] {
  return (Array.isArray(element) ? element : [element]).filter((e) => !!e);
}

export function elementIndex(element: HTMLElement): number | undefined {
  if (!element?.parentElement) return undefined;
  return [...element.parentElement.children].indexOf(element);
}

export function classesToSelector(classes = ""): string {
  return `.${classes
    .trim()
    .replaceAll(/([!()+./:[\]])/g, String.raw`\$1`)
    .replaceAll(" ", ".")}`;
}

function randomChar(): string {
  return Math.round(16 * Math.random()).toString(16);
}

export function getRandomNumber(size = 16): string {
  return "x".repeat(size).replaceAll("x", randomChar);
}
