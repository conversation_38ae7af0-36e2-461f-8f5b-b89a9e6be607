"use client";

import { Form as BaseFormProps } from "@/generated/graphql/graphql";
import clsx from "clsx";
import { useCallback, useId, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, SubmitHandler, useForm } from "react-hook-form";
import FieldRenderer from "./fields/FieldRenderer";
import styles from "./Form.module.scss";
import FormFeedback from "./FormFeedback";
import FormNavigation from "./FormNavigation";
import RecapScreen from "./RecapScreen";
import StepIndicator from "./StepIndicator";

type Inputs = Record<string, unknown>;

type FormProps = Omit<BaseFormProps, "__typename"> & {
  className?: string;
  onSubmit?: (data: Inputs) => Promise<void>;
};

export default function Form({ steps, totalSteps, submitButton, className, onSubmit }: FormProps) {
  const actualTotalSteps = totalSteps + 1;

  const [currentStep, setCurrentStep] = useState(0);
  const [currentError, setCurrentError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const isRecapStep = currentStep === totalSteps;
  const step = isRecapStep ? null : steps[currentStep];

  const methods = useForm({
    shouldUnregister: false,
    mode: "onChange",
  });

  const {
    handleSubmit,
    trigger,
    setFocus,
    reset,
    setError,
    clearErrors,
    formState: { errors, isSubmitting, isSubmitSuccessful },
  } = methods;

  const legendId = useId();

  const handlePrevious = () => setCurrentStep((prev) => Math.max(prev - 1, 0));

  const getStepFieldNames = useCallback(
    (stepIndex: number): string[] => {
      const step = steps[stepIndex];
      const fieldNames: string[] = [];

      const collectFieldNames = (fields: unknown[]) => {
        for (const field of fields) {
          if (field && typeof field === "object") {
            if ("name" in field && field.name) {
              fieldNames.push(field.name as string);
            }

            if ("fields" in field && Array.isArray(field.fields)) {
              collectFieldNames(field.fields);
            }
          }
        }
      };

      if (step?.fields) collectFieldNames(step.fields);

      return fieldNames;
    },
    [steps]
  );

  const handleNext = async () => {
    const stepFieldNames = getStepFieldNames(currentStep);

    const isValid = await trigger(stepFieldNames.length > 0 ? stepFieldNames : undefined);

    if (isValid) {
      setCurrentStep((prev) => prev + 1);
    } else {
      const firstErrorField = Object.keys(errors)[0];

      if (firstErrorField) setFocus(firstErrorField);
    }
  };

  const submitHandler: SubmitHandler<Inputs> = async (data) => {
    try {
      setCurrentError(null);
      clearErrors();

      await (onSubmit ? onSubmit(data) : new Promise((resolve) => setTimeout(resolve, 1000)));

      setSuccessMessage("Formulaire envoyé avec succès");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Une erreur est survenue";

      setCurrentError(errorMessage);
      setError("root", { message: errorMessage });
    }
  };

  const handleReset = useCallback(() => {
    reset();
    setCurrentError(null);
    setSuccessMessage(null);
  }, [reset]);

  const handleStepReset = useCallback(() => {
    setCurrentStep(0);
    setCurrentError(null);
    setSuccessMessage(null);
  }, []);

  const handleNavigateToStep = useCallback((step: number) => {
    setCurrentStep(step);
  }, []);

  const submissionState = {
    isSubmitting,
    isSuccess: isSubmitSuccessful,
    error: currentError,
    successMessage,
  };

  return (
    <section
      className={clsx(styles.formContainer, className)}
      role={totalSteps > 1 ? "region" : undefined}
      aria-labelledby={totalSteps > 1 ? legendId : undefined}
    >
      {actualTotalSteps > 1 && (
        <StepIndicator id={legendId} steps={steps} currentStep={currentStep} totalSteps={actualTotalSteps} />
      )}

      <form className={styles.form} onSubmit={handleSubmit(submitHandler)} noValidate>
        <div className={styles.formStep}>
          <FormProvider {...methods}>
            {isRecapStep ? <RecapScreen steps={steps} /> : <FieldRenderer fields={step?.fields ?? []} />}
          </FormProvider>
        </div>

        <FormNavigation
          onPrev={handlePrevious}
          onNext={handleNext}
          totalSteps={totalSteps}
          currentStep={currentStep}
          isRecapStep={isRecapStep}
          actualTotalSteps={actualTotalSteps}
          submitLabel={submitButton?.label ?? undefined}
          disabled={submissionState.isSuccess}
          isSubmitting={submissionState.isSubmitting}
        />

        <FormFeedback
          onReset={handleReset}
          onStepReset={handleStepReset}
          submissionState={submissionState}
          onNavigateToStep={handleNavigateToStep}
        />
      </form>
    </section>
  );
}
