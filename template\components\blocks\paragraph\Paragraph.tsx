import { ParagraphBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import stripTags from "striptags";
import styles from "./Paragraph.module.scss";

type ParagraphProps = Partial<Omit<ParagraphBlock, "__typename" | "innerBlocks">>;

export default function Paragraph({ anchor, html = "", align, className }: ParagraphProps) {
  const innerHTML = stripTags(html, ["b", "i", "strong", "em", "a"]);

  return (
    innerHTML && (
      <p
        id={anchor ?? undefined}
        style={{ textAlign: align ?? undefined }}
        className={clsx("block-paragraph contained", styles.paragraph, className)}
        // eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- HTML is sanitized
        dangerouslySetInnerHTML={{ __html: innerHTML }}
      />
    )
  );
}
