import Button from "@/components/ui/button/Button";
import modalStyles from "@/components/ui/modal/Modal.module.scss";
import ToggleMenu from "@/components/ui/toggle-menu/ToggleMenu";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { MenuItem } from "@/generated/graphql/graphql";
import { DialogClose, DialogContent, DialogTitle } from "@radix-ui/react-dialog";
import clsx from "clsx";
import NextLink from "next/link";
import { useRef } from "react";
import { focusable } from "tabbable";
import styles from "./MenuModal.module.scss";

interface MenuModalProps {
  menuItems?: MenuItem[];
}

interface Link {
  title: string;
  icon?: string;
  url: string;
}

// TODO: These links are static
const quickAccess1: Link[] = [
  // { title: "Démarches", icon: "fa-computer-mouse-scrollwheel", url: "#" },
  // { title: "Mon compte", icon: "fa-circle-user", url: "#" },
];

// TODO: These links are static
const quickAccess2: Link[] = [
  { title: "Actualités", icon: "fa-newspaper", url: "/actualites" },
  { title: "Agenda", icon: "fa-calendar", url: "/evenements" },
  { title: "Publications", icon: "fa-book", url: "/publications" },
  // { title: "Lettre d'information", icon: "fa-paper-plane" },
  // { title: "Espace presse", icon: "fa-link" },
  { title: "Contact", icon: "fa-envelope", url: "/contactez-nous" },
  // { title: "Extranet", icon: "fa-lock" },
];

export default function MenuModal({ menuItems }: MenuModalProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <DialogContent
      aria-modal="true"
      aria-describedby={undefined}
      className={clsx(modalStyles.modal, modalStyles.fullscreen, styles.menuModal)}
      onOpenAutoFocus={(event) => {
        if (contentRef.current) {
          const elements = focusable(contentRef.current, { includeContainer: false });

          if (elements.length > 0) {
            event.preventDefault();
            elements[0]?.focus();
          }
        }
      }}
    >
      <div className={styles.header}>
        <DialogClose asChild>
          <Tooltip content="Fermer le menu principal" placement="left">
            <Button className={clsx(modalStyles.closeButton, styles.closeButton)} variant="text">
              <i className="fas fa-xmark" aria-hidden="true"></i>
              <span className="sr-only">Fermer le menu principal</span>
            </Button>
          </Tooltip>
        </DialogClose>

        <DialogTitle asChild>
          <h2 className="sr-only">Menu principal</h2>
        </DialogTitle>
      </div>

      <div className={styles.content} ref={contentRef}>
        <div className={styles.mainContent}>
          <div className={styles.toggleMenuWrapper}>
            <ToggleMenu menuItems={menuItems} />
          </div>
        </div>

        <div className={styles.sideContent} role="group" aria-roledescription="Accès rapides">
          <div className={styles.quickAccessWrapper}>
            <ul className={clsx(styles.list, styles.primaryList)}>
              {quickAccess1.map((item, index) => (
                <li key={index}>
                  <NextLink className={styles.link} href={item.url}>
                    {item.icon && <i className={clsx("far", item.icon)} aria-hidden="true"></i>}
                    <span>{item.title}</span>
                  </NextLink>
                </li>
              ))}
            </ul>

            <div className={styles.divider}></div>

            <ul className={clsx(styles.list, styles.secondaryList)}>
              {quickAccess2.map((item, index) => (
                <li key={index}>
                  <NextLink className={styles.link} href={item.url}>
                    {item.icon && <i className={clsx("far", item.icon)} aria-hidden="true"></i>}
                    <span>{item.title}</span>
                  </NextLink>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </DialogContent>
  );
}
