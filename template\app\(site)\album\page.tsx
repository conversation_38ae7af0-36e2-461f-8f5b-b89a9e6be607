import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Gallery from "@/components/ui/gallery/Gallery";
import Heading from "@/components/ui/heading/Heading";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";
import striptags from "striptags";
import type { SlideIframe, SlideImage } from "yet-another-react-lightbox";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const ALBUM_QUERY = graphql(`
  query GetAlbum($url: URL!) {
    route(url: $url) {
      ... on Album {
        id
        mediaCount
        photoCount
        videoCount
        media {
          ... on AlbumVideo {
            url
            caption
            provider
            thumbnail {
              url
              width
              height
              alt
            }
          }
          ... on AlbumPhoto {
            alt
            caption
            src
            height
            width
          }
        }
        title
        status
        leadText
        structuredContent
        publicationDate
        modifiedDate
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        categories {
          relativeUrl
          title
          description
          parent {
            __typename
          }
        }
        breadcrumbs {
          items {
            title
            url
            siblings {
              title
              url
            }
          }
        }
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: ALBUM_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "Album");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: ALBUM_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "all",
  });

  assert.ok(data.route?.__typename === "Album");

  const { title, leadText, categories, pager, breadcrumbs, modifiedDate, publicationDate, media, structuredContent } =
    data.route;
  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");
  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  // Generate the gallery slides based on the included media
  const slides: (SlideImage | SlideIframe)[] = media
    .map((item) => {
      if (item.__typename === "AlbumPhoto" && item.src) {
        return {
          type: "image",
          src: item.src,
          width: item.width ?? undefined,
          height: item.height ?? undefined,
          alt: item.alt ?? "",
          description: striptags(item.caption ?? ""),
        } satisfies SlideImage;
      } else if (item.__typename === "AlbumVideo" && item.url && item.provider) {
        return {
          type: "iframe",
          src: item.url,
          poster: item.thumbnail?.url,
          // FIXME: This could be determined upfront by the alternative (or the provider)
          titleAttr: "Vidéo embarquée",
          description: striptags(item.caption ?? ""),
        } satisfies SlideIframe;
      }
    })
    .filter((slide) => !!slide);

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <Heading
        surtitle={surtitle}
        title={title ?? ""}
        leadText={leadText}
        tags={tags}
        tagsRoleDescription="Thématiques"
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />
      <div className="layout-2columns-right">
        <div className="column main">
          <Gallery slides={slides} />
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
      </div>
      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
