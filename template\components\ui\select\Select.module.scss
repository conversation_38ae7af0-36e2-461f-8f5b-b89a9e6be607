@use "sass:color";
@use "@/styles/lib/variables.scss" as *;

.select {
  width: 100%;
  padding: 12px 44px 12px 16px;
  font-size: 1.6rem;
  line-height: 130%;
  appearance: none;
  cursor: pointer;
  user-select: none;
  background: url("./chevron-down.svg") no-repeat right 16px center;

  &.variant-outlined {
    border: 1px solid $color-neutral-500;
    border-radius: 4px;

    &.error {
      background-color: color.adjust($color-danger, $lightness: 57%);
    }
  }

  &.variant-underline {
    border-bottom: 1px solid $color-neutral-500;

    &.error {
      color: $color-danger;
    }
  }

  &.variant-filled {
    background-color: $color-neutral-100;
    border-bottom: 1px solid $color-neutral-500;
    border-radius: 4px 4px 0 0;

    &.error {
      background-color: color.adjust($color-danger, $lightness: 57%);
    }
  }

  &.error {
    border-color: $color-danger;
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}
