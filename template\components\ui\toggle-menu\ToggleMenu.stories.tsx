import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import ToggleMenu from "./ToggleMenu";

const meta: Meta<typeof ToggleMenu> = {
  title: "Components/ToggleMenu",
  component: ToggleMenu,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof ToggleMenu>;

export const Default: Story = {
  args: {
    menuItems: [
      {
        title: "Vivre dans la Commune",
        level: 0,
        url: "/vivre-dans-la-commune",
        children: [
          {
            title: "Éducation & Jeunesse",
            level: 1,
            url: "/vivre-dans-la-commune/education",
            children: [
              // @ts-expect-error -- Incomplete menu item
              {
                title: "Écoles Maternelles",
                level: 2,
                url: "/vivre-dans-la-commune/education/ecoles-maternelles",
                children: [],
              },
              // @ts-expect-error -- Incomplete menu item
              {
                title: "Écoles Élémentaires",
                level: 2,
                url: "/vivre-dans-la-commune/education/ecoles-elementaires",
                children: [],
              },
            ],
          },
          {
            title: "Santé & Social",
            level: 1,
            url: "/vivre-dans-la-commune/sante-social",
            children: [
              // @ts-expect-error -- Incomplete menu item
              {
                title: "Pharmacies",
                level: 2,
                url: "/vivre-dans-la-commune/sante-social/pharmacies",
                children: [],
              },
              // @ts-expect-error -- Incomplete menu item
              {
                title: "Aides Sociales",
                level: 2,
                url: "/vivre-dans-la-commune/sante-social/aides-sociales",
                children: [],
              },
            ],
          },
        ],
      },
      // @ts-expect-error -- Incomplete menu item
      {
        title: "Contact",
        level: 0,
        url: "/contact",
        children: [],
      },
    ],
  },
};
