@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.quickAccessFocus {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  color: $color-white;
  background-color: $color-primary-500;
  border-radius: 4px;
  transition: background-color 300ms linear;

  &:hover {
    background-color: $color-primary-400;
  }

  @include breakpoint(large up) {
    flex-shrink: 0;
    max-width: 280px;
  }
}

.icon {
  max-width: 43px;
  max-height: 43px;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 130%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.arrowIcon {
  font-size: 1.8rem;
  line-height: normal;
}
