@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.item {
  position: relative;
  display: flex;
  flex-flow: row-reverse;
  gap: 12px;
  justify-content: flex-end;

  @include breakpoint(medium up) {
    flex-direction: column-reverse;
    gap: 16px;
  }
}

.imageWrapper {
  display: flex;
  align-items: flex-start;

  img {
    @include object-fit;

    width: 100%;
    min-width: 74px;
    min-height: 50px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(medium up) {
    font-size: 2.4rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}
