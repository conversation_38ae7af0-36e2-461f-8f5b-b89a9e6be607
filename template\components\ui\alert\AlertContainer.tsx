"use client";

import { graphql } from "@/generated/graphql";
import { useQuery } from "@apollo/client";
import dynamic from "next/dynamic";

const AlertRenderer = dynamic(() => import("./AlertRenderer"));

const ALERT_QUERY = graphql(`
  query GetAlerts {
    alertSearch {
      totalCount
      items {
        id
        variant
        title
        description
        modifiedDate
        action {
          url
          text
        }
      }
    }
  }
`);

export default function AlertContainer() {
  const { data, error, loading } = useQuery(ALERT_QUERY);

  if (loading || error || !data?.alertSearch) {
    return;
  }

  return <AlertRenderer entries={data?.alertSearch?.items ?? []} />;
}
