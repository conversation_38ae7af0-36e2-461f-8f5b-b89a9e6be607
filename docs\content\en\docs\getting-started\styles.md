---
title: "Styles"
weight: 1
---

# Styles

The project uses **SASS** in combination with **CSS modules**.

## CSS Modules

SCSS modules allows some styles to be scoped for a single component.
It avoids class name clashing across the whole website.

## Depth

SCSS modules are **scoped** and selectors must be written with minimal depth.

Example:

```scss
// ❌
.collapsible {
  .trigger {
    .triggerIcon {
    }
  }
  .content {
    p {
    }
  }
}
```

```scss
// ✅
.collapsible {
}

.trigger {
}

.triggerIcon {
}

.content {
  p {
  }
}
```

## Naming

The naming convention for CSS modules is **camelCase**.

Example:

```scss
// ✅
.navigation {
}

// ✅
.navigationMenu {
}

// ❌
.NavigationMenu__inner {
}

// ❌
.navigation-menu {
}
```

{{% hint info %}}
CSS modules classes are named this way so they are easily usable in React components.
{{% /hint %}}

The naming convention for the CSS module file, is the same as the component's name.

Example:

```sh
# ✅
Button.module.scss

# ❌
button.module.scss

# ❌
customButton.module.scss
```

{{% hint info %}}
CSS modules file name must correspond to an existing component or idea. It is allowed to import a CSS module from another component **if both are in the same scope**.
{{% /hint %}}

The naming convention for global CSS classes is **snake-case**.

Example:

```scss
// ✅
.overflow-hidden {
}

// ❌
.body--overflow-hidden {
}

// ❌
.Body__overflow-hidden {
}
```
