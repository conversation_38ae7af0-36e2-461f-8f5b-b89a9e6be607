@use "sass:color";
@use "sass:math";
@use "sass:meta";
@use "sass:string";

@function str-replace($string, $search, $replace: "") {
  $index: string.index($string, $search);

  @if $index {
    // stylelint-disable-next-line scss/operator-no-newline-after
    @return string.slice($string, 1, $index - 1) + $replace +
      str-replace(string.slice($string, $index + string.length($search)), $search, $replace);
  }

  @return $string;
}

// Encode symbols
@function url-encode($string) {
  $map: (
    "%": "%25",
    "<": "%3C",
    ">": "%3E",
    " ": "%20",
    "!": "%21",
    "*": "%2A",
    "'": "%27",
    '"': "%22",
    "(": "%28",
    ")": "%29",
    ";": "%3B",
    ":": "%3A",
    "@": "%40",
    "&": "%26",
    "=": "%3D",
    "+": "%2B",
    "$": "%24",
    ",": "%2C",
    "/": "%2F",
    "?": "%3F",
    "#": "%23",
    "[": "%5B",
    "]": "%5D",
  );
  $new: $string;

  @each $search, $replace in $map {
    $new: str-replace($new, $search, $replace);
  }

  @return $new;
}

// Format the SVG as a URL
@function inline-svg($string) {
  @return url("data:image/svg+xml,#{url-encode($string)}");
}

/// Remove the unit of a length
/// @param {Number} $number - Number to remove unit from
/// @return {Number} - Unitless number
@function strip-unit($number) {
  @if meta.type-of($number) == "number" and not math.is-unitless($number) {
    @return math.div($number, ($number * 0 + 1));
  }

  @return $number;
}

// Calcul em size depending of the context
// Use em(fontSizeDoIWantInEm, fontContextInEM)
@function em($fontSize, $context) {
  @return #{math.div($fontSize, $context)}em;
}

@function lighten-color($color, $percentage) {
  $adjusted-percentage: if(math.is-unitless($percentage), $percentage * 1%, $percentage);

  @return color.adjust($color, $lightness: $adjusted-percentage);
}

@function darken-color($color, $percentage) {
  $adjusted-percentage: if(math.is-unitless($percentage), $percentage * 1%, $percentage);

  @return color.adjust($color, $lightness: -1 * $adjusted-percentage);
}
