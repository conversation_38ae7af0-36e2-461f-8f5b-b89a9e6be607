"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@citeopolis-fontawesome+optimized@1.0.5";
exports.ids = ["vendor-chunks/@citeopolis-fontawesome+optimized@1.0.5"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/brands.css":
/*!***********************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/brands.css ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"38158e3d7738\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjaXRlb3BvbGlzLWZvbnRhd2Vzb21lK29wdGltaXplZEAxLjAuNS9ub2RlX21vZHVsZXMvQGNpdGVvcG9saXMtZm9udGF3ZXNvbWUvb3B0aW1pemVkL2Nzcy9icmFuZHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBjaXRlb3BvbGlzLWZvbnRhd2Vzb21lK29wdGltaXplZEAxLjAuNVxcbm9kZV9tb2R1bGVzXFxAY2l0ZW9wb2xpcy1mb250YXdlc29tZVxcb3B0aW1pemVkXFxjc3NcXGJyYW5kcy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIzODE1OGUzZDc3MzhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/brands.css\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/fontawesome.css":
/*!****************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/fontawesome.css ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"315b587c406e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjaXRlb3BvbGlzLWZvbnRhd2Vzb21lK29wdGltaXplZEAxLjAuNS9ub2RlX21vZHVsZXMvQGNpdGVvcG9saXMtZm9udGF3ZXNvbWUvb3B0aW1pemVkL2Nzcy9mb250YXdlc29tZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGNpdGVvcG9saXMtZm9udGF3ZXNvbWUrb3B0aW1pemVkQDEuMC41XFxub2RlX21vZHVsZXNcXEBjaXRlb3BvbGlzLWZvbnRhd2Vzb21lXFxvcHRpbWl6ZWRcXGNzc1xcZm9udGF3ZXNvbWUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzE1YjU4N2M0MDZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/fontawesome.css\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/regular.css":
/*!************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/regular.css ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"89adc56d6347\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BjaXRlb3BvbGlzLWZvbnRhd2Vzb21lK29wdGltaXplZEAxLjAuNS9ub2RlX21vZHVsZXMvQGNpdGVvcG9saXMtZm9udGF3ZXNvbWUvb3B0aW1pemVkL2Nzcy9yZWd1bGFyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAY2l0ZW9wb2xpcy1mb250YXdlc29tZStvcHRpbWl6ZWRAMS4wLjVcXG5vZGVfbW9kdWxlc1xcQGNpdGVvcG9saXMtZm9udGF3ZXNvbWVcXG9wdGltaXplZFxcY3NzXFxyZWd1bGFyLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg5YWRjNTZkNjM0N1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/regular.css\n");

/***/ })

};
;