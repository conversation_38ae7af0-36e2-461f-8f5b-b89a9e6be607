import { loadConfigFile } from "@citeopolis/butcher";
import * as prompts from "@clack/prompts";
import { execa } from "execa";
import fs from "fs-extra";
import { tmpdir } from "node:os";
import path from "node:path";
import process from "node:process";
import { logger } from "../utils/logger";

const CITEOPOLIS_PACKAGE_NAME = "@citeopolis/template";

interface CreateProjectOptions {
  template?: string; // path to local template
  features?: string[];
  skipButcher?: boolean;
}

export async function createProject(cwd?: string, options?: CreateProjectOptions) {
  const { directory } = await prompts.group(
    {
      directory: () => {
        if (cwd) {
          return Promise.resolve(path.resolve(cwd));
        }

        const defaultPath = "./";
        return prompts.text({
          message: "Where would you like your project to be created?",
          placeholder: `  (hit Enter to use '${defaultPath}')`,
          defaultValue: defaultPath,
        });
      },
      force: async ({ results }) => {
        // TODO: Ignore `.git` subfolder
        if (fs.existsSync(results.directory!) && !isDirEmpty(results.directory!)) {
          const force = await prompts.confirm({
            message: "Directory not empty. Continue?",
            initialValue: false,
          });

          if (prompts.isCancel(force) || !force) {
            prompts.cancel("Exiting.");
            // eslint-disable-next-line unicorn/no-process-exit -- Hard exit
            process.exit(0);
          }
        }
      },
    },
    {
      onCancel: () => {
        prompts.cancel("Operation cancelled.");
        // eslint-disable-next-line unicorn/no-process-exit -- Hard exit
        process.exit(0);
      },
    }
  );

  logger.break();
  logger.info("Creating a new Citéopolis project. This might take a few minutes...");

  await (options?.template ? setupLocalTemplate(options.template, directory) : setupRemoteTemplate(directory));

  const butcherConfigFile = path.join(directory, "butcher.json");

  if (fs.existsSync(butcherConfigFile)) {
    const butcher = await loadConfigFile(butcherConfigFile);

    await butcher.execute(options?.features ?? []);
  }

  logger.success("Citéopolis project successfully created! 🎉");

  logger.break();
  logger.log(`\tcd ${directory}`);
  logger.log(`\tnpm install`);
  logger.log(`\tnpm run dev`);
}

function isDirEmpty(path: string): boolean {
  return fs.readdirSync(path).length === 0;
}

async function setupLocalTemplate(templatePath: string, targetDirectory: string) {
  logger.info(`Using local template from: ${templatePath}`);

  if (!fs.existsSync(templatePath)) {
    throw new Error(`Template path does not exist: ${templatePath}`);
  }

  await fs.ensureDir(targetDirectory);

  await fs.copy(templatePath, targetDirectory, {
    overwrite: true,
    filter: (src) => {
      const relativePath = path.relative(templatePath, src);

      return (
        (!relativePath.includes("node_modules") && !relativePath.includes(".git") && !relativePath.startsWith(".")) ||
        relativePath === ""
      );
    },
  });

  logger.success("Template copied successfully!");
}

async function setupRemoteTemplate(targetDirectory: string) {
  // Get the template url by nicely asking the package registry
  // TODO: Handle error
  // TODO: Ensure we use the correct registry
  // const { stdout: url } = await execa("npm", ["view", CITEOPOLIS_PACKAGE_NAME, "dist.tarball"]);
  const { stdout: url } = await execa("npm", [
    "view",
    CITEOPOLIS_PACKAGE_NAME,
    "dist.tarball",
    "--registry=https://code.stratis.fr/api/v4/projects/5490/packages/npm/",
  ]);

  const templatePath = await fs.mkdtemp(path.join(tmpdir(), "citeopolis-template-"));

  await fs.ensureDir(templatePath);

  // Get the archive
  // TODO: Handle no response from url
  const response = await fetch(url);

  if (!response.ok) {
    throw new Error(`Failed to download template: ${response.statusText}`);
  }

  // Extract it
  const tarPath = path.resolve(templatePath, "template.tar.gz");

  await fs.writeFile(tarPath, Buffer.from(await response.arrayBuffer()));
  await execa("tar", [
    "-xzf",
    tarPath,
    "-C",
    templatePath,
    // Skip the first level /package/
    "--strip-components=1",
  ]);

  // Remove tar.gz file
  await fs.remove(tarPath);

  // Copy the files to the desired directory
  // TODO: Remove tmp dir on error
  await fs.move(path.join(templatePath), targetDirectory, { overwrite: true });
  await fs.remove(templatePath);
}
