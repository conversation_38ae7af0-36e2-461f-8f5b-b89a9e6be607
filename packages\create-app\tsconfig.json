{"compilerOptions": {"module": "es2022", "target": "es2022", "types": ["node"], "composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "isolatedModules": false, "resolveJsonModule": true, "baseUrl": "."}, "include": ["."], "exclude": ["node_modules", "dist"]}