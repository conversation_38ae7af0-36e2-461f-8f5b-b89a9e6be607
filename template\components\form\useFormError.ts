import { useEffect } from "react";
import { useFormContext } from "react-hook-form";

interface FormErrorOptions {
  validationMessage?: string | null;
}

export default function useFormError(name: string, { validationMessage = null }: FormErrorOptions = {}) {
  const {
    formState: { errors },
    setError,
  } = useFormContext();

  const error = errors[name];

  useEffect(() => {
    if (error && validationMessage) {
      setError(name, { message: validationMessage });
    } else if (!error?.message && error?.type === "required") {
      setError(name, { message: "Ce champ est requis." });
    }
  }, [error, name, validationMessage, setError]);

  return error;
}
