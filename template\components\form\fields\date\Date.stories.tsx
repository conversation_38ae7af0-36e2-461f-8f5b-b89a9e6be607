import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Date from "./Date";

const meta: Meta<typeof Date> = {
  title: "Form/Date",
  component: Date,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Date>;

export const DateOnly: Story = {
  args: {
    name: "date_1",
    label: "Date de naissance",
    description: "Sélectionnez votre date de naissance",
  },
};

export const DateTime: Story = {
  args: {
    name: "datetime_1",
    label: "Date et heure de rendez-vous",
    description: "Choisissez la date et l'heure",
  },
};

export const TimeOnly: Story = {
  args: {
    name: "time_1",
    label: "Heure de préférence",
    description: "Indiquez votre heure de préférence",
  },
};

export const Required: Story = {
  args: {
    name: "date_2",
    label: "Date obligatoire",
    description: "Ce champ est obligatoire",
    required: true,
  },
};

export const WithDefaultValue: Story = {
  args: {
    name: "date_3",
    label: "Date avec valeur par défaut",
    description: "Valeur pré-remplie",
    defaultValue: "2024-01-01",
  },
};
