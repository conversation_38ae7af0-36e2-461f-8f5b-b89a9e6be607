"use client";

import { SelectFilter } from "@/generated/graphql/graphql";
import clsx from "clsx";
import { useId } from "react";
import List from "./List";
import styles from "./Tree.module.scss";
import { TreeContextProvider } from "./TreeContext";

export interface TreeProps {
  filter: Partial<Omit<SelectFilter, "__typename">>;
  selectedOptions: string[];
  className?: string;
  defaultExpanded?: boolean;
}

export default function Tree({ filter, selectedOptions, className, defaultExpanded }: TreeProps) {
  const id = useId();
  const { options = [], label, attribute } = filter ?? {};

  return (
    <details className={clsx(styles.root, className)} open={true}>
      <summary className={styles.summary}>
        <i className="far fa-caret-down" aria-hidden={true} />
        <h3>{label}</h3>
      </summary>
      <div className={styles.content}>
        <TreeContextProvider
          attribute={attribute}
          options={options}
          selectedOptions={selectedOptions}
          defaultExpanded={defaultExpanded}
        >
          <List root={true} id={id} />
        </TreeContextProvider>
      </div>
    </details>
  );
}
