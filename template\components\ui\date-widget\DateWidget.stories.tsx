import type { Meta, StoryObj } from "@storybook/nextjs";
import { useState } from "react";
import DateWidget from "./DateWidget";

const meta: Meta<typeof DateWidget> = {
  title: "Components/Widget/DateWidget",
  component: DateWidget,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof DateWidget>;

// Helper function to generate mock dates
const generateMockDates = (count: number, daysOffset = 0) => {
  const dates = [];
  const now = new Date("2025-08-06");

  for (let index = 0; index < count; index++) {
    const startDate = new Date(now);

    startDate.setDate(now.getDate() + daysOffset + index);
    startDate.setHours(10 + index, 0, 0, 0);

    const endDate = new Date(startDate);

    endDate.setHours(18, 0, 0, 0);

    dates.push({
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      fullday: index % 3 === 0,
    });
  }

  return dates;
};

function DateWidgetRenderer(args: Story["args"]) {
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const mockDates = generateMockDates(currentPage * 7);
  const totalCount = 30;

  const handleLoadMore = () => {
    setIsLoading(true);
    setTimeout(() => {
      setCurrentPage((prev) => prev + 1);
      setIsLoading(false);
    }, 1000);
  };

  return (
    <DateWidget {...args} dates={mockDates} totalCount={totalCount} loading={isLoading} onLoadMore={handleLoadMore} />
  );
}

export const Default: Story = {
  args: {
    dates: [
      {
        startDate: "2025-07-21T08:00:00.000Z",
        endDate: "2025-07-21T16:00:00.000Z",
        fullday: true,
      },
      {
        startDate: "2025-07-21T08:00:00.000Z",
        endDate: "2025-07-21T16:00:00.000Z",
        fullday: false,
      },
      {
        startDate: "2025-07-23T10:00:00.000Z",
        endDate: "2025-07-26T16:00:00.000Z",
        fullday: true,
      },
      {
        startDate: "2025-07-23T10:00:00.000Z",
        endDate: "2025-07-26T16:00:00.000Z",
        fullday: false,
      },
    ],
  },
};

export const Summary: Story = {
  args: {
    recurrenceSummary: "Tous les mercredis et vendredis à 14h",
    dates: [
      {
        startDate: "2025-07-23T08:00:00.000Z",
        endDate: "2025-07-23T16:00:00.000Z",
        fullday: true,
      },
      {
        startDate: "2025-07-25T12:00:00.000Z",
        endDate: "2025-07-25T14:00:00.000Z",
        fullday: false,
      },
    ],
  },
};

export const LoadMore: Story = {
  render: (args) => <DateWidgetRenderer {...args} />,
  args: {
    dates: generateMockDates(3),
  },
};
