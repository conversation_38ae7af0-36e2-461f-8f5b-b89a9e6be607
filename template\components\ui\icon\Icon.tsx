import { Icon as BaseIconProps, IconType } from "@/generated/graphql/graphql";
import clsx from "clsx";

type IconProps = Partial<Omit<BaseIconProps, "__typename">> & {
  className?: string;
};

/**
 * Renders an icon from GraphQL.
 */
export default function Icon({ type, src, className }: IconProps) {
  switch (type) {
    case IconType.FONT: {
      return <i className={clsx(src, className)} aria-hidden="true"></i>;
    }
    case IconType.URL: {
      // eslint-disable-next-line @next/next/no-img-element -- Allow bare img element for icons
      return <img src={src} className={className} alt="" />;
    }
    case IconType.SVG: {
      console.warn("Icon: The SVG format is not yet supported");
    }
  }
}
