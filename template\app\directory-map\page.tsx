import Button from "@/components/ui/button/Button";
import DirectoriesMap from "@/components/ui/cartography/directories/DirectoriesMap";
import { MapLinksNavigation } from "@/components/ui/cartography/MapLinksNavigation";
import MapMenuButton from "@/components/ui/cartography/MapMenuButton";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { graphql } from "@/generated/graphql";
import {
  DirectoryFilterInput,
  Image as ImageType,
  Link as LinkType,
  MenuItem,
  SortDirection,
} from "@/generated/graphql/graphql";
import { createFilterInput } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { Metadata } from "next";
import Link from "next/link";
import assert from "node:assert";
import styles from "./MapDirectoriesPage.module.scss";

const DIRECTORY_LOCATIONS_SEARCH_QUERY = graphql(`
  query GetDirectoriesLocation(
    $type: String!
    $filter: DirectoryFilterInput
    $sort: DirectorySortInput
    $pageSize: Int
  ) {
    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize) {
      totalCount
      items {
        id
        title
        location {
          latitude
          longitude
        }
        categories {
          title
          relativeUrl
          description
        }
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
      }
    }
  }
`);

const GET_MAP_DETAILS_QUERY = graphql(`
  query GetRouteAndType($url: URL) {
    route(url: $url) {
      ... on DirectoryMap {
        types
        metadata {
          title
          description
        }
        filters {
          __typename
          attribute
          ... on TextFilter {
            attribute
          }
        }
        url
      }
    }
  }
`);

const GET_DIRECTORY_AND_MAP_LINKS_QUERY = graphql(`
  query GetDirectoriesAndMapLinks($url: URL) {
    menu(position: "header") {
      items {
        ...MenuItemFragment
        children {
          ...MenuItemFragment
          children {
            ...MenuItemFragment
          }
        }
      }
    }
    siteConfig {
      siteName
      header {
        logoDark {
          alt
          height
          url
          width
        }
      }
      footer {
        quickAccess2 {
          rel
          target
          text
          url
        }
      }
    }
    route(url: $url) {
      ... on GlobalMap {
        mapLinks {
          url
          text
          icon {
            src
            type
          }
        }
      }
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: GET_MAP_DETAILS_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "DirectoryMap");
  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function MapDirectoriesPage({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | null>>;
}) {
  const url = await getCurrentServerUrl();

  const { data: routeData } = await query({
    query: GET_MAP_DETAILS_QUERY,
    variables: { url },
  });

  const { route } = routeData ?? {};

  assert.ok(route?.__typename === "DirectoryMap");
  const { types, filters: defaultFilters } = route ?? {};
  const type = types?.[0];

  const { data: directoryAndMapData } = await query({
    query: GET_DIRECTORY_AND_MAP_LINKS_QUERY,
    variables: { url: "/carte-interactive/" },
  });

  const { ...params } = await searchParams;

  const pageSize = 9999;
  const filterInput = createFilterInput<DirectoryFilterInput>({ params, filters: defaultFilters });

  const { data: directorySearch } = await query({
    query: DIRECTORY_LOCATIONS_SEARCH_QUERY,
    variables: {
      type,
      pageSize,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const { mapLinks } = directoryAndMapData?.route?.__typename === "GlobalMap" ? directoryAndMapData.route : {};
  const { siteConfig, menu } = directoryAndMapData ?? {};
  const { logoDark } = siteConfig?.header ?? {};
  const { footer } = siteConfig ?? {};

  const { quickAccess2 = [] } = footer ?? {};

  const { items = [] } = directorySearch?.directorySearch ?? {};

  return (
    <div className={styles.main}>
      <header className={styles.header}>
        <div className={styles.wrapper}>
          <Tooltip content="Revenir au site" placement="bottom">
            <Button asChild size="sm" variant="text">
              <Link href="/">
                <i className="far fa-arrow-left" aria-hidden="true" /> <span className="sr-only">Revenir au site</span>
              </Link>
            </Button>
          </Tooltip>
          <h1 className={styles.logo} title="Aller à la page d'accueil">
            {siteConfig?.siteName}
          </h1>
        </div>
        <div className={styles.actions}>
          <div className={styles.navWrapper}>
            <MapLinksNavigation mapLinks={mapLinks as LinkType[]} pathname={url} />
          </div>

          <div className={styles.filter}>
            <div className={styles.search}>{/* // TODO: Add search form */}</div>
            <div className={styles.filterButton}>{/* // TODO: Add filters */}</div>
          </div>
        </div>

        <div className={styles.trigger}>
          <MapMenuButton
            logoDark={logoDark as ImageType}
            quickAccess2={quickAccess2 as LinkType[]}
            mapLinks={mapLinks as LinkType[]}
            menuItems={(menu?.items ?? []) as MenuItem[]}
          >
            <Tooltip content="Afficher la Popin de navigation carte" placement="bottom">
              <Button size="sm" variant="text" endIcon="fal fa-ellipsis-vertical" />
            </Tooltip>
          </MapMenuButton>
        </div>
      </header>
      <DirectoriesMap directories={items} />
      <div className={styles.fixedNavWrapper}>
        <MapLinksNavigation mapLinks={mapLinks as LinkType[]} pathname={url} />
      </div>
    </div>
  );
}
