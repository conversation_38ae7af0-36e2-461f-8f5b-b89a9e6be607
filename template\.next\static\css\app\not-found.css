/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/textfield/Textfield.module.scss ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.Textfield_container__xrO46 {
  position: relative;
  display: flex;
  width: 100%;
}

.Textfield_input__qN4LI {
  width: 100%;
  font-size: 1.6rem;
  line-height: 1.3;
  background-color: #fff;
}
.Textfield_input__qN4LI::placeholder {
  color: #707070;
}
.Textfield_input__qN4LI.Textfield_variant-outlined__yJXgw {
  border: 1px solid #707070;
  border-radius: 4px;
}
.Textfield_input__qN4LI.Textfield_variant-outlined__yJXgw.Textfield_error__M6Vc7 {
  background-color: rgb(255, 250.1457943925, 249.7);
}
.Textfield_input__qN4LI.Textfield_variant-underline__jEv60 {
  border-bottom: 1px solid #707070;
}
.Textfield_input__qN4LI.Textfield_variant-underline__jEv60.Textfield_error__M6Vc7 {
  color: #d61200;
}
.Textfield_input__qN4LI.Textfield_variant-filled__SiWAD {
  background-color: #f7f7f7;
  border-bottom: 1px solid #707070;
  border-radius: 4px 4px 0 0;
}
.Textfield_input__qN4LI.Textfield_variant-filled__SiWAD.Textfield_error__M6Vc7 {
  background-color: rgb(255, 250.1457943925, 249.7);
}
.Textfield_input__qN4LI.Textfield_variant-text__HHprt {
  border-radius: 4px;
}
.Textfield_input__qN4LI.Textfield_size-small__tvK_P {
  padding: 8px 16px;
}
.Textfield_input__qN4LI.Textfield_size-large__Aj9f3, .Textfield_input__qN4LI.Textfield_size-medium__P4cPN {
  padding: 12px 16px;
}
.Textfield_container__xrO46:has(.Textfield_startIcon__yiNDE) .Textfield_input__qN4LI.Textfield_size-large__Aj9f3, .Textfield_container__xrO46:has(.Textfield_startIcon__yiNDE) .Textfield_input__qN4LI.Textfield_size-medium__P4cPN {
  padding-left: 44px;
}
.Textfield_container__xrO46:has(.Textfield_endIcon__oQx_9) .Textfield_input__qN4LI.Textfield_size-large__Aj9f3, .Textfield_container__xrO46:has(.Textfield_endIcon__oQx_9) .Textfield_input__qN4LI.Textfield_size-medium__P4cPN {
  padding-right: 44px;
}
@media screen and (min-width: 1302px) {
  .Textfield_input__qN4LI.Textfield_size-large__Aj9f3 {
    padding: 21px 24px;
  }
  .Textfield_container__xrO46:has(.Textfield_startIcon__yiNDE) .Textfield_input__qN4LI.Textfield_size-large__Aj9f3 {
    padding-left: 52px;
  }
  .Textfield_container__xrO46:has(.Textfield_endIcon__oQx_9) .Textfield_input__qN4LI.Textfield_size-large__Aj9f3 {
    padding-right: 52px;
  }
}
.Textfield_input__qN4LI.Textfield_error__M6Vc7 {
  border-color: #d61200;
}
.Textfield_input__qN4LI.Textfield_error__M6Vc7::placeholder {
  color: #d61200;
}
.Textfield_input__qN4LI:disabled {
  opacity: 0.3;
}

.Textfield_innerLabel__sEkXA {
  position: absolute;
  top: 50%;
  left: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.6rem;
  line-height: 130%;
  color: #707070;
  white-space: nowrap;
  pointer-events: none;
  transform: translateY(-50%);
  transform-origin: top left;
  transition: font-size 0.2s ease-out, line-height 0.2s ease-out, left 0.2s ease-out, top 0.2s ease-out;
}
.Textfield_input__qN4LI.Textfield_error__M6Vc7 ~ .Textfield_innerLabel__sEkXA {
  color: #d61200;
}
.Textfield_container__xrO46:has(.Textfield_startIcon__yiNDE) .Textfield_innerLabel__sEkXA {
  left: 44px;
}
@media screen and (min-width: 1302px) {
  .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3) {
    left: 24px;
  }
  .Textfield_container__xrO46:has(.Textfield_startIcon__yiNDE) .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3) {
    left: 52px;
  }
}
.Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI:focus), .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI:not(:placeholder-shown)) {
  top: 0;
  left: 16px;
  font-size: 1.2rem;
  line-height: 110%;
  background-color: #fff;
  box-shadow: 0 0 0 4px #fff;
}
.Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3:focus), .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3:not(:placeholder-shown)) {
  font-size: 1.4rem;
}
@media screen and (min-width: 1302px) {
  .Textfield_container__xrO46 .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3:focus), .Textfield_container__xrO46 .Textfield_innerLabel__sEkXA:has(+ .Textfield_input__qN4LI.Textfield_size-large__Aj9f3:not(:placeholder-shown)) {
    left: 24px;
  }
}

.Textfield_startIcon__yiNDE,
.Textfield_endIcon__oQx_9 {
  position: absolute;
  top: 50%;
  margin-inline: 16px;
  vertical-align: middle;
  pointer-events: none;
  transform: translateY(-50%);
}
.Textfield_container__xrO46:has(input:placeholder-shown) .Textfield_startIcon__yiNDE,
.Textfield_container__xrO46:has(input:placeholder-shown) .Textfield_endIcon__oQx_9 {
  color: #707070;
}
.Textfield_container__xrO46:has(input.Textfield_error__M6Vc7) .Textfield_startIcon__yiNDE,
.Textfield_container__xrO46:has(input.Textfield_error__M6Vc7) .Textfield_endIcon__oQx_9 {
  color: #d61200;
}
@media screen and (min-width: 1302px) {
  .Textfield_container__xrO46:has(.Textfield_input__qN4LI.Textfield_size-large__Aj9f3) .Textfield_startIcon__yiNDE,
  .Textfield_container__xrO46:has(.Textfield_input__qN4LI.Textfield_size-large__Aj9f3) .Textfield_endIcon__oQx_9 {
    margin-inline: 24px;
  }
}

.Textfield_startIcon__yiNDE {
  left: 0;
}

.Textfield_endIcon__oQx_9 {
  right: 0;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/tooltip/Tooltip.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.Tooltip_tooltipContent__rhWD0 {
  position: relative;
  z-index: 1300;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 344px;
  height: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.1);
}

.Tooltip_tooltipArrow__nUriN {
  visibility: visible;
}

.Tooltip_variant-neutral__iRMy9 {
  color: #363636;
  background: #f7f7f7;
  background-color: #fff;
  border: 1px solid #707070;
}
.Tooltip_variant-neutral__iRMy9 .Tooltip_tooltipArrow__nUriN {
  fill: #fff;
}

.Tooltip_variant-primary__6wuts {
  color: #fff;
  background-color: #214fab;
}
.Tooltip_variant-primary__6wuts .Tooltip_tooltipArrow__nUriN {
  fill: #214fab;
}

.Tooltip_variant-secondary__DLVem {
  background-color: #9ce3d5;
}
.Tooltip_variant-secondary__DLVem .Tooltip_tooltipArrow__nUriN {
  fill: #9ce3d5;
}

.Tooltip_variant-tertiary__lWkUI {
  background-color: #f5dcad;
}
.Tooltip_variant-tertiary__lWkUI .Tooltip_tooltipArrow__nUriN {
  fill: #f5dcad;
}

.Tooltip_size-md__ce_Ie {
  padding: 12px 16px;
  font-size: 1.4rem;
  line-height: 110%;
  border-radius: 4px;
}
.Tooltip_size-md__ce_Ie[data-side=top][data-align=start] {
  left: 0;
  margin-bottom: 8px;
  transform: none;
}
.Tooltip_size-md__ce_Ie[data-side=top][data-align=start] > span {
  left: 12px !important;
}

.Tooltip_size-lg__63tyZ {
  padding: 16px 32px;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 130%;
  border-radius: 4px;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=start] {
  left: 0;
  border-radius: 4px 4px 4px 0;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=start] > span {
  left: -1px !important;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=end] {
  border-radius: 4px 4px 0;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=end] > span {
  right: -1px !important;
  left: auto !important;
  transform: translateY(100%) rotateY(180deg) !important;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=start] {
  left: 0;
  border-radius: 0 4px 4px;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=start] > span {
  left: -1px !important;
  transform: rotateX(180deg) !important;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=end] {
  right: 0;
  border-radius: 4px 0 4px 4px;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=end] > span {
  right: -1px !important;
  left: auto !important;
}
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./styles/ErrorPage.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.ErrorPage_errorPage__Jf741 {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  margin-top: 24px;
  text-align: start;
}
@media screen and (min-width: 768px) {
  .ErrorPage_errorPage__Jf741 {
    max-width: 80%;
    margin: 24px auto;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_errorPage__Jf741 {
    flex-direction: row-reverse;
    gap: 82px;
    justify-content: space-between;
    max-width: none;
    text-align: left;
  }
}

.ErrorPage_content__L1sqT {
  order: -1;
}
.ErrorPage_content__L1sqT h1 {
  font-size: 1.6rem;
  line-height: 130%;
  color: #214fab;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT h1 {
    font-size: 1.8rem;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_content__L1sqT h1 {
    font-size: 2.4rem;
  }
}
.ErrorPage_content__L1sqT h2 {
  margin-block: 12px;
  font-size: 4rem;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT h2 {
    font-size: 4.8rem;
    letter-spacing: -0.96px;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_content__L1sqT h2 {
    margin-block: 16px;
    font-size: 6.4rem;
    letter-spacing: -1.28px;
  }
}
.ErrorPage_content__L1sqT h2 em {
  font-style: normal;
  color: #214fab;
}
.ErrorPage_content__L1sqT p {
  max-width: 505px;
  margin-block: 12px;
  font-size: 1.6rem;
  line-height: 150%;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT p {
    margin-block: 16px;
    font-size: 1.8rem;
  }
}

.ErrorPage_returnLink__W24tW {
  display: inline-block;
  font-size: 1.6rem;
  line-height: 120%;
  color: #214fab;
  text-decoration: underline;
  cursor: pointer;
}
.ErrorPage_returnLink__W24tW i {
  margin-right: 3px;
  font-size: 1.4rem;
}

.ErrorPage_illustration__Y4MaR {
  flex-shrink: 0;
  width: 100%;
  max-width: 396px;
}
@media screen and (min-width: 768px) {
  .ErrorPage_illustration__Y4MaR {
    max-width: 538px;
  }
}

.ErrorPage_searchForm__mIol1 {
  position: relative;
  display: flex;
  padding: 16px 24px;
  margin-top: 32px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}
@media screen and (min-width: 768px) {
  .ErrorPage_searchForm__mIol1 {
    margin-top: 40px;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_searchForm__mIol1 {
    margin-top: 48px;
  }
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchInput__IUV47 input:focus-visible {
  outline: none;
}
.ErrorPage_searchForm__mIol1:has(.ErrorPage_searchInput__IUV47 input:focus-visible) {
  outline: 2px solid #000;
  outline-offset: 2px;
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchButton__Ot5tc {
  pointer-events: all;
  cursor: pointer;
  border-radius: 4px;
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchButton__Ot5tc:hover {
  color: #3269d7;
}

.ErrorPage_contact__xdEXS {
  margin-block: 24px;
  font-size: 1.2rem;
  line-height: 110%;
  color: #707070;
  text-align: center;
}
.ErrorPage_contact__xdEXS a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  text-decoration-thickness: 8%;
  -webkit-text-decoration-style: solid;
          text-decoration-style: solid;
  text-underline-offset: 13.5%;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto;
  cursor: pointer;
}
@media screen and (min-width: 768px) {
  .ErrorPage_contact__xdEXS {
    display: block;
    max-width: 80%;
    margin: 50px auto;
    font-size: 1.4rem;
  }
}
