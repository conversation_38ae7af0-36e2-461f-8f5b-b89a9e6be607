@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.toast {
  position: relative;
  display: flex;
  gap: 25px;
  padding: 16px;
  background: white;
  border-radius: 0.8rem;
  box-shadow: 0 3px 10px 0 rgb(0 0 0 / 10%);
}

.toast[data-state="open"] {
  animation: slide-in 150ms ease-in;
}

.toast[data-state="closed"] {
  animation: hide 100ms ease-in;
}

.toast[data-swipe="move"] {
  transform: translateX(var(--radix-toast-swipe-move-x));
}

.toast[data-swipe="cancel"] {
  transform: translateY(0);
  transition: transform 200ms ease-out;
}

.toast[data-swipe="end"] {
  animation: swipe-out 100ms ease-out;
}

.toast.success {
  color: $color-positive-400;
  background: $color-positive-300;

  .icon {
    background-color: $color-positive-400;
  }
}

.toast.error {
  color: $color-negative-400;
  background: $color-negative-300;

  .icon {
    background-color: $color-negative-400;
  }
}

.toast.info {
  color: $color-info-400;
  background: $color-info-300;

  .icon {
    background-color: $color-info-400;
  }
}

.toast.warning {
  color: $color-warning-400;
  background: $color-warning-300;

  .icon {
    background-color: $color-warning-400;
  }
}

.icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  align-self: baseline;
  justify-content: center;
  width: 28px;
  height: 28px;
  font-size: 1.2rem;
  color: $color-white;
  border-radius: 50%;
}

.description {
  flex-grow: 1;
  align-self: baseline;
  font-size: 1.4rem;
  line-height: 120%;
}

.action {
  padding-inline: 10px;
  margin-left: auto;
  font-size: 1.4rem;
  line-height: 120%;
  text-decoration: underline;
  cursor: pointer;
}

.close {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  cursor: pointer;
}

@keyframes hide {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes slide-in {
  from {
    transform: translateY(calc(100% + 2rem));
  }

  to {
    transform: translateY(0);
  }
}

@keyframes swipe-out {
  from {
    transform: translateX(var(--radix-toast-swipe-end-x));
  }

  to {
    transform: translateX(100%);
  }
}
