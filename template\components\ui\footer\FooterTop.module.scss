@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.footerTop {
  display: flex;
  justify-content: center;
  padding: 56px 16px;
  background: $color-primary-50;

  @include breakpoint(medium up) {
    padding: 56px;
  }

  @include breakpoint(large up) {
    padding: 72px;
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    width: 100%;
    max-width: 1216px;
    text-align: center;

    @include breakpoint(medium up) {
      flex-direction: row;
      gap: 24px;
      text-align: left;
    }

    @include breakpoint(large up) {
      gap: 32px;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 6px;

    @include breakpoint(large up) {
      gap: 8px;
    }
  }

  .title {
    font-size: 2rem;
    font-weight: $fw-bold;
    line-height: 110%;
    color: $color-primary-500;

    @include breakpoint(medium up) {
      font-size: 2.4rem;
    }

    @include breakpoint(large up) {
      font-size: 4rem;
    }
  }

  .description {
    font-size: 1.2rem;
    line-height: 110%;
    color: $color-neutral-700;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }

    @include breakpoint(large up) {
      font-size: 1.6rem;
    }
  }

  .actions {
    @include breakpoint(small only) {
      width: 100%;
    }

    a.actionButton {
      min-width: 100%;

      @include breakpoint(medium up) {
        min-width: 240px;
      }

      @include breakpoint(large up) {
        min-width: 270px;
        font-size: 1.8rem;
      }
    }
  }
}
