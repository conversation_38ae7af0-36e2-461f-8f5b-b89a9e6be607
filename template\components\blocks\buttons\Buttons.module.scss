@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.buttons {
  --buttons-gap: 6px;

  display: flex;
  flex-wrap: wrap;
  gap: var(--buttons-gap);
  width: 100%;
  margin-block: $space-5;

  &.horizontal {
    flex-direction: row;
    justify-content: flex-start;

    @include breakpoint(medium down) {
      flex-direction: column;

      :global(.block-button) {
        width: auto !important;
      }
    }
  }

  &.vertical {
    flex-direction: column;
  }

  @include breakpoint(large up) {
    --buttons-gap: 8px;

    margin-block: $space-6;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}
