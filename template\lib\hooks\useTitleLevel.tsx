import { createContext, use } from "react";

export const TitleLevelContext = createContext<number>(1);

/**
 * Returns the title level in the current context.
 */
export default function useTitleLevel() {
  return use(TitleLevelContext);
}

/**
 * Set the title level for the current section.
 * The title level is clamped between 1 and 6.
 */
export function TitleLevelProvider({ level, children }: { level: number; children: React.ReactNode }) {
  return <TitleLevelContext value={Math.max(1, Math.min(level, 6))}>{children}</TitleLevelContext>;
}

/**
 * Provide an automatically incremented title level to a content section.
 * The title level is clamped between 1 and 6.
 * If `level` is defined, the level provided is the next one (level + 1).
 */
export function SubtitleLevelProvider({ level, children }: { level?: number; children: React.ReactNode }) {
  const currentLevel = useTitleLevel();

  return (
    <TitleLevelContext value={Math.max(1, Math.min((level || currentLevel) + 1, 6))}>{children}</TitleLevelContext>
  );
}
