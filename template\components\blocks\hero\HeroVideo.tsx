"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import type { HeroBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import { useCallback, useRef, useState } from "react";
import HeroContent from "./HeroContent";
import styles from "./HeroVideo.module.scss";

/**
 * Render the Hero block with a single video in background.
 */
export default function HeroVideo({ slides }: Pick<HeroBlock, "slides">) {
  const [isPlaying, setIsPlaying] = useState(true);
  const videoRef = useRef<HTMLVideoElement | null>(null);

  const togglePlayPause = useCallback(() => {
    if (!videoRef.current) return;

    if (videoRef.current.paused) {
      videoRef.current.play();
      setIsPlaying(true);
    } else {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  }, []);

  const slide = slides?.[0];

  if (!slide?.videoSrc) return null;

  return (
    <section className={clsx("block-hero", styles.heroVideo)}>
      <div className={styles.contentWrapper}>
        <HeroContent
          className={styles.content}
          title={slide.title}
          surtitle={slide.leadText}
          url={slide.link}
          noIcon={true}
        />

        <Tooltip content={isPlaying ? "Arrêter la vidéo" : "Lire la vidéo"}>
          <button
            type="button"
            onClick={togglePlayPause}
            className={styles.playButton}
            aria-label={isPlaying ? "Arrêter la vidéo" : "Lire la vidéo"}
          >
            <i className={clsx("far", isPlaying ? "fa-pause" : "fa-play-pause")} aria-hidden="true" />
          </button>
        </Tooltip>
      </div>

      <video
        ref={videoRef}
        src={slide.videoSrc}
        autoPlay
        muted
        loop
        playsInline
        className={styles.video}
        aria-label="Vidéo décorative"
      />
    </section>
  );
}
