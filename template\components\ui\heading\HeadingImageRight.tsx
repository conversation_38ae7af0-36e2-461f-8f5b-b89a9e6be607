import Tag from "@/components/ui/tag/Tag";
import Image from "next/image";
import Link from "next/link";
import striptags from "striptags";
import styles from "./HeadingImageRight.module.scss";

interface HeadingImageRightProps {
  imageSrc?: string;
  leadText?: string | null;
  modifiedDate?: string;
  publicationDate?: string;
  surtitle?: string | null;
  tagsRoleDescription?: string;
  tags?: { url?: string; text: string }[];
  title: string;
}

/**
 * The heading with an image on the right side.
 */
export default function HeadingImageRight({
  imageSrc,
  leadText,
  modifiedDate,
  publicationDate,
  surtitle,
  tags,
  tagsRoleDescription,
  title,
}: HeadingImageRightProps) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.content}>
          <div className={styles.titleWrapper}>
            <h1 className={styles.title}>
              {surtitle && (
                <span className={styles.surtitle}>
                  {surtitle}
                  <span className="sr-only">:</span>
                </span>
              )}
              {title}
            </h1>
          </div>
          {leadText && <p className={styles.teaser}>{striptags(leadText)}</p>}
          {tags && tags.length > 0 && (
            <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
              {tags.map((tag, index) => (
                <li key={index}>
                  {tag.url ? (
                    <Tag variant="primary" asChild>
                      <Link href={tag.url}>{tag.text}</Link>
                    </Tag>
                  ) : (
                    <Tag variant="primary">{tag.text}</Tag>
                  )}
                </li>
              ))}
            </ul>
          )}
          {(publicationDate || modifiedDate) && (
            <p className={styles.dates}>
              {publicationDate && (
                <>
                  <span>Publié le </span>
                  <time dateTime={publicationDate}>
                    {new Date(publicationDate).toLocaleDateString("fr-FR", {
                      dateStyle: "long",
                    })}
                  </time>
                </>
              )}
              {modifiedDate && (
                <>
                  <span>Mis à jour le </span>
                  <time dateTime={modifiedDate}>
                    {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                      dateStyle: "long",
                    })}
                  </time>
                </>
              )}
            </p>
          )}
        </div>
        {imageSrc && (
          <Image
            className={styles.image}
            src={imageSrc}
            width={488}
            height={325}
            alt=""
            sizes="(max-width: 767px) 344px, 488px"
          />
        )}
      </div>
    </header>
  );
}
