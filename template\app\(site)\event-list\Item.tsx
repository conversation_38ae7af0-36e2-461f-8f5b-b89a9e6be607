"use client";

import type { Event } from "@/generated/graphql/graphql";
import { format as formatDate } from "date-fns";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

const DateInterval = dynamic(() => import("@/components/ui/date/DateInterval"));

interface ItemProps {
  event: PartialDeep<Event, { recurseIntoArrays: true }>;
}

export default function Item({
  event: { images, url, title, startDate, endDate, location, categories, periods },
}: ItemProps) {
  const [upcomingPeriod] = periods?.items ?? [];
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;

  title ??= "Sans titre";

  return (
    <article className={styles.eventItem}>
      <div className={styles.details}>
        <h3 className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </h3>
        {(upcomingPeriod?.startDate || location?.address?.city || location?.title) && (
          <div className={styles.infos}>
            {upcomingPeriod?.startDate && (
              <p className={styles.nextDate}>Prochaine date : {formatDate(upcomingPeriod.startDate, "dd/MM/yyyy")}</p>
            )}
            {location?.address?.city && (
              <p className={styles.city} aria-roledescription="Ville">
                {location.address.city}
              </p>
            )}
            {location?.title && (
              <p className={styles.place} aria-roledescription="Lieu">
                {location.title}
              </p>
            )}
          </div>
        )}
      </div>
      <div className={styles.top}>
        {startDate && <DateInterval className={styles.date} from={startDate} to={endDate} />}
        {image?.url && (
          <div className={styles.imageWrapper}>
            <Image
              src={image.url}
              width={image.width}
              height={image.height}
              alt={image.alt ?? ""}
              sizes="(max-width: 767px) 74px, (max-width: 1301px) 224px, 384px"
            />
          </div>
        )}
      </div>
    </article>
  );
}
