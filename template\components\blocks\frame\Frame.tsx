import type { FrameBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./Frame.module.scss";

type FrameProps = Omit<FrameBlock, "__typename" | "innerBlocks">;

export default function Frame({
  anchor,
  className,
  textAlign,
  variant = "primary",
  children,
}: React.PropsWithChildren<FrameProps>) {
  return (
    <div
      id={anchor ?? undefined}
      role="group"
      aria-roledescription="Information"
      className={clsx("block-frame contained", styles.container, className)}
      style={{ textAlign: textAlign }}
    >
      <div className={clsx(styles.frame, variant && styles[`variant-${variant}`])}>
        <i className={clsx("far fa-circle-info", styles.icon)} aria-hidden="true"></i>
        <div className={styles.content}>{children}</div>
      </div>
    </div>
  );
}
