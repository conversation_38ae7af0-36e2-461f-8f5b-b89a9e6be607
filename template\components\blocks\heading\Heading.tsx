import Title from "@/components/ui/title/Title";
import { HeadingBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import striptags from "striptags";
import styles from "./Heading.module.scss";

type HeadingProps = Partial<Omit<HeadingBlock, "__typename" | "innerBLocks">>;

export default function Heading({ anchor, level, html, className }: HeadingProps) {
  const innerHTML = striptags(html ?? "", ["b", "i", "strong", "em", "a"]);
  return (
    innerHTML && (
      <Title
        level={level}
        id={anchor ?? undefined}
        className={clsx("block-heading contained", styles.heading, className)}
        // eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- HTML is sanitized
        dangerouslySetInnerHTML={{ __html: innerHTML }}
      />
    )
  );
}
