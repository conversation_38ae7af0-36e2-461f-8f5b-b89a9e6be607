import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Breadcrumbs from "./Breadcrumbs";

const meta: Meta<typeof Breadcrumbs> = {
  title: "Components/Breadcrumbs",
  component: Breadcrumbs,
  tags: ["autodocs"],
  argTypes: {
    items: {
      control: "object",
      description: "Array of breadcrumb items",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Breadcrumbs>;

export const Default: Story = {
  args: {
    items: [
      {
        title: "Accueil",
        url: "/",
      },
      {
        title: "Événements1",
        url: "/evenements1/",
      },
      {
        title: "Événements2",
        url: "/evenements2/",
      },
    ],
  },
};

export const VeryLong: Story = {
  args: {
    items: [
      {
        title: "Accueil",
        url: "/",
      },
      {
        title: "Événements1",
        url: "/evenements1/",
      },
      {
        title: "Événements2",
        url: "/evenements2/",
      },
      {
        title: "Événements3",
        url: "/evenements3/",
      },
      {
        title: "Événements4",
        url: "/evenements4/",
      },
      {
        title: "Événements5",
        url: "/evenements5/",
      },
      {
        title: "Événements6",
        url: "/evenements6/",
      },
      {
        title: "Événements7",
        url: "/evenements7/",
      },
    ],
  },
};
