import clsx from "clsx";
import styles from "./FormControl.module.scss";

interface FormControlProps {
  spacing?: "normal" | "dense";
  columnSpan?: number;
}

/**
 * The `FormControl` component visually organizes the parts of a form field.
 */
export default function FormControl({
  spacing,
  children,
  className,
  columnSpan = 12,
  ...restProps
}: React.PropsWithChildren<FormControlProps> & React.HTMLAttributes<HTMLDivElement>) {
  const classList = clsx(
    styles.formControl,
    styles[`col-span-${columnSpan}`],
    spacing && styles[`spacing-${spacing}`],
    className
  );

  return (
    <div className={classList} {...restProps}>
      {children}
    </div>
  );
}
