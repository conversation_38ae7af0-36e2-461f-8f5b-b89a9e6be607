import type { Meta, StoryObj } from "@storybook/nextjs";
import DateRange from "./DateRange";

const meta: Meta<typeof DateRange> = {
  title: "Filters/DateRange",
  component: DateRange,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof DateRange>;

export const Default: Story = {
  args: {
    filter: {
      label: "Date de publication",
      attribute: "publication_date",
    },
  },
};
