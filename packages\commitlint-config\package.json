{"name": "@citeopolis/commitlint-config", "type": "module", "version": "0.1.1", "scripts": {"format": "prettier -w .", "test": "vitest run"}, "files": ["./commitlint.config.js"], "lint-staged": {"*.{js,json,md}": "prettier --write", "*.js": "vitest related --run"}, "devDependencies": {"@citeopolis/prettier-config": "workspace:^", "@commitlint/cli": "^19", "@commitlint/lint": "^19", "prettier": "^3", "vitest": "^3.0.2"}, "peerDependency": {"@commitlint/cli": "^19"}, "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/commitlint-config"}, "exports": {".": {"import": "./commitlint.config.js"}}}