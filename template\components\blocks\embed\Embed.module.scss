@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.embed {
  max-width: 800px;
  padding-block: 24px;
  margin-block: 12px;

  @include breakpoint(medium up) {
    padding-block: 32px;
    margin-block: 16px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.caption {
  max-width: 100%;
  margin-bottom: 16px;
  font-size: 1.6rem;
  line-height: 130%;
  color: $color-neutral-700;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.meta {
  display: block;
  margin-top: 2px;
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.content {
  width: 100%;

  iframe {
    width: 100%;
    height: 100%;
    border: 0;
  }

  &.video {
    aspect-ratio: 16/9;
  }
}

.transcription {
  margin-top: 8px;
}
