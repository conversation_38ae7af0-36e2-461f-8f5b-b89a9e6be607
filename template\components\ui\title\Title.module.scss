@use "sass:map";
@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

$heading-colors: (
  1: $color-black,
  2: $color-primary-500,
  3: $color-primary-600,
  4: $color-black,
  5: $color-neutral-700,
  6: $color-neutral-500,
);
$heading-sizes: (
  1: 3.2rem,
  2: 2.4rem,
  3: 2rem,
  4: 1.8rem,
  5: 1.6rem,
  6: 1.4rem,
);
$heading-sizes-tablet: (
  1: 4rem,
  2: 3.2rem,
  3: 2.4rem,
  4: 2rem,
  5: 1.8rem,
  6: 1.6rem,
);
$heading-sizes-desktop: (
  1: 5.6rem,
  2: 4.8rem,
  3: 4rem,
  4: 3.2rem,
  5: 2.4rem,
  6: 1.6rem,
);

@each $level, $size in $heading-sizes {
  .heading-#{$level} {
    font-size: $size;
    font-weight: 700;
    line-height: 110%;
    color: map.get($heading-colors, $level);
  }
}

// Responsive Scaling (Tablet)
@include breakpoint(medium up) {
  @each $level, $size in $heading-sizes-tablet {
    .heading-#{$level} {
      font-size: $size;
    }
  }
}

// Responsive Scaling (Desktop)
@include breakpoint(large up) {
  @each $level, $size in $heading-sizes-desktop {
    .heading-#{$level} {
      font-size: $size;
    }
  }
}
