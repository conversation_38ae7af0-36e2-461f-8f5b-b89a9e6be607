import { ConditionAction, ConditionMatchType, ConditionRuleOperator } from "@/generated/graphql/graphql";
import { ToastProvider, ToastViewport } from "@radix-ui/react-toast";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Form from "./Form";

const meta: Meta<typeof Form> = {
  title: "Form/Form",
  component: Form,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider>
        <Story />
        <ToastViewport />
      </ToastProvider>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof Form>;

/**
 * A single step form.
 */
export const Single: Story = {
  args: {
    id: "123456789",
    totalSteps: 1,
    steps: [
      {
        title: "Titre de l'étape",
        description: "Description de l'étape",
        stepNumber: 0,
        fields: [
          {
            __typename: "TextField",
            name: "input_1-1",
            label: "Firstname",
            description: "Texte de description additionel",
            autocomplete: null,
            columnSpan: 12,
            defaultValue: null,
            condition: null,
            descriptionPlacement: null,
            hidden: false,
            pattern: null,
            placeholder: null,
            required: false,
            size: null,
            cssClass: null,
            showLabel: true,
            type: "",
            validationMessage: "",
          },
        ],
        prev: {
          label: "Précédent",
          condition: null,
          icon: null,
        },
        next: {
          label: "Suivant",
          condition: null,
          icon: null,
        },
        condition: null,
      },
    ],
  },
};

/**
 * A 2 steps form, with the first input being required.
 */
export const MultiStep: Story = {
  args: {
    id: "123456789",
    totalSteps: 2,
    steps: [
      {
        title: "Titre de l'étape #1",
        description: "Description de l'étape",
        stepNumber: 0,
        fields: [
          {
            __typename: "TextField",
            name: "input_2-1",
            label: "Firstname",
            description: "Texte de description additionel",
            autocomplete: null,
            columnSpan: 12,
            defaultValue: null,
            condition: null,
            descriptionPlacement: null,
            hidden: false,
            pattern: null,
            placeholder: null,
            required: true,
            size: null,
            cssClass: null,
            showLabel: true,
            type: "",
            validationMessage: "Champ incomplet.",
          },
        ],
        prev: {
          label: "Précédent",
          condition: null,
          icon: null,
        },
        next: {
          label: "Suivant",
          condition: null,
          icon: null,
        },
        condition: null,
      },
      {
        title: "Titre de l'étape #2",
        description: "Description de l'étape",
        stepNumber: 1,
        fields: [
          {
            __typename: "TextField",
            name: "input_2-2",
            label: "Lastname",
            description: "Texte de description additionel",
            autocomplete: null,
            columnSpan: 12,
            defaultValue: null,
            condition: null,
            descriptionPlacement: null,
            hidden: false,
            pattern: null,
            placeholder: null,
            required: true,
            size: null,
            cssClass: null,
            showLabel: true,
            type: "",
            validationMessage: "",
          },
        ],
        prev: {
          label: "Précédent",
          condition: null,
          icon: null,
        },
        next: {
          label: "Suivant",
          condition: null,
          icon: null,
        },
        condition: null,
      },
    ],
  },
};

/**
 * A single step form that contains a conditional field.
 * If the first input contains text, the second input appears.
 */
export const Condition: Story = {
  args: {
    id: "123456789",
    totalSteps: 1,
    steps: [
      {
        title: "Titre de l'étape",
        description: "Description de l'étape",
        stepNumber: 0,
        fields: [
          {
            __typename: "TextField",
            name: "input_3-1",
            label: "Firstname",
            description: "Fill this input to reveal the 2nd one",
            autocomplete: null,
            columnSpan: 12,
            defaultValue: null,
            condition: null,
            descriptionPlacement: null,
            hidden: false,
            pattern: null,
            placeholder: null,
            required: false,
            size: null,
            cssClass: null,
            showLabel: true,
            type: "",
            validationMessage: "",
          },
          {
            __typename: "TextField",
            name: "input_3-2",
            label: "Lastname",
            description: null,
            autocomplete: null,
            columnSpan: 12,
            defaultValue: null,
            descriptionPlacement: null,
            hidden: false,
            pattern: null,
            placeholder: null,
            required: false,
            size: null,
            cssClass: null,
            showLabel: true,
            type: "",
            validationMessage: "",
            condition: {
              action: ConditionAction.SHOW,
              operator: ConditionMatchType.AND,
              rules: [
                {
                  field: "input_3-1",
                  operator: ConditionRuleOperator.IS_NOT,
                  value: "",
                },
              ],
            },
          },
          {
            __typename: "Fieldset",
            title: "Fieldset",
            description: "This should appear if the first input is empty",
            hidden: false,
            type: "",
            fields: [
              {
                __typename: "TextField",
                name: "input_3-3",
                label: "Surname",
                description: null,
                autocomplete: null,
                columnSpan: 12,
                defaultValue: null,
                descriptionPlacement: null,
                hidden: false,
                pattern: null,
                placeholder: null,
                required: false,
                size: null,
                cssClass: null,
                showLabel: true,
                type: "",
                validationMessage: "",
                condition: null,
              },
            ],
            condition: {
              action: ConditionAction.SHOW,
              operator: ConditionMatchType.AND,
              rules: [
                {
                  field: "input_3-1",
                  operator: ConditionRuleOperator.IS,
                  value: "",
                },
              ],
            },
          },
        ],
        prev: {
          label: "Précédent",
          condition: null,
          icon: null,
        },
        next: {
          label: "Suivant",
          condition: null,
          icon: null,
        },
        condition: null,
      },
    ],
  },
};
