@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.flashInfoBanner {
  display: flex;
  display: grid;
  flex-direction: column;
  grid-template: "controls . close" min-content "content content content" 1fr / min-content 1fr min-content;
  gap: 6px;
  align-items: flex-start;
  padding: 0 16px 24px;

  @include breakpoint(medium up) {
    padding: 0 24px 24px;
  }

  @include breakpoint(large up) {
    display: flex;
    flex-direction: row;
    gap: 32px;
    align-items: center;
    padding: 32px;
  }

  &.color-primary {
    color: $color-white;
    background-color: $color-primary-500;

    --button-hover-color: #{$color-primary-500};
    --button-hover-background: #{$color-white};
  }

  &.color-secondary {
    color: $color-black;
    background-color: $color-secondary-500;

    --button-hover-color: #{$color-black};
    --button-hover-background: #{$color-white};
  }

  &.color-tertiary {
    color: $color-black;
    background-color: $color-tertiary-500;

    --button-hover-color: #{$color-black};
    --button-hover-background: #{$color-white};
  }
}

.content {
  grid-area: content;

  @include breakpoint(large up) {
    flex-grow: 1;
  }
}

.closeButton {
  display: flex;
  grid-area: close;
  gap: 6px;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding-inline: 12px;
  margin-left: auto;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 4px;
  transition:
    background 0.2s ease-in-out,
    color 0.2s ease-in-out;

  i {
    font-size: 1.4rem;
  }

  @include breakpoint(large up) {
    gap: 8px;
    order: 2;
  }

  &:hover,
  &:focus-visible {
    color: var(--button-hover-color);
    background-color: var(--button-hover-background);
  }
}
