import { FilterValues } from "@/lib/filters";

interface HiddenInputsProps {
  filterValues: FilterValues;
  omit: string[];
}

export default function HiddenInputs({ filterValues, omit }: HiddenInputsProps) {
  let inputIndex = 0;

  const inputs = [];

  for (const [key, values] of filterValues.entries()) {
    if (values === null || omit.includes(key)) {
      continue;
    }

    for (const value of values) {
      inputs.push(<input key={`${key}-${value}-${inputIndex++}`} type="hidden" name={key} value={value} />);
    }
  }

  return <>{inputs}</>;
}
