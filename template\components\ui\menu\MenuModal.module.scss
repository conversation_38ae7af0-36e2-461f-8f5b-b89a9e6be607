@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.menuModal {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
}

.header {
  display: flex;
  width: 100%;
  padding: 16px 16px 0;

  @include breakpoint(medium up) {
    padding: 16px 32px 0;
  }

  @include breakpoint(large up) {
    position: absolute;
    padding: 24px 40px 0;
  }

  .closeButton {
    margin-left: auto;
  }
}

.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: auto;

  @include breakpoint(large up) {
    display: flex;
    flex-direction: row;
    overflow-y: revert;
  }
}

// Accordion Menu section
.mainContent {
  flex: 1;
  padding: 16px 16px 48px;

  @include breakpoint(medium up) {
    padding: 16px 56px 56px;
  }

  @include breakpoint(large up) {
    padding-top: 72px;
    overflow-y: auto;
  }
}

.toggleMenuWrapper {
  @include breakpoint(large up) {
    max-width: 592px;
    margin-inline: auto;
  }
}

// Quick Accesses section
.sideContent {
  flex: 1;
  background-color: #f7f7f7;
  border-radius: 0 0 8px 8px;

  @include breakpoint(large up) {
    max-width: 656px;
    border-radius: 0 8px 8px 0;
  }
}

.quickAccessWrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  justify-content: flex-start;
  padding: 48px 40px;

  .list {
    flex: 1;
  }

  @include breakpoint(medium up) {
    flex-direction: row;
    align-items: flex-start;
    padding: 56px;
  }

  @include breakpoint(large up) {
    flex-direction: column;
    width: fit-content;
    padding-top: 72px;
    margin-inline: auto;

    .list {
      flex: 0;
    }
  }
}

.link {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 2rem;
  line-height: normal;
  cursor: pointer;
  transition: color 200ms ease;

  i {
    vertical-align: middle;
    text-align: center;
  }

  span {
    transition: text-decoration 300ms ease-in-out;
  }

  @include breakpoint(large up) {
    gap: 8px;
  }

  &:hover {
    color: $color-black;

    span {
      text-decoration-line: underline;
      text-decoration-thickness: 1px;
      text-decoration-color: currentcolor;
    }
  }
}

.primaryList {
  .link {
    padding-block: 4px;
    font-weight: 700;
    color: $color-primary-500;

    i {
      min-width: 40px;
      font-size: 3.2rem;
    }
  }
}

.secondaryList {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .link {
    padding-block: 3px;
    color: $color-neutral-700;

    i {
      min-width: 24px;
    }
  }
}

// List separator
.divider {
  display: block;
  width: 79px;
  margin-block: 12px;
  content: "";
  border-bottom: 1px solid $color-neutral-300;

  @include breakpoint(large up) {
    margin-block: 16px;
  }
}
