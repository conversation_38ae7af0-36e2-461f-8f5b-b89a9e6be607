"use client";

import { graphql } from "@/generated/graphql";
import { useQuery } from "@apollo/client";
import DateWidget from "./DateWidget";

interface DateWidgetContainerProps {
  eventId: number;
  recurrenceSummary?: string | null;
}

const PERIODS_QUERY = graphql(`
  query GetEventPeriods($id: Int!, $pageSize: Int, $currentPage: Int) {
    event(id: $id) {
      recurrenceSummary
      periods(pageSize: $pageSize, currentPage: $currentPage) {
        items {
          startDate
          fullday
          endDate
        }
        totalCount
        pageInfo {
          currentPage
        }
      }
    }
  }
`);

export default function DateWidgetContainer({ eventId, recurrenceSummary }: DateWidgetContainerProps) {
  const { data, loading, fetchMore } = useQuery(PERIODS_QUERY, {
    variables: { id: eventId, pageSize: 7, currentPage: 1 },
  });

  return (
    <DateWidget
      recurrenceSummary={recurrenceSummary}
      loading={loading}
      dates={data?.event?.periods?.items || []}
      totalCount={data?.event?.periods?.totalCount}
      onLoadMore={() => {
        const currentItemCount = data?.event?.periods?.items?.length || 0;
        const pageSize = currentItemCount + 7;

        fetchMore({
          variables: {
            pageSize: pageSize,
            currentPage: 1,
          },
          updateQuery: (prev, { fetchMoreResult }) => {
            if (!fetchMoreResult) return prev;
            return fetchMoreResult;
          },
        });
      }}
    />
  );
}
