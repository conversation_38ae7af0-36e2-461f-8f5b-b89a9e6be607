import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Select from "./Select";

const meta: Meta<typeof Select> = {
  title: "Form/Select",
  component: Select,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Select>;

export const Default: Story = {
  args: {
    name: "input_1",
    label: "Ville",
    description: "Texte de description additionel",
    placeholder: "Toutes les villes",
    choices: [
      {
        defaultSelected: false,
        icon: null,
        label: "Paris",
        value: "1",
      },
      {
        defaultSelected: false,
        icon: null,
        label: "New York",
        value: "2",
      },
      {
        defaultSelected: false,
        icon: null,
        label: "Tokyo",
        value: "3",
      },
    ],
  },
};
