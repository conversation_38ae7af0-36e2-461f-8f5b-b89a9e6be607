@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.columns {
  display: flex;
  gap: 3.2rem;
  width: 100%;
  margin-block: 32px;

  @include breakpoint(medium down) {
    gap: 2.4rem;
    margin-block: 40px;
  }

  @include breakpoint(small down) {
    gap: 1.6rem;
    margin-block: 48px;
  }

  // According to design, the columns stack starting from tablet and below
  &.stacked {
    @include breakpoint(medium down) {
      display: grid;
    }
  }
}
