import clsx from "clsx";
import Item from "./Item";
import styles from "./Tree.module.scss";
import { TreeNode, useTreeContext } from "./TreeContext";

interface ListProps {
  nodes?: TreeNode[];
  root?: boolean;
}

export default function List({
  className,
  id,
  nodes,
  root,
  ...restProps
}: ListProps & React.HTMLAttributes<HTMLUListElement>) {
  const { tree } = useTreeContext();

  return (
    <ul id={id} className={clsx(styles.list, root && styles.rootList, className)} {...restProps}>
      {(nodes ?? tree).map((node) => (
        <Item root={root} node={node} key={node.value} />
      ))}
    </ul>
  );
}
