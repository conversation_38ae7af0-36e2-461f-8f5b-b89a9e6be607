"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bprogress+react@1.2.7_reac_e8f398973626fa0965135b886aa1ef9a";
exports.ids = ["vendor-chunks/@bprogress+react@1.2.7_reac_e8f398973626fa0965135b886aa1ef9a"];
exports.modules = {

/***/ "(ssr)/../node_modules/.pnpm/@bprogress+react@1.2.7_reac_e8f398973626fa0965135b886aa1ef9a/node_modules/@bprogress/react/dist/index.js":
/*!**************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@bprogress+react@1.2.7_reac_e8f398973626fa0965135b886aa1ef9a/node_modules/@bprogress/react/dist/index.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bar: () => (/* binding */ Bar),\n/* harmony export */   Indeterminate: () => (/* binding */ Indeterminate),\n/* harmony export */   Peg: () => (/* binding */ Peg),\n/* harmony export */   Progress: () => (/* binding */ Progress),\n/* harmony export */   ProgressProvider: () => (/* binding */ ProgressProvider),\n/* harmony export */   Spinner: () => (/* binding */ Spinner),\n/* harmony export */   SpinnerIcon: () => (/* binding */ SpinnerIcon),\n/* harmony export */   useAnchorProgress: () => (/* binding */ useAnchorProgress),\n/* harmony export */   useProgress: () => (/* binding */ useProgress),\n/* harmony export */   withMemo: () => (/* binding */ withMemo),\n/* harmony export */   withSuspense: () => (/* binding */ withSuspense)\n/* harmony export */ });\n/* harmony import */ var _bprogress_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @bprogress/core */ \"(ssr)/../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/providers/progress-provider.tsx\nfunction _define_property(obj, key, value) {\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _object_spread(target) {\n    for(var i = 1; i < arguments.length; i++){\n        var source = arguments[i] != null ? arguments[i] : {};\n        var ownKeys = Object.keys(source);\n        if (typeof Object.getOwnPropertySymbols === \"function\") {\n            ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n            }));\n        }\n        ownKeys.forEach(function(key) {\n            _define_property(target, key, source[key]);\n        });\n    }\n    return target;\n}\nfunction ownKeys(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n        var symbols = Object.getOwnPropertySymbols(object);\n        if (enumerableOnly) {\n            symbols = symbols.filter(function(sym) {\n                return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n            });\n        }\n        keys.push.apply(keys, symbols);\n    }\n    return keys;\n}\nfunction _object_spread_props(target, source) {\n    source = source != null ? source : {};\n    if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n        ownKeys(Object(source)).forEach(function(key) {\n            Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n    }\n    return target;\n}\nfunction _object_without_properties(source, excluded) {\n    if (source == null) return {};\n    var target = _object_without_properties_loose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n        var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n        for(i = 0; i < sourceSymbolKeys.length; i++){\n            key = sourceSymbolKeys[i];\n            if (excluded.indexOf(key) >= 0) continue;\n            if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n            target[key] = source[key];\n        }\n    }\n    return target;\n}\nfunction _object_without_properties_loose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n\n\nvar ProgressContext = (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(void 0);\nvar useProgress = function() {\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ProgressContext);\n    if (!context) {\n        throw new Error(\"useProgress must be used within a ProgressProvider\");\n    }\n    return context;\n};\nvar ProgressProvider = function(param) {\n    var children = param.children, _param_color = param.color, color = _param_color === void 0 ? \"#0A2FFF\" : _param_color, _param_height = param.height, height = _param_height === void 0 ? \"2px\" : _param_height, options = param.options, _param_spinnerPosition = param.spinnerPosition, spinnerPosition = _param_spinnerPosition === void 0 ? \"top-right\" : _param_spinnerPosition, style = param.style, _param_disableStyle = param.disableStyle, disableStyle = _param_disableStyle === void 0 ? false : _param_disableStyle, nonce = param.nonce, _param_shallowRouting = param.shallowRouting, shallowRouting = _param_shallowRouting === void 0 ? false : _param_shallowRouting, _param_disableSameURL = param.disableSameURL, disableSameURL = _param_disableSameURL === void 0 ? true : _param_disableSameURL, _param_startPosition = param.startPosition, startPosition = _param_startPosition === void 0 ? 0 : _param_startPosition, _param_delay = param.delay, delay = _param_delay === void 0 ? 0 : _param_delay, _param_stopDelay = param.stopDelay, stopDelay = _param_stopDelay === void 0 ? 0 : _param_stopDelay;\n    var timer = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var isAutoStopDisabled = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    var disableAutoStop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        return isAutoStopDisabled.current = true;\n    }, []);\n    var enableAutoStop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        return isAutoStopDisabled.current = false;\n    }, []);\n    var start = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        var startPosition2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, delay2 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, autoStopDisabled = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        if (autoStopDisabled) disableAutoStop();\n        timer.current = setTimeout(function() {\n            if (startPosition2 > 0) _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.set(startPosition2);\n            _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.start();\n        }, delay2);\n    }, [\n        disableAutoStop\n    ]);\n    var stop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        var stopDelay2 = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, forcedStopDelay = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        setTimeout(function() {\n            if (timer.current) clearTimeout(timer.current);\n            timer.current = setTimeout(function() {\n                if (!_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.isStarted()) return;\n                _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.done();\n                if (isAutoStopDisabled.current) enableAutoStop();\n            }, stopDelay2);\n        }, forcedStopDelay);\n    }, [\n        enableAutoStop\n    ]);\n    var inc = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(amount) {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.inc(amount);\n    }, []);\n    var dec = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(amount) {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.dec(amount);\n    }, []);\n    var set = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(n) {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.set(n);\n    }, []);\n    var pause = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.pause();\n    }, []);\n    var resume = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.resume();\n    }, []);\n    var getOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function() {\n        return _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.settings;\n    }, []);\n    var setOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function(newOptions) {\n        var currentOptions = getOptions();\n        var updates = typeof newOptions === \"function\" ? newOptions(currentOptions) : newOptions;\n        var nextOptions = _object_spread({}, currentOptions, updates);\n        _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.configure(nextOptions);\n    }, [\n        getOptions\n    ]);\n    var styles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"style\", {\n            nonce: nonce\n        }, style || (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.css)({\n            color: color,\n            height: height,\n            spinnerPosition: spinnerPosition\n        }));\n    }, [\n        color,\n        height,\n        nonce,\n        spinnerPosition,\n        style\n    ]);\n    _bprogress_core__WEBPACK_IMPORTED_MODULE_0__.BProgress.configure(options || {});\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(ProgressContext.Provider, {\n        value: {\n            start: start,\n            stop: stop,\n            inc: inc,\n            dec: dec,\n            set: set,\n            pause: pause,\n            resume: resume,\n            setOptions: setOptions,\n            getOptions: getOptions,\n            isAutoStopDisabled: isAutoStopDisabled,\n            disableAutoStop: disableAutoStop,\n            enableAutoStop: enableAutoStop,\n            shallowRouting: shallowRouting,\n            disableSameURL: disableSameURL,\n            startPosition: startPosition,\n            delay: delay,\n            stopDelay: stopDelay\n        }\n    }, !disableStyle ? styles : null, children);\n};\n// src/hooks/use-anchor-progress.tsx\n\n\nfunction useAnchorProgress(param) {\n    var _param_shallowRouting = param.shallowRouting, shallowRouting = _param_shallowRouting === void 0 ? false : _param_shallowRouting, _param_disableSameURL = param.disableSameURL, disableSameURL = _param_disableSameURL === void 0 ? true : _param_disableSameURL, _param_startPosition = param.startPosition, startPosition = _param_startPosition === void 0 ? 0 : _param_startPosition, _param_delay = param.delay, delay = _param_delay === void 0 ? 0 : _param_delay, _param_stopDelay = param.stopDelay, stopDelay = _param_stopDelay === void 0 ? 0 : _param_stopDelay, targetPreprocessor = param.targetPreprocessor, _param_disableAnchorClick = param.disableAnchorClick, disableAnchorClick = _param_disableAnchorClick === void 0 ? false : _param_disableAnchorClick, _param_startOnLoad = param.startOnLoad, startOnLoad = _param_startOnLoad === void 0 ? false : _param_startOnLoad, _param_forcedStopDelay = param.forcedStopDelay, forcedStopDelay = _param_forcedStopDelay === void 0 ? 0 : _param_forcedStopDelay, deps = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n    var elementsWithAttachedHandlers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    var timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var _useProgress = useProgress(), start = _useProgress.start, stop = _useProgress.stop, isAutoStopDisabled = _useProgress.isAutoStopDisabled;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (startOnLoad) start(startPosition, delay);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (timerRef.current) clearTimeout(timerRef.current);\n        timerRef.current = setTimeout(function() {\n            if (!isAutoStopDisabled.current) stop();\n        }, stopDelay);\n        return function() {\n            if (timerRef.current) clearTimeout(timerRef.current);\n        };\n    }, deps);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (disableAnchorClick) return;\n        var handleAnchorClick = function(event) {\n            if (event.defaultPrevented) return;\n            var anchorElement = event.currentTarget;\n            if (anchorElement.hasAttribute(\"download\")) return;\n            var target = event.target;\n            var preventProgress = (target === null || target === void 0 ? void 0 : target.getAttribute(\"data-prevent-progress\")) === \"true\" || (anchorElement === null || anchorElement === void 0 ? void 0 : anchorElement.getAttribute(\"data-prevent-progress\")) === \"true\";\n            if (!preventProgress) {\n                var element = target;\n                while(element && element.tagName.toLowerCase() !== \"a\"){\n                    var _element_parentElement;\n                    if (((_element_parentElement = element.parentElement) === null || _element_parentElement === void 0 ? void 0 : _element_parentElement.getAttribute(\"data-prevent-progress\")) === \"true\") {\n                        preventProgress = true;\n                        break;\n                    }\n                    element = element.parentElement;\n                }\n            }\n            if (preventProgress) return;\n            var anchorTarget = (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.getAnchorProperty)(anchorElement, \"target\");\n            if (anchorTarget === \"_blank\") return;\n            if (event.metaKey || event.ctrlKey || event.shiftKey || event.altKey) return;\n            var targetHref = (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.getAnchorProperty)(anchorElement, \"href\");\n            var targetUrl = targetPreprocessor ? targetPreprocessor(new URL(targetHref)) : new URL(targetHref);\n            var currentUrl = new URL(location.href);\n            if (shallowRouting && (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.isSameURLWithoutSearch)(targetUrl, currentUrl) && disableSameURL) return;\n            if ((0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.isSameURL)(targetUrl, currentUrl) && disableSameURL) return;\n            start(startPosition, delay);\n        };\n        var handleMutation = function() {\n            var anchorElements = Array.from(document.querySelectorAll(\"a\"));\n            var validAnchorElements = anchorElements.filter(function(anchor) {\n                var href = (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.getAnchorProperty)(anchor, \"href\");\n                var isBProgressDisabled = anchor.getAttribute(\"data-disable-progress\") === \"true\";\n                var isNotTelOrMailto = href && !href.startsWith(\"tel:\") && !href.startsWith(\"mailto:\") && !href.startsWith(\"blob:\") && !href.startsWith(\"javascript:\");\n                return !isBProgressDisabled && isNotTelOrMailto && (0,_bprogress_core__WEBPACK_IMPORTED_MODULE_0__.getAnchorProperty)(anchor, \"target\") !== \"_blank\";\n            });\n            validAnchorElements.forEach(function(anchor) {\n                anchor.addEventListener(\"click\", handleAnchorClick, true);\n            });\n            elementsWithAttachedHandlers.current = validAnchorElements;\n        };\n        var mutationObserver = new MutationObserver(handleMutation);\n        mutationObserver.observe(document, {\n            childList: true,\n            subtree: true\n        });\n        var originalWindowHistoryPushState = window.history.pushState;\n        window.history.pushState = new Proxy(window.history.pushState, {\n            apply: function(target, thisArg, argArray) {\n                if (!isAutoStopDisabled.current) stop(stopDelay, forcedStopDelay);\n                return target.apply(thisArg, argArray);\n            }\n        });\n        return function() {\n            mutationObserver.disconnect();\n            elementsWithAttachedHandlers.current.forEach(function(anchor) {\n                anchor.removeEventListener(\"click\", handleAnchorClick, true);\n            });\n            elementsWithAttachedHandlers.current = [];\n            window.history.pushState = originalWindowHistoryPushState;\n        };\n    }, [\n        disableAnchorClick,\n        targetPreprocessor,\n        shallowRouting,\n        disableSameURL,\n        delay,\n        stopDelay,\n        startPosition,\n        start,\n        stop,\n        forcedStopDelay,\n        isAutoStopDisabled\n    ]);\n}\n// src/components/progress.tsx\n\n// src/utils/classnames.ts\nfunction classNames() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter(Boolean).join(\" \");\n}\n// src/components/bar.tsx\n\nfunction BarInner(_param, ref) {\n    var as = _param.as, children = _param.children, className = _param.className, _param_classSelector = _param.classSelector, classSelector = _param_classSelector === void 0 ? \"bar\" : _param_classSelector, rest = _object_without_properties(_param, [\n        \"as\",\n        \"children\",\n        \"className\",\n        \"classSelector\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(classSelector, className)\n    }, rest), children);\n}\nvar Bar = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(BarInner);\n// src/components/peg.tsx\n\nfunction PegInner(_param, ref) {\n    var as = _param.as, children = _param.children, className = _param.className, _param_classSelector = _param.classSelector, classSelector = _param_classSelector === void 0 ? \"peg\" : _param_classSelector, rest = _object_without_properties(_param, [\n        \"as\",\n        \"children\",\n        \"className\",\n        \"classSelector\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(classSelector, className)\n    }, rest), children);\n}\nvar Peg = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(PegInner);\n// src/components/spinner.tsx\n\nfunction SpinnerInner(_param, ref) {\n    var as = _param.as, children = _param.children, className = _param.className, _param_classSelector = _param.classSelector, classSelector = _param_classSelector === void 0 ? \"spinner\" : _param_classSelector, rest = _object_without_properties(_param, [\n        \"as\",\n        \"children\",\n        \"className\",\n        \"classSelector\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(classSelector, className)\n    }, rest), children);\n}\nvar Spinner = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(SpinnerInner);\n// src/components/spinner-icon.tsx\n\nfunction SpinnerIconInner(_param, ref) {\n    var as = _param.as, children = _param.children, className = _param.className, _param_classSelector = _param.classSelector, classSelector = _param_classSelector === void 0 ? \"spinner-icon\" : _param_classSelector, rest = _object_without_properties(_param, [\n        \"as\",\n        \"children\",\n        \"className\",\n        \"classSelector\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(classSelector, className)\n    }, rest), children);\n}\nvar SpinnerIcon = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(SpinnerIconInner);\n// src/components/progress.tsx\nfunction ProgressInner(_param, ref) {\n    var as = _param.as, children = _param.children, className = _param.className, style = _param.style, rest = _object_without_properties(_param, [\n        \"as\",\n        \"children\",\n        \"className\",\n        \"style\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(\"bprogress\", className),\n        style: _object_spread_props(_object_spread({}, style), {\n            display: \"none\"\n        })\n    }, rest), children || /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Bar, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Peg, null)), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Spinner, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(SpinnerIcon, null))));\n}\nvar Progress = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(ProgressInner);\n// src/components/indeterminate.tsx\n\nfunction IndeterminateInner(_param, ref) {\n    var as = _param.as, className = _param.className, _param_classSelector = _param.classSelector, classSelector = _param_classSelector === void 0 ? \"indeterminate\" : _param_classSelector, rest = _object_without_properties(_param, [\n        \"as\",\n        \"className\",\n        \"classSelector\"\n    ]);\n    var Component = as !== null && as !== void 0 ? as : \"div\";\n    return react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({\n        ref: ref,\n        className: classNames(classSelector, className)\n    }, rest), react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: \"inc\"\n    }), react__WEBPACK_IMPORTED_MODULE_1__.createElement(\"div\", {\n        className: \"dec\"\n    }));\n}\nvar Indeterminate = react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(IndeterminateInner);\n// src/utils/with-suspense.tsx\n\nfunction withSuspense(Component) {\n    return function WithSuspenseComponent(props) {\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Component, _object_spread({}, props)));\n    };\n}\n// src/utils/with-memo.tsx\n\nfunction shallowCompareProps(prevProps, nextProps) {\n    var ignoreKeys = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    var prevKeys = Object.keys(prevProps).filter(function(key) {\n        return !ignoreKeys.includes(key);\n    });\n    var nextKeys = Object.keys(nextProps).filter(function(key) {\n        return !ignoreKeys.includes(key);\n    });\n    if (prevKeys.length !== nextKeys.length) {\n        return false;\n    }\n    var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n    try {\n        for(var _iterator = prevKeys[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n            var key = _step.value;\n            if (prevProps[key] !== nextProps[key]) {\n                return false;\n            }\n        }\n    } catch (err) {\n        _didIteratorError = true;\n        _iteratorError = err;\n    } finally{\n        try {\n            if (!_iteratorNormalCompletion && _iterator.return != null) {\n                _iterator.return();\n            }\n        } finally{\n            if (_didIteratorError) {\n                throw _iteratorError;\n            }\n        }\n    }\n    return true;\n}\nfunction withMemo(Component) {\n    var ignoreKeys = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [\n        \"memo\",\n        \"shouldCompareComplexProps\"\n    ];\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(Component, function(prevProps, nextProps) {\n        if (nextProps.memo === false) return false;\n        if (!nextProps.shouldCompareComplexProps) return true;\n        return shallowCompareProps(prevProps, nextProps, ignoreKeys);\n    });\n}\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@bprogress+react@1.2.7_reac_e8f398973626fa0965135b886aa1ef9a/node_modules/@bprogress/react/dist/index.js\n");

/***/ })

};
;