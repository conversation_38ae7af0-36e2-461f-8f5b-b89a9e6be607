"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import { useEffect, useState } from "react";
import styles from "./SocialShare.module.scss";

const networkIcons: Record<string, string> = {
  facebook: "fab fa-facebook-f",
  x: "fab fa-x-twitter",
  linkedin: "fab fa-linkedin-in",
};

interface SocialMediaItem {
  name: string;
  network: string;
  shareUrl: string;
}

export default function SocialShare() {
  const [currentUrl, setCurrentUrl] = useState("");

  useEffect(() => {
    setCurrentUrl(globalThis.location.href);
  }, []);

  if (!currentUrl) return null;

  const socialMedia: SocialMediaItem[] = [
    {
      name: "Facebook",
      network: "facebook",
      shareUrl: `https://www.facebook.com/sharer/sharer.php?u=${currentUrl}`,
    },
    {
      name: "X",
      network: "x",
      shareUrl: `https://x.com/intent/tweet?url=${currentUrl}`,
    },
    {
      name: "LinkedIn",
      network: "linkedin",
      shareUrl: `https://www.linkedin.com/sharing/share-offsite/?url=${currentUrl}`,
    },
  ];

  return (
    <div className="container">
      <div className={styles.socialShare}>
        <h3 className={styles.title}>
          <i className="fa-light fa-share-nodes" aria-hidden="true"></i>
          Partager cette page sur
        </h3>
        <div className={styles.linksContainer}>
          {socialMedia.map((social) => (
            <Tooltip key={social.network} content={`Partager sur ${social.name}`}>
              <a href={social.shareUrl} className={styles.socialLink} target="_blank" rel="noopener noreferrer">
                <span className="sr-only">{`Partager sur ${social.name}`}</span>
                <i className={networkIcons[social.network]} aria-hidden="true"></i>
              </a>
            </Tooltip>
          ))}
        </div>
      </div>
    </div>
  );
}
