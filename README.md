# Citéopolis 5 - FE

This is Citéopolis 5's frontend repository.

You should only install this repository for development purposes. Projects should depend on individual packages in this repository.

## Requirements

- Node.js 22
- PNPM 10

## Packages

- [@citeopolis/commitlint-config](./packages/commitlint-config)
- [@citeopolis/create-app](./packages/create-app)
- [@citeopolis/eslint-config](./packages/eslint-config)
- [@citeopolis/prettier-config](./packages/prettier-config)
- [@citeopolis/stylelint-config](./packages/stylelint-config)

## Templates

- [@citeopolis/template](./template)

## Install

    pnpm install

> Installs all the dependencies for all the workspace packages.
