import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Time from "./Time";

const meta: Meta<typeof Time> = {
  title: "Form/Time",
  component: Time,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Time>;

export const Default: Story = {
  args: {
    name: "time_1",
    label: "Heure de préférence",
    description: "Sélectionnez votre heure de préférence",
    placeholder: "12:00",
  },
};

export const Required: Story = {
  args: {
    name: "time_2",
    label: "Heure obligatoire",
    description: "Ce champ est obligatoire",
    required: true,
  },
};

export const WithDefaultValue: Story = {
  args: {
    name: "time_3",
    label: "Heure avec valeur par défaut",
    description: "Heure pré-remplie",
    defaultValue: "14:30",
  },
};

export const MeetingTime: Story = {
  args: {
    name: "meeting_time",
    label: "Heure du rendez-vous",
    description: "<PERSON>sissez l'heure de votre rendez-vous",
    required: true,
  },
};
