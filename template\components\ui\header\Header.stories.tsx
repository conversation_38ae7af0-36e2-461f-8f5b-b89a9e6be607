import { SocialNetwork } from "@/generated/graphql/graphql";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Header from "./Header";

const meta: Meta<typeof Header> = {
  title: "Components/Header",
  component: Header,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Header>;

export const Default: Story = {
  args: {
    siteName: "Citéopolis 5",
    logo: "/assets/logo.svg",
    searchUrl: "/recherche",
    menu: {
      __typename: "Menu",
      items: [
        {
          __typename: "MenuItem",
          children: [
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "#",
                  title: "Aenean pellentesque massa et libero fringilla",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "https://wordpress.com",
                  title: "N3 - Lien un peu long qui passe sur plusieurs lignes",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/?page_id=177",
                  title: "Praesent pellentesque bibendum ex vel bibendum",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/page-de-test/sous-page-1/",
                  title: "N5 -  Google",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/sdzd/",
                  title: "Nulla sit amet elit in magna sollicitudin volutpat pellentesque",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "/associations/",
              title: "N3 - event page",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "/button-test/",
                  title: "button test",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/page-avec-formulaire-test/",
                  title: "N2 - Formulaire",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/test-evenements/",
                  title: "N5 - événements",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "https://instragram.com",
                  title: "N3 - Instagram",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "/test-deaf-modules-contenu/",
              title: "[TEST DEAF] Modules contenu",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "https://stratis.fr",
                  title: "N3- Stratis",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "https://youtube.com",
                  title: "N3- Youtube",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "/deliberation/deliberation-n2025-014-budget-primitif-2025/",
                  title: "Délibération n°2025-014 – Budget primitif 2025",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "/page-de-test/sous-page-2/",
              title: "N3 - Sous page",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "#",
                  title: "N2 - Yahoo",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
                {
                  __typename: "MenuItem",
                  url: "https://google.fr",
                  title: "N4 - Google",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "https://google.fr",
              title: "Mon quotidien",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "/test-discover/",
                  title: "N5 - Découvrir",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "/page-de-test/",
              title: "Suspendisse ullamcorper leo vitae massa faucibus",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
            {
              __typename: "MenuItem",
              children: [],
              url: "/page-de-test/sous-page-2/",
              title: "Mes loisirs",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
          ],
          url: "/test-download/",
          title: "Ma ville",
          target: null,
          linkTitle: null,
          level: 0,
          description: "",
          className: "",
        },
        {
          __typename: "MenuItem",
          children: [
            {
              __typename: "MenuItem",
              children: [
                {
                  __typename: "MenuItem",
                  url: "/annu/",
                  title: "[TEST] module albums",
                  target: null,
                  linkTitle: null,
                  level: 2,
                  description: "",
                  className: "",
                },
              ],
              url: "/les-modules-de-contenu/",
              title: "N3 - Actualités",
              target: null,
              linkTitle: null,
              level: 1,
              description: "",
              className: "",
            },
          ],
          url: "/test-annuaire-22/",
          title: "annuaires",
          target: null,
          linkTitle: null,
          level: 0,
          description: "",
          className: "",
        },
        {
          __typename: "MenuItem",
          children: [],
          url: "/test-tableau/",
          title: "test tableau",
          target: null,
          linkTitle: null,
          level: 0,
          description: "",
          className: "",
        },
      ],
    },
    flashInfoItems: [
      {
        __typename: "FlashInfo",
        id: 4706,
        url: null,
        description: null,
        title: "Plan canicule (sans description)",
        modifiedDate: "2025-07-02T14:23:00+02:00",
      },
      {
        __typename: "FlashInfo",
        id: 4547,
        url: null,
        description:
          "Ce flash contient des informations importantes concernant une mise à jour imminente du système. dddddddddddddddddddddddddddddddddddddddddddddddddddddd",
        title:
          "Flash Info exceptionnel : découvrez dès maintenant les dernières actualités importantes concernant notre plate",
        modifiedDate: "2025-07-09T16:23:44+02:00",
      },
      {
        __typename: "FlashInfo",
        id: 3950,
        url: "https://stratis.fr",
        description:
          "Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur.Ut enim ad minima",
        title:
          "[TEST] Ut enim ad minima veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliqu",
        modifiedDate: "2025-07-09T16:23:50+02:00",
      },
      {
        __typename: "FlashInfo",
        id: 3947,
        url: "https://stratis.fr",
        description:
          "Seed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo whww",
        title: null,
        modifiedDate: "2025-07-01T12:06:20+02:00",
      },
      {
        __typename: "FlashInfo",
        id: 3894,
        url: "https://stratis.fr",
        description:
          "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque tristique, tortor ac eleifend tincidunt, eros elit ornare diam.",
        title: "Sortie de la V5 imminente",
        modifiedDate: "2025-07-02T13:05:27+02:00",
      },
    ],
    socialLinks: [
      {
        __typename: "SocialLink",
        network: SocialNetwork.FACEBOOK,
        text: "Notre page Facebook",
        url: "https://facebook.com/agence.stratis",
      },
      {
        __typename: "SocialLink",
        network: SocialNetwork.INSTAGRAM,
        text: "Notre compte Instagram",
        url: "https://instagram.com/agence.stratis",
      },
      {
        __typename: "SocialLink",
        network: SocialNetwork.LINKEDIN,
        text: "Notre page LinkedIn",
        url: "https://linkedin.com/stratis",
      },
      {
        __typename: "SocialLink",
        network: SocialNetwork.TWITTER,
        text: "Notre compte Twitter",
        url: "https://twitter.com/@agence.stratis",
      },
      {
        __typename: "SocialLink",
        network: SocialNetwork.YOUTUBE,
        text: "youtube",
        url: "https://youtube.com/agence.stratis",
      },
    ],
  },
};
