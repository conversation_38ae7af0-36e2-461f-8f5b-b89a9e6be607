@use "@/styles/lib/variables.scss" as *;

.socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 1;
  color: $color-primary-500;
  border: 1px solid $color-primary-500;
  border-radius: 100%;
  transition:
    color 0.2s ease,
    background-color 0.2s ease;

  i {
    vertical-align: middle;
  }

  &.size-xxs {
    width: 24px;
    height: 24px;
    font-size: 1.2rem;
  }

  &.size-xs {
    width: 32px;
    height: 32px;
    font-size: 1.4rem;
  }

  &.size-lg {
    width: 64px;
    height: 64px;
    font-size: 2rem;
    border-width: 2px;
  }

  &.size-xl {
    width: 96px;
    height: 96px;
    font-size: 2.4rem;
    border-width: 2px;
  }

  &:hover {
    color: $color-white;
    background-color: $color-primary-500;
  }
}
