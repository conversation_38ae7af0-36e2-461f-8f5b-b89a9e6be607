import {
  OneHalfColumnsDecorator,
  OneThirdColumnsDecorator,
  TwoThirdColumnsDecorator,
} from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Paragraph from "../paragraph/Paragraph";
import Quote from "./Quote";

const meta: Meta<typeof Quote> = {
  title: "Blocks/Quote",
  component: Quote,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Quote>;

const quoteContent = (
  <Paragraph html="Et licet quocumque oculos flexeris feminas adfatim multas spectare cirratas, quibus, si nupsissent, per aetatem ter iam nixus poterat suppetere liberorum, ad usque taedium pedibus pavimenta tergentes iactari volucriter gyris, dum exprimunt innumera simulacra, quae finxere fabulae theatrales." />
);

export const Default: Story = {
  args: {
    children: quoteContent,
  },
};

export const Author: Story = {
  args: {
    children: quoteContent,
    sourceHTML: "Lorem ipsum",
  },
};

export const RichContent: Story = {
  args: {
    children: (
      <Paragraph html="Et licet quocumque oculos flexeris <b>feminas</b> adfatim multas spectare <i>cirratas</i>, quibus, si nupsissent, per aetatem ter iam <a href='#'>nixus</a> poterat suppetere liberorum." />
    ),
    sourceHTML: "<strong>Important</strong> source with <em>formatting</em>",
  },
};

export const TwoColumns: Story = {
  args: {
    children: quoteContent,
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    children: quoteContent,
  },
  decorators: [OneThirdColumnsDecorator],
};

export const HalfColumn: Story = {
  args: {
    children: quoteContent,
  },
  decorators: [OneHalfColumnsDecorator],
};
