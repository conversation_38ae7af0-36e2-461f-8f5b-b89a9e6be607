"use client";

import Hx from "@/components/ui/title/Hx";
import type { Event } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import styles from "./EventsFocus.module.scss";

const DateInterval = dynamic(() => import("@/components/ui/date/DateInterval"));

interface FocusProps {
  event: Event;
}

export default function Focus({ event: { images, url, title, startDate, endDate, location, categories } }: FocusProps) {
  const titleLevel = useTitleLevel();
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;

  return (
    <article className={styles.eventsFocus}>
      <div className={styles.details}>
        <Hx level={titleLevel} className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </Hx>
        {startDate && <DateInterval className={styles.date} size="lg" from={startDate} to={endDate} />}
        {(location?.address?.city || location?.title) && (
          <div className={styles.location}>
            {location?.address?.city && (
              <p className={styles.city} aria-roledescription="Ville">
                {location.address.city}
              </p>
            )}
            {location?.title && (
              <p className={styles.place} aria-roledescription="Lieu">
                {location.title}
              </p>
            )}
          </div>
        )}
      </div>
      {image && (
        <Image
          className={styles.image}
          src={image.url}
          width={592}
          height={395}
          alt={image.alt ?? ""}
          sizes="(max-width: 767px) 344px, (max-width: 1301px) 348px, 592px"
        />
      )}
    </article>
  );
}
