import type { Meta, StoryObj } from "@storybook/nextjs";
import PublicationFile from "./PublicationFile";

const meta: Meta<typeof PublicationFile> = {
  title: "Components/PublicationFile",
  component: PublicationFile,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ containerType: "inline-size" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof PublicationFile>;

export const Default: Story = {
  args: {
    label: "Lorem dolor sit amet consectur elis jacta est",
    extname: "pdf",
    size: 1_000_000, // 1MB
    downloadUrl: "/assets/placeholder-720x480.png",
    viewUrl: "#",
  },
};

export const LongFilename: Story = {
  args: {
    label:
      "Lorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur est",
    extname: "pdf",
    size: 1_000_000, // 1MB
    downloadUrl: "/assets/placeholder-720x480.png",
    viewUrl: "#",
  },
};

export const WithoutExtension: Story = {
  args: {
    label: "Lorem dolor sit amet consectur elis jacta est",
    extname: "",
    size: 1_000_000, // 1MB
    downloadUrl: "/assets/placeholder-720x480.png",
    viewUrl: "#",
  },
};
