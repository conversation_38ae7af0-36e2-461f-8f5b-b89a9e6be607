import type { Meta, StoryObj } from "@storybook/nextjs";
import Title from "./Title";

const meta: Meta<typeof Title> = {
  title: "Components/Title",
  component: Title,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Title>;

export const One: Story = {
  name: "H1",
  args: {
    level: 1,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Two: Story = {
  name: "H2",
  args: {
    level: 2,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Three: Story = {
  name: "H3",
  args: {
    level: 3,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Four: Story = {
  name: "H4",
  args: {
    level: 4,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Five: Story = {
  name: "H5",
  args: {
    level: 5,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Six: Story = {
  name: "H6",
  args: {
    level: 6,
    children: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};
