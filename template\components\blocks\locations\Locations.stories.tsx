import { DirectoryAccessibilityStatus, PhoneDeviceType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Locations from "./Locations";

const meta: Meta<typeof Locations> = {
  title: "Blocks/Locations",
  component: Locations,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Locations>;

export const Default: Story = {
  args: {
    locations: [
      {
        __typename: "Directory",
        url: "#",
        title: "Quartier du Musée du Louvre",
        categories: [
          // @ts-expect-error -- Incomplete category
          {
            title: "Thématique",
          },
        ],
        // @ts-expect-error -- Incomplete image collection
        images: {
          ratio_3x2: {
            url: "/assets/placeholder-200x134.png",
            width: 200,
            height: 134,
            alt: "",
          },
        },
        // @ts-expect-error -- Incomplete accessibility
        accessibility: {
          hearingImpairment: DirectoryAccessibilityStatus.SUPPORTED,
          signLanguageReception: DirectoryAccessibilityStatus.SUPPORTED,
          visualImpairment: DirectoryAccessibilityStatus.SUPPORTED,
        },
        location: {
          title: "Quartier du Musée du Louvre",
          longitude: 2.3499,
          latitude: 48.8529,
          address: {
            street: ["Rue de Rivoli"],
            city: "Paris",
            country: "France",
            zip: "75001",
          },
        },
        email: "<EMAIL>",
        website: "https://www.stratis.fr",
        phones: [
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "0465715233",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "06399845",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.FAX,
            number: "0465715234",
          },
        ],
      },
      {
        __typename: "Directory",
        url: "#",
        title: "Quartier de la Place Victor Hugo",
        categories: [
          // @ts-expect-error -- Incomplete category
          {
            title: "Thématique",
          },
        ],
        // @ts-expect-error -- Incomplete image collection
        images: {
          ratio_3x2: {
            url: "/assets/placeholder-200x134.png",
            width: 200,
            height: 134,
            alt: "",
          },
        },
        // @ts-expect-error -- Incomplete accessibility
        accessibility: {
          hearingImpairment: DirectoryAccessibilityStatus.SUPPORTED,
          reducedMobility: DirectoryAccessibilityStatus.NOT_SUPPORTED,
        },
        location: {
          title: "Quartier de la Place Victor Hugo",
          longitude: 2.295,
          latitude: 48.8738,
          address: {
            street: ["Avenue Victor Hugo"],
            city: "Paris",
            country: "France",
            zip: "75116",
          },
        },
        phones: [
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "0465715233",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "06399845",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.FAX,
            number: "0465715234",
          },
        ],
      },
      {
        __typename: "Directory",
        url: "#",
        title: "Quartier de la Place Victor Hugo (Duplicata)",
        categories: [
          // @ts-expect-error -- Incomplete category
          {
            title: "Thématique 1",
          },
          // @ts-expect-error -- Incomplete category
          {
            title: "Thématique 2",
          },
        ],
        // @ts-expect-error -- Incomplete image collection
        images: {
          ratio_3x2: {
            url: "/assets/placeholder-200x134.png",
            width: 200,
            height: 134,
            alt: "",
          },
        },
        // @ts-expect-error -- Incomplete accessibility
        accessibility: {
          reducedMobility: DirectoryAccessibilityStatus.NOT_SUPPORTED,
          strollers: DirectoryAccessibilityStatus.NOT_SUPPORTED,
        },
        location: {
          title: "Quartier de la Place Victor Hugo",
          longitude: 2.295,
          latitude: 48.8738,
          address: {
            street: ["Avenue Victor Hugo"],
            city: "Paris",
            country: "France",
            zip: "75116",
          },
        },
        phones: [
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "0465715233",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.MOBILE,
            number: "06399845",
          },
          // @ts-expect-error -- Incomplete phone
          {
            deviceType: PhoneDeviceType.FAX,
            number: "0465715234",
          },
        ],
      },
    ],
  },
};
