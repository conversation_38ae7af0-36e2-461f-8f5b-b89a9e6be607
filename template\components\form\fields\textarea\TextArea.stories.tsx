import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import TextArea from "./TextArea";

const meta: Meta<typeof TextArea> = {
  title: "Form/TextArea",
  component: TextArea,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof TextArea>;

export const Default: Story = {
  args: {
    name: "input_1",
    label: "Message",
    description: "Texte de description additionel",
    placeholder: "What are your thoughts?",
  },
};
