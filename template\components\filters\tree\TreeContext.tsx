import { SelectFilterOption } from "@/generated/graphql/graphql";
import { usePathname, useSearchParams } from "next/navigation";
import { createContext, use, useMemo } from "react";

export interface TreeNode extends SelectFilterOption {
  status?: "ACTIVE" | "PARTIAL" | "INACTIVE";
  defaultExpanded?: boolean;
  children: TreeNode[];
}

interface TreeContextValue {
  attribute: string;
  tree: TreeNode[];
}

const TreeContext = createContext<TreeContextValue | null>(null);

interface TreeContextProviderProps {
  attribute?: string;
  options: SelectFilterOption[];
  selectedOptions: string[];
  defaultExpanded?: boolean;
}

export function TreeContextProvider({
  attribute,
  options,
  selectedOptions,
  defaultExpanded,
  children,
}: React.PropsWithChildren<TreeContextProviderProps>) {
  if (!attribute) {
    throw new Error("No attribute defined for the Tree component.");
  }

  const tree = useMemo(() => {
    const calculateStatus = (
      node: SelectFilterOption | TreeNode,
      selected: string[],
      parentIsActive: boolean = false
    ): TreeNode => {
      let status: TreeNode["status"];

      if (parentIsActive || selected.includes(node.value)) {
        status = "ACTIVE";
      } else if (node.children && node.children.length > 0) {
        const hasSelectedDescendant = node.children.some((child) => {
          const childWithStatus = calculateStatus(child, selected, false);
          return childWithStatus.status === "ACTIVE" || childWithStatus.status === "PARTIAL";
        });

        status = hasSelectedDescendant ? "PARTIAL" : undefined;
      }

      return {
        ...node,
        ...(status && { status }),
        defaultExpanded,
        children: node.children.map((child) => calculateStatus(child, selected, status === "ACTIVE")),
      };
    };

    return options.map((node) => calculateStatus(node, selectedOptions));
  }, [options, selectedOptions]);

  return <TreeContext value={{ attribute, tree }}>{children}</TreeContext>;
}

export function useTreeContext() {
  const context = use(TreeContext);

  if (!context) {
    throw new Error("useTreeContext must be used inside a Tree component");
  }

  return context;
}

export function useLink(options: TreeNode[], attribute: string, value: string) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Memoize lookup maps for parent-child relationships and ancestors
  const { childrenMap, parentMap } = useMemo(() => {
    const childrenMap = new Map<string, Set<string>>();
    const parentMap = new Map<string, string>();

    // Recursive function to build maps
    function buildMaps(node: TreeNode, parentValue?: string) {
      if (parentValue) {
        parentMap.set(node.value, parentValue);
      }

      if (node.children?.length) {
        const childValues = node.children.map((child) => child.value);

        childrenMap.set(node.value, new Set(childValues));

        for (const child of node.children) {
          buildMaps(child, node.value);
        }
      }
    }

    for (const option of options) {
      buildMaps(option);
    }

    return { childrenMap, parentMap };
  }, [options]);

  const params = new URLSearchParams(searchParams?.toString());
  const currentValues = params.getAll(attribute);

  // Toggle value
  const valueSet = new Set(currentValues);

  if (currentValues.includes(value)) {
    valueSet.delete(value);
  } else {
    valueSet.add(value);
  }

  // Handle clicking a child when any ancestor is active
  let currentValue = value;
  let handled = false;

  while (currentValue && parentMap.has(currentValue) && !handled) {
    const parentValue = parentMap.get(currentValue)!;
    const siblings = childrenMap.get(parentValue) || new Set();

    if (valueSet.has(parentValue)) {
      // Remove the parent
      valueSet.delete(parentValue);

      // Add all siblings except the clicked child
      for (const sibling of siblings) {
        if (sibling !== value) {
          valueSet.add(sibling);
        }
      }

      // Remove the clicked child to match expected behavior
      valueSet.delete(value);
      handled = true; // Stop after handling the closest active ancestor
    }

    currentValue = parentValue; // Move up to check next ancestor
  }

  // Replace children with parent if all siblings active
  for (const [parentValue, children] of childrenMap) {
    const allChildrenActive = [...children].every((child) => valueSet.has(child));

    if (allChildrenActive) {
      for (const child of children) {
        valueSet.delete(child);
      }

      valueSet.add(parentValue);
    }
  }

  // Remove children if parent is active (recursively)
  function removeDescendants(currentValue: string) {
    const children = childrenMap.get(currentValue) || new Set();

    for (const child of children) {
      valueSet.delete(child);
      removeDescendants(child); // Recursively remove children of children
    }
  }

  for (const [parentValue, children] of childrenMap) {
    if (valueSet.has(parentValue)) {
      for (const child of children) {
        valueSet.delete(child);
        removeDescendants(child);
      }
    }
  }

  // Build final URL
  params.delete(attribute);
  params.delete("p");

  for (const v of valueSet) {
    params.append(attribute, v);
  }

  return { link: `${pathname}?${params?.toString()}` };
}
