import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { ResolutionFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

const RESOLUTION_LIST_QUERY = graphql(`
  query GetResolutionListConfig($url: URL) {
    route(url: $url) {
      ... on ResolutionList {
        defaultPageSize
        leadText
        url
        title
        rssUrl
        filters {
          __typename
          attribute
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const RESOLUTION_SEARCH_QUERY = graphql(`
  query GetResolutionList(
    $filter: ResolutionFilterInput
    $sort: ResolutionSortInput
    $pageSize: Int
    $currentPage: Int
  ) {
    resolutionSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        id
        title
        fileCount
        issueDate
        publicationDate
        url
        categories {
          title
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
          placeholder
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: RESOLUTION_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "ResolutionList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<FilterInputParams<ResolutionFilterInput> & { p?: string }>;
}) {
  const { data: resolutionList } = await query({
    query: RESOLUTION_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(resolutionList.route?.__typename === "ResolutionList");

  const { title, leadText, breadcrumbs, filters: defaultFilters, rssUrl, url, defaultPageSize } = resolutionList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<ResolutionFilterInput>({
    params,
    filters: defaultFilters,
  });

  const { data: resolutionSearch } = await query({
    query: RESOLUTION_SEARCH_QUERY,
    variables: {
      pageSize,
      currentPage,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = resolutionSearch?.resolutionSearch ?? {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Délibérations" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les délibérations" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />
          </div>

          {items.length > 0 && (
            <ol className={styles.grid}>
              {items.map((resolution) => (
                <li key={resolution.id}>
                  <Item resolution={resolution} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
