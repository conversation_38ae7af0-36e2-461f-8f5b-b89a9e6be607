import type { Marker } from "./types";

export type DenseMarker = Marker<{ density_count: number; point_ids: string[] }>;

// TODO: Add tests
// eslint-disable-next-line @typescript-eslint/no-explicit-any -- Unknown marker data
export function consolidateMarkers(markers: Marker<any>[]): (<PERSON><PERSON><any> | DenseMarker)[] {
  const grouped = Object.groupBy(markers, (m) => m.coordinates.join(":"));

  return Object.entries(grouped).map(([coordinates, points]) => {
    return points?.length === 1
      ? points[0]
      : {
          id: "dense-" + coordinates,
          coordinates: points![0].coordinates,
          data: {
            point_ids: points!.map((p) => p.id),
            density_count: points!.length,
          },
        };
  });
}
