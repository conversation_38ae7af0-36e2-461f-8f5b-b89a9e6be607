@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.quote {
  display: flex;
  flex-direction: column;
  gap: $space-7;
  padding: $space-7;
  margin-block: $space-5;
  container: block-quote / inline-size;
  color: $color-black;
  overflow-wrap: break-word;

  @include breakpoint(medium up) {
    padding: $space-8;
  }

  @include breakpoint(large up) {
    margin-block: $space-6;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: $space-7;
  width: 100%;
  padding-top: $space-9;
  padding-left: 0;
  border-top: 4px solid $color-primary-500;
  border-left: none;

  @include breakpoint(large up) {
    @container (min-width: 400px) {
      padding-top: 0;
      padding-left: $space-11;
      border-top: none;
      border-left: 4px solid $color-primary-500;
    }
  }
}

.content {
  :global(.block-paragraph) {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1.6;

    @include breakpoint(medium up) {
      @container (min-width: 400px) {
        font-size: 3.2rem;
      }
    }
  }
}

.icon {
  width: $space-11;
  fill: $color-primary-500;

  @include breakpoint(medium up) {
    @container (min-width: 400px) {
      width: $space-14;
    }
  }
}

.author {
  font-size: 1.6rem;
  font-weight: 700;
  color: $color-primary-500;

  @include breakpoint(large up) {
    font-size: 1.8rem;
  }

  &::before {
    display: block;
    width: $space-5;
    height: 1px;
    margin-bottom: $space-3;
    content: "";
    background-color: $color-neutral-300;
  }
}
