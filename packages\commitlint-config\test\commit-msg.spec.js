import lint from "@commitlint/lint";
import config from "../commitlint.config.js";
import { test, describe } from "vitest";
import { expect } from "vitest";

/** @param {string} message */
const expectLint = async (message) =>
  expect(
    await lint(message, config.rules, {
      parserOpts: config.parserPreset.parserOpts,
    }),
  );

describe("commit-msg", () => {
  test("valid commit messages", async () => {
    (await expectLint("[TASK] Update header")).toHaveProperty("valid", true);
    (
      await expectLint("[BUGFIX] #12345 Logo not displayed properly on mobile")
    ).toHaveProperty("valid", true);
  });
});

describe("commit-msg", () => {
  test("invalid commit messages", async () => {
    (await expectLint("[TASK] update")).toHaveProperty("valid", false);
    (await expectLint("feat: accordion block")).toHaveProperty("valid", false);
    (await expectLint("Select filter")).toHaveProperty("valid", false);
    (await expectLint("[FEAT] pagination")).toHaveProperty("valid", false);
    (await expectLint("[FIX] - #289160 - fix module Annuaire")).toHaveProperty(
      "valid",
      false,
    );
  });
});
