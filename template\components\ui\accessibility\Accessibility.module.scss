@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.accessibilityList {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  color: $color-neutral-500;
  cursor: help;
  background: $color-neutral-100;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;

  &.size-sm {
    @include size(40px);
  }

  &.size-md {
    @include size(45px);
  }

  &.supported {
    color: $color-white;
    background-color: $color-primary-500;
    border: 1px solid $color-primary-500;
  }

  &.notSupported {
    color: $color-white;
    background-color: $color-negative-400;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0NSIgaGVpZ2h0PSI0NSIgZmlsbD0ibm9uZSI+PHBhdGggc3Ryb2tlPSIjZmZmIiBkPSJtMSAxIDQzIDQzbTAtNDNMMSA0NCIvPjwvc3ZnPg==");
    background-position: 50% 50%;
    border: none;
  }

  svg {
    @include size(27px);
  }
}
