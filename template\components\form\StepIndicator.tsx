import Stepper from "@/components/ui/stepper/Stepper";
import { FormStep } from "@/generated/graphql/graphql";
import styles from "./StepIndicator.module.scss";

interface StepIndicatorProps {
  id?: string;
  steps: FormStep[];
  currentStep: number;
  totalSteps: number;
}

export default function StepIndicator({ id, steps, currentStep, totalSteps }: StepIndicatorProps) {
  const isRecapStep = currentStep >= steps.length;
  const step = isRecapStep ? null : steps[Math.max(0, Math.min(currentStep, steps.length - 1))];

  const nextStep = isRecapStep
    ? null
    : (currentStep === steps.length - 1
      ? { title: "Récapitulatif et envoi" }
      : (steps[currentStep + 1] ?? null));

  const currentStepTitle = isRecapStep ? "Récapitulatif et envoi" : step?.title || "";
  const stepNumber = currentStep + 1;

  return (
    <div className={styles.stepIndicator}>
      <p id={id} className={styles.title} aria-atomic="true" aria-live="polite">
        <span className={styles.currentStep}>
          Étape {stepNumber} sur {totalSteps}
        </span>
        {currentStepTitle}
      </p>

      <Stepper current={currentStep} total={totalSteps} />

      {nextStep && (
        <p className={styles.nextStepInfo}>
          <strong>Étape suivante :</strong> {nextStep.title}
        </p>
      )}
    </div>
  );
}
