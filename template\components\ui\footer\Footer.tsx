import SocialLink from "@/components/ui/social-link/SocialLink";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import styles from "./Footer.module.scss";

const FooterTop = dynamic(() => import("@/components/ui/footer/FooterTop"));
const Button = dynamic(() => import("@/components/ui/button/Button"));

const FOOTER_QUERY = graphql(`
  query GetFooter {
    siteConfig {
      footer {
        top {
          action {
            text
            url
            class
            icon {
              src
              type
            }
            rel
            target
          }
          description
          title
        }
        logoLight {
          url
          width
          height
        }
        title
        buttons {
          class
          icon {
            src
            type
          }
          rel
          target
          text
          url
        }
        clientInfo {
          address
          openingHours
          tel
        }
        quickAccess1 {
          class
          icon {
            src
            type
          }
          rel
          target
          text
          url
        }
        quickAccess2 {
          class
          icon {
            src
            type
          }
          rel
          target
          text
          url
        }
      }
      socialLinks {
        network
        text
        url
      }
    }
  }
`);

export default async function Footer() {
  const { data } = await query({ query: FOOTER_QUERY, errorPolicy: "all" });

  const { footer, socialLinks } = data.siteConfig ?? {};

  if (!footer) {
    return null;
  }

  const { logoLight, title, buttons = [], quickAccess1 = [], quickAccess2 = [], clientInfo, top } = footer;
  // TODO split and lazy load component
  return (
    <footer className={styles.footer}>
      {top && <FooterTop top={top} />}

      <div className={styles.wrapper}>
        <div className={styles.container}>
          {/* Left Section: Logo & Client Info */}
          <div className={styles.footerLeft}>
            {logoLight && (
              <Link href="/" className={styles.logo}>
                <Image
                  className={styles.image}
                  src={logoLight.url}
                  width={logoLight.width}
                  height={logoLight.height}
                  alt={title ?? ""}
                />
              </Link>
            )}
            <div className={styles.details}>
              <h3 className={styles.siteName}>{title}</h3>
              {clientInfo && (
                <address className={styles.address}>
                  <p>{clientInfo.address}</p>
                  <p>{clientInfo.openingHours}</p>
                  <p>{clientInfo.tel}</p>
                </address>
              )}
              <div className={styles.socials}>
                {socialLinks?.map((link, index) => (
                  <SocialLink key={index} {...link} />
                ))}
              </div>
            </div>
            {/* Action Buttons */}
            {buttons && buttons.length > 0 && (
              <ul className={styles.buttons}>
                {buttons.map(
                  (button, index) =>
                    button?.url && (
                      <li key={index}>
                        <Button variant="outlined" size="md" startIcon="far fa-arrow-to-bottom" asChild>
                          <Link href={button.url} target={button.target ?? undefined} rel={button.rel ?? undefined}>
                            {button.text}
                          </Link>
                        </Button>
                      </li>
                    )
                )}
              </ul>
            )}
          </div>

          {/* Right Section: Navigation Links */}
          <nav className={styles.nav} aria-label="Footer Navigation">
            {quickAccess1 && quickAccess1.length > 0 && (
              <div className={styles.leftList}>
                {quickAccess1.map(
                  (link, index) =>
                    link?.url && (
                      <Link key={index} href={link.url} target={link.target ?? undefined} rel={link.rel ?? undefined}>
                        {link.text}
                      </Link>
                    )
                )}
              </div>
            )}
            {quickAccess2 && quickAccess2.length > 0 && (
              <div className={styles.listRight}>
                {quickAccess2.map(
                  (link, index) =>
                    link?.url && (
                      <Link key={index} href={link.url} target={link.target ?? undefined} rel={link.rel ?? undefined}>
                        {link.text}
                      </Link>
                    )
                )}
              </div>
            )}
          </nav>
        </div>

        {/* Bottom Section */}
        <div className={styles.footerInfo}>
          <div className={styles.cover}>
            <div className={styles.separator}></div>
            <div className={clsx(styles.container, styles.bottom)}>
              <div className={styles.infoLeft}>
                <span>Accessibilité : Conforme</span>
                <span>Écoconception RGESN : Conforme</span>
              </div>
              <div className={styles.infoRight}>
                <a href="#top" className={styles.infoTop} aria-label="Retour en haut de la page">
                  <i className="far fa-arrow-up" aria-hidden="true"></i>
                  <p>Haut de page</p>
                </a>
                <a href="https://stratis.fr" target="_blank" rel="noopener noreferrer" className={styles.brand}>
                  STRATIS
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
