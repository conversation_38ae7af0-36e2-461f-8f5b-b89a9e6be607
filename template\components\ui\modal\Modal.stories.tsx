import Textfield from "@/components/ui/textfield/Textfield";
import { Dialog } from "@radix-ui/react-dialog";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Modal from "./Modal";

const meta: Meta<typeof Modal> = {
  title: "Components/Modal",
  component: Modal,
  decorators: [
    (Story) => (
      <Dialog defaultOpen={true}>
        <Story />
      </Dialog>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Modal>;

export const Default: Story = {
  render: () => (
    <Modal title="Fenêtre modale d'exemple">
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla feugiat purus ac aliquet tincidunt. Praesent a
        ante quis metus consectetur consectetur. Quisque et est vel ipsum posuere placerat. Orci varius natoque
        penatibus et magnis dis parturient montes, nascetur ridiculus mus. Pellentesque porta tincidunt mattis. Mauris
        non varius neque. Nam porttitor lectus nec lectus interdum dapibus. Maecenas viverra massa nulla, et euismod
        urna congue vel. Vivamus urna nunc, iaculis vitae libero eu, condimentum iaculis purus.
      </p>
    </Modal>
  ),
};

export function Fullscreen() {
  return (
    <Modal size="lg" fullscreen title="Fenêtre modale d'exemple en plein écran">
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla feugiat purus ac aliquet tincidunt. Praesent a
        ante quis metus consectetur consectetur. Quisque et est vel ipsum posuere placerat. Orci varius natoque
        penatibus et magnis dis parturient montes, nascetur ridiculus mus. Pellentesque porta tincidunt mattis. Mauris
        non varius neque. Nam porttitor lectus nec lectus interdum dapibus. Maecenas viverra massa nulla, et euismod
        urna congue vel. Vivamus urna nunc, iaculis vitae libero eu, condimentum iaculis purus.FullscreenModal
      </p>
    </Modal>
  );
}

/**
 * Focus the content of the modal if any.
 */
export function FocusContent() {
  return (
    <Modal focusStrategy="content" title="Fenêtre modale de recherche">
      <Textfield type="search" aria-label="Rechercher une actualité" placeholder="Rechercher une actualité..." />
    </Modal>
  );
}
