"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { EmailField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type EmailProps = Omit<EmailField, "__typename">;

const emailPattern = `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$`;

export default function Email({
  autocomplete,
  condition,
  defaultValue,
  description,
  label,
  name,
  pattern,
  placeholder,
  required,
  columnSpan,
}: EmailProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name);
  const inputId = useId();
  const errorId = useId();

  const validationPattern = pattern ?? emailPattern;

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <Textfield
          id={inputId}
          type="email"
          inputMode="email"
          startIcon="far fa-envelope"
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          pattern={validationPattern}
          placeholder={placeholder ?? undefined}
          required={required}
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
          {...register(name, {
            required,
            pattern: {
              value: new RegExp(validationPattern),
              message: "Please enter a valid email address",
            },
          })}
        />

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
