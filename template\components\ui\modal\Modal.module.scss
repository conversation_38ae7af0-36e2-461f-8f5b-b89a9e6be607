@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.overlay {
  position: fixed;
  inset: 0;
  z-index: $layer-modal - 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 20%);
}

.modalContainer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: $layer-modal;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  padding: 16px;

  @include breakpoint(medium up) {
    padding: 24px;
  }
}

.modal {
  display: flex;
  flex-direction: column;
  width: auto;
  max-width: 100%;
  background: $color-white;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);

  &.fullscreen {
    width: 100%;
    height: 100%;
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  margin-bottom: 8px;
}

.headerAction {
  display: flex;
  align-items: center;
  margin-right: auto;
}

.title {
  margin-bottom: 24px;
  font-weight: 700;
  line-height: 110%;
  color: $color-neutral-700;

  @include breakpoint(medium up) {
    margin-bottom: 32px;
  }
}

.closeButton {
  @include size(48px);

  margin-left: auto;

  i {
    font-size: 2rem;
    pointer-events: none;
  }
}

.content {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow-y: auto;

  .modal.size-sm & {
    padding: 0 16px 24px;

    @include breakpoint(medium up) {
      padding: 0 32px 32px;
    }
  }

  .modal.size-md & {
    padding: 0 16px 24px;

    @include breakpoint(medium up) {
      padding: 0 32px 32px;
    }
  }

  .modal.size-lg & {
    padding: 0 16px 48px;

    @include breakpoint(medium up) {
      padding: 0 56px 56px;
    }
  }
}

// Animation
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
