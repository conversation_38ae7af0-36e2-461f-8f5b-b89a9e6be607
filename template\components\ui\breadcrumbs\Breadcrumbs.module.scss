@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.breadcrumbs {
  width: 100%;
  padding: 12px 16px;
  font-size: 1.4rem;
  line-height: 130%;
  color: $color-neutral-700;
  background-color: $color-primary-100;

  @include breakpoint(medium up) {
    padding: 16px 24px;
  }

  @include breakpoint(large up) {
    padding: 24px 32px;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.crumb {
  display: flex;
  align-items: center;
  min-height: 32px;
}

// Home page crumb
.home {
  padding: 8px 12px;

  i {
    font-size: 1.4rem;
  }

  &:hover {
    color: $color-primary-500;
  }
}

.separator {
  @include size(32px);

  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: $color-neutral-500;
}

// Link crumb
.link {
  padding: 8px 12px;

  &:hover {
    color: $color-primary-500;
    text-decoration: underline;
  }
}

// Latest crumb
.current {
  padding: 8px 12px;
  font-weight: 700;
  color: $color-primary-500;
}

// Expand the list
.expandButton {
  @include size(32px);

  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: $color-neutral-500;
  cursor: pointer;
}
