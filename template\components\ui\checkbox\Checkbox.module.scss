@use "sass:math";
@use "@/styles/lib/variables.scss" as *;

.input {
  width: 24px;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  color: $color-white;
  text-align: center;
  appearance: none;
  cursor: pointer;
  border: 1px solid $color-neutral-500;
  border-radius: 4px;
  transition:
    color 200ms ease-in-out,
    background-color 200ms ease-in-out,
    border-color 200ms ease-in-out;

  &:checked {
    background-color: $color-primary-500;
    border-color: $color-primary-400;
  }

  &.size-small {
    width: 16px;
    height: 16px;
    line-height: 16px;
  }

  &.error {
    border-color: $color-danger;
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}
