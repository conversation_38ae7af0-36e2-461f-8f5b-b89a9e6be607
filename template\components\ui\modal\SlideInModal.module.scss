@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.overlay {
  position: fixed;
  inset: 0;
  z-index: $layer-modal - 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 20%);
}

.modalContainer {
  position: fixed;
  top: 0;
  z-index: $layer-modal;
  display: flex;
  height: 100%;
  padding: 16px;
  transition: transform 0.3s ease-in-out;

  .fas.fa-xmark {
    font-size: 1.6rem !important;
  }

  &.active {
    right: 0;
    animation: slide-in-left 0.3s ease-in-out;
  }
}

.modal {
  top: 0;
  left: 0;
  width: auto;
  max-width: 100%;
  height: auto;
  max-height: 100vh;
  background: $color-white;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px;
  margin-bottom: 8px;
}

.headerAction {
  display: flex;
  align-items: center;
  margin-right: auto;
}

.title {
  margin-bottom: 24px;
  font-weight: 700;
  line-height: 110%;
  color: $color-neutral-700;

  @include breakpoint(medium up) {
    margin-bottom: 32px;
  }
}

.closeButton {
  @include size(48px);

  margin-left: auto;

  i {
    font-size: 2rem;
    pointer-events: none;
  }
}

.content {
  flex: 1;
  width: 100%;
  overflow-y: auto;

  .modal.size-sm & {
    padding: 0 16px 24px;

    @include breakpoint(medium up) {
      padding: 0 32px 32px;
    }
  }

  .modal.size-md & {
    padding: 0 16px 24px;

    @include breakpoint(medium up) {
      padding: 0 32px 32px;
    }
  }

  .modal.size-lg & {
    padding: 0 16px 48px;

    @include breakpoint(medium up) {
      padding: 0 56px 56px;
    }
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}
