"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ipContent,
  TooltipContentProps,
  TooltipPortal,
  TooltipProvider,
  <PERSON>ltip as TooltipRoot,
  TooltipTrigger,
} from "@radix-ui/react-tooltip";
import clsx from "clsx";
import React, { useState } from "react";
import styles from "./Tooltip.module.scss";

type TooltipSide = NonNullable<TooltipContentProps["side"]>;
type TooltipAlign = NonNullable<TooltipContentProps["align"]>;
type Placement = "top" | "bottom" | "left" | "right" | "top-left" | "top-right" | "bottom-left" | "bottom-right";

interface TooltipProps {
  isOpen?: boolean;
  content?: string;
  placement?: Placement;
  variant?: "primary" | "secondary" | "tertiary" | "neutral";
  size?: "md" | "lg";
  className?: string;
  trigger?: "focus";
  enabled?: boolean;
}

function parsePlacement(placement: Placement): { side: TooltipSide; align: TooltipAlign } {
  switch (placement) {
    case "top-left": {
      return { side: "top", align: "start" };
    }
    case "top-right": {
      return { side: "top", align: "end" };
    }
    case "bottom-left": {
      return { side: "bottom", align: "start" };
    }
    case "bottom-right": {
      return { side: "bottom", align: "end" };
    }
    case "left": {
      return { side: "left", align: "center" };
    }
    case "right": {
      return { side: "right", align: "center" };
    }
    case "top": {
      return { side: "top", align: "center" };
    }
    case "bottom": {
      return { side: "bottom", align: "center" };
    }
    default: {
      return { side: "top", align: "center" };
    }
  }
}

/**
 * Assign a tooltip to an element. The direct child acts as the trigger.
 */
export default function Tooltip({
  children,
  isOpen: controlledOpen,
  placement = "top",
  variant = "neutral",
  size = "md",
  className,
  content,
  trigger,
  enabled = true,
  ...restProps
}: React.PropsWithChildren<TooltipProps>) {
  const [open, setOpen] = useState(false);

  // Lend the open controller to a parent component
  const actualOpen = controlledOpen === undefined ? open : controlledOpen;

  if (!content || !enabled) {
    return <>{children}</>;
  }

  const { side, align } = parsePlacement(placement);

  /**
   * If the trigger is set to focus, the tooltip will be shown only
   * when the element is focused.
   */
  const preventPointerEvents = (event: React.PointerEvent<HTMLElement>) => {
    if (trigger === "focus") {
      event.preventDefault();
    }
  };

  return (
    <TooltipProvider delayDuration={0}>
      <TooltipRoot open={actualOpen} onOpenChange={setOpen}>
        <TooltipTrigger
          asChild
          onPointerMove={preventPointerEvents}
          onPointerLeave={preventPointerEvents}
          {...restProps}
        >
          {children}
        </TooltipTrigger>
        <TooltipPortal>
          <TooltipContent
            side={side}
            align={align}
            sideOffset={5}
            tabIndex={-1}
            className={clsx(
              styles.tooltipContent,
              styles[`placement-${placement}`],
              styles[`variant-${variant}`],
              styles[`size-${size}`],
              className
            )}
          >
            {content}
            <TooltipArrow className={styles.tooltipArrow} asChild={size === "lg" || variant === "neutral"}>
              <span>
                {variant === "neutral" && size === "lg" && (
                  <svg width="18" height="13" viewBox="0 0 18 13">
                    <path d="M0.5 0H17.5L0.5 12V0Z" fill="none" stroke="#707070" strokeWidth="1" />
                    <path d="M0.5 0H16.5L1 11V0Z" fill="#FFFFFF" stroke="none" strokeWidth="1" />
                  </svg>
                )}
                {variant != "neutral" && size === "lg" && (
                  <svg width="16" height="13" viewBox="0 0 16 13">
                    <path d="M0 0H16L0 13V0Z" />
                  </svg>
                )}
                {variant === "neutral" && size === "md" && (
                  <svg width="11" height="8" viewBox="0 0 11 8">
                    <path d="M0.5 0H10.5L5.5 8L0.5 0Z" fill="#FFFFFF" stroke="none" strokeWidth="1" />
                    <path
                      d="M0.5 0H10.5L5.5 8L0.5 0Z"
                      fill="none"
                      stroke="#707070"
                      strokeWidth="0.7"
                      transform="translate(0, -0.5)"
                    />
                  </svg>
                )}
              </span>
            </TooltipArrow>
          </TooltipContent>
        </TooltipPortal>
      </TooltipRoot>
    </TooltipProvider>
  );
}
