{"[javascript][typescript][typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}}, "[css][scss]": {"editor.defaultFormatter": "stylelint.vscode-stylelint", "editor.codeActionsOnSave": {"source.fixAll.stylelint": "always"}}, "eslint.validate": ["javascript", "typescript", "typescriptreact"], "stylelint.validate": ["css", "scss"], "cSpell.language": "en,fr", "cSpell.words": ["citeopolis", "Citéopolis", "xmark"], "cSpell.ignorePaths": ["package-lock.json", "pnpm-lock.yaml", "node_modules", "vscode-extension", ".git/{info,lfs,logs,refs,objects}/**", ".git/{index,*refs,*HEAD}", ".vscode", ".vscode-insiders"]}