import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import styles from "./HeadingContact.module.scss";

const Tag = dynamic(() => import("@/components/ui/tag/Tag"));

interface HeadingContactProps {
  title: string;
  surtitle?: string | null;
  roles?: string[];
  rolesRoleDescription?: string;
  tags?: { url?: string; text: string }[];
  tagsRoleDescription?: string;
  publicationDate?: string;
  modifiedDate?: string;
  imageSrc?: string;
}

/**
 * The heading used in "person" directory detailed views.
 */
export default function HeadingContact({
  surtitle,
  title,
  roles,
  rolesRoleDescription,
  tags,
  tagsRoleDescription,
  publicationDate,
  modifiedDate,
  imageSrc,
}: HeadingContactProps) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.top}>
          <div className={styles.content}>
            <h1 className={styles.title}>
              {surtitle && (
                <span className={styles.surtitle}>
                  {surtitle}
                  <span className="sr-only">:</span>
                </span>
              )}
              {title}
            </h1>
            {roles && roles.length > 0 && (
              <ul className={styles.roles} aria-roledescription={rolesRoleDescription}>
                {roles.map((role, index) => (
                  <li key={index}>{role}</li>
                ))}
              </ul>
            )}
          </div>
          {imageSrc && (
            <Image
              className={styles.image}
              src={imageSrc}
              width={280}
              height={280}
              alt=""
              sizes="(max-width: 1301px) 176px, 280px"
            />
          )}
        </div>
        {tags && tags.length > 0 && (
          <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
            {tags.map((tag, index) => (
              <li key={index}>
                {tag.url ? (
                  <Tag variant="primary" asChild>
                    <Link href={tag.url}>{tag.text}</Link>
                  </Tag>
                ) : (
                  <Tag variant="primary">{tag.text}</Tag>
                )}
              </li>
            ))}
          </ul>
        )}
        {(publicationDate || modifiedDate) && (
          <p className={styles.dates}>
            {publicationDate && (
              <>
                <span>Publié le </span>
                <time dateTime={publicationDate}>
                  {new Date(publicationDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
            {modifiedDate && (
              <>
                <span>Mis à jour le </span>
                <time dateTime={modifiedDate}>
                  {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
          </p>
        )}
      </div>
    </header>
  );
}
