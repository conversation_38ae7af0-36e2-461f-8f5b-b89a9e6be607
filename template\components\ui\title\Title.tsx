import clsx from "clsx";
import * as React from "react";
import Hx from "./Hx";
import styles from "./Title.module.scss";

interface TitleProps {
  level?: number;
}

export default function Title({
  level,
  className,
  children,
  ...restProps
}: React.PropsWithChildren<TitleProps & React.HTMLAttributes<HTMLHeadingElement>>) {
  return (
    <Hx level={level} className={clsx(styles.heading, styles[`heading-${level}`], className)} {...restProps}>
      {children}
    </Hx>
  );
}
