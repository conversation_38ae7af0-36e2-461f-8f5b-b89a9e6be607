"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_ui_alert_AlertRenderer_tsx"],{

/***/ "(app-pages-browser)/./components/ui/alert/AlertRenderer.tsx":
/*!***********************************************!*\
  !*** ./components/ui/alert/AlertRenderer.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlertRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/api/app-dynamic.js\");\n\n\nconst PopUpAlert = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_ui_alert_PopUpAlert_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./PopUpAlert */ \"(app-pages-browser)/./components/ui/alert/PopUpAlert.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\AlertRenderer.tsx -> \" + \"./PopUpAlert\"\n        ]\n    }\n});\n_c1 = PopUpAlert;\nconst StickyNote = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_c2 = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_ui_alert_StickyNote_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./StickyNote */ \"(app-pages-browser)/./components/ui/alert/StickyNote.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\AlertRenderer.tsx -> \" + \"./StickyNote\"\n        ]\n    }\n});\n_c3 = StickyNote;\nconst componentMap = {\n    POPUP: PopUpAlert,\n    STICKY: StickyNote\n};\n/**\r\n * Render the alert based on the variant type (POPUP or STICKY).\r\n */ function AlertRenderer(param) {\n    let { entries } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: entries.map((alert)=>{\n            const Component = alert.variant && componentMap[alert.variant];\n            return Component ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                alert: alert\n            }, alert.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\AlertRenderer.tsx\",\n                lineNumber: 33,\n                columnNumber: 28\n            }, this) : null;\n        })\n    }, void 0, false);\n}\n_c4 = AlertRenderer;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"PopUpAlert$dynamic\");\n$RefreshReg$(_c1, \"PopUpAlert\");\n$RefreshReg$(_c2, \"StickyNote$dynamic\");\n$RefreshReg$(_c3, \"StickyNote\");\n$RefreshReg$(_c4, \"AlertRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/alert/AlertRenderer.tsx\n"));

/***/ })

}]);