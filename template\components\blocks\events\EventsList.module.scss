@use "@/styles/lib/mixins.scss" as *;

.eventList {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  @include breakpoint(medium up) {
    grid-template-columns: repeat(3, 1fr);
    gap: 40px 24px;
  }

  @include breakpoint(large up) {
    grid-template-columns: 1fr;
    gap: 48px 32px;

    @container (min-width: 720px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @container (min-width: 1216px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}
