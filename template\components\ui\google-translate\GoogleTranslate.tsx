"use client";

import Script from "next/script";
import styles from "./GoogleTranslate.module.scss";

interface GoogleTranslateProps {
  pageLanguage: string;
  includedLanguages: string[];
}

/**
 * Render a `GoogleTranslate` widget to translate the whole website.
 */
export default function GoogleTranslate({
  pageLanguage = "fr",
  includedLanguages = ["fr", "en"],
}: GoogleTranslateProps) {
  return (
    <>
      <Script id="google-translate-loader">{`
          window.googleTranslateElementInit = function () {
            new window.google.translate.TranslateElement({
              autoDisplay: false,
              pageLanguage: "${pageLanguage}",
              includedLanguages: "${includedLanguages.join(",")}",
              layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
            }, "google-translate-element");
          }
      `}</Script>
      <Script
        src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
        strategy="afterInteractive"
      />
      <div id="google-translate-element" className={styles.googleTranslate} />
    </>
  );
}
