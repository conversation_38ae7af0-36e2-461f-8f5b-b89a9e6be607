@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

$thumbnail-width: 74px;

.root {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 3px;
}

.button {
  position: relative;
  display: block;
  padding-left: calc($thumbnail-width + 12px);
  text-align: left;
  cursor: pointer;
  background: transparent;
  border: 0;
}

.thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  max-width: $thumbnail-width;
  object-fit: contain;
  object-position: top;
}

.title {
  font-size: 1.4rem;
  font-style: normal;
  font-weight: 400;
  line-height: 110%;
  color: $color-primary-500;
}

.address {
  font-size: 1.4rem;
  font-style: normal;
  line-height: 110%;
  color: $color-black;
}
