"use client";

import Collapsible from "@/components/ui/collapsible/Collapsible";
import { EmbedBlock, OEmbedResourceType } from "@/generated/graphql/graphql";
import formatSize from "@/utils/formatSize";
import clsx from "clsx";
import { useId } from "react";
import striptags from "striptags";
import styles from "./Embed.module.scss";

type EmbedProps = Omit<EmbedBlock, "__typename" | "innerBlocks">;

const processHTML = (htmlString: string, captionText?: string | null) => {
  if (!htmlString) return htmlString;

  let processedHTML = htmlString;

  processedHTML = processedHTML.replaceAll(/\s+frameborder="[^"]*"/gi, "");

  if (captionText) {
    processedHTML = processedHTML.replaceAll(/<iframe([^>]*?)>/gi, (match, attributes) => {
      if (attributes.toLowerCase().includes("title=")) {
        return match;
      }

      return `<iframe${attributes} title="${captionText.replaceAll('"', "&quot;")}">`;
    });
  }

  return processedHTML;
};

export default function Embed(props: EmbedProps) {
  const {
    allowResponsive,
    anchor,
    caption,
    className,
    html,
    responsive,
    transcription,
    type: contentType,
    videoDuration,
    videoFormats,
  } = props;

  const isVideo = contentType?.toUpperCase() === OEmbedResourceType.VIDEO;

  const showMetadata = isVideo && !!videoDuration && Array.isArray(videoFormats) && videoFormats.length > 0;

  const classList = clsx(
    "block-embed contained",
    styles.embed,
    allowResponsive && responsive && styles.responsive,
    className
  );
  return (
    <WithCaption
      caption={caption}
      id={anchor ?? undefined}
      className={classList}
      showMetadata={showMetadata}
      videoDuration={videoDuration}
      videoFormats={videoFormats}
    >
      <div
        className={clsx(styles.content, isVideo && styles.video)}
        dangerouslySetInnerHTML={{ __html: striptags(processHTML(html, caption), ["iframe"]) }}
      />
      {transcription && (
        <Collapsible
          className={styles.transcription}
          icon={<i className="far fa-subtitles" aria-hidden="true"></i>}
          label="Transcription"
          noHeading={true}
        >
          {transcription.split("\n").map((line, index) => (
            <p key={index}>{line}</p>
          ))}
        </Collapsible>
      )}
    </WithCaption>
  );
}

type WithCaptionProps = React.PropsWithChildren<
  React.HTMLAttributes<HTMLElement> & {
    caption?: string | null;
    className?: string;
    showMetadata?: boolean;
    videoDuration?: number | null;
    videoFormats?: EmbedBlock["videoFormats"];
  }
>;

function WithCaption(props: WithCaptionProps) {
  const { caption, children, showMetadata, videoDuration, videoFormats, ...restProps } = props;

  const captionId = useId();

  const renderMetadata = () => {
    if (!videoDuration || !videoFormats?.length) return null;

    const durationInMinutes = Math.round(videoDuration / 60);

    return (
      <span className={styles.meta}>
        Durée de la vidéo : {durationInMinutes}mn
        <br />
        Poids estimé : {videoFormats.map((f) => `${formatSize(f.weight)} en ${f.name}`).join(", ")}
      </span>
    );
  };

  if (!caption) {
    return <section {...restProps}>{children}</section>;
  }

  return (
    <figure role="figure" aria-labelledby={captionId} {...restProps}>
      <figcaption id={captionId} className={styles.caption}>
        {caption}
        {showMetadata && renderMetadata()}
      </figcaption>

      {children}
    </figure>
  );
}
