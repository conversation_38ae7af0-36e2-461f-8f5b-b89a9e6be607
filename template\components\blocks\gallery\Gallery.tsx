"use client";

import BaseGallery from "@/components/ui/gallery/Gallery";
import type { GalleryBlock, ImageBlock } from "@/generated/graphql/graphql";
import React from "react";
import flattenChildren from "react-keyed-flatten-children";
import striptags from "striptags";
import { SlideImage } from "yet-another-react-lightbox";

type GalleryProps = Omit<GalleryBlock, "__typename" | "innerBlocks">;

export default function Gallery({ columns = 3, children }: React.PropsWithChildren<GalleryProps>) {
  // Get direct children that are components
  // TODO: Make sure the children have the correct props
  const items = flattenChildren(children, 1, []).filter((child) => React.isValidElement<ImageBlock>(child));

  // Get the images from the children props
  // NOTE: Images without a valid src attribute are filtered out.
  const images = items.map((item) => item.props).filter(({ src }) => src);

  // Create lightbox slide entries from the images props
  const slides: SlideImage[] = images.map((image) => ({
    src: image.src,
    width: image.width ?? undefined,
    height: image.height ?? undefined,
    alt: image.alt ?? "",
    description: striptags(image.caption ?? ""),
  }));

  if (images.length === 0) {
    return;
  }

  return (
    <BaseGallery
      className="block-gallery contained"
      slides={slides}
      columns={columns}
      aria-label="Galerie photos"
      lightboxProps={{
        labels: {
          Previous: "Image précédente",
          Next: "Image suivante",
          "Photo gallery": "Galerie photos",
          "{index} of {total}": "{index} sur {total}",
          Slide: "Image",
          Lightbox: "Visionneuse d'images",
        },
      }}
    />
  );
}
