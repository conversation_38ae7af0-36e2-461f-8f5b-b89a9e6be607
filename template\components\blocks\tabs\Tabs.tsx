"use client";

import { Orientation, TabBlock, TabsBlock } from "@/generated/graphql/graphql";
import { List, Root } from "@radix-ui/react-tabs";
import clsx from "clsx";
import React, { useEffect, useId, useMemo, useRef } from "react";
import flattenChildren from "react-keyed-flatten-children";
import { useIsClient, useMediaQuery } from "usehooks-ts";
import styles from "./Tabs.module.scss";
import { TabsContext } from "./TabsContext";

type TabsProps = Partial<Omit<TabsBlock, "__typename" | "innerBlocks">>;

export default function Tabs({
  anchor,
  children,
  orientation = Orientation.HORIZONTAL,
}: React.PropsWithChildren<TabsProps>) {
  const isClient = useIsClient();
  const isTabletViewport = useMediaQuery("(max-width: 1301px)");
  const listRef = useRef<HTMLDivElement>(null);
  const viewMode = useMemo(() => (isTabletViewport ? "accordion" : "tabs"), [isTabletViewport]);
  const accordionName = useId();

  // Fetch items props to get the first child id
  const items = useMemo(
    () =>
      flattenChildren(children)
        .filter((child) => React.isValidElement<TabBlock>(child))
        .filter((child) => child.props.id),
    [children]
  );

  useEffect(() => {
    if (isTabletViewport || !listRef.current) return;

    const updateActiveTabIndicator = () => {
      const activeTrigger = listRef.current?.querySelector('[data-state="active"]');

      if (!activeTrigger) return;

      if (orientation.toLowerCase() === "horizontal") {
        const { width, left } = activeTrigger.getBoundingClientRect();
        const listLeft = listRef.current!.getBoundingClientRect().left;

        listRef.current!.style.setProperty("--tab-width", `${width}px`);
        listRef.current!.style.setProperty("--tab-left", `${left - listLeft}px`);
      } else {
        const { height, top } = activeTrigger.getBoundingClientRect();
        const listTop = listRef.current!.getBoundingClientRect().top;

        listRef.current!.style.setProperty("--tab-height", `${height}px`);
        listRef.current!.style.setProperty("--tab-top", `${top - listTop}px`);
      }
    };

    updateActiveTabIndicator();
    const observer = new MutationObserver(updateActiveTabIndicator);

    if (listRef.current) {
      observer.observe(listRef.current, { attributes: true, subtree: true });
    }

    return () => observer.disconnect();
  }, [items, orientation, isTabletViewport, isClient]);

  if (!isClient || items.length === 0) {
    return;
  }

  if (viewMode === "accordion") {
    return (
      <TabsContext value={{ as: "accordion-item", name: accordionName }}>
        <div id={anchor ?? undefined} className={clsx("block-tabs contained", styles.accordion)}>
          {children}
        </div>
      </TabsContext>
    );
  }

  return (
    <Root
      className={clsx("block-tabs contained", styles.tabs)}
      defaultValue={items[0].props.id}
      orientation={orientation.toLowerCase() as "horizontal" | "vertical"}
    >
      <List className={styles.list} ref={listRef}>
        <TabsContext value={{ as: "tab" }}>{children}</TabsContext>
      </List>
      <TabsContext value={{ as: "tabpanel" }}>{children}</TabsContext>
    </Root>
  );
}
