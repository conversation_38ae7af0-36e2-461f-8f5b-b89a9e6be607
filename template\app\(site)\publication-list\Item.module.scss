@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.publicationItem {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include breakpoint(medium up) {
    gap: 32px;
  }
}

.imageWrapper {
  order: -1;
  max-width: 126px;
  aspect-ratio: 1;

  @include breakpoint(medium up) {
    max-width: 250px;
  }
}

.image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: left;
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.subtitle {
  margin-top: 6px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 150%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-top: 8px;
  }
}

.infos {
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.fileCount {
  font-weight: 700;
}
