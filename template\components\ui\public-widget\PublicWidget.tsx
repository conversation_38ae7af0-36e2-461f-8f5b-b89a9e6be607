import Accessibility from "@/components/ui/accessibility/Accessibility";
import { type EventAccessibility } from "@/generated/graphql/graphql";
import styles from "./PublicWidget.module.scss";

interface PublicWidgetProps {
  audience?: string[];
  accessibility?: EventAccessibility;
}

export default function PublicWidget({ audience, accessibility }: PublicWidgetProps) {
  return (
    <div className={styles.publicWidget}>
      <h2 className={styles.title}>
        <i className="far fa-people-group" aria-hidden="true" />
        Public
      </h2>
      <div className={styles.content}>
        <ul className={styles.audienceList}>
          {audience && audience.length > 0 ? (
            audience.map((item, index) => <li key={index}>{item}</li>)
          ) : (
            <li>Tout public</li>
          )}
        </ul>
        <Accessibility accessibility={accessibility} />
      </div>
    </div>
  );
}
