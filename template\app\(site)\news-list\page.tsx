import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Button from "@/components/ui/button/Button";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { NewsFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import Link from "next/link";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

const NEWS_LIST_QUERY = graphql(`
  query GetNewsListConfig($url: URL) {
    route(url: $url) {
      ... on NewsList {
        defaultPageSize
        leadText
        filters {
          __typename
          attribute
        }
        proposeUrl
        rssUrl
        title
        url
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const NEWS_SEARCH_QUERY = graphql(`
  query GetNewsList($filter: NewsFilterInput, $sort: NewsSortInput, $pageSize: Int, $currentPage: Int) {
    newsSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        id
        leadText
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        slug
        status
        title
        url
        categories {
          description
          relativeUrl
          title
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
          placeholder
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: NEWS_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "NewsList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<FilterInputParams<NewsFilterInput> & { p?: string }>;
}) {
  const { data: newsList } = await query({
    query: NEWS_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(newsList.route?.__typename === "NewsList");

  const {
    title,
    leadText,
    breadcrumbs,
    filters: defaultFilters,
    rssUrl,
    proposeUrl,
    url,
    defaultPageSize,
  } = newsList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<NewsFilterInput>({
    params,
    filters: defaultFilters,
  });

  const { data: newsSearch } = await query({
    query: NEWS_SEARCH_QUERY,
    variables: {
      pageSize,
      currentPage,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = newsSearch?.newsSearch || {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Actualités" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les actualités" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />

            {proposeUrl && (
              <Button asChild variant="outlined" startIcon="far fa-lightbulb-on">
                <Link href={proposeUrl}>Suggérer une actualité</Link>
              </Button>
            )}
          </div>

          {items.length > 0 && (
            <ol className={styles.grid}>
              {items.map((news) => (
                <li key={news.id}>
                  <Item news={news} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
