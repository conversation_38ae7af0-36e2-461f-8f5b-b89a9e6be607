import { type ButtonsBlock, Orientation } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./Buttons.module.scss";

type ButtonsProps = Omit<ButtonsBlock, "__typename" | "innerBlocks">;

export default function Buttons({ children, justifyContent, orientation }: React.PropsWithChildren<ButtonsProps>) {
  const orientationClass = orientation === Orientation.VERTICAL ? "vertical" : "horizontal";

  return (
    <section
      className={clsx("block-buttons contained", styles.buttons, styles[orientationClass])}
      style={{ justifyContent: justifyContent ?? undefined }}
    >
      {children}
    </section>
  );
}
