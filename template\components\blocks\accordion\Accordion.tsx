"use client";

import type { AccordionBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import React, { useId, useMemo } from "react";
import styles from "./Accordion.module.scss";
import { AccordionContext } from "./AccordionContext";

type AccordionProps = Partial<Omit<AccordionBlock, "__typename" | "innerBlocks">>;

// TODO: Create the primitive ourselves, so the item handles its open state and we can avoid these shenanigans
export default function Accordion({ children, multiple = false }: React.PropsWithChildren<AccordionProps>) {
  const name = useId();
  const contextValue = useMemo(() => ({ name: multiple ? undefined : name }), [name, multiple]);
  return (
    <AccordionContext value={contextValue}>
      <div className={clsx("block-accordion contained", styles.accordion)}>{children}</div>
    </AccordionContext>
  );
}
