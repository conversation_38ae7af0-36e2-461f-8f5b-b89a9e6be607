"use client";

import Hx from "@/components/ui/title/Hx";
import { AlbumsBlock } from "@/generated/graphql/graphql";
import { SubtitleLevelProvider } from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import styles from "./Albums.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));
const AlbumsList = dynamic(() => import("./AlbumsList"));

export type AlbumsProps = Partial<Omit<AlbumsBlock, "__typename" | "innerBlocks">>;

export default function Albums({ anchor, listUrl, titleLevel = 2, title, albums }: AlbumsProps) {
  return (
    <section id={anchor ?? undefined} className={clsx("block-albums contained", styles.albums)}>
      {title && (
        <Hx level={titleLevel} className={styles.title}>
          {title}
        </Hx>
      )}
      <SubtitleLevelProvider level={titleLevel}>
        {albums && albums.length > 0 && <AlbumsList items={albums} />}
      </SubtitleLevelProvider>

      {listUrl && (
        <div className={styles.actions}>
          {listUrl && (
            <Button asChild variant="contained" size="lg" startIcon="fas fa-plus">
              <Link href={listUrl}>Tous les albums</Link>
            </Button>
          )}
        </div>
      )}
    </section>
  );
}
