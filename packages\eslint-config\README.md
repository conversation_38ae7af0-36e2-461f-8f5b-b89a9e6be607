# @citeopolis/eslint-config

ESLint configuration with sensible defaults for a11y and eco design.

## Plugins

- [typescript-eslint](https://www.npmjs.com/package/typescript-eslint)
- [eslint-plugin-unicorn](https://www.npmjs.com/package/eslint-plugin-unicorn)
- [@creedengo/eslint-plugin](https://www.npmjs.com/package/@creedengo/eslint-plugin)
- [@stylistic/eslint-plugin](https://www.npmjs.com/package/@stylistic/eslint-plugin)

### React

- [eslint-plugin-react](https://www.npmjs.com/package/eslint-plugin-react)
- [eslint-plugin-react-hooks](https://www.npmjs.com/package/eslint-plugin-react-hooks)
- [@eslint-react/eslint-plugin](https://www.npmjs.com/package/@eslint-react/eslint-plugin)
- [eslint-plugin-jsx-a11y](https://www.npmjs.com/package/eslint-plugin-jsx-a11y)
- [eslint-plugin-clsx](https://www.npmjs.com/package/eslint-plugin-clsx)
