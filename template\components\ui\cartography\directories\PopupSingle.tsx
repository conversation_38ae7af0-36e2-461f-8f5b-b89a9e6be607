import Button from "@/components/ui/button/Button";
import MarkerDetailsContainer from "./MarkerDetailsContainer";
import styles from "./Popup.module.scss";

interface PopupSingleProps {
  selectedId: number;
  onClose: () => void;
}

export default function PopupSingle({ selectedId, onClose }: PopupSingleProps) {
  return (
    <div className={styles.root}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <Button
            size="sm"
            color="primary"
            variant="text"
            aria-label="Retour à la liste des points"
            onClick={() => {}}
            startIcon="far fa-arrow-left"
          />
          <h2 className={styles.title}>Détails de l'emplacement</h2>
          <Button
            className={styles.closeButton}
            size="sm"
            color="primary"
            variant="text"
            aria-label="Fermer"
            onClick={onClose}
            startIcon="far fa-close"
          />
        </div>
      </div>

      <div className={styles.content}>
        <MarkerDetailsContainer id={selectedId} />
      </div>
    </div>
  );
}
