import { Address as AddressType } from "@/generated/graphql/graphql";
import React from "react";
import { PartialDeep } from "type-fest";

interface AddressProps {
  className?: string;
  address?: PartialDeep<AddressType, { recurseIntoArrays: true }> | null;
}

/**
 * Formatted address element.
 */
export default function Address({ className, address }: AddressProps) {
  const content = formatAddress(address);
  return content && <address className={className}>{content}</address>;
}

/**
 * Format an address from graphql into a react element.
 */
export function formatAddress(address?: AddressProps["address"]) {
  const { street, zip, city } = address ?? {};

  if ((street?.length ?? 0) > 0 || zip || city) {
    return (
      <>
        {Array.isArray(street) &&
          street.length > 0 &&
          street.map(
            (line, index) =>
              line && (
                <React.Fragment key={index}>
                  {index > 0 && <br />}
                  {line}
                </React.Fragment>
              )
          )}
        {city && zip && <br />}
        {zip} {city}
      </>
    );
  }
}
