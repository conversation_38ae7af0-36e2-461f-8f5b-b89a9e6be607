import { Condition, ConditionMatchType, ConditionRule, ConditionRuleOperator } from "@/generated/graphql/graphql";
import { useCallback, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

interface ConditionalField {
  visible: boolean;
}

export default function useFormCondition(name: string | null, condition?: Condition | null): ConditionalField {
  const { unregister, watch } = useFormContext();

  // If the field has a condition we set it hidden by default to avoid FOUC.
  const [visible, setVisible] = useState(condition ? false : true);

  // TODO: Watch only the values from the conditions to avoid unecessary computation
  const formValues = watch();

  const evaluateRule = useCallback(
    (rule: ConditionRule): boolean => {
      const fieldValue = formValues[rule.field] ?? "";
      const conditionValue = rule.value;

      switch (rule.operator) {
        case ConditionRuleOperator.IS: {
          return fieldValue == conditionValue;
        }
        case ConditionRuleOperator.IS_NOT: {
          return fieldValue != conditionValue;
        }
        case ConditionRuleOperator.GREATER_THAN: {
          return Number(fieldValue) > Number(conditionValue);
        }
        case ConditionRuleOperator.LESS_THAN: {
          return Number(fieldValue) < Number(conditionValue);
        }
        case ConditionRuleOperator.CONTAINS: {
          return String(fieldValue).includes(conditionValue);
        }
        case ConditionRuleOperator.STARTS_WITH: {
          return String(fieldValue).startsWith(conditionValue);
        }
        case ConditionRuleOperator.ENDS_WITH: {
          return String(fieldValue).endsWith(conditionValue);
        }
        default: {
          return false;
        }
      }
    },
    [formValues]
  );

  const evaluateCondition = useCallback(
    (condition: Condition): boolean => {
      const results = condition.rules.map((rule) => evaluateRule(rule));
      return condition.operator === ConditionMatchType.AND ? results.every(Boolean) : results.some(Boolean);
    },
    [evaluateRule]
  );

  // When the form values changes, check if the field is visible or not.
  // TODO: Handle side effects (recursive loop)
  useEffect(() => {
    if (!condition) {
      return;
    }

    setVisible(evaluateCondition(condition));
  }, [formValues, evaluateCondition, condition]);

  // Unregister the component when it is not visible anymore.
  // TODO: Make this an option of the hook
  useEffect(() => {
    if (!condition || !name) {
      return;
    }

    if (!visible) {
      unregister(name);
    }
  }, [unregister, visible, name, condition]);

  return { visible };
}
