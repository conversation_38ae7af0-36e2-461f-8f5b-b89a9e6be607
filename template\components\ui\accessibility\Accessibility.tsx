import Tooltip from "@/components/ui/tooltip/Tooltip";
import { DirectoryAccessibility, EventAccessibility } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./Accessibility.module.scss";
import { accessibilityOptions } from "./options";
import { AccessibilityKey, AccessibilityStatus } from "./types";

interface AccessibilityProps {
  className?: string;
  accessibility?: Partial<EventAccessibility | DirectoryAccessibility>;
  size?: "sm" | "md";
}

const sortOrder: Record<AccessibilityStatus, number> = {
  SUPPORTED: 1,
  NOT_SUPPORTED: 2,
  UNKNOWN: 3,
};

/**
 * Ordered list of accessibility infos.
 */
export default function Accessibility({ className, accessibility, size = "md" }: AccessibilityProps) {
  const entries = Object.entries(accessibilityOptions)
    .map(([key, item]) => {
      const status: AccessibilityStatus = accessibility?.[key as AccessibilityKey] ?? "UNKNOWN";
      return {
        key: key as AccessibilityKey,
        status: status,
        Icon: item.icon,
        tooltipContent: item.tooltip[status],
      };
    })
    .sort((a, b) => sortOrder[a.status] - sortOrder[b.status]);

  return (
    <ul className={clsx(styles.accessibilityList, className)} aria-roledescription="Accessibilité">
      {entries.map(({ key, tooltipContent, Icon, status }) => (
        <Tooltip key={key} content={tooltipContent} placement="bottom" size="lg">
          <li
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex -- The list-item is interactive, the tooltip is not.
            tabIndex={0}
            className={clsx(styles.iconWrapper, styles[`size-${size}`], {
              [styles.supported]: status === "SUPPORTED",
              [styles.notSupported]: status === "NOT_SUPPORTED",
            })}
          >
            <Icon className={styles.icon} aria-hidden="true" />
          </li>
        </Tooltip>
      ))}
    </ul>
  );
}
