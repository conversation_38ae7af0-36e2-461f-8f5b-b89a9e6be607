"use client";

import {
  Action,
  Close,
  Description,
  ToastProps as PrimitiveToastProps,
  Root,
  ToastActionProps,
} from "@radix-ui/react-toast";
import clsx from "clsx";
import styles from "./Toast.module.scss";

interface ToastProps {
  variant?: "info" | "success" | "error" | "warning";
  children?: React.ReactNode;
  actionProps?: ToastActionProps;
  actionText?: string;
}

const variantIconMap: Record<NonNullable<ToastProps["variant"]>, string> = {
  success: "fa-check",
  error: "fa-xmark",
  info: "fa-info",
  warning: "fa-exclamation",
};

export default function Toast({
  className,
  variant = "info",
  children,
  actionProps,
  actionText,
  ...restProps
}: ToastProps & PrimitiveToastProps) {
  return (
    <Root className={clsx(styles.toast, variant && styles[variant], className)} {...restProps}>
      <i className={clsx("far", styles.icon, variant && variantIconMap[variant])} aria-hidden="true" />

      {children && <Description className={styles.description}>{children}</Description>}

      {actionText && (
        <Action className={styles.action} altText="" {...actionProps}>
          {actionText}
        </Action>
      )}

      <Close className={styles.close} aria-label="Fermer">
        <i className="fas fa-xmark" aria-hidden="true" />
      </Close>
    </Root>
  );
}
