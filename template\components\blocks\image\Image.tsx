"use client";

import type { ImageBlock, Link } from "@/generated/graphql/graphql";
import clsx from "clsx";
import dynamic from "next/dynamic";
import NextLink from "next/link";
import { useId } from "react";
import styles from "./Image.module.scss";

export type ImageProps = Partial<Omit<ImageBlock, "__typename" | "innerBlocks">>;

const ImageLightbox = dynamic(() => import("./ImageLightbox"));

export function WithLink({
  link,
  imageAlt,
  children,
}: React.PropsWithChildren<{ imageAlt?: string | null; link?: Link | null }>) {
  return link ? (
    <NextLink
      href={link.url}
      target={link.target ?? undefined}
      className={styles.imageLink}
      aria-label={link.target === "_blank" && imageAlt ? `${imageAlt} (ouvre un nouvel onglet)` : undefined}
    >
      {children}
    </NextLink>
  ) : (
    <>{children}</>
  );
}

export function WithCaption({
  caption,
  children,
}: React.PropsWithChildren<{
  caption?: string | null;
}>) {
  const captionId = useId();

  return caption ? (
    <figure role="figure" aria-labelledby={captionId}>
      {children}

      <figcaption id={captionId} className={styles.caption}>
        {caption}
      </figcaption>
    </figure>
  ) : (
    <>{children}</>
  );
}

export function ImageWrapper({
  aspectRatio,
  width,
  height,
  children,
}: React.PropsWithChildren<{
  aspectRatio?: number;
  width?: number | null;
  height?: number | null;
}>) {
  const containerStyles = {
    // Apply width if provided
    ...(width && { width }),
    // Apply height if provided
    ...(height && { height }),
    // Apply aspectRatio only if we don't have BOTH width and height
    ...(!(width && height) && aspectRatio && { aspectRatio: aspectRatio.toString() }),
  };

  return (
    <div className={styles.imageWrapper} style={containerStyles}>
      {children}
    </div>
  );
}

export default function Image(props: ImageProps) {
  const { src, alt, width, height, link, lightbox, caption, aspectRatio, scale } = props;

  if (!src) {
    return null;
  }

  if (lightbox) {
    return <ImageLightbox {...props} />;
  }

  return (
    <div className={clsx("block-image", styles.imageContainer)}>
      <WithCaption caption={caption}>
        <WithLink imageAlt={alt} link={link}>
          <ImageWrapper aspectRatio={aspectRatio ?? undefined} width={width} height={height}>
            {/* eslint-disable-next-line @next/next/no-img-element -- Image dimensions are user defined */}
            <img
              src={src}
              alt={alt ?? ""}
              className={styles.image}
              style={{ objectFit: (scale ?? "contain") as React.CSSProperties["objectFit"] }}
            />
          </ImageWrapper>
        </WithLink>
      </WithCaption>
    </div>
  );
}
