"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import { ReCaptchaField, ReCaptchaMode } from "@/generated/graphql/graphql";
import { GoogleReCaptchaCheckbox, GoogleReCaptchaProvider } from "@google-recaptcha/react";
import { useCallback, useId, useMemo } from "react";
import { Controller, useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import styles from "./captcha.module.scss";

type ReCaptchaProps = Omit<ReCaptchaField, "__typename">;

const captchaErrorMessage = "Please complete the captcha verification";

function resolveProviderType({ version, mode }: Pick<ReCaptchaProps, "version" | "mode">) {
  if (version === 3) return "v3";

  if (version === 2 && mode === ReCaptchaMode.CHECKBOX) return "v2-checkbox";

  if (version === 2 && mode === ReCaptchaMode.INVISIBLE) return "v2-invisible";
}

function ReCaptchaCheckbox({
  onChange,
  onExpired,
  onError,
}: {
  onChange: (token: string) => void;
  onExpired: () => void;
  onError: () => void;
}) {
  return <GoogleReCaptchaCheckbox onChange={onChange} onExpired={onExpired} onError={onError} />;
}

export default function ReCaptcha({ name, siteKey, version, mode, columnSpan, condition }: ReCaptchaProps) {
  const type = resolveProviderType({ version, mode });
  const { control, setValue, watch } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const fieldValue = watch(name);
  const isChecked = useMemo(() => Boolean(fieldValue), [fieldValue]);
  const helperId = useId();

  const handleChange = useCallback(
    (token: string) => {
      setValue(name, token, { shouldValidate: true });
    },
    [name, setValue]
  );

  const handleExpired = useCallback(() => {
    console.log("handleExpired..");

    setValue(name, null, { shouldValidate: true });
  }, [name, setValue]);

  const handleError = useCallback(() => {
    console.log("handleError..");

    setValue(name, null, { shouldValidate: true });
  }, [name, setValue]);

  if (!siteKey || !visible) {
    return null;
  }

  return (
    <GoogleReCaptchaProvider type={type ?? "v3"} siteKey={siteKey}>
      <FormControl columnSpan={columnSpan} className={styles.captcha}>
        <Controller
          name={name}
          control={control}
          rules={{ required: captchaErrorMessage }}
          render={({ fieldState }) => (
            <>
              {type === "v2-checkbox" && (
                <>
                  {(!isChecked && (
                    <ReCaptchaCheckbox onChange={handleChange} onExpired={handleExpired} onError={handleError} />
                  )) || (
                    <FormHelper id={helperId} variant="success">
                      Captcha Verified.
                    </FormHelper>
                  )}
                </>
              )}

              {fieldState.error && (
                <FormHelper id={helperId} variant="error">
                  {fieldState.error.message?.toString()}
                </FormHelper>
              )}
            </>
          )}
        />
      </FormControl>
    </GoogleReCaptchaProvider>
  );
}
