import styles from "./SkipLinks.module.scss";

interface SkipLinksProps {
  links: { url: string; text: string }[];
}

export default function SkipLinks({ links }: SkipLinksProps) {
  return (
    links.length > 0 && (
      <nav role="navigation" aria-label="Évitement">
        {links.map((link, index) => (
          <a key={index} href={link.url} className={styles.skipLink}>
            {link.text}
          </a>
        ))}
      </nav>
    )
  );
}
