import { QuoteBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import React from "react";
import striptags from "striptags";
import styles from "./Quote.module.scss";
import QuoteIcon from "./quote.svg";

type QuoteProps = Omit<QuoteBlock, "__typename" | "innerBlocks">;

/**
 * A block quote with an optional source.
 */
export default function Quote({ anchor, sourceHTML, children }: React.PropsWithChildren<QuoteProps>) {
  return (
    <div id={anchor ?? undefined} className={clsx("block-quote contained", styles.quote)}>
      <WithAuthor author={sourceHTML}>
        <QuoteIcon className={styles.icon} aria-hidden="true" />
        <blockquote className={styles.content}>{children}</blockquote>
      </WithAuthor>
    </div>
  );
}

/**
 * Wraps the quote block with a figure when it has an author.
 */
function WithAuthor({ author, children }: React.PropsWithChildren<{ author: string }>) {
  return author ? (
    <figure role="group" className={styles.wrapper}>
      {children}
      <figcaption
        className={styles.author}
        aria-roledescription="Auteur"
        // eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- Author HTML is sanitized
        dangerouslySetInnerHTML={{
          __html: striptags(author, ["em", "i", "strong", "b", "a", "sup", "sub", "bdo"]),
        }}
      />
    </figure>
  ) : (
    <div className={styles.wrapper}>{children}</div>
  );
}
