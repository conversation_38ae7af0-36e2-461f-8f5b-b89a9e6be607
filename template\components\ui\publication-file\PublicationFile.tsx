"use client";

import Button from "@/components/ui/button/Button";
import formatSize from "@/utils/formatSize";
import clsx from "clsx";
import Link from "next/link";
import { useId } from "react";
import styles from "./PublicationFile.module.scss";

interface PublicationFileProps {
  label?: string;
  extname?: string;
  size?: number;
  viewUrl?: string;
  downloadUrl?: string;
}

// TODO: Expand this list
const fileIcons: Record<string, string> = {
  pdf: "fa-file-pdf",
  png: "fa-file-image",
  mp4: "fa-file-video",
  mp3: "fa-file-audio",
  xlsx: "fa-file-excel",
  pptx: "fa-file-powerpoint",
  docx: "fa-file-word",
};

// TODO: Expand this list
const fileTypes: Record<string, string> = {
  pdf: "PDF",
  png: "Image",
  mp4: "Vidéo",
  mp3: "Audio",
  xlsx: "Excel",
  pptx: "PowerPoint",
  docx: "Word",
};

export default function PublicationFile({ label, extname, size, downloadUrl, viewUrl }: PublicationFileProps) {
  const fileInfoId = useId();
  const fileMetaId = useId();

  return (
    <li className={styles.publicationFile}>
      {extname && fileIcons[extname.toLowerCase()] && (
        <i className={clsx(styles.fileIcon, "far", fileIcons[extname.toLowerCase()])} aria-hidden="true"></i>
      )}
      <p className={styles.fileInfo} id={fileInfoId}>
        {label}
      </p>
      <div className={styles.actions}>
        {size && (
          <p id={fileMetaId} className={styles.fileMeta} aria-roledescription="Type et poids du fichier">
            {extname && fileTypes[extname.toLowerCase()] && <>{fileTypes[extname.toLowerCase()]} - </>}
            {formatSize(size)}
          </p>
        )}
        {(downloadUrl || viewUrl) && (
          <div className={styles.buttons}>
            {downloadUrl && (
              <Button className={styles.actionButton} size="xs" startIcon="far fa-arrow-down-to-line" asChild>
                <Link
                  href={downloadUrl}
                  download
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-describedby={`${fileInfoId} ${fileMetaId}`}
                >
                  Télécharger
                </Link>
              </Button>
            )}
            {viewUrl && (
              <Button className={styles.actionButton} color="secondary" size="xs" startIcon="far fa-book-open" asChild>
                <Link href={viewUrl} download target="_blank" rel="noopener noreferrer" aria-describedby={fileInfoId}>
                  Feuilleter
                </Link>
              </Button>
            )}
          </div>
        )}
      </div>
    </li>
  );
}
