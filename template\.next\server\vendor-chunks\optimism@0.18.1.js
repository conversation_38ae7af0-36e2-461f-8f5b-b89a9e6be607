"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/optimism@0.18.1";
exports.ids = ["vendor-chunks/optimism@0.18.1"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.bind),\n/* harmony export */   noContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.noContext),\n/* harmony export */   nonReactive: () => (/* binding */ nonReactive),\n/* harmony export */   parentEntrySlot: () => (/* binding */ parentEntrySlot),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.setTimeout)\n/* harmony export */ });\n/* harmony import */ var _wry_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/context */ \"(rsc)/../node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js\");\n\nconst parentEntrySlot = new _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot();\nfunction nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\n\n\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBb0M7QUFDN0IsNEJBQTRCLDhDQUFJO0FBQ2hDO0FBQ1A7QUFDQTtBQUNnQjtBQUN5RTtBQUN6RiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxvcHRpbWlzbUAwLjE4LjFcXG5vZGVfbW9kdWxlc1xcb3B0aW1pc21cXGxpYlxcY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTbG90IH0gZnJvbSBcIkB3cnkvY29udGV4dFwiO1xuZXhwb3J0IGNvbnN0IHBhcmVudEVudHJ5U2xvdCA9IG5ldyBTbG90KCk7XG5leHBvcnQgZnVuY3Rpb24gbm9uUmVhY3RpdmUoZm4pIHtcbiAgICByZXR1cm4gcGFyZW50RW50cnlTbG90LndpdGhWYWx1ZSh2b2lkIDAsIGZuKTtcbn1cbmV4cG9ydCB7IFNsb3QgfTtcbmV4cG9ydCB7IGJpbmQgYXMgYmluZENvbnRleHQsIG5vQ29udGV4dCwgc2V0VGltZW91dCwgYXN5bmNGcm9tR2VuLCB9IGZyb20gXCJAd3J5L2NvbnRleHRcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js":
/*!******************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dep: () => (/* binding */ dep)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nfunction dep(options) {\n    const depsByKey = new Map();\n    const subscribe = options && options.subscribe;\n    function depend(key) {\n        const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n        if (parent) {\n            let dep = depsByKey.get(key);\n            if (!dep) {\n                depsByKey.set(key, dep = new Set);\n            }\n            parent.dependOn(dep);\n            if (typeof subscribe === \"function\") {\n                (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n                dep.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        const dep = depsByKey.get(key);\n        if (dep) {\n            const m = (entryMethodName &&\n                _helpers_js__WEBPACK_IMPORTED_MODULE_1__.hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(dep).forEach(entry => entry[m]());\n            depsByKey.delete(key);\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n        }\n    };\n    return depend;\n}\n//# sourceMappingURL=dep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js":
/*!********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Entry: () => (/* binding */ Entry)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    const len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nclass Entry {\n    constructor(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    peek() {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    }\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    recompute(args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    }\n    setDirty() {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(this);\n    }\n    dispose() {\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, (parent, child) => {\n            parent.setDirty();\n            forgetChild(parent, this);\n        });\n    }\n    forget() {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    }\n    dependOn(dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    }\n    forgetDeps() {\n        if (this.deps) {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(this.deps).forEach(dep => dep.delete(this));\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    }\n}\nEntry.count = 0;\nfunction rememberParent(child) {\n    const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    const { normalizeResult } = entry;\n    let oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    const parentCount = child.parents.size;\n    if (parentCount) {\n        const parents = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(child.parents);\n        for (let i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    const parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    const childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    const dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach((_value, child) => {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n//# sourceMappingURL=entry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayFromSet: () => (/* binding */ arrayFromSet),\n/* harmony export */   hasOwnProperty: () => (/* binding */ hasOwnProperty),\n/* harmony export */   maybeUnsubscribe: () => (/* binding */ maybeUnsubscribe)\n/* harmony export */ });\nconst { hasOwnProperty, } = Object.prototype;\nconst arrayFromSet = Array.from ||\n    function (set) {\n        const array = [];\n        set.forEach(item => array.push(item));\n        return array;\n    };\nfunction maybeUnsubscribe(entryOrDep) {\n    const { unsubscribe } = entryOrDep;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2hlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sUUFBUSxrQkFBa0I7QUFDMUI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxZQUFZLGNBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG9wdGltaXNtQDAuMTguMVxcbm9kZV9tb2R1bGVzXFxvcHRpbWlzbVxcbGliXFxoZWxwZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB7IGhhc093blByb3BlcnR5LCB9ID0gT2JqZWN0LnByb3RvdHlwZTtcbmV4cG9ydCBjb25zdCBhcnJheUZyb21TZXQgPSBBcnJheS5mcm9tIHx8XG4gICAgZnVuY3Rpb24gKHNldCkge1xuICAgICAgICBjb25zdCBhcnJheSA9IFtdO1xuICAgICAgICBzZXQuZm9yRWFjaChpdGVtID0+IGFycmF5LnB1c2goaXRlbSkpO1xuICAgICAgICByZXR1cm4gYXJyYXk7XG4gICAgfTtcbmV4cG9ydCBmdW5jdGlvbiBtYXliZVVuc3Vic2NyaWJlKGVudHJ5T3JEZXApIHtcbiAgICBjb25zdCB7IHVuc3Vic2NyaWJlIH0gPSBlbnRyeU9yRGVwO1xuICAgIGlmICh0eXBlb2YgdW5zdWJzY3JpYmUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICBlbnRyeU9yRGVwLnVuc3Vic2NyaWJlID0gdm9pZCAwO1xuICAgICAgICB1bnN1YnNjcmliZSgpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlbHBlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\n");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js":
/*!********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyTrie: () => (/* reexport safe */ _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie),\n/* harmony export */   Slot: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.bindContext),\n/* harmony export */   defaultMakeCacheKey: () => (/* binding */ defaultMakeCacheKey),\n/* harmony export */   dep: () => (/* reexport safe */ _dep_js__WEBPACK_IMPORTED_MODULE_3__.dep),\n/* harmony export */   noContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.noContext),\n/* harmony export */   nonReactive: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.nonReactive),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.setTimeout),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _wry_trie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/trie */ \"(rsc)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\");\n/* harmony import */ var _wry_caches__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wry/caches */ \"(rsc)/../node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js\");\n/* harmony import */ var _entry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./entry.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _dep_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dep.js */ \"(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\");\n\n\n\n\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\n\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\n\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nfunction defaultMakeCacheKey(...args) {\n    const trie = defaultKeyTrie || (defaultKeyTrie = new _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie(typeof WeakMap === \"function\"));\n    return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\n\n;\nconst caches = new Set();\nfunction wrap(originalFunction, { max = Math.pow(2, 16), keyArgs, makeCacheKey = defaultMakeCacheKey, normalizeResult, subscribe, cache: cacheOption = _wry_caches__WEBPACK_IMPORTED_MODULE_4__.StrongCache, } = Object.create(null)) {\n    const cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, entry => entry.dispose())\n        : cacheOption;\n    const optimistic = function () {\n        const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        let entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new _entry_js__WEBPACK_IMPORTED_MODULE_1__.Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = () => cache.delete(key);\n        }\n        const value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!_context_js__WEBPACK_IMPORTED_MODULE_2__.parentEntrySlot.hasValue()) {\n            caches.forEach(cache => cache.clean());\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: () => cache.size,\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max,\n        keyArgs,\n        makeCacheKey,\n        normalizeResult,\n        subscribe,\n        cache,\n    });\n    function dirtyKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.bind),\n/* harmony export */   noContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.noContext),\n/* harmony export */   nonReactive: () => (/* binding */ nonReactive),\n/* harmony export */   parentEntrySlot: () => (/* binding */ parentEntrySlot),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.setTimeout)\n/* harmony export */ });\n/* harmony import */ var _wry_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/context */ \"(ssr)/../node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js\");\n\nconst parentEntrySlot = new _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot();\nfunction nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\n\n\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBb0M7QUFDN0IsNEJBQTRCLDhDQUFJO0FBQ2hDO0FBQ1A7QUFDQTtBQUNnQjtBQUN5RTtBQUN6RiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxvcHRpbWlzbUAwLjE4LjFcXG5vZGVfbW9kdWxlc1xcb3B0aW1pc21cXGxpYlxcY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTbG90IH0gZnJvbSBcIkB3cnkvY29udGV4dFwiO1xuZXhwb3J0IGNvbnN0IHBhcmVudEVudHJ5U2xvdCA9IG5ldyBTbG90KCk7XG5leHBvcnQgZnVuY3Rpb24gbm9uUmVhY3RpdmUoZm4pIHtcbiAgICByZXR1cm4gcGFyZW50RW50cnlTbG90LndpdGhWYWx1ZSh2b2lkIDAsIGZuKTtcbn1cbmV4cG9ydCB7IFNsb3QgfTtcbmV4cG9ydCB7IGJpbmQgYXMgYmluZENvbnRleHQsIG5vQ29udGV4dCwgc2V0VGltZW91dCwgYXN5bmNGcm9tR2VuLCB9IGZyb20gXCJAd3J5L2NvbnRleHRcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnRleHQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js":
/*!******************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dep: () => (/* binding */ dep)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nfunction dep(options) {\n    const depsByKey = new Map();\n    const subscribe = options && options.subscribe;\n    function depend(key) {\n        const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n        if (parent) {\n            let dep = depsByKey.get(key);\n            if (!dep) {\n                depsByKey.set(key, dep = new Set);\n            }\n            parent.dependOn(dep);\n            if (typeof subscribe === \"function\") {\n                (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n                dep.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        const dep = depsByKey.get(key);\n        if (dep) {\n            const m = (entryMethodName &&\n                _helpers_js__WEBPACK_IMPORTED_MODULE_1__.hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(dep).forEach(entry => entry[m]());\n            depsByKey.delete(key);\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n        }\n    };\n    return depend;\n}\n//# sourceMappingURL=dep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js":
/*!********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Entry: () => (/* binding */ Entry)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    const len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nclass Entry {\n    constructor(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    peek() {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    }\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    recompute(args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    }\n    setDirty() {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(this);\n    }\n    dispose() {\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, (parent, child) => {\n            parent.setDirty();\n            forgetChild(parent, this);\n        });\n    }\n    forget() {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    }\n    dependOn(dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    }\n    forgetDeps() {\n        if (this.deps) {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(this.deps).forEach(dep => dep.delete(this));\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    }\n}\nEntry.count = 0;\nfunction rememberParent(child) {\n    const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    const { normalizeResult } = entry;\n    let oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    const parentCount = child.parents.size;\n    if (parentCount) {\n        const parents = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(child.parents);\n        for (let i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    const parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    const childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    const dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach((_value, child) => {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n//# sourceMappingURL=entry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js":
/*!**********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayFromSet: () => (/* binding */ arrayFromSet),\n/* harmony export */   hasOwnProperty: () => (/* binding */ hasOwnProperty),\n/* harmony export */   maybeUnsubscribe: () => (/* binding */ maybeUnsubscribe)\n/* harmony export */ });\nconst { hasOwnProperty, } = Object.prototype;\nconst arrayFromSet = Array.from ||\n    function (set) {\n        const array = [];\n        set.forEach(item => array.push(item));\n        return array;\n    };\nfunction maybeUnsubscribe(entryOrDep) {\n    const { unsubscribe } = entryOrDep;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2hlbHBlcnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQU8sUUFBUSxrQkFBa0I7QUFDMUI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxZQUFZLGNBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG9wdGltaXNtQDAuMTguMVxcbm9kZV9tb2R1bGVzXFxvcHRpbWlzbVxcbGliXFxoZWxwZXJzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCB7IGhhc093blByb3BlcnR5LCB9ID0gT2JqZWN0LnByb3RvdHlwZTtcbmV4cG9ydCBjb25zdCBhcnJheUZyb21TZXQgPSBBcnJheS5mcm9tIHx8XG4gICAgZnVuY3Rpb24gKHNldCkge1xuICAgICAgICBjb25zdCBhcnJheSA9IFtdO1xuICAgICAgICBzZXQuZm9yRWFjaChpdGVtID0+IGFycmF5LnB1c2goaXRlbSkpO1xuICAgICAgICByZXR1cm4gYXJyYXk7XG4gICAgfTtcbmV4cG9ydCBmdW5jdGlvbiBtYXliZVVuc3Vic2NyaWJlKGVudHJ5T3JEZXApIHtcbiAgICBjb25zdCB7IHVuc3Vic2NyaWJlIH0gPSBlbnRyeU9yRGVwO1xuICAgIGlmICh0eXBlb2YgdW5zdWJzY3JpYmUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICBlbnRyeU9yRGVwLnVuc3Vic2NyaWJlID0gdm9pZCAwO1xuICAgICAgICB1bnN1YnNjcmliZSgpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWhlbHBlcnMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js":
/*!********************************************************************************!*\
  !*** ../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyTrie: () => (/* reexport safe */ _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie),\n/* harmony export */   Slot: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.bindContext),\n/* harmony export */   defaultMakeCacheKey: () => (/* binding */ defaultMakeCacheKey),\n/* harmony export */   dep: () => (/* reexport safe */ _dep_js__WEBPACK_IMPORTED_MODULE_3__.dep),\n/* harmony export */   noContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.noContext),\n/* harmony export */   nonReactive: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.nonReactive),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.setTimeout),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _wry_trie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/trie */ \"(ssr)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\");\n/* harmony import */ var _wry_caches__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wry/caches */ \"(ssr)/../node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js\");\n/* harmony import */ var _entry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./entry.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _dep_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dep.js */ \"(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\");\n\n\n\n\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\n\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\n\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nfunction defaultMakeCacheKey(...args) {\n    const trie = defaultKeyTrie || (defaultKeyTrie = new _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie(typeof WeakMap === \"function\"));\n    return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\n\n;\nconst caches = new Set();\nfunction wrap(originalFunction, { max = Math.pow(2, 16), keyArgs, makeCacheKey = defaultMakeCacheKey, normalizeResult, subscribe, cache: cacheOption = _wry_caches__WEBPACK_IMPORTED_MODULE_4__.StrongCache, } = Object.create(null)) {\n    const cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, entry => entry.dispose())\n        : cacheOption;\n    const optimistic = function () {\n        const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        let entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new _entry_js__WEBPACK_IMPORTED_MODULE_1__.Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = () => cache.delete(key);\n        }\n        const value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!_context_js__WEBPACK_IMPORTED_MODULE_2__.parentEntrySlot.hasValue()) {\n            caches.forEach(cache => cache.clean());\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: () => cache.size,\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max,\n        keyArgs,\n        makeCacheKey,\n        normalizeResult,\n        subscribe,\n        cache,\n    });\n    function dirtyKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js\n");

/***/ })

};
;