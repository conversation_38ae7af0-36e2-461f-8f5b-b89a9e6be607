"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997";
exports.ids = ["vendor-chunks/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SimulatePreloadedQuery: () => (/* binding */ SimulatePreloadedQuery),
/* harmony export */   built_for_browser: () => (/* binding */ built_for_browser)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const SimulatePreloadedQuery = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SimulatePreloadedQuery() from the server but SimulatePreloadedQuery is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\citeopolis-frontend\\node_modules\\.pnpm\\@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997\\node_modules\\@apollo\\client-react-streaming\\dist\\index.cc.js",
"SimulatePreloadedQuery",
);const built_for_browser = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call built_for_browser() from the server but built_for_browser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\citeopolis-frontend\\node_modules\\.pnpm\\@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997\\node_modules\\@apollo\\client-react-streaming\\dist\\index.cc.js",
"built_for_browser",
);

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.rsc.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.rsc.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApolloClient: () => (/* binding */ ApolloClient),\n/* harmony export */   DebounceMultipartResponsesLink: () => (/* binding */ AccumulateMultipartResponsesLink),\n/* harmony export */   InMemoryCache: () => (/* binding */ InMemoryCache),\n/* harmony export */   RemoveMultipartDirectivesLink: () => (/* binding */ RemoveMultipartDirectivesLink),\n/* harmony export */   SSRMultipartLink: () => (/* binding */ SSRMultipartLink),\n/* harmony export */   built_for_rsc: () => (/* binding */ built_for_rsc),\n/* harmony export */   registerApolloClient: () => (/* binding */ registerApolloClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _index_cc_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.cc.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/link/core/ApolloLink.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/cache/inmemory/inMemoryCache.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/core/ApolloClient.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/directives.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(rsc)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/incrementalResult.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(rsc)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/transform.js\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql */ \"(rsc)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/utilities/stripIgnoredCharacters.mjs\");\n\n\n\n\n\n\n// src/registerApolloClient.tsx\nfunction serializeOptions(options) {\n  return {\n    ...options,\n    query: printMinified(options.query)\n  };\n}\nfunction printMinified(query) {\n  return (0,graphql__WEBPACK_IMPORTED_MODULE_2__.stripIgnoredCharacters)((0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.print)(query));\n}\nfunction createTransportedQueryRef(options, queryKey, _promise) {\n  const ref = {\n    __transportedQueryRef: true,\n    options,\n    queryKey\n  };\n  return ref;\n}\n\n// src/PreloadQuery.tsx\nfunction PreloadQuery({\n  getClient,\n  children,\n  ...options\n}) {\n  const preloadOptions = {\n    ...options,\n    fetchPolicy: \"cache-first\",\n    returnPartialData: false,\n    pollInterval: void 0,\n    nextFetchPolicy: void 0\n  };\n  const transportedOptions = sanitizeForTransport(\n    serializeOptions(preloadOptions)\n  );\n  const resultPromise = Promise.resolve(getClient()).then((client) => client.query(preloadOptions)).then(\n    (result) => [\n      { type: \"data\", result: sanitizeForTransport(result) },\n      { type: \"complete\" }\n    ],\n    () => [{ type: \"error\" }]\n  );\n  const queryKey = crypto.randomUUID();\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    _index_cc_js__WEBPACK_IMPORTED_MODULE_1__.SimulatePreloadedQuery,\n    {\n      options: transportedOptions,\n      result: resultPromise,\n      queryKey: typeof children === \"function\" ? queryKey : void 0\n    },\n    typeof children === \"function\" ? children(\n      createTransportedQueryRef(\n        transportedOptions,\n        queryKey)\n    ) : children\n  );\n}\nfunction sanitizeForTransport(value) {\n  return JSON.parse(JSON.stringify(value));\n}\n\n// src/registerApolloClient.tsx\nvar seenWrappers = WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\nvar seenClients = WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\nfunction registerApolloClient(makeClient) {\n  const getClient = makeGetClient(makeClient);\n  const getPreloadClient = makeGetClient(makeClient);\n  const PreloadQuery2 = makePreloadQuery(getPreloadClient);\n  return {\n    getClient,\n    query: async (...args) => (await getClient()).query(...args),\n    PreloadQuery: PreloadQuery2\n  };\n}\nfunction makeGetClient(makeClient) {\n  function makeWrappedClient() {\n    return { client: makeClient() };\n  }\n  const cachedMakeWrappedClient = (0,react__WEBPACK_IMPORTED_MODULE_0__.cache)(makeWrappedClient);\n  function getClient() {\n    if (arguments.length) {\n      throw new Error(\n        `\nYou cannot pass arguments into \\`getClient\\`.\nPassing arguments to \\`getClient\\` returns a different instance\nof Apollo Client each time it is called with different arguments, potentially \nresulting in duplicate requests and a non-functional cache. \n      `.trim()\n      );\n    }\n    const wrapper = cachedMakeWrappedClient();\n    if (seenWrappers && seenClients) {\n      if (!seenWrappers.has(wrapper)) {\n        if (seenClients.has(wrapper.client)) {\n          console.warn(\n            `\nMultiple calls to \\`getClient\\` for different requests returned the same client instance.\nThis means that private user data could accidentally be shared between requests.\nThis happens, for example, if you create a global \\`ApolloClient\\` instance and your \\`makeClient\\`\nimplementation just looks like \\`() => client\\`.\nAlways call \\`new ApolloClient\\` **inside** your \\`makeClient\\` function and\nreturn a new instance every time \\`makeClient\\` is called.\n`.trim()\n          );\n        }\n        seenWrappers.add(wrapper);\n        seenClients.add(wrapper.client);\n      }\n    }\n    return wrapper.client;\n  }\n  return getClient;\n}\nfunction makePreloadQuery(getClient) {\n  return function PreloadQuery2(props) {\n    return PreloadQuery({ ...props, getClient });\n  };\n}\nvar AccumulateMultipartResponsesLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_4__.ApolloLink {\n  maxDelay;\n  constructor(config) {\n    super();\n    this.maxDelay = config.cutoffDelay;\n  }\n  request(operation, forward) {\n    if (!forward) {\n      throw new Error(\"This is not a terminal link!\");\n    }\n    const operationContainsMultipartDirectives = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__.hasDirectives)(\n      [\"defer\"],\n      operation.query\n    );\n    const upstream = forward(operation);\n    if (!operationContainsMultipartDirectives)\n      return upstream;\n    const maxDelay = this.maxDelay;\n    let accumulatedData, maxDelayTimeout;\n    return new _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_6__.Observable((subscriber) => {\n      const upstreamSubscription = upstream.subscribe({\n        next: (result) => {\n          if (accumulatedData) {\n            if (accumulatedData.data && \"incremental\" in result) {\n              accumulatedData.data = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_7__.mergeIncrementalData)(\n                accumulatedData.data,\n                result\n              );\n            } else if (result.data) {\n              accumulatedData.data = result.data;\n            }\n            if (result.errors) {\n              accumulatedData.errors = [\n                ...accumulatedData.errors || [],\n                ...result.errors || []\n              ];\n            }\n            if (result.extensions)\n              accumulatedData.extensions = {\n                ...accumulatedData.extensions,\n                ...result.extensions\n              };\n          } else {\n            accumulatedData = result;\n          }\n          if (!maxDelay) {\n            flushAccumulatedData();\n          } else if (!maxDelayTimeout) {\n            maxDelayTimeout = setTimeout(flushAccumulatedData, maxDelay);\n          }\n        },\n        error: (error) => {\n          if (maxDelayTimeout)\n            clearTimeout(maxDelayTimeout);\n          subscriber.error(error);\n        },\n        complete: () => {\n          if (maxDelayTimeout) {\n            clearTimeout(maxDelayTimeout);\n            flushAccumulatedData();\n          }\n          subscriber.complete();\n        }\n      });\n      function flushAccumulatedData() {\n        subscriber.next(accumulatedData);\n        subscriber.complete();\n        upstreamSubscription.unsubscribe();\n      }\n      return function cleanUp() {\n        clearTimeout(maxDelayTimeout);\n        upstreamSubscription.unsubscribe();\n      };\n    });\n  }\n};\nfunction getDirectiveArgumentValue(directive, argument) {\n  return directive.arguments?.find((arg) => arg.name.value === argument)?.value;\n}\nvar RemoveMultipartDirectivesLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_4__.ApolloLink {\n  stripDirectives = [];\n  constructor(config) {\n    super();\n    if (config.stripDefer !== false)\n      this.stripDirectives.push(\"defer\");\n  }\n  request(operation, forward) {\n    if (!forward) {\n      throw new Error(\"This is not a terminal link!\");\n    }\n    const { query } = operation;\n    let modifiedQuery = query;\n    modifiedQuery = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_8__.removeDirectivesFromDocument)(\n      this.stripDirectives.map((directive) => ({\n        test(node) {\n          let shouldStrip = node.kind === \"Directive\" && node.name.value === directive;\n          const label = getDirectiveArgumentValue(node, \"label\");\n          if (label?.kind === \"StringValue\" && label.value.startsWith(\"SsrDontStrip\")) {\n            shouldStrip = false;\n          }\n          return shouldStrip;\n        },\n        remove: true\n      })).concat({\n        test(node) {\n          if (node.kind !== \"Directive\")\n            return false;\n          const label = getDirectiveArgumentValue(node, \"label\");\n          return label?.kind === \"StringValue\" && label.value.startsWith(\"SsrStrip\");\n        },\n        remove: true\n      }),\n      modifiedQuery\n    );\n    if (modifiedQuery === null) {\n      return _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_6__.Observable.of({});\n    }\n    operation.query = modifiedQuery;\n    return forward(operation);\n  }\n};\nvar SSRMultipartLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_4__.ApolloLink {\n  constructor(config = {}) {\n    const combined = _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_4__.ApolloLink.from([\n      new RemoveMultipartDirectivesLink({\n        stripDefer: config.stripDefer\n      }),\n      new AccumulateMultipartResponsesLink({\n        cutoffDelay: config.cutoffDelay || 0\n      })\n    ]);\n    super(combined.request);\n  }\n};\n\n// src/bundleInfo.ts\nvar bundle = {\n  pkg: \"@apollo/client-react-streaming\"\n};\nvar sourceSymbol = Symbol.for(\"apollo.source_package\");\n\n// src/DataTransportAbstraction/WrappedInMemoryCache.tsx\nvar InMemoryCache = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_9__.InMemoryCache {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n  [sourceSymbol];\n  constructor(config) {\n    super(config);\n    const info = this.constructor.info;\n    this[sourceSymbol] = `${info.pkg}:InMemoryCache`;\n  }\n};\n\n// src/assertInstance.ts\nfunction assertInstance(value, info, name) {\n  if (value[sourceSymbol] !== `${info.pkg}:${name}`) {\n    throw new Error(\n      `When using \\`${name}\\` in streaming SSR, you must use the \\`${name}\\` export provided by \\`\"${info.pkg}\"\\`.`\n    );\n  }\n}\nvar ApolloClientBase = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_10__.ApolloClient {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n  [sourceSymbol];\n  constructor(options) {\n    const warnings = [];\n    if (\"ssrMode\" in options) {\n      delete options.ssrMode;\n      warnings.push(\n        \"The `ssrMode` option is not supported in %s. Please remove it from your %s constructor options.\"\n      );\n    }\n    if (\"ssrForceFetchDelay\" in options) {\n      delete options.ssrForceFetchDelay;\n      warnings.push(\n        \"The `ssrForceFetchDelay` option is not supported in %s. Please remove it from your %s constructor options.\"\n      );\n    }\n    super(\n      {\n        connectToDevTools: false,\n        ...options\n      } \n    );\n    const info = this.constructor.info;\n    this[sourceSymbol] = `${info.pkg}:ApolloClient`;\n    for (const warning of warnings) {\n      console.warn(warning, info.pkg, \"ApolloClient\");\n    }\n    assertInstance(\n      this.cache,\n      info,\n      \"InMemoryCache\"\n    );\n  }\n};\nvar ApolloClientImplementation = ApolloClientBase;\nvar ApolloClient = class extends ApolloClientImplementation {\n};\nconst built_for_rsc = true;\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.rsc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5Ny9ub2RlX21vZHVsZXMvQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nL2Rpc3QvaW5kZXgucnNjLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFxQztBQUNrQjtBQUNnRTtBQUNrQjtBQUN4Rjs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsK0RBQXNCLENBQUMsd0VBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxvREFBb0Q7QUFDNUQsUUFBUTtBQUNSO0FBQ0EsYUFBYSxlQUFlO0FBQzVCO0FBQ0E7QUFDQSx5QkFBeUIsZ0RBQW1CO0FBQzVDLElBQUksZ0VBQXNCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLGtDQUFrQyw0Q0FBSztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIscUJBQXFCO0FBQy9DO0FBQ0E7QUFDQSxxREFBcUQsK0RBQVU7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGdGQUFhO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLHlFQUFVO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLHVGQUFvQjtBQUN6RDtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCwrREFBVTtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQjtBQUNBLG9CQUFvQiwrRkFBNEI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGFBQWEseUVBQVUsTUFBTTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLCtEQUFVO0FBQy9DLHlCQUF5QjtBQUN6QixxQkFBcUIsK0RBQVU7QUFDL0I7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxrQ0FBa0Msa0VBQWU7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsU0FBUztBQUNyQztBQUNBOztBQUVBO0FBQ0E7QUFDQSxpQ0FBaUMsU0FBUyxHQUFHLEtBQUs7QUFDbEQ7QUFDQSxzQkFBc0IsS0FBSywwQ0FBMEMsS0FBSywyQkFBMkIsU0FBUztBQUM5RztBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsa0VBQWM7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsU0FBUztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVpTTtBQUNqTTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5N1xcbm9kZV9tb2R1bGVzXFxAYXBvbGxvXFxjbGllbnQtcmVhY3Qtc3RyZWFtaW5nXFxkaXN0XFxpbmRleC5yc2MuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNhY2hlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgU2ltdWxhdGVQcmVsb2FkZWRRdWVyeSB9IGZyb20gJy4vaW5kZXguY2MuanMnO1xuaW1wb3J0IHsgQXBvbGxvTGluaywgSW5NZW1vcnlDYWNoZSBhcyBJbk1lbW9yeUNhY2hlJDEsIEFwb2xsb0NsaWVudCBhcyBBcG9sbG9DbGllbnQkMSB9IGZyb20gJ0BhcG9sbG8vY2xpZW50L2luZGV4LmpzJztcbmltcG9ydCB7IGhhc0RpcmVjdGl2ZXMsIE9ic2VydmFibGUsIHJlbW92ZURpcmVjdGl2ZXNGcm9tRG9jdW1lbnQsIG1lcmdlSW5jcmVtZW50YWxEYXRhLCBwcmludCB9IGZyb20gJ0BhcG9sbG8vY2xpZW50L3V0aWxpdGllcy9pbmRleC5qcyc7XG5pbXBvcnQgeyBzdHJpcElnbm9yZWRDaGFyYWN0ZXJzIH0gZnJvbSAnZ3JhcGhxbCc7XG5cbi8vIHNyYy9yZWdpc3RlckFwb2xsb0NsaWVudC50c3hcbmZ1bmN0aW9uIHNlcmlhbGl6ZU9wdGlvbnMob3B0aW9ucykge1xuICByZXR1cm4ge1xuICAgIC4uLm9wdGlvbnMsXG4gICAgcXVlcnk6IHByaW50TWluaWZpZWQob3B0aW9ucy5xdWVyeSlcbiAgfTtcbn1cbmZ1bmN0aW9uIHByaW50TWluaWZpZWQocXVlcnkpIHtcbiAgcmV0dXJuIHN0cmlwSWdub3JlZENoYXJhY3RlcnMocHJpbnQocXVlcnkpKTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZVRyYW5zcG9ydGVkUXVlcnlSZWYob3B0aW9ucywgcXVlcnlLZXksIF9wcm9taXNlKSB7XG4gIGNvbnN0IHJlZiA9IHtcbiAgICBfX3RyYW5zcG9ydGVkUXVlcnlSZWY6IHRydWUsXG4gICAgb3B0aW9ucyxcbiAgICBxdWVyeUtleVxuICB9O1xuICByZXR1cm4gcmVmO1xufVxuXG4vLyBzcmMvUHJlbG9hZFF1ZXJ5LnRzeFxuZnVuY3Rpb24gUHJlbG9hZFF1ZXJ5KHtcbiAgZ2V0Q2xpZW50LFxuICBjaGlsZHJlbixcbiAgLi4ub3B0aW9uc1xufSkge1xuICBjb25zdCBwcmVsb2FkT3B0aW9ucyA9IHtcbiAgICAuLi5vcHRpb25zLFxuICAgIGZldGNoUG9saWN5OiBcImNhY2hlLWZpcnN0XCIsXG4gICAgcmV0dXJuUGFydGlhbERhdGE6IGZhbHNlLFxuICAgIHBvbGxJbnRlcnZhbDogdm9pZCAwLFxuICAgIG5leHRGZXRjaFBvbGljeTogdm9pZCAwXG4gIH07XG4gIGNvbnN0IHRyYW5zcG9ydGVkT3B0aW9ucyA9IHNhbml0aXplRm9yVHJhbnNwb3J0KFxuICAgIHNlcmlhbGl6ZU9wdGlvbnMocHJlbG9hZE9wdGlvbnMpXG4gICk7XG4gIGNvbnN0IHJlc3VsdFByb21pc2UgPSBQcm9taXNlLnJlc29sdmUoZ2V0Q2xpZW50KCkpLnRoZW4oKGNsaWVudCkgPT4gY2xpZW50LnF1ZXJ5KHByZWxvYWRPcHRpb25zKSkudGhlbihcbiAgICAocmVzdWx0KSA9PiBbXG4gICAgICB7IHR5cGU6IFwiZGF0YVwiLCByZXN1bHQ6IHNhbml0aXplRm9yVHJhbnNwb3J0KHJlc3VsdCkgfSxcbiAgICAgIHsgdHlwZTogXCJjb21wbGV0ZVwiIH1cbiAgICBdLFxuICAgICgpID0+IFt7IHR5cGU6IFwiZXJyb3JcIiB9XVxuICApO1xuICBjb25zdCBxdWVyeUtleSA9IGNyeXB0by5yYW5kb21VVUlEKCk7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcbiAgICBTaW11bGF0ZVByZWxvYWRlZFF1ZXJ5LFxuICAgIHtcbiAgICAgIG9wdGlvbnM6IHRyYW5zcG9ydGVkT3B0aW9ucyxcbiAgICAgIHJlc3VsdDogcmVzdWx0UHJvbWlzZSxcbiAgICAgIHF1ZXJ5S2V5OiB0eXBlb2YgY2hpbGRyZW4gPT09IFwiZnVuY3Rpb25cIiA/IHF1ZXJ5S2V5IDogdm9pZCAwXG4gICAgfSxcbiAgICB0eXBlb2YgY2hpbGRyZW4gPT09IFwiZnVuY3Rpb25cIiA/IGNoaWxkcmVuKFxuICAgICAgY3JlYXRlVHJhbnNwb3J0ZWRRdWVyeVJlZihcbiAgICAgICAgdHJhbnNwb3J0ZWRPcHRpb25zLFxuICAgICAgICBxdWVyeUtleSlcbiAgICApIDogY2hpbGRyZW5cbiAgKTtcbn1cbmZ1bmN0aW9uIHNhbml0aXplRm9yVHJhbnNwb3J0KHZhbHVlKSB7XG4gIHJldHVybiBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHZhbHVlKSk7XG59XG5cbi8vIHNyYy9yZWdpc3RlckFwb2xsb0NsaWVudC50c3hcbnZhciBzZWVuV3JhcHBlcnMgPSBXZWFrU2V0ID8gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrU2V0KCkgOiB2b2lkIDA7XG52YXIgc2VlbkNsaWVudHMgPSBXZWFrU2V0ID8gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrU2V0KCkgOiB2b2lkIDA7XG5mdW5jdGlvbiByZWdpc3RlckFwb2xsb0NsaWVudChtYWtlQ2xpZW50KSB7XG4gIGNvbnN0IGdldENsaWVudCA9IG1ha2VHZXRDbGllbnQobWFrZUNsaWVudCk7XG4gIGNvbnN0IGdldFByZWxvYWRDbGllbnQgPSBtYWtlR2V0Q2xpZW50KG1ha2VDbGllbnQpO1xuICBjb25zdCBQcmVsb2FkUXVlcnkyID0gbWFrZVByZWxvYWRRdWVyeShnZXRQcmVsb2FkQ2xpZW50KTtcbiAgcmV0dXJuIHtcbiAgICBnZXRDbGllbnQsXG4gICAgcXVlcnk6IGFzeW5jICguLi5hcmdzKSA9PiAoYXdhaXQgZ2V0Q2xpZW50KCkpLnF1ZXJ5KC4uLmFyZ3MpLFxuICAgIFByZWxvYWRRdWVyeTogUHJlbG9hZFF1ZXJ5MlxuICB9O1xufVxuZnVuY3Rpb24gbWFrZUdldENsaWVudChtYWtlQ2xpZW50KSB7XG4gIGZ1bmN0aW9uIG1ha2VXcmFwcGVkQ2xpZW50KCkge1xuICAgIHJldHVybiB7IGNsaWVudDogbWFrZUNsaWVudCgpIH07XG4gIH1cbiAgY29uc3QgY2FjaGVkTWFrZVdyYXBwZWRDbGllbnQgPSBjYWNoZShtYWtlV3JhcHBlZENsaWVudCk7XG4gIGZ1bmN0aW9uIGdldENsaWVudCgpIHtcbiAgICBpZiAoYXJndW1lbnRzLmxlbmd0aCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICBgXG5Zb3UgY2Fubm90IHBhc3MgYXJndW1lbnRzIGludG8gXFxgZ2V0Q2xpZW50XFxgLlxuUGFzc2luZyBhcmd1bWVudHMgdG8gXFxgZ2V0Q2xpZW50XFxgIHJldHVybnMgYSBkaWZmZXJlbnQgaW5zdGFuY2Vcbm9mIEFwb2xsbyBDbGllbnQgZWFjaCB0aW1lIGl0IGlzIGNhbGxlZCB3aXRoIGRpZmZlcmVudCBhcmd1bWVudHMsIHBvdGVudGlhbGx5IFxucmVzdWx0aW5nIGluIGR1cGxpY2F0ZSByZXF1ZXN0cyBhbmQgYSBub24tZnVuY3Rpb25hbCBjYWNoZS4gXG4gICAgICBgLnRyaW0oKVxuICAgICAgKTtcbiAgICB9XG4gICAgY29uc3Qgd3JhcHBlciA9IGNhY2hlZE1ha2VXcmFwcGVkQ2xpZW50KCk7XG4gICAgaWYgKHNlZW5XcmFwcGVycyAmJiBzZWVuQ2xpZW50cykge1xuICAgICAgaWYgKCFzZWVuV3JhcHBlcnMuaGFzKHdyYXBwZXIpKSB7XG4gICAgICAgIGlmIChzZWVuQ2xpZW50cy5oYXMod3JhcHBlci5jbGllbnQpKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICAgYFxuTXVsdGlwbGUgY2FsbHMgdG8gXFxgZ2V0Q2xpZW50XFxgIGZvciBkaWZmZXJlbnQgcmVxdWVzdHMgcmV0dXJuZWQgdGhlIHNhbWUgY2xpZW50IGluc3RhbmNlLlxuVGhpcyBtZWFucyB0aGF0IHByaXZhdGUgdXNlciBkYXRhIGNvdWxkIGFjY2lkZW50YWxseSBiZSBzaGFyZWQgYmV0d2VlbiByZXF1ZXN0cy5cblRoaXMgaGFwcGVucywgZm9yIGV4YW1wbGUsIGlmIHlvdSBjcmVhdGUgYSBnbG9iYWwgXFxgQXBvbGxvQ2xpZW50XFxgIGluc3RhbmNlIGFuZCB5b3VyIFxcYG1ha2VDbGllbnRcXGBcbmltcGxlbWVudGF0aW9uIGp1c3QgbG9va3MgbGlrZSBcXGAoKSA9PiBjbGllbnRcXGAuXG5BbHdheXMgY2FsbCBcXGBuZXcgQXBvbGxvQ2xpZW50XFxgICoqaW5zaWRlKiogeW91ciBcXGBtYWtlQ2xpZW50XFxgIGZ1bmN0aW9uIGFuZFxucmV0dXJuIGEgbmV3IGluc3RhbmNlIGV2ZXJ5IHRpbWUgXFxgbWFrZUNsaWVudFxcYCBpcyBjYWxsZWQuXG5gLnRyaW0oKVxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgICAgc2VlbldyYXBwZXJzLmFkZCh3cmFwcGVyKTtcbiAgICAgICAgc2VlbkNsaWVudHMuYWRkKHdyYXBwZXIuY2xpZW50KTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHdyYXBwZXIuY2xpZW50O1xuICB9XG4gIHJldHVybiBnZXRDbGllbnQ7XG59XG5mdW5jdGlvbiBtYWtlUHJlbG9hZFF1ZXJ5KGdldENsaWVudCkge1xuICByZXR1cm4gZnVuY3Rpb24gUHJlbG9hZFF1ZXJ5Mihwcm9wcykge1xuICAgIHJldHVybiBQcmVsb2FkUXVlcnkoeyAuLi5wcm9wcywgZ2V0Q2xpZW50IH0pO1xuICB9O1xufVxudmFyIEFjY3VtdWxhdGVNdWx0aXBhcnRSZXNwb25zZXNMaW5rID0gY2xhc3MgZXh0ZW5kcyBBcG9sbG9MaW5rIHtcbiAgbWF4RGVsYXk7XG4gIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy5tYXhEZWxheSA9IGNvbmZpZy5jdXRvZmZEZWxheTtcbiAgfVxuICByZXF1ZXN0KG9wZXJhdGlvbiwgZm9yd2FyZCkge1xuICAgIGlmICghZm9yd2FyZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBpcyBub3QgYSB0ZXJtaW5hbCBsaW5rIVwiKTtcbiAgICB9XG4gICAgY29uc3Qgb3BlcmF0aW9uQ29udGFpbnNNdWx0aXBhcnREaXJlY3RpdmVzID0gaGFzRGlyZWN0aXZlcyhcbiAgICAgIFtcImRlZmVyXCJdLFxuICAgICAgb3BlcmF0aW9uLnF1ZXJ5XG4gICAgKTtcbiAgICBjb25zdCB1cHN0cmVhbSA9IGZvcndhcmQob3BlcmF0aW9uKTtcbiAgICBpZiAoIW9wZXJhdGlvbkNvbnRhaW5zTXVsdGlwYXJ0RGlyZWN0aXZlcylcbiAgICAgIHJldHVybiB1cHN0cmVhbTtcbiAgICBjb25zdCBtYXhEZWxheSA9IHRoaXMubWF4RGVsYXk7XG4gICAgbGV0IGFjY3VtdWxhdGVkRGF0YSwgbWF4RGVsYXlUaW1lb3V0O1xuICAgIHJldHVybiBuZXcgT2JzZXJ2YWJsZSgoc3Vic2NyaWJlcikgPT4ge1xuICAgICAgY29uc3QgdXBzdHJlYW1TdWJzY3JpcHRpb24gPSB1cHN0cmVhbS5zdWJzY3JpYmUoe1xuICAgICAgICBuZXh0OiAocmVzdWx0KSA9PiB7XG4gICAgICAgICAgaWYgKGFjY3VtdWxhdGVkRGF0YSkge1xuICAgICAgICAgICAgaWYgKGFjY3VtdWxhdGVkRGF0YS5kYXRhICYmIFwiaW5jcmVtZW50YWxcIiBpbiByZXN1bHQpIHtcbiAgICAgICAgICAgICAgYWNjdW11bGF0ZWREYXRhLmRhdGEgPSBtZXJnZUluY3JlbWVudGFsRGF0YShcbiAgICAgICAgICAgICAgICBhY2N1bXVsYXRlZERhdGEuZGF0YSxcbiAgICAgICAgICAgICAgICByZXN1bHRcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAocmVzdWx0LmRhdGEpIHtcbiAgICAgICAgICAgICAgYWNjdW11bGF0ZWREYXRhLmRhdGEgPSByZXN1bHQuZGF0YTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChyZXN1bHQuZXJyb3JzKSB7XG4gICAgICAgICAgICAgIGFjY3VtdWxhdGVkRGF0YS5lcnJvcnMgPSBbXG4gICAgICAgICAgICAgICAgLi4uYWNjdW11bGF0ZWREYXRhLmVycm9ycyB8fCBbXSxcbiAgICAgICAgICAgICAgICAuLi5yZXN1bHQuZXJyb3JzIHx8IFtdXG4gICAgICAgICAgICAgIF07XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocmVzdWx0LmV4dGVuc2lvbnMpXG4gICAgICAgICAgICAgIGFjY3VtdWxhdGVkRGF0YS5leHRlbnNpb25zID0ge1xuICAgICAgICAgICAgICAgIC4uLmFjY3VtdWxhdGVkRGF0YS5leHRlbnNpb25zLFxuICAgICAgICAgICAgICAgIC4uLnJlc3VsdC5leHRlbnNpb25zXG4gICAgICAgICAgICAgIH07XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGFjY3VtdWxhdGVkRGF0YSA9IHJlc3VsdDtcbiAgICAgICAgICB9XG4gICAgICAgICAgaWYgKCFtYXhEZWxheSkge1xuICAgICAgICAgICAgZmx1c2hBY2N1bXVsYXRlZERhdGEoKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKCFtYXhEZWxheVRpbWVvdXQpIHtcbiAgICAgICAgICAgIG1heERlbGF5VGltZW91dCA9IHNldFRpbWVvdXQoZmx1c2hBY2N1bXVsYXRlZERhdGEsIG1heERlbGF5KTtcbiAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIGVycm9yOiAoZXJyb3IpID0+IHtcbiAgICAgICAgICBpZiAobWF4RGVsYXlUaW1lb3V0KVxuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KG1heERlbGF5VGltZW91dCk7XG4gICAgICAgICAgc3Vic2NyaWJlci5lcnJvcihlcnJvcik7XG4gICAgICAgIH0sXG4gICAgICAgIGNvbXBsZXRlOiAoKSA9PiB7XG4gICAgICAgICAgaWYgKG1heERlbGF5VGltZW91dCkge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KG1heERlbGF5VGltZW91dCk7XG4gICAgICAgICAgICBmbHVzaEFjY3VtdWxhdGVkRGF0YSgpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzdWJzY3JpYmVyLmNvbXBsZXRlKCk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgZnVuY3Rpb24gZmx1c2hBY2N1bXVsYXRlZERhdGEoKSB7XG4gICAgICAgIHN1YnNjcmliZXIubmV4dChhY2N1bXVsYXRlZERhdGEpO1xuICAgICAgICBzdWJzY3JpYmVyLmNvbXBsZXRlKCk7XG4gICAgICAgIHVwc3RyZWFtU3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgICB9XG4gICAgICByZXR1cm4gZnVuY3Rpb24gY2xlYW5VcCgpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KG1heERlbGF5VGltZW91dCk7XG4gICAgICAgIHVwc3RyZWFtU3Vic2NyaXB0aW9uLnVuc3Vic2NyaWJlKCk7XG4gICAgICB9O1xuICAgIH0pO1xuICB9XG59O1xuZnVuY3Rpb24gZ2V0RGlyZWN0aXZlQXJndW1lbnRWYWx1ZShkaXJlY3RpdmUsIGFyZ3VtZW50KSB7XG4gIHJldHVybiBkaXJlY3RpdmUuYXJndW1lbnRzPy5maW5kKChhcmcpID0+IGFyZy5uYW1lLnZhbHVlID09PSBhcmd1bWVudCk/LnZhbHVlO1xufVxudmFyIFJlbW92ZU11bHRpcGFydERpcmVjdGl2ZXNMaW5rID0gY2xhc3MgZXh0ZW5kcyBBcG9sbG9MaW5rIHtcbiAgc3RyaXBEaXJlY3RpdmVzID0gW107XG4gIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgIHN1cGVyKCk7XG4gICAgaWYgKGNvbmZpZy5zdHJpcERlZmVyICE9PSBmYWxzZSlcbiAgICAgIHRoaXMuc3RyaXBEaXJlY3RpdmVzLnB1c2goXCJkZWZlclwiKTtcbiAgfVxuICByZXF1ZXN0KG9wZXJhdGlvbiwgZm9yd2FyZCkge1xuICAgIGlmICghZm9yd2FyZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBpcyBub3QgYSB0ZXJtaW5hbCBsaW5rIVwiKTtcbiAgICB9XG4gICAgY29uc3QgeyBxdWVyeSB9ID0gb3BlcmF0aW9uO1xuICAgIGxldCBtb2RpZmllZFF1ZXJ5ID0gcXVlcnk7XG4gICAgbW9kaWZpZWRRdWVyeSA9IHJlbW92ZURpcmVjdGl2ZXNGcm9tRG9jdW1lbnQoXG4gICAgICB0aGlzLnN0cmlwRGlyZWN0aXZlcy5tYXAoKGRpcmVjdGl2ZSkgPT4gKHtcbiAgICAgICAgdGVzdChub2RlKSB7XG4gICAgICAgICAgbGV0IHNob3VsZFN0cmlwID0gbm9kZS5raW5kID09PSBcIkRpcmVjdGl2ZVwiICYmIG5vZGUubmFtZS52YWx1ZSA9PT0gZGlyZWN0aXZlO1xuICAgICAgICAgIGNvbnN0IGxhYmVsID0gZ2V0RGlyZWN0aXZlQXJndW1lbnRWYWx1ZShub2RlLCBcImxhYmVsXCIpO1xuICAgICAgICAgIGlmIChsYWJlbD8ua2luZCA9PT0gXCJTdHJpbmdWYWx1ZVwiICYmIGxhYmVsLnZhbHVlLnN0YXJ0c1dpdGgoXCJTc3JEb250U3RyaXBcIikpIHtcbiAgICAgICAgICAgIHNob3VsZFN0cmlwID0gZmFsc2U7XG4gICAgICAgICAgfVxuICAgICAgICAgIHJldHVybiBzaG91bGRTdHJpcDtcbiAgICAgICAgfSxcbiAgICAgICAgcmVtb3ZlOiB0cnVlXG4gICAgICB9KSkuY29uY2F0KHtcbiAgICAgICAgdGVzdChub2RlKSB7XG4gICAgICAgICAgaWYgKG5vZGUua2luZCAhPT0gXCJEaXJlY3RpdmVcIilcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICBjb25zdCBsYWJlbCA9IGdldERpcmVjdGl2ZUFyZ3VtZW50VmFsdWUobm9kZSwgXCJsYWJlbFwiKTtcbiAgICAgICAgICByZXR1cm4gbGFiZWw/LmtpbmQgPT09IFwiU3RyaW5nVmFsdWVcIiAmJiBsYWJlbC52YWx1ZS5zdGFydHNXaXRoKFwiU3NyU3RyaXBcIik7XG4gICAgICAgIH0sXG4gICAgICAgIHJlbW92ZTogdHJ1ZVxuICAgICAgfSksXG4gICAgICBtb2RpZmllZFF1ZXJ5XG4gICAgKTtcbiAgICBpZiAobW9kaWZpZWRRdWVyeSA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuIE9ic2VydmFibGUub2Yoe30pO1xuICAgIH1cbiAgICBvcGVyYXRpb24ucXVlcnkgPSBtb2RpZmllZFF1ZXJ5O1xuICAgIHJldHVybiBmb3J3YXJkKG9wZXJhdGlvbik7XG4gIH1cbn07XG52YXIgU1NSTXVsdGlwYXJ0TGluayA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvTGluayB7XG4gIGNvbnN0cnVjdG9yKGNvbmZpZyA9IHt9KSB7XG4gICAgY29uc3QgY29tYmluZWQgPSBBcG9sbG9MaW5rLmZyb20oW1xuICAgICAgbmV3IFJlbW92ZU11bHRpcGFydERpcmVjdGl2ZXNMaW5rKHtcbiAgICAgICAgc3RyaXBEZWZlcjogY29uZmlnLnN0cmlwRGVmZXJcbiAgICAgIH0pLFxuICAgICAgbmV3IEFjY3VtdWxhdGVNdWx0aXBhcnRSZXNwb25zZXNMaW5rKHtcbiAgICAgICAgY3V0b2ZmRGVsYXk6IGNvbmZpZy5jdXRvZmZEZWxheSB8fCAwXG4gICAgICB9KVxuICAgIF0pO1xuICAgIHN1cGVyKGNvbWJpbmVkLnJlcXVlc3QpO1xuICB9XG59O1xuXG4vLyBzcmMvYnVuZGxlSW5mby50c1xudmFyIGJ1bmRsZSA9IHtcbiAgcGtnOiBcIkBhcG9sbG8vY2xpZW50LXJlYWN0LXN0cmVhbWluZ1wiXG59O1xudmFyIHNvdXJjZVN5bWJvbCA9IFN5bWJvbC5mb3IoXCJhcG9sbG8uc291cmNlX3BhY2thZ2VcIik7XG5cbi8vIHNyYy9EYXRhVHJhbnNwb3J0QWJzdHJhY3Rpb24vV3JhcHBlZEluTWVtb3J5Q2FjaGUudHN4XG52YXIgSW5NZW1vcnlDYWNoZSA9IGNsYXNzIGV4dGVuZHMgSW5NZW1vcnlDYWNoZSQxIHtcbiAgLyoqXG4gICAqIEluZm9ybWF0aW9uIGFib3V0IHRoZSBjdXJyZW50IHBhY2thZ2UgYW5kIGl0J3MgZXhwb3J0IG5hbWVzLCBmb3IgdXNlIGluIGVycm9yIG1lc3NhZ2VzLlxuICAgKlxuICAgKiBAaW50ZXJuYWxcbiAgICovXG4gIHN0YXRpYyBpbmZvID0gYnVuZGxlO1xuICBbc291cmNlU3ltYm9sXTtcbiAgY29uc3RydWN0b3IoY29uZmlnKSB7XG4gICAgc3VwZXIoY29uZmlnKTtcbiAgICBjb25zdCBpbmZvID0gdGhpcy5jb25zdHJ1Y3Rvci5pbmZvO1xuICAgIHRoaXNbc291cmNlU3ltYm9sXSA9IGAke2luZm8ucGtnfTpJbk1lbW9yeUNhY2hlYDtcbiAgfVxufTtcblxuLy8gc3JjL2Fzc2VydEluc3RhbmNlLnRzXG5mdW5jdGlvbiBhc3NlcnRJbnN0YW5jZSh2YWx1ZSwgaW5mbywgbmFtZSkge1xuICBpZiAodmFsdWVbc291cmNlU3ltYm9sXSAhPT0gYCR7aW5mby5wa2d9OiR7bmFtZX1gKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgYFdoZW4gdXNpbmcgXFxgJHtuYW1lfVxcYCBpbiBzdHJlYW1pbmcgU1NSLCB5b3UgbXVzdCB1c2UgdGhlIFxcYCR7bmFtZX1cXGAgZXhwb3J0IHByb3ZpZGVkIGJ5IFxcYFwiJHtpbmZvLnBrZ31cIlxcYC5gXG4gICAgKTtcbiAgfVxufVxudmFyIEFwb2xsb0NsaWVudEJhc2UgPSBjbGFzcyBleHRlbmRzIEFwb2xsb0NsaWVudCQxIHtcbiAgLyoqXG4gICAqIEluZm9ybWF0aW9uIGFib3V0IHRoZSBjdXJyZW50IHBhY2thZ2UgYW5kIGl0J3MgZXhwb3J0IG5hbWVzLCBmb3IgdXNlIGluIGVycm9yIG1lc3NhZ2VzLlxuICAgKlxuICAgKiBAaW50ZXJuYWxcbiAgICovXG4gIHN0YXRpYyBpbmZvID0gYnVuZGxlO1xuICBbc291cmNlU3ltYm9sXTtcbiAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgIGNvbnN0IHdhcm5pbmdzID0gW107XG4gICAgaWYgKFwic3NyTW9kZVwiIGluIG9wdGlvbnMpIHtcbiAgICAgIGRlbGV0ZSBvcHRpb25zLnNzck1vZGU7XG4gICAgICB3YXJuaW5ncy5wdXNoKFxuICAgICAgICBcIlRoZSBgc3NyTW9kZWAgb3B0aW9uIGlzIG5vdCBzdXBwb3J0ZWQgaW4gJXMuIFBsZWFzZSByZW1vdmUgaXQgZnJvbSB5b3VyICVzIGNvbnN0cnVjdG9yIG9wdGlvbnMuXCJcbiAgICAgICk7XG4gICAgfVxuICAgIGlmIChcInNzckZvcmNlRmV0Y2hEZWxheVwiIGluIG9wdGlvbnMpIHtcbiAgICAgIGRlbGV0ZSBvcHRpb25zLnNzckZvcmNlRmV0Y2hEZWxheTtcbiAgICAgIHdhcm5pbmdzLnB1c2goXG4gICAgICAgIFwiVGhlIGBzc3JGb3JjZUZldGNoRGVsYXlgIG9wdGlvbiBpcyBub3Qgc3VwcG9ydGVkIGluICVzLiBQbGVhc2UgcmVtb3ZlIGl0IGZyb20geW91ciAlcyBjb25zdHJ1Y3RvciBvcHRpb25zLlwiXG4gICAgICApO1xuICAgIH1cbiAgICBzdXBlcihcbiAgICAgIHtcbiAgICAgICAgY29ubmVjdFRvRGV2VG9vbHM6IGZhbHNlLFxuICAgICAgICAuLi5vcHRpb25zXG4gICAgICB9IFxuICAgICk7XG4gICAgY29uc3QgaW5mbyA9IHRoaXMuY29uc3RydWN0b3IuaW5mbztcbiAgICB0aGlzW3NvdXJjZVN5bWJvbF0gPSBgJHtpbmZvLnBrZ306QXBvbGxvQ2xpZW50YDtcbiAgICBmb3IgKGNvbnN0IHdhcm5pbmcgb2Ygd2FybmluZ3MpIHtcbiAgICAgIGNvbnNvbGUud2Fybih3YXJuaW5nLCBpbmZvLnBrZywgXCJBcG9sbG9DbGllbnRcIik7XG4gICAgfVxuICAgIGFzc2VydEluc3RhbmNlKFxuICAgICAgdGhpcy5jYWNoZSxcbiAgICAgIGluZm8sXG4gICAgICBcIkluTWVtb3J5Q2FjaGVcIlxuICAgICk7XG4gIH1cbn07XG52YXIgQXBvbGxvQ2xpZW50SW1wbGVtZW50YXRpb24gPSBBcG9sbG9DbGllbnRCYXNlO1xudmFyIEFwb2xsb0NsaWVudCA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvQ2xpZW50SW1wbGVtZW50YXRpb24ge1xufTtcbmNvbnN0IGJ1aWx0X2Zvcl9yc2MgPSB0cnVlO1xuXG5leHBvcnQgeyBBcG9sbG9DbGllbnQsIEFjY3VtdWxhdGVNdWx0aXBhcnRSZXNwb25zZXNMaW5rIGFzIERlYm91bmNlTXVsdGlwYXJ0UmVzcG9uc2VzTGluaywgSW5NZW1vcnlDYWNoZSwgUmVtb3ZlTXVsdGlwYXJ0RGlyZWN0aXZlc0xpbmssIFNTUk11bHRpcGFydExpbmssIGJ1aWx0X2Zvcl9yc2MsIHJlZ2lzdGVyQXBvbGxvQ2xpZW50IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vdXQuanMubWFwXG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5yc2MuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.rsc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   built_for_browser: () => (/* binding */ built_for_browser),\n/* harmony export */   \"default\": () => (/* binding */ SimulatePreloadedQuery)\n/* harmony export */ });\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useApolloClient.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useBackgroundQuery.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client/index.js */ \"(ssr)/../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var ts_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ts-invariant */ \"(ssr)/../node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\");\n/* __next_internal_client_entry_do_not_use__ default,built_for_browser auto */ // src/SimulatePreloadedQuery.cc.ts\n\n// src/DataTransportAbstraction/transportedOptions.ts\n\n\n\nfunction deserializeOptions(options) {\n    return {\n        ...options,\n        // `gql` memoizes results, but based on the input string.\n        // We parse-stringify-parse here to ensure that our minified query\n        // has the best chance of being the referential same query as the one used in\n        // client-side code.\n        query: (0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__.gql)((0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.print)((0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__.gql)(options.query)))\n    };\n}\n// src/SimulatePreloadedQuery.cc.ts\n\n\nvar handledRequests = /* @__PURE__ */ new WeakMap();\nfunction SimulatePreloadedQuery({ options, result, children, queryKey }) {\n    const client = (0,_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_4__.useApolloClient)();\n    if (!handledRequests.has(options)) {\n        const id = `preloadedQuery:${client[\"queryManager\"].generateQueryId()}`;\n        handledRequests.set(options, id);\n        ts_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"].debug(\"Preloaded query %s started on the server, simulating ongoing request\", id);\n        client.onQueryStarted({\n            type: \"started\",\n            id,\n            options\n        });\n        result.then((results)=>{\n            ts_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"].debug(\"Preloaded query %s: received events: %o\", id, results);\n            for (const event of results){\n                client.onQueryProgress({\n                    ...event,\n                    id\n                });\n            }\n        });\n    }\n    const bgQueryArgs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"SimulatePreloadedQuery.useMemo[bgQueryArgs]\": ()=>{\n            const { query, ...hydratedOptions } = deserializeOptions(options);\n            return [\n                query,\n                // If we didn't pass in a `queryKey` prop, the user didn't use the render props form and we don't\n                // need to create a real `queryRef` => skip.\n                // Otherwise we call `useBackgroundQuery` with options in this component to create a `queryRef`\n                // and have it soft-retained in the SuspenseCache.\n                queryKey ? {\n                    ...hydratedOptions,\n                    queryKey\n                } : _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_5__.skipToken\n            ];\n        }\n    }[\"SimulatePreloadedQuery.useMemo[bgQueryArgs]\"], [\n        options,\n        queryKey\n    ]);\n    (0,_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_6__.useBackgroundQuery)(...bgQueryArgs);\n    return children;\n}\n\nconst built_for_browser = true; //# sourceMappingURL=SimulatePreloadedQuery.cc.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimulatePreloadedQuery: () => (/* binding */ SimulatePreloadedQuery),\n/* harmony export */   built_for_browser: () => (/* binding */ built_for_browser)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ SimulatePreloadedQuery,built_for_browser auto */ // src/index.cc.tsx\n\nvar RealSimulatePreloadedQuery;\nvar SimulatePreloadedQuery = (props)=>{\n    if (!RealSimulatePreloadedQuery) {\n        RealSimulatePreloadedQuery = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.lazy(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802\"), __webpack_require__.e(\"vendor-chunks/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./SimulatePreloadedQuery.cc.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js\")));\n    }\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(RealSimulatePreloadedQuery, {\n        ...props\n    });\n};\n\nconst built_for_browser = true; //# sourceMappingURL=index.cc.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5Ny9ub2RlX21vZHVsZXMvQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nL2Rpc3QvaW5kZXguY2MuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUV1QjtBQUV2QixJQUFJO0FBQ0csSUFBTSx5QkFDWCxDQUFDO0lBQ0MsSUFBSSxDQUFDLDRCQUE0QjtRQUMvQiwyQ0FBbUMsd0NBQ2pDLElBQU0sMGVBQXdDO0lBRWxEO0lBQ0EsT0FBTywrRUFBQztRQUE0QixHQUFHO0lBQUEsQ0FBTztBQUNoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxzcmNcXGluZGV4LmNjLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmxldCBSZWFsU2ltdWxhdGVQcmVsb2FkZWRRdWVyeTogdHlwZW9mIGltcG9ydChcIi4vU2ltdWxhdGVQcmVsb2FkZWRRdWVyeS5jYy5qc1wiKS5kZWZhdWx0O1xuZXhwb3J0IGNvbnN0IFNpbXVsYXRlUHJlbG9hZGVkUXVlcnk6IHR5cGVvZiBpbXBvcnQoXCIuL1NpbXVsYXRlUHJlbG9hZGVkUXVlcnkuY2MuanNcIikuZGVmYXVsdCA9XG4gIChwcm9wcykgPT4ge1xuICAgIGlmICghUmVhbFNpbXVsYXRlUHJlbG9hZGVkUXVlcnkpIHtcbiAgICAgIFJlYWxTaW11bGF0ZVByZWxvYWRlZFF1ZXJ5ID0gUmVhY3QubGF6eShcbiAgICAgICAgKCkgPT4gaW1wb3J0KFwiLi9TaW11bGF0ZVByZWxvYWRlZFF1ZXJ5LmNjLmpzXCIpXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gPFJlYWxTaW11bGF0ZVByZWxvYWRlZFF1ZXJ5IHsuLi5wcm9wc30gLz47XG4gIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.cc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.ssr.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.ssr.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApolloClient: () => (/* binding */ ApolloClient),\n/* harmony export */   DataTransportContext: () => (/* binding */ DataTransportContext),\n/* harmony export */   DebounceMultipartResponsesLink: () => (/* binding */ AccumulateMultipartResponsesLink),\n/* harmony export */   InMemoryCache: () => (/* binding */ InMemoryCache),\n/* harmony export */   RemoveMultipartDirectivesLink: () => (/* binding */ RemoveMultipartDirectivesLink),\n/* harmony export */   SSRMultipartLink: () => (/* binding */ SSRMultipartLink),\n/* harmony export */   WrapApolloProvider: () => (/* binding */ WrapApolloProvider),\n/* harmony export */   built_for_ssr: () => (/* binding */ built_for_ssr),\n/* harmony export */   resetApolloSingletons: () => (/* binding */ resetApolloSingletons)\n/* harmony export */ });\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @apollo/client/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/link/core/ApolloLink.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/cache/inmemory/inMemoryCache.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client/index.js */ \"(ssr)/../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @apollo/client/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/core/ApolloClient.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/directives.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/incrementalResult.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/transform.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js\");\n/* harmony import */ var _apollo_client_cache_index_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client/cache/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/canonicalStringify.js\");\n/* harmony import */ var ts_invariant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ts-invariant */ \"(ssr)/../node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _wry_equality__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wry/equality */ \"(ssr)/../node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js\");\n/* harmony import */ var _apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client/react/internal/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js\");\n/* harmony import */ var _apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client/react/internal/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useApolloClient.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(ssr)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/context/ApolloProvider.js\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! graphql */ \"(ssr)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/utilities/stripIgnoredCharacters.mjs\");\n\n\n\n\n\n\n\n\n\n\n// src/AccumulateMultipartResponsesLink.ts\nvar AccumulateMultipartResponsesLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_3__.ApolloLink {\n  maxDelay;\n  constructor(config) {\n    super();\n    this.maxDelay = config.cutoffDelay;\n  }\n  request(operation, forward) {\n    if (!forward) {\n      throw new Error(\"This is not a terminal link!\");\n    }\n    const operationContainsMultipartDirectives = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_4__.hasDirectives)(\n      [\"defer\"],\n      operation.query\n    );\n    const upstream = forward(operation);\n    if (!operationContainsMultipartDirectives)\n      return upstream;\n    const maxDelay = this.maxDelay;\n    let accumulatedData, maxDelayTimeout;\n    return new _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__.Observable((subscriber) => {\n      const upstreamSubscription = upstream.subscribe({\n        next: (result) => {\n          if (accumulatedData) {\n            if (accumulatedData.data && \"incremental\" in result) {\n              accumulatedData.data = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_6__.mergeIncrementalData)(\n                accumulatedData.data,\n                result\n              );\n            } else if (result.data) {\n              accumulatedData.data = result.data;\n            }\n            if (result.errors) {\n              accumulatedData.errors = [\n                ...accumulatedData.errors || [],\n                ...result.errors || []\n              ];\n            }\n            if (result.extensions)\n              accumulatedData.extensions = {\n                ...accumulatedData.extensions,\n                ...result.extensions\n              };\n          } else {\n            accumulatedData = result;\n          }\n          if (!maxDelay) {\n            flushAccumulatedData();\n          } else if (!maxDelayTimeout) {\n            maxDelayTimeout = setTimeout(flushAccumulatedData, maxDelay);\n          }\n        },\n        error: (error) => {\n          if (maxDelayTimeout)\n            clearTimeout(maxDelayTimeout);\n          subscriber.error(error);\n        },\n        complete: () => {\n          if (maxDelayTimeout) {\n            clearTimeout(maxDelayTimeout);\n            flushAccumulatedData();\n          }\n          subscriber.complete();\n        }\n      });\n      function flushAccumulatedData() {\n        subscriber.next(accumulatedData);\n        subscriber.complete();\n        upstreamSubscription.unsubscribe();\n      }\n      return function cleanUp() {\n        clearTimeout(maxDelayTimeout);\n        upstreamSubscription.unsubscribe();\n      };\n    });\n  }\n};\nfunction getDirectiveArgumentValue(directive, argument) {\n  return directive.arguments?.find((arg) => arg.name.value === argument)?.value;\n}\nvar RemoveMultipartDirectivesLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_3__.ApolloLink {\n  stripDirectives = [];\n  constructor(config) {\n    super();\n    if (config.stripDefer !== false)\n      this.stripDirectives.push(\"defer\");\n  }\n  request(operation, forward) {\n    if (!forward) {\n      throw new Error(\"This is not a terminal link!\");\n    }\n    const { query } = operation;\n    let modifiedQuery = query;\n    modifiedQuery = (0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_7__.removeDirectivesFromDocument)(\n      this.stripDirectives.map((directive) => ({\n        test(node) {\n          let shouldStrip = node.kind === \"Directive\" && node.name.value === directive;\n          const label = getDirectiveArgumentValue(node, \"label\");\n          if (label?.kind === \"StringValue\" && label.value.startsWith(\"SsrDontStrip\")) {\n            shouldStrip = false;\n          }\n          return shouldStrip;\n        },\n        remove: true\n      })).concat({\n        test(node) {\n          if (node.kind !== \"Directive\")\n            return false;\n          const label = getDirectiveArgumentValue(node, \"label\");\n          return label?.kind === \"StringValue\" && label.value.startsWith(\"SsrStrip\");\n        },\n        remove: true\n      }),\n      modifiedQuery\n    );\n    if (modifiedQuery === null) {\n      return _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__.Observable.of({});\n    }\n    operation.query = modifiedQuery;\n    return forward(operation);\n  }\n};\nvar SSRMultipartLink = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_3__.ApolloLink {\n  constructor(config = {}) {\n    const combined = _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_3__.ApolloLink.from([\n      new RemoveMultipartDirectivesLink({\n        stripDefer: config.stripDefer\n      }),\n      new AccumulateMultipartResponsesLink({\n        cutoffDelay: config.cutoffDelay || 0\n      })\n    ]);\n    super(combined.request);\n  }\n};\n\n// src/bundleInfo.ts\nvar bundle = {\n  pkg: \"@apollo/client-react-streaming\"\n};\nvar sourceSymbol = Symbol.for(\"apollo.source_package\");\n\n// src/DataTransportAbstraction/WrappedInMemoryCache.tsx\nvar InMemoryCache = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_8__.InMemoryCache {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n  [sourceSymbol];\n  constructor(config) {\n    super(config);\n    const info = this.constructor.info;\n    this[sourceSymbol] = `${info.pkg}:InMemoryCache`;\n  }\n};\n\n// src/DataTransportAbstraction/backpressuredCallback.ts\nfunction createBackpressuredCallback() {\n  const queue = [];\n  let push = queue.push.bind(queue);\n  return {\n    push: (value) => push(value),\n    register: (callback) => {\n      if (callback) {\n        push = callback;\n        while (queue.length) {\n          callback(queue.shift());\n        }\n      } else {\n        push = queue.push.bind(queue);\n      }\n    }\n  };\n}\nvar DataTransportContext = /* @__PURE__ */ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(null);\nvar CLEAN = {};\nfunction useTransportValue(value) {\n  const dataTransport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(DataTransportContext);\n  if (!dataTransport)\n    throw new Error(\n      \"useTransportValue must be used within a streaming-specific ApolloProvider\"\n    );\n  const valueRef = dataTransport.useStaticValueRef(value);\n  const whichResult = (0,react__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(\n    () => () => {\n    },\n    () => 0 /* client */,\n    () => valueRef.current === CLEAN ? 0 /* client */ : (0,_wry_equality__WEBPACK_IMPORTED_MODULE_2__.equal)(value, valueRef.current) ? 0 /* client */ : 1 /* server */\n  );\n  if (whichResult === 0 /* client */) {\n    valueRef.current = CLEAN;\n  }\n  return whichResult === 1 /* server */ ? valueRef.current : value;\n}\nfunction serializeOptions(options) {\n  return {\n    ...options,\n    query: printMinified(options.query)\n  };\n}\nfunction deserializeOptions(options) {\n  return {\n    ...options,\n    // `gql` memoizes results, but based on the input string.\n    // We parse-stringify-parse here to ensure that our minified query\n    // has the best chance of being the referential same query as the one used in\n    // client-side code.\n    query: (0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_9__.gql)((0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_10__.print)((0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_9__.gql)(options.query)))\n  };\n}\nfunction printMinified(query) {\n  return (0,graphql__WEBPACK_IMPORTED_MODULE_11__.stripIgnoredCharacters)((0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_10__.print)(query));\n}\nfunction reviveTransportedQueryRef(queryRef, client) {\n  const hydratedOptions = deserializeOptions(queryRef.options);\n  const cacheKey = [\n    hydratedOptions.query,\n    (0,_apollo_client_cache_index_js__WEBPACK_IMPORTED_MODULE_12__.canonicalStringify)(hydratedOptions.variables),\n    queryRef.queryKey\n  ];\n  if (queryRef.__transportedQueryRef === true) {\n    queryRef.__transportedQueryRef = (0,_apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_13__.wrapQueryRef)(\n      (0,_apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_14__.getSuspenseCache)(client).getQueryRef(\n        cacheKey,\n        () => client.watchQuery(hydratedOptions)\n      )\n    );\n  }\n  return [queryRef.__transportedQueryRef, cacheKey];\n}\nfunction isTransportedQueryRef(queryRef) {\n  return \"__transportedQueryRef\" in queryRef;\n}\nfunction useWrapTransportedQueryRef(queryRef) {\n  const client = (0,_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_15__.useApolloClient)();\n  let cacheKey;\n  let isTransported;\n  if (isTransported = isTransportedQueryRef(queryRef)) {\n    [queryRef, cacheKey] = reviveTransportedQueryRef(queryRef, client);\n  }\n  (0,_apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_13__.assertWrappedQueryRef)(queryRef);\n  const unwrapped = (0,_apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_13__.unwrapQueryRef)(queryRef);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (!isTransported)\n      return;\n    if (cacheKey) {\n      if (unwrapped.disposed) {\n        (0,_apollo_client_react_internal_index_js__WEBPACK_IMPORTED_MODULE_14__.getSuspenseCache)(client).add(cacheKey, unwrapped);\n      }\n    }\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    if (isTransported) {\n      return unwrapped.softRetain();\n    }\n  }, [isTransported, unwrapped]);\n  return queryRef;\n}\nvar hookWrappers = {\n  useFragment(orig_useFragment) {\n    return wrap(orig_useFragment, [\"data\", \"complete\", \"missing\"]);\n  },\n  useQuery(orig_useQuery) {\n    return wrap(\n      (query, options) => orig_useQuery(query, { ...options, fetchPolicy: \"cache-only\" }) ,\n      [\"data\", \"loading\", \"networkStatus\", \"called\"]\n    );\n  },\n  useSuspenseQuery(orig_useSuspenseQuery) {\n    return wrap(orig_useSuspenseQuery, [\"data\", \"networkStatus\"]);\n  },\n  useReadQuery(orig_useReadQuery) {\n    return wrap(\n      (queryRef) => {\n        return orig_useReadQuery(useWrapTransportedQueryRef(queryRef));\n      },\n      [\"data\", \"networkStatus\"]\n    );\n  },\n  useQueryRefHandlers(orig_useQueryRefHandlers) {\n    return wrap((queryRef) => {\n      return orig_useQueryRefHandlers(useWrapTransportedQueryRef(queryRef));\n    }, []);\n  }\n};\nfunction wrap(useFn, transportKeys) {\n  return (...args) => {\n    const result = useFn(...args);\n    if (transportKeys.length == 0) {\n      return result;\n    }\n    const forTransport = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => {\n      const transport = {};\n      for (const key of transportKeys) {\n        transport[key] = result[key];\n      }\n      return transport;\n    }, [result]);\n    const transported = useTransportValue(forTransport);\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(\n      () => ({ ...result, ...transported }),\n      [result, transported]\n    );\n  };\n}\n\n// src/assertInstance.ts\nfunction assertInstance(value, info, name) {\n  if (value[sourceSymbol] !== `${info.pkg}:${name}`) {\n    throw new Error(\n      `When using \\`${name}\\` in streaming SSR, you must use the \\`${name}\\` export provided by \\`\"${info.pkg}\"\\`.`\n    );\n  }\n}\n\n// src/DataTransportAbstraction/WrappedApolloClient.tsx\nfunction getQueryManager(client) {\n  return client[\"queryManager\"];\n}\nfunction getTrieConstructor(client) {\n  return getQueryManager(client)[\"inFlightLinkObservables\"].constructor;\n}\nvar wrappers = Symbol.for(\"apollo.hook.wrappers\");\nvar ApolloClientBase = class extends _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_16__.ApolloClient {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n  [sourceSymbol];\n  constructor(options) {\n    const warnings = [];\n    if (\"ssrMode\" in options) {\n      delete options.ssrMode;\n      warnings.push(\n        \"The `ssrMode` option is not supported in %s. Please remove it from your %s constructor options.\"\n      );\n    }\n    if (\"ssrForceFetchDelay\" in options) {\n      delete options.ssrForceFetchDelay;\n      warnings.push(\n        \"The `ssrForceFetchDelay` option is not supported in %s. Please remove it from your %s constructor options.\"\n      );\n    }\n    super(\n      {\n        connectToDevTools: false,\n        ...options\n      } \n    );\n    const info = this.constructor.info;\n    this[sourceSymbol] = `${info.pkg}:ApolloClient`;\n    for (const warning of warnings) {\n      console.warn(warning, info.pkg, \"ApolloClient\");\n    }\n    assertInstance(\n      this.cache,\n      info,\n      \"InMemoryCache\"\n    );\n  }\n};\nvar ApolloClientClientBaseImpl = class extends ApolloClientBase {\n  constructor(options) {\n    super(options);\n    this.onQueryStarted = this.onQueryStarted.bind(this);\n    getQueryManager(this)[wrappers] = hookWrappers;\n  }\n  simulatedStreamingQueries = /* @__PURE__ */ new Map();\n  transportedQueryOptions = /* @__PURE__ */ new Map();\n  identifyUniqueQuery(options) {\n    const transformedDocument = this.documentTransform.transformDocument(\n      options.query\n    );\n    const queryManager = getQueryManager(this);\n    const { serverQuery } = queryManager.getDocumentInfo(transformedDocument);\n    if (!serverQuery) {\n      throw new Error(\"could not identify unique query\");\n    }\n    const canonicalVariables = (0,_apollo_client_cache_index_js__WEBPACK_IMPORTED_MODULE_12__.canonicalStringify)(options.variables || {});\n    const cacheKeyArr = [(0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_10__.print)(serverQuery), canonicalVariables];\n    const cacheKey = JSON.stringify(cacheKeyArr);\n    return {\n      cacheKey,\n      cacheKeyArr\n    };\n  }\n  onQueryStarted({ options, id }) {\n    const hydratedOptions = deserializeOptions(options);\n    const { cacheKey, cacheKeyArr } = this.identifyUniqueQuery(hydratedOptions);\n    this.transportedQueryOptions.set(id, hydratedOptions);\n    const queryManager = getQueryManager(this);\n    if (!queryManager[\"inFlightLinkObservables\"].peekArray(cacheKeyArr)?.observable) {\n      let simulatedStreamingQuery, fetchCancelFn;\n      const cleanup = () => {\n        if (queryManager[\"fetchCancelFns\"].get(cacheKey) === fetchCancelFn)\n          queryManager[\"fetchCancelFns\"].delete(cacheKey);\n        queryManager[\"inFlightLinkObservables\"].removeArray(cacheKeyArr);\n        if (this.simulatedStreamingQueries.get(id) === simulatedStreamingQuery)\n          this.simulatedStreamingQueries.delete(id);\n      };\n      const promise = new Promise((resolve, reject) => {\n        this.simulatedStreamingQueries.set(\n          id,\n          simulatedStreamingQuery = {\n            resolve,\n            reject,\n            options: hydratedOptions\n          }\n        );\n      });\n      promise.then(cleanup, cleanup);\n      const observable = new _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__.Observable((observer) => {\n        promise.then((result) => {\n          observer.next(result);\n          observer.complete();\n        }).catch((err) => {\n          observer.error(err);\n        });\n      });\n      queryManager[\"inFlightLinkObservables\"].lookupArray(\n        cacheKeyArr\n      ).observable = observable;\n      queryManager[\"fetchCancelFns\"].set(\n        cacheKey,\n        fetchCancelFn = (reason) => {\n          const { reject } = this.simulatedStreamingQueries.get(id) ?? {};\n          if (reject) {\n            reject(reason);\n          }\n          cleanup();\n        }\n      );\n    }\n  }\n  onQueryProgress = (event) => {\n    const queryInfo = this.simulatedStreamingQueries.get(event.id);\n    if (event.type === \"data\") {\n      queryInfo?.resolve?.({\n        data: event.result.data\n      });\n      const options = this.transportedQueryOptions.get(event.id);\n      if (options) {\n        this.cache.writeQuery({\n          query: options.query,\n          data: event.result.data,\n          variables: options.variables\n        });\n      }\n    } else if (event.type === \"error\") {\n      if (queryInfo) {\n        this.simulatedStreamingQueries.delete(event.id);\n        {\n          ts_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant.debug(\n            \"Query failed upstream, will fail it during SSR and rerun it in the browser:\",\n            queryInfo.options\n          );\n          queryInfo?.reject?.(new Error(\"Query failed upstream.\"));\n        }\n      }\n      this.transportedQueryOptions.delete(event.id);\n    } else if (event.type === \"complete\") {\n      this.transportedQueryOptions.delete(event.id);\n    }\n  };\n  /**\n   * Can be called when the stream closed unexpectedly while there might still be unresolved\n   * simulated server-side queries going on.\n   * Those queries will be cancelled and then re-run in the browser.\n   */\n  rerunSimulatedQueries = () => {\n    for (const [id, queryInfo] of this.simulatedStreamingQueries) {\n      this.simulatedStreamingQueries.delete(id);\n      ts_invariant__WEBPACK_IMPORTED_MODULE_0__.invariant.debug(\n        \"streaming connection closed before server query could be fully transported, rerunning:\",\n        queryInfo.options\n      );\n      this.rerunSimulatedQuery(queryInfo);\n    }\n  };\n  rerunSimulatedQuery = (queryInfo) => {\n    const queryManager = getQueryManager(this);\n    const queryId = queryManager.generateQueryId();\n    queryManager.fetchQuery(queryId, {\n      ...queryInfo.options,\n      query: queryManager.transform(queryInfo.options.query),\n      context: {\n        ...queryInfo.options.context,\n        queryDeduplication: false\n      }\n    }).finally(() => queryManager.stopQuery(queryId)).then(queryInfo.resolve, queryInfo.reject);\n  };\n};\nvar ApolloClientSSRImpl = class extends ApolloClientClientBaseImpl {\n  forwardedQueries = new (getTrieConstructor(this))();\n  watchQueryQueue = createBackpressuredCallback();\n  watchQuery(options) {\n    const { cacheKeyArr } = this.identifyUniqueQuery(options);\n    if (options.fetchPolicy !== \"cache-only\" && options.fetchPolicy !== \"standby\" && !this.forwardedQueries.peekArray(cacheKeyArr)) {\n      this.forwardedQueries.lookupArray(cacheKeyArr);\n      const observableQuery = super.watchQuery(options);\n      const queryInfo = observableQuery[\"queryInfo\"];\n      const id = queryInfo.queryId;\n      const streamObservable = new _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_5__.Observable((subscriber) => {\n        const { markResult, markError, markReady } = queryInfo;\n        queryInfo.markResult = function(result) {\n          subscriber.next({\n            type: \"data\",\n            id,\n            result\n          });\n          return markResult.apply(queryInfo, arguments);\n        };\n        queryInfo.markError = function() {\n          subscriber.next({\n            type: \"error\",\n            id\n          });\n          subscriber.complete();\n          return markError.apply(queryInfo, arguments);\n        };\n        queryInfo.markReady = function() {\n          subscriber.next({\n            type: \"complete\",\n            id\n          });\n          subscriber.complete();\n          return markReady.apply(queryInfo, arguments);\n        };\n      });\n      this.watchQueryQueue.push({\n        event: {\n          type: \"started\",\n          options: serializeOptions(options),\n          id\n        },\n        observable: streamObservable\n      });\n      return observableQuery;\n    }\n    return super.watchQuery(options);\n  }\n  onQueryStarted(event) {\n    const hydratedOptions = deserializeOptions(event.options);\n    const { cacheKeyArr } = this.identifyUniqueQuery(hydratedOptions);\n    this.forwardedQueries.lookupArray(cacheKeyArr);\n    super.onQueryStarted(event);\n  }\n};\nvar ApolloClientImplementation = ApolloClientSSRImpl ;\nvar ApolloClient = class extends ApolloClientImplementation {\n};\n\n// src/DataTransportAbstraction/symbols.ts\nvar ApolloClientSingleton = /* @__PURE__ */ Symbol.for(\n  \"ApolloClientSingleton\"\n);\n\n// src/DataTransportAbstraction/testHelpers.ts\nfunction resetApolloSingletons() {\n  delete window[ApolloClientSingleton];\n}\nfunction WrapApolloProvider(TransportProvider) {\n  const WrappedApolloProvider3 = ({\n    makeClient,\n    children,\n    ...extraProps\n  }) => {\n    const clientRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(void 0);\n    if (!clientRef.current) {\n      {\n        clientRef.current = makeClient();\n      }\n      assertInstance(\n        clientRef.current,\n        WrappedApolloProvider3.info,\n        \"ApolloClient\"\n      );\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_17__.ApolloProvider, { client: clientRef.current }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(\n      TransportProvider,\n      {\n        onQueryEvent: (event) => event.type === \"started\" ? clientRef.current.onQueryStarted(event) : clientRef.current.onQueryProgress(event),\n        rerunSimulatedQueries: clientRef.current.rerunSimulatedQueries,\n        registerDispatchRequestStarted: clientRef.current.watchQueryQueue?.register,\n        ...extraProps\n      },\n      children\n    ));\n  };\n  WrappedApolloProvider3.info = bundle;\n  return WrappedApolloProvider3;\n}\nconst built_for_ssr = true;\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.ssr.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5Ny9ub2RlX21vZHVsZXMvQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nL2Rpc3QvaW5kZXguc3NyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3SjtBQUNmO0FBQ3RFO0FBQzFCO0FBQ2tFO0FBQ3JFO0FBQ3lGO0FBQy9DO0FBQy9COztBQUVqRDtBQUNBLHFEQUFxRCwrREFBVTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsZ0ZBQWE7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUseUVBQVU7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsdUZBQW9CO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELCtEQUFVO0FBQzVEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCO0FBQ0Esb0JBQW9CLCtGQUE0QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsYUFBYSx5RUFBVSxNQUFNO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQ0FBcUMsK0RBQVU7QUFDL0MseUJBQXlCO0FBQ3pCLHFCQUFxQiwrREFBVTtBQUMvQjtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGtDQUFrQyxrRUFBZTtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixTQUFTO0FBQ3JDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQ0FBMkMsb0RBQWE7QUFDeEQ7QUFDQTtBQUNBLHdCQUF3QixpREFBVTtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDJEQUFvQjtBQUMxQztBQUNBLEtBQUs7QUFDTDtBQUNBLHdEQUF3RCxvREFBSztBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsNERBQUcsQ0FBQyx5RUFBSyxDQUFDLDREQUFHO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBLFNBQVMsZ0VBQXNCLENBQUMseUVBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksa0ZBQWtCO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxxRkFBWTtBQUNqRCxNQUFNLHlGQUFnQjtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCLCtFQUFlO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLDhGQUFxQjtBQUN2QixvQkFBb0IsdUZBQWM7QUFDbEMsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx5RkFBZ0I7QUFDeEI7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLGlEQUFpRCx1Q0FBdUM7QUFDeEY7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOENBQU87QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVcsOENBQU87QUFDbEIsZUFBZSwyQkFBMkI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlDQUFpQyxTQUFTLEdBQUcsS0FBSztBQUNsRDtBQUNBLHNCQUFzQixLQUFLLDBDQUEwQyxLQUFLLDJCQUEyQixTQUFTO0FBQzlHO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLGtFQUFjO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFNBQVM7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQSwrQkFBK0Isa0ZBQWtCLHdCQUF3QjtBQUN6RSx5QkFBeUIseUVBQUs7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGFBQWE7QUFDaEM7QUFDQSxZQUFZLHdCQUF3QjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0EsNkJBQTZCLHlFQUFZO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLFNBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFVBQVUsbURBQVM7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sbURBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGNBQWM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyx5RUFBWTtBQUMvQyxnQkFBZ0IsbUNBQW1DO0FBQ25EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsc0JBQXNCLDZDQUFNO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGdEQUFtQixDQUFDLDBFQUFjLElBQUksMkJBQTJCLGtCQUFrQixnREFBbUI7QUFDakk7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU0TztBQUM1TztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5N1xcbm9kZV9tb2R1bGVzXFxAYXBvbGxvXFxjbGllbnQtcmVhY3Qtc3RyZWFtaW5nXFxkaXN0XFxpbmRleC5zc3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBvbGxvTGluaywgSW5NZW1vcnlDYWNoZSBhcyBJbk1lbW9yeUNhY2hlJDEsIE9ic2VydmFibGUgYXMgT2JzZXJ2YWJsZSQxLCBncWwsIEFwb2xsb0NsaWVudCBhcyBBcG9sbG9DbGllbnQkMSB9IGZyb20gJ0BhcG9sbG8vY2xpZW50L2luZGV4LmpzJztcbmltcG9ydCB7IGhhc0RpcmVjdGl2ZXMsIE9ic2VydmFibGUsIHJlbW92ZURpcmVjdGl2ZXNGcm9tRG9jdW1lbnQsIG1lcmdlSW5jcmVtZW50YWxEYXRhLCBwcmludCB9IGZyb20gJ0BhcG9sbG8vY2xpZW50L3V0aWxpdGllcy9pbmRleC5qcyc7XG5pbXBvcnQgeyBjYW5vbmljYWxTdHJpbmdpZnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudC9jYWNoZS9pbmRleC5qcyc7XG5pbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICd0cy1pbnZhcmlhbnQnO1xuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZVJlZiwgdXNlTWVtbywgdXNlQ29udGV4dCwgdXNlU3luY0V4dGVybmFsU3RvcmUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGVxdWFsIH0gZnJvbSAnQHdyeS9lcXVhbGl0eSc7XG5pbXBvcnQgeyBhc3NlcnRXcmFwcGVkUXVlcnlSZWYsIHVud3JhcFF1ZXJ5UmVmLCBnZXRTdXNwZW5zZUNhY2hlLCB3cmFwUXVlcnlSZWYgfSBmcm9tICdAYXBvbGxvL2NsaWVudC9yZWFjdC9pbnRlcm5hbC9pbmRleC5qcyc7XG5pbXBvcnQgeyBBcG9sbG9Qcm92aWRlciwgdXNlQXBvbGxvQ2xpZW50IH0gZnJvbSAnQGFwb2xsby9jbGllbnQvcmVhY3QvaW5kZXguanMnO1xuaW1wb3J0IHsgc3RyaXBJZ25vcmVkQ2hhcmFjdGVycyB9IGZyb20gJ2dyYXBocWwnO1xuXG4vLyBzcmMvQWNjdW11bGF0ZU11bHRpcGFydFJlc3BvbnNlc0xpbmsudHNcbnZhciBBY2N1bXVsYXRlTXVsdGlwYXJ0UmVzcG9uc2VzTGluayA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvTGluayB7XG4gIG1heERlbGF5O1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICBzdXBlcigpO1xuICAgIHRoaXMubWF4RGVsYXkgPSBjb25maWcuY3V0b2ZmRGVsYXk7XG4gIH1cbiAgcmVxdWVzdChvcGVyYXRpb24sIGZvcndhcmQpIHtcbiAgICBpZiAoIWZvcndhcmQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgaXMgbm90IGEgdGVybWluYWwgbGluayFcIik7XG4gICAgfVxuICAgIGNvbnN0IG9wZXJhdGlvbkNvbnRhaW5zTXVsdGlwYXJ0RGlyZWN0aXZlcyA9IGhhc0RpcmVjdGl2ZXMoXG4gICAgICBbXCJkZWZlclwiXSxcbiAgICAgIG9wZXJhdGlvbi5xdWVyeVxuICAgICk7XG4gICAgY29uc3QgdXBzdHJlYW0gPSBmb3J3YXJkKG9wZXJhdGlvbik7XG4gICAgaWYgKCFvcGVyYXRpb25Db250YWluc011bHRpcGFydERpcmVjdGl2ZXMpXG4gICAgICByZXR1cm4gdXBzdHJlYW07XG4gICAgY29uc3QgbWF4RGVsYXkgPSB0aGlzLm1heERlbGF5O1xuICAgIGxldCBhY2N1bXVsYXRlZERhdGEsIG1heERlbGF5VGltZW91dDtcbiAgICByZXR1cm4gbmV3IE9ic2VydmFibGUoKHN1YnNjcmliZXIpID0+IHtcbiAgICAgIGNvbnN0IHVwc3RyZWFtU3Vic2NyaXB0aW9uID0gdXBzdHJlYW0uc3Vic2NyaWJlKHtcbiAgICAgICAgbmV4dDogKHJlc3VsdCkgPT4ge1xuICAgICAgICAgIGlmIChhY2N1bXVsYXRlZERhdGEpIHtcbiAgICAgICAgICAgIGlmIChhY2N1bXVsYXRlZERhdGEuZGF0YSAmJiBcImluY3JlbWVudGFsXCIgaW4gcmVzdWx0KSB7XG4gICAgICAgICAgICAgIGFjY3VtdWxhdGVkRGF0YS5kYXRhID0gbWVyZ2VJbmNyZW1lbnRhbERhdGEoXG4gICAgICAgICAgICAgICAgYWNjdW11bGF0ZWREYXRhLmRhdGEsXG4gICAgICAgICAgICAgICAgcmVzdWx0XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKHJlc3VsdC5kYXRhKSB7XG4gICAgICAgICAgICAgIGFjY3VtdWxhdGVkRGF0YS5kYXRhID0gcmVzdWx0LmRhdGE7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocmVzdWx0LmVycm9ycykge1xuICAgICAgICAgICAgICBhY2N1bXVsYXRlZERhdGEuZXJyb3JzID0gW1xuICAgICAgICAgICAgICAgIC4uLmFjY3VtdWxhdGVkRGF0YS5lcnJvcnMgfHwgW10sXG4gICAgICAgICAgICAgICAgLi4ucmVzdWx0LmVycm9ycyB8fCBbXVxuICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHJlc3VsdC5leHRlbnNpb25zKVxuICAgICAgICAgICAgICBhY2N1bXVsYXRlZERhdGEuZXh0ZW5zaW9ucyA9IHtcbiAgICAgICAgICAgICAgICAuLi5hY2N1bXVsYXRlZERhdGEuZXh0ZW5zaW9ucyxcbiAgICAgICAgICAgICAgICAuLi5yZXN1bHQuZXh0ZW5zaW9uc1xuICAgICAgICAgICAgICB9O1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBhY2N1bXVsYXRlZERhdGEgPSByZXN1bHQ7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmICghbWF4RGVsYXkpIHtcbiAgICAgICAgICAgIGZsdXNoQWNjdW11bGF0ZWREYXRhKCk7XG4gICAgICAgICAgfSBlbHNlIGlmICghbWF4RGVsYXlUaW1lb3V0KSB7XG4gICAgICAgICAgICBtYXhEZWxheVRpbWVvdXQgPSBzZXRUaW1lb3V0KGZsdXNoQWNjdW11bGF0ZWREYXRhLCBtYXhEZWxheSk7XG4gICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBlcnJvcjogKGVycm9yKSA9PiB7XG4gICAgICAgICAgaWYgKG1heERlbGF5VGltZW91dClcbiAgICAgICAgICAgIGNsZWFyVGltZW91dChtYXhEZWxheVRpbWVvdXQpO1xuICAgICAgICAgIHN1YnNjcmliZXIuZXJyb3IoZXJyb3IpO1xuICAgICAgICB9LFxuICAgICAgICBjb21wbGV0ZTogKCkgPT4ge1xuICAgICAgICAgIGlmIChtYXhEZWxheVRpbWVvdXQpIHtcbiAgICAgICAgICAgIGNsZWFyVGltZW91dChtYXhEZWxheVRpbWVvdXQpO1xuICAgICAgICAgICAgZmx1c2hBY2N1bXVsYXRlZERhdGEoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgc3Vic2NyaWJlci5jb21wbGV0ZSgpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIGZ1bmN0aW9uIGZsdXNoQWNjdW11bGF0ZWREYXRhKCkge1xuICAgICAgICBzdWJzY3JpYmVyLm5leHQoYWNjdW11bGF0ZWREYXRhKTtcbiAgICAgICAgc3Vic2NyaWJlci5jb21wbGV0ZSgpO1xuICAgICAgICB1cHN0cmVhbVN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIGZ1bmN0aW9uIGNsZWFuVXAoKSB7XG4gICAgICAgIGNsZWFyVGltZW91dChtYXhEZWxheVRpbWVvdXQpO1xuICAgICAgICB1cHN0cmVhbVN1YnNjcmlwdGlvbi51bnN1YnNjcmliZSgpO1xuICAgICAgfTtcbiAgICB9KTtcbiAgfVxufTtcbmZ1bmN0aW9uIGdldERpcmVjdGl2ZUFyZ3VtZW50VmFsdWUoZGlyZWN0aXZlLCBhcmd1bWVudCkge1xuICByZXR1cm4gZGlyZWN0aXZlLmFyZ3VtZW50cz8uZmluZCgoYXJnKSA9PiBhcmcubmFtZS52YWx1ZSA9PT0gYXJndW1lbnQpPy52YWx1ZTtcbn1cbnZhciBSZW1vdmVNdWx0aXBhcnREaXJlY3RpdmVzTGluayA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvTGluayB7XG4gIHN0cmlwRGlyZWN0aXZlcyA9IFtdO1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICBzdXBlcigpO1xuICAgIGlmIChjb25maWcuc3RyaXBEZWZlciAhPT0gZmFsc2UpXG4gICAgICB0aGlzLnN0cmlwRGlyZWN0aXZlcy5wdXNoKFwiZGVmZXJcIik7XG4gIH1cbiAgcmVxdWVzdChvcGVyYXRpb24sIGZvcndhcmQpIHtcbiAgICBpZiAoIWZvcndhcmQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoaXMgaXMgbm90IGEgdGVybWluYWwgbGluayFcIik7XG4gICAgfVxuICAgIGNvbnN0IHsgcXVlcnkgfSA9IG9wZXJhdGlvbjtcbiAgICBsZXQgbW9kaWZpZWRRdWVyeSA9IHF1ZXJ5O1xuICAgIG1vZGlmaWVkUXVlcnkgPSByZW1vdmVEaXJlY3RpdmVzRnJvbURvY3VtZW50KFxuICAgICAgdGhpcy5zdHJpcERpcmVjdGl2ZXMubWFwKChkaXJlY3RpdmUpID0+ICh7XG4gICAgICAgIHRlc3Qobm9kZSkge1xuICAgICAgICAgIGxldCBzaG91bGRTdHJpcCA9IG5vZGUua2luZCA9PT0gXCJEaXJlY3RpdmVcIiAmJiBub2RlLm5hbWUudmFsdWUgPT09IGRpcmVjdGl2ZTtcbiAgICAgICAgICBjb25zdCBsYWJlbCA9IGdldERpcmVjdGl2ZUFyZ3VtZW50VmFsdWUobm9kZSwgXCJsYWJlbFwiKTtcbiAgICAgICAgICBpZiAobGFiZWw/LmtpbmQgPT09IFwiU3RyaW5nVmFsdWVcIiAmJiBsYWJlbC52YWx1ZS5zdGFydHNXaXRoKFwiU3NyRG9udFN0cmlwXCIpKSB7XG4gICAgICAgICAgICBzaG91bGRTdHJpcCA9IGZhbHNlO1xuICAgICAgICAgIH1cbiAgICAgICAgICByZXR1cm4gc2hvdWxkU3RyaXA7XG4gICAgICAgIH0sXG4gICAgICAgIHJlbW92ZTogdHJ1ZVxuICAgICAgfSkpLmNvbmNhdCh7XG4gICAgICAgIHRlc3Qobm9kZSkge1xuICAgICAgICAgIGlmIChub2RlLmtpbmQgIT09IFwiRGlyZWN0aXZlXCIpXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgY29uc3QgbGFiZWwgPSBnZXREaXJlY3RpdmVBcmd1bWVudFZhbHVlKG5vZGUsIFwibGFiZWxcIik7XG4gICAgICAgICAgcmV0dXJuIGxhYmVsPy5raW5kID09PSBcIlN0cmluZ1ZhbHVlXCIgJiYgbGFiZWwudmFsdWUuc3RhcnRzV2l0aChcIlNzclN0cmlwXCIpO1xuICAgICAgICB9LFxuICAgICAgICByZW1vdmU6IHRydWVcbiAgICAgIH0pLFxuICAgICAgbW9kaWZpZWRRdWVyeVxuICAgICk7XG4gICAgaWYgKG1vZGlmaWVkUXVlcnkgPT09IG51bGwpIHtcbiAgICAgIHJldHVybiBPYnNlcnZhYmxlLm9mKHt9KTtcbiAgICB9XG4gICAgb3BlcmF0aW9uLnF1ZXJ5ID0gbW9kaWZpZWRRdWVyeTtcbiAgICByZXR1cm4gZm9yd2FyZChvcGVyYXRpb24pO1xuICB9XG59O1xudmFyIFNTUk11bHRpcGFydExpbmsgPSBjbGFzcyBleHRlbmRzIEFwb2xsb0xpbmsge1xuICBjb25zdHJ1Y3Rvcihjb25maWcgPSB7fSkge1xuICAgIGNvbnN0IGNvbWJpbmVkID0gQXBvbGxvTGluay5mcm9tKFtcbiAgICAgIG5ldyBSZW1vdmVNdWx0aXBhcnREaXJlY3RpdmVzTGluayh7XG4gICAgICAgIHN0cmlwRGVmZXI6IGNvbmZpZy5zdHJpcERlZmVyXG4gICAgICB9KSxcbiAgICAgIG5ldyBBY2N1bXVsYXRlTXVsdGlwYXJ0UmVzcG9uc2VzTGluayh7XG4gICAgICAgIGN1dG9mZkRlbGF5OiBjb25maWcuY3V0b2ZmRGVsYXkgfHwgMFxuICAgICAgfSlcbiAgICBdKTtcbiAgICBzdXBlcihjb21iaW5lZC5yZXF1ZXN0KTtcbiAgfVxufTtcblxuLy8gc3JjL2J1bmRsZUluZm8udHNcbnZhciBidW5kbGUgPSB7XG4gIHBrZzogXCJAYXBvbGxvL2NsaWVudC1yZWFjdC1zdHJlYW1pbmdcIlxufTtcbnZhciBzb3VyY2VTeW1ib2wgPSBTeW1ib2wuZm9yKFwiYXBvbGxvLnNvdXJjZV9wYWNrYWdlXCIpO1xuXG4vLyBzcmMvRGF0YVRyYW5zcG9ydEFic3RyYWN0aW9uL1dyYXBwZWRJbk1lbW9yeUNhY2hlLnRzeFxudmFyIEluTWVtb3J5Q2FjaGUgPSBjbGFzcyBleHRlbmRzIEluTWVtb3J5Q2FjaGUkMSB7XG4gIC8qKlxuICAgKiBJbmZvcm1hdGlvbiBhYm91dCB0aGUgY3VycmVudCBwYWNrYWdlIGFuZCBpdCdzIGV4cG9ydCBuYW1lcywgZm9yIHVzZSBpbiBlcnJvciBtZXNzYWdlcy5cbiAgICpcbiAgICogQGludGVybmFsXG4gICAqL1xuICBzdGF0aWMgaW5mbyA9IGJ1bmRsZTtcbiAgW3NvdXJjZVN5bWJvbF07XG4gIGNvbnN0cnVjdG9yKGNvbmZpZykge1xuICAgIHN1cGVyKGNvbmZpZyk7XG4gICAgY29uc3QgaW5mbyA9IHRoaXMuY29uc3RydWN0b3IuaW5mbztcbiAgICB0aGlzW3NvdXJjZVN5bWJvbF0gPSBgJHtpbmZvLnBrZ306SW5NZW1vcnlDYWNoZWA7XG4gIH1cbn07XG5cbi8vIHNyYy9EYXRhVHJhbnNwb3J0QWJzdHJhY3Rpb24vYmFja3ByZXNzdXJlZENhbGxiYWNrLnRzXG5mdW5jdGlvbiBjcmVhdGVCYWNrcHJlc3N1cmVkQ2FsbGJhY2soKSB7XG4gIGNvbnN0IHF1ZXVlID0gW107XG4gIGxldCBwdXNoID0gcXVldWUucHVzaC5iaW5kKHF1ZXVlKTtcbiAgcmV0dXJuIHtcbiAgICBwdXNoOiAodmFsdWUpID0+IHB1c2godmFsdWUpLFxuICAgIHJlZ2lzdGVyOiAoY2FsbGJhY2spID0+IHtcbiAgICAgIGlmIChjYWxsYmFjaykge1xuICAgICAgICBwdXNoID0gY2FsbGJhY2s7XG4gICAgICAgIHdoaWxlIChxdWV1ZS5sZW5ndGgpIHtcbiAgICAgICAgICBjYWxsYmFjayhxdWV1ZS5zaGlmdCgpKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcHVzaCA9IHF1ZXVlLnB1c2guYmluZChxdWV1ZSk7XG4gICAgICB9XG4gICAgfVxuICB9O1xufVxudmFyIERhdGFUcmFuc3BvcnRDb250ZXh0ID0gLyogQF9fUFVSRV9fICovIGNyZWF0ZUNvbnRleHQobnVsbCk7XG52YXIgQ0xFQU4gPSB7fTtcbmZ1bmN0aW9uIHVzZVRyYW5zcG9ydFZhbHVlKHZhbHVlKSB7XG4gIGNvbnN0IGRhdGFUcmFuc3BvcnQgPSB1c2VDb250ZXh0KERhdGFUcmFuc3BvcnRDb250ZXh0KTtcbiAgaWYgKCFkYXRhVHJhbnNwb3J0KVxuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIFwidXNlVHJhbnNwb3J0VmFsdWUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIHN0cmVhbWluZy1zcGVjaWZpYyBBcG9sbG9Qcm92aWRlclwiXG4gICAgKTtcbiAgY29uc3QgdmFsdWVSZWYgPSBkYXRhVHJhbnNwb3J0LnVzZVN0YXRpY1ZhbHVlUmVmKHZhbHVlKTtcbiAgY29uc3Qgd2hpY2hSZXN1bHQgPSB1c2VTeW5jRXh0ZXJuYWxTdG9yZShcbiAgICAoKSA9PiAoKSA9PiB7XG4gICAgfSxcbiAgICAoKSA9PiAwIC8qIGNsaWVudCAqLyxcbiAgICAoKSA9PiB2YWx1ZVJlZi5jdXJyZW50ID09PSBDTEVBTiA/IDAgLyogY2xpZW50ICovIDogZXF1YWwodmFsdWUsIHZhbHVlUmVmLmN1cnJlbnQpID8gMCAvKiBjbGllbnQgKi8gOiAxIC8qIHNlcnZlciAqL1xuICApO1xuICBpZiAod2hpY2hSZXN1bHQgPT09IDAgLyogY2xpZW50ICovKSB7XG4gICAgdmFsdWVSZWYuY3VycmVudCA9IENMRUFOO1xuICB9XG4gIHJldHVybiB3aGljaFJlc3VsdCA9PT0gMSAvKiBzZXJ2ZXIgKi8gPyB2YWx1ZVJlZi5jdXJyZW50IDogdmFsdWU7XG59XG5mdW5jdGlvbiBzZXJpYWxpemVPcHRpb25zKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHtcbiAgICAuLi5vcHRpb25zLFxuICAgIHF1ZXJ5OiBwcmludE1pbmlmaWVkKG9wdGlvbnMucXVlcnkpXG4gIH07XG59XG5mdW5jdGlvbiBkZXNlcmlhbGl6ZU9wdGlvbnMob3B0aW9ucykge1xuICByZXR1cm4ge1xuICAgIC4uLm9wdGlvbnMsXG4gICAgLy8gYGdxbGAgbWVtb2l6ZXMgcmVzdWx0cywgYnV0IGJhc2VkIG9uIHRoZSBpbnB1dCBzdHJpbmcuXG4gICAgLy8gV2UgcGFyc2Utc3RyaW5naWZ5LXBhcnNlIGhlcmUgdG8gZW5zdXJlIHRoYXQgb3VyIG1pbmlmaWVkIHF1ZXJ5XG4gICAgLy8gaGFzIHRoZSBiZXN0IGNoYW5jZSBvZiBiZWluZyB0aGUgcmVmZXJlbnRpYWwgc2FtZSBxdWVyeSBhcyB0aGUgb25lIHVzZWQgaW5cbiAgICAvLyBjbGllbnQtc2lkZSBjb2RlLlxuICAgIHF1ZXJ5OiBncWwocHJpbnQoZ3FsKG9wdGlvbnMucXVlcnkpKSlcbiAgfTtcbn1cbmZ1bmN0aW9uIHByaW50TWluaWZpZWQocXVlcnkpIHtcbiAgcmV0dXJuIHN0cmlwSWdub3JlZENoYXJhY3RlcnMocHJpbnQocXVlcnkpKTtcbn1cbmZ1bmN0aW9uIHJldml2ZVRyYW5zcG9ydGVkUXVlcnlSZWYocXVlcnlSZWYsIGNsaWVudCkge1xuICBjb25zdCBoeWRyYXRlZE9wdGlvbnMgPSBkZXNlcmlhbGl6ZU9wdGlvbnMocXVlcnlSZWYub3B0aW9ucyk7XG4gIGNvbnN0IGNhY2hlS2V5ID0gW1xuICAgIGh5ZHJhdGVkT3B0aW9ucy5xdWVyeSxcbiAgICBjYW5vbmljYWxTdHJpbmdpZnkoaHlkcmF0ZWRPcHRpb25zLnZhcmlhYmxlcyksXG4gICAgcXVlcnlSZWYucXVlcnlLZXlcbiAgXTtcbiAgaWYgKHF1ZXJ5UmVmLl9fdHJhbnNwb3J0ZWRRdWVyeVJlZiA9PT0gdHJ1ZSkge1xuICAgIHF1ZXJ5UmVmLl9fdHJhbnNwb3J0ZWRRdWVyeVJlZiA9IHdyYXBRdWVyeVJlZihcbiAgICAgIGdldFN1c3BlbnNlQ2FjaGUoY2xpZW50KS5nZXRRdWVyeVJlZihcbiAgICAgICAgY2FjaGVLZXksXG4gICAgICAgICgpID0+IGNsaWVudC53YXRjaFF1ZXJ5KGh5ZHJhdGVkT3B0aW9ucylcbiAgICAgIClcbiAgICApO1xuICB9XG4gIHJldHVybiBbcXVlcnlSZWYuX190cmFuc3BvcnRlZFF1ZXJ5UmVmLCBjYWNoZUtleV07XG59XG5mdW5jdGlvbiBpc1RyYW5zcG9ydGVkUXVlcnlSZWYocXVlcnlSZWYpIHtcbiAgcmV0dXJuIFwiX190cmFuc3BvcnRlZFF1ZXJ5UmVmXCIgaW4gcXVlcnlSZWY7XG59XG5mdW5jdGlvbiB1c2VXcmFwVHJhbnNwb3J0ZWRRdWVyeVJlZihxdWVyeVJlZikge1xuICBjb25zdCBjbGllbnQgPSB1c2VBcG9sbG9DbGllbnQoKTtcbiAgbGV0IGNhY2hlS2V5O1xuICBsZXQgaXNUcmFuc3BvcnRlZDtcbiAgaWYgKGlzVHJhbnNwb3J0ZWQgPSBpc1RyYW5zcG9ydGVkUXVlcnlSZWYocXVlcnlSZWYpKSB7XG4gICAgW3F1ZXJ5UmVmLCBjYWNoZUtleV0gPSByZXZpdmVUcmFuc3BvcnRlZFF1ZXJ5UmVmKHF1ZXJ5UmVmLCBjbGllbnQpO1xuICB9XG4gIGFzc2VydFdyYXBwZWRRdWVyeVJlZihxdWVyeVJlZik7XG4gIGNvbnN0IHVud3JhcHBlZCA9IHVud3JhcFF1ZXJ5UmVmKHF1ZXJ5UmVmKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzVHJhbnNwb3J0ZWQpXG4gICAgICByZXR1cm47XG4gICAgaWYgKGNhY2hlS2V5KSB7XG4gICAgICBpZiAodW53cmFwcGVkLmRpc3Bvc2VkKSB7XG4gICAgICAgIGdldFN1c3BlbnNlQ2FjaGUoY2xpZW50KS5hZGQoY2FjaGVLZXksIHVud3JhcHBlZCk7XG4gICAgICB9XG4gICAgfVxuICB9KTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNUcmFuc3BvcnRlZCkge1xuICAgICAgcmV0dXJuIHVud3JhcHBlZC5zb2Z0UmV0YWluKCk7XG4gICAgfVxuICB9LCBbaXNUcmFuc3BvcnRlZCwgdW53cmFwcGVkXSk7XG4gIHJldHVybiBxdWVyeVJlZjtcbn1cbnZhciBob29rV3JhcHBlcnMgPSB7XG4gIHVzZUZyYWdtZW50KG9yaWdfdXNlRnJhZ21lbnQpIHtcbiAgICByZXR1cm4gd3JhcChvcmlnX3VzZUZyYWdtZW50LCBbXCJkYXRhXCIsIFwiY29tcGxldGVcIiwgXCJtaXNzaW5nXCJdKTtcbiAgfSxcbiAgdXNlUXVlcnkob3JpZ191c2VRdWVyeSkge1xuICAgIHJldHVybiB3cmFwKFxuICAgICAgKHF1ZXJ5LCBvcHRpb25zKSA9PiBvcmlnX3VzZVF1ZXJ5KHF1ZXJ5LCB7IC4uLm9wdGlvbnMsIGZldGNoUG9saWN5OiBcImNhY2hlLW9ubHlcIiB9KSAsXG4gICAgICBbXCJkYXRhXCIsIFwibG9hZGluZ1wiLCBcIm5ldHdvcmtTdGF0dXNcIiwgXCJjYWxsZWRcIl1cbiAgICApO1xuICB9LFxuICB1c2VTdXNwZW5zZVF1ZXJ5KG9yaWdfdXNlU3VzcGVuc2VRdWVyeSkge1xuICAgIHJldHVybiB3cmFwKG9yaWdfdXNlU3VzcGVuc2VRdWVyeSwgW1wiZGF0YVwiLCBcIm5ldHdvcmtTdGF0dXNcIl0pO1xuICB9LFxuICB1c2VSZWFkUXVlcnkob3JpZ191c2VSZWFkUXVlcnkpIHtcbiAgICByZXR1cm4gd3JhcChcbiAgICAgIChxdWVyeVJlZikgPT4ge1xuICAgICAgICByZXR1cm4gb3JpZ191c2VSZWFkUXVlcnkodXNlV3JhcFRyYW5zcG9ydGVkUXVlcnlSZWYocXVlcnlSZWYpKTtcbiAgICAgIH0sXG4gICAgICBbXCJkYXRhXCIsIFwibmV0d29ya1N0YXR1c1wiXVxuICAgICk7XG4gIH0sXG4gIHVzZVF1ZXJ5UmVmSGFuZGxlcnMob3JpZ191c2VRdWVyeVJlZkhhbmRsZXJzKSB7XG4gICAgcmV0dXJuIHdyYXAoKHF1ZXJ5UmVmKSA9PiB7XG4gICAgICByZXR1cm4gb3JpZ191c2VRdWVyeVJlZkhhbmRsZXJzKHVzZVdyYXBUcmFuc3BvcnRlZFF1ZXJ5UmVmKHF1ZXJ5UmVmKSk7XG4gICAgfSwgW10pO1xuICB9XG59O1xuZnVuY3Rpb24gd3JhcCh1c2VGbiwgdHJhbnNwb3J0S2V5cykge1xuICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSB1c2VGbiguLi5hcmdzKTtcbiAgICBpZiAodHJhbnNwb3J0S2V5cy5sZW5ndGggPT0gMCkge1xuICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG4gICAgY29uc3QgZm9yVHJhbnNwb3J0ID0gdXNlTWVtbygoKSA9PiB7XG4gICAgICBjb25zdCB0cmFuc3BvcnQgPSB7fTtcbiAgICAgIGZvciAoY29uc3Qga2V5IG9mIHRyYW5zcG9ydEtleXMpIHtcbiAgICAgICAgdHJhbnNwb3J0W2tleV0gPSByZXN1bHRba2V5XTtcbiAgICAgIH1cbiAgICAgIHJldHVybiB0cmFuc3BvcnQ7XG4gICAgfSwgW3Jlc3VsdF0pO1xuICAgIGNvbnN0IHRyYW5zcG9ydGVkID0gdXNlVHJhbnNwb3J0VmFsdWUoZm9yVHJhbnNwb3J0KTtcbiAgICByZXR1cm4gdXNlTWVtbyhcbiAgICAgICgpID0+ICh7IC4uLnJlc3VsdCwgLi4udHJhbnNwb3J0ZWQgfSksXG4gICAgICBbcmVzdWx0LCB0cmFuc3BvcnRlZF1cbiAgICApO1xuICB9O1xufVxuXG4vLyBzcmMvYXNzZXJ0SW5zdGFuY2UudHNcbmZ1bmN0aW9uIGFzc2VydEluc3RhbmNlKHZhbHVlLCBpbmZvLCBuYW1lKSB7XG4gIGlmICh2YWx1ZVtzb3VyY2VTeW1ib2xdICE9PSBgJHtpbmZvLnBrZ306JHtuYW1lfWApIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBgV2hlbiB1c2luZyBcXGAke25hbWV9XFxgIGluIHN0cmVhbWluZyBTU1IsIHlvdSBtdXN0IHVzZSB0aGUgXFxgJHtuYW1lfVxcYCBleHBvcnQgcHJvdmlkZWQgYnkgXFxgXCIke2luZm8ucGtnfVwiXFxgLmBcbiAgICApO1xuICB9XG59XG5cbi8vIHNyYy9EYXRhVHJhbnNwb3J0QWJzdHJhY3Rpb24vV3JhcHBlZEFwb2xsb0NsaWVudC50c3hcbmZ1bmN0aW9uIGdldFF1ZXJ5TWFuYWdlcihjbGllbnQpIHtcbiAgcmV0dXJuIGNsaWVudFtcInF1ZXJ5TWFuYWdlclwiXTtcbn1cbmZ1bmN0aW9uIGdldFRyaWVDb25zdHJ1Y3RvcihjbGllbnQpIHtcbiAgcmV0dXJuIGdldFF1ZXJ5TWFuYWdlcihjbGllbnQpW1wiaW5GbGlnaHRMaW5rT2JzZXJ2YWJsZXNcIl0uY29uc3RydWN0b3I7XG59XG52YXIgd3JhcHBlcnMgPSBTeW1ib2wuZm9yKFwiYXBvbGxvLmhvb2sud3JhcHBlcnNcIik7XG52YXIgQXBvbGxvQ2xpZW50QmFzZSA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvQ2xpZW50JDEge1xuICAvKipcbiAgICogSW5mb3JtYXRpb24gYWJvdXQgdGhlIGN1cnJlbnQgcGFja2FnZSBhbmQgaXQncyBleHBvcnQgbmFtZXMsIGZvciB1c2UgaW4gZXJyb3IgbWVzc2FnZXMuXG4gICAqXG4gICAqIEBpbnRlcm5hbFxuICAgKi9cbiAgc3RhdGljIGluZm8gPSBidW5kbGU7XG4gIFtzb3VyY2VTeW1ib2xdO1xuICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgY29uc3Qgd2FybmluZ3MgPSBbXTtcbiAgICBpZiAoXCJzc3JNb2RlXCIgaW4gb3B0aW9ucykge1xuICAgICAgZGVsZXRlIG9wdGlvbnMuc3NyTW9kZTtcbiAgICAgIHdhcm5pbmdzLnB1c2goXG4gICAgICAgIFwiVGhlIGBzc3JNb2RlYCBvcHRpb24gaXMgbm90IHN1cHBvcnRlZCBpbiAlcy4gUGxlYXNlIHJlbW92ZSBpdCBmcm9tIHlvdXIgJXMgY29uc3RydWN0b3Igb3B0aW9ucy5cIlxuICAgICAgKTtcbiAgICB9XG4gICAgaWYgKFwic3NyRm9yY2VGZXRjaERlbGF5XCIgaW4gb3B0aW9ucykge1xuICAgICAgZGVsZXRlIG9wdGlvbnMuc3NyRm9yY2VGZXRjaERlbGF5O1xuICAgICAgd2FybmluZ3MucHVzaChcbiAgICAgICAgXCJUaGUgYHNzckZvcmNlRmV0Y2hEZWxheWAgb3B0aW9uIGlzIG5vdCBzdXBwb3J0ZWQgaW4gJXMuIFBsZWFzZSByZW1vdmUgaXQgZnJvbSB5b3VyICVzIGNvbnN0cnVjdG9yIG9wdGlvbnMuXCJcbiAgICAgICk7XG4gICAgfVxuICAgIHN1cGVyKFxuICAgICAge1xuICAgICAgICBjb25uZWN0VG9EZXZUb29sczogZmFsc2UsXG4gICAgICAgIC4uLm9wdGlvbnNcbiAgICAgIH0gXG4gICAgKTtcbiAgICBjb25zdCBpbmZvID0gdGhpcy5jb25zdHJ1Y3Rvci5pbmZvO1xuICAgIHRoaXNbc291cmNlU3ltYm9sXSA9IGAke2luZm8ucGtnfTpBcG9sbG9DbGllbnRgO1xuICAgIGZvciAoY29uc3Qgd2FybmluZyBvZiB3YXJuaW5ncykge1xuICAgICAgY29uc29sZS53YXJuKHdhcm5pbmcsIGluZm8ucGtnLCBcIkFwb2xsb0NsaWVudFwiKTtcbiAgICB9XG4gICAgYXNzZXJ0SW5zdGFuY2UoXG4gICAgICB0aGlzLmNhY2hlLFxuICAgICAgaW5mbyxcbiAgICAgIFwiSW5NZW1vcnlDYWNoZVwiXG4gICAgKTtcbiAgfVxufTtcbnZhciBBcG9sbG9DbGllbnRDbGllbnRCYXNlSW1wbCA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvQ2xpZW50QmFzZSB7XG4gIGNvbnN0cnVjdG9yKG9wdGlvbnMpIHtcbiAgICBzdXBlcihvcHRpb25zKTtcbiAgICB0aGlzLm9uUXVlcnlTdGFydGVkID0gdGhpcy5vblF1ZXJ5U3RhcnRlZC5iaW5kKHRoaXMpO1xuICAgIGdldFF1ZXJ5TWFuYWdlcih0aGlzKVt3cmFwcGVyc10gPSBob29rV3JhcHBlcnM7XG4gIH1cbiAgc2ltdWxhdGVkU3RyZWFtaW5nUXVlcmllcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG4gIHRyYW5zcG9ydGVkUXVlcnlPcHRpb25zID0gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKTtcbiAgaWRlbnRpZnlVbmlxdWVRdWVyeShvcHRpb25zKSB7XG4gICAgY29uc3QgdHJhbnNmb3JtZWREb2N1bWVudCA9IHRoaXMuZG9jdW1lbnRUcmFuc2Zvcm0udHJhbnNmb3JtRG9jdW1lbnQoXG4gICAgICBvcHRpb25zLnF1ZXJ5XG4gICAgKTtcbiAgICBjb25zdCBxdWVyeU1hbmFnZXIgPSBnZXRRdWVyeU1hbmFnZXIodGhpcyk7XG4gICAgY29uc3QgeyBzZXJ2ZXJRdWVyeSB9ID0gcXVlcnlNYW5hZ2VyLmdldERvY3VtZW50SW5mbyh0cmFuc2Zvcm1lZERvY3VtZW50KTtcbiAgICBpZiAoIXNlcnZlclF1ZXJ5KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJjb3VsZCBub3QgaWRlbnRpZnkgdW5pcXVlIHF1ZXJ5XCIpO1xuICAgIH1cbiAgICBjb25zdCBjYW5vbmljYWxWYXJpYWJsZXMgPSBjYW5vbmljYWxTdHJpbmdpZnkob3B0aW9ucy52YXJpYWJsZXMgfHwge30pO1xuICAgIGNvbnN0IGNhY2hlS2V5QXJyID0gW3ByaW50KHNlcnZlclF1ZXJ5KSwgY2Fub25pY2FsVmFyaWFibGVzXTtcbiAgICBjb25zdCBjYWNoZUtleSA9IEpTT04uc3RyaW5naWZ5KGNhY2hlS2V5QXJyKTtcbiAgICByZXR1cm4ge1xuICAgICAgY2FjaGVLZXksXG4gICAgICBjYWNoZUtleUFyclxuICAgIH07XG4gIH1cbiAgb25RdWVyeVN0YXJ0ZWQoeyBvcHRpb25zLCBpZCB9KSB7XG4gICAgY29uc3QgaHlkcmF0ZWRPcHRpb25zID0gZGVzZXJpYWxpemVPcHRpb25zKG9wdGlvbnMpO1xuICAgIGNvbnN0IHsgY2FjaGVLZXksIGNhY2hlS2V5QXJyIH0gPSB0aGlzLmlkZW50aWZ5VW5pcXVlUXVlcnkoaHlkcmF0ZWRPcHRpb25zKTtcbiAgICB0aGlzLnRyYW5zcG9ydGVkUXVlcnlPcHRpb25zLnNldChpZCwgaHlkcmF0ZWRPcHRpb25zKTtcbiAgICBjb25zdCBxdWVyeU1hbmFnZXIgPSBnZXRRdWVyeU1hbmFnZXIodGhpcyk7XG4gICAgaWYgKCFxdWVyeU1hbmFnZXJbXCJpbkZsaWdodExpbmtPYnNlcnZhYmxlc1wiXS5wZWVrQXJyYXkoY2FjaGVLZXlBcnIpPy5vYnNlcnZhYmxlKSB7XG4gICAgICBsZXQgc2ltdWxhdGVkU3RyZWFtaW5nUXVlcnksIGZldGNoQ2FuY2VsRm47XG4gICAgICBjb25zdCBjbGVhbnVwID0gKCkgPT4ge1xuICAgICAgICBpZiAocXVlcnlNYW5hZ2VyW1wiZmV0Y2hDYW5jZWxGbnNcIl0uZ2V0KGNhY2hlS2V5KSA9PT0gZmV0Y2hDYW5jZWxGbilcbiAgICAgICAgICBxdWVyeU1hbmFnZXJbXCJmZXRjaENhbmNlbEZuc1wiXS5kZWxldGUoY2FjaGVLZXkpO1xuICAgICAgICBxdWVyeU1hbmFnZXJbXCJpbkZsaWdodExpbmtPYnNlcnZhYmxlc1wiXS5yZW1vdmVBcnJheShjYWNoZUtleUFycik7XG4gICAgICAgIGlmICh0aGlzLnNpbXVsYXRlZFN0cmVhbWluZ1F1ZXJpZXMuZ2V0KGlkKSA9PT0gc2ltdWxhdGVkU3RyZWFtaW5nUXVlcnkpXG4gICAgICAgICAgdGhpcy5zaW11bGF0ZWRTdHJlYW1pbmdRdWVyaWVzLmRlbGV0ZShpZCk7XG4gICAgICB9O1xuICAgICAgY29uc3QgcHJvbWlzZSA9IG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgdGhpcy5zaW11bGF0ZWRTdHJlYW1pbmdRdWVyaWVzLnNldChcbiAgICAgICAgICBpZCxcbiAgICAgICAgICBzaW11bGF0ZWRTdHJlYW1pbmdRdWVyeSA9IHtcbiAgICAgICAgICAgIHJlc29sdmUsXG4gICAgICAgICAgICByZWplY3QsXG4gICAgICAgICAgICBvcHRpb25zOiBoeWRyYXRlZE9wdGlvbnNcbiAgICAgICAgICB9XG4gICAgICAgICk7XG4gICAgICB9KTtcbiAgICAgIHByb21pc2UudGhlbihjbGVhbnVwLCBjbGVhbnVwKTtcbiAgICAgIGNvbnN0IG9ic2VydmFibGUgPSBuZXcgT2JzZXJ2YWJsZSQxKChvYnNlcnZlcikgPT4ge1xuICAgICAgICBwcm9taXNlLnRoZW4oKHJlc3VsdCkgPT4ge1xuICAgICAgICAgIG9ic2VydmVyLm5leHQocmVzdWx0KTtcbiAgICAgICAgICBvYnNlcnZlci5jb21wbGV0ZSgpO1xuICAgICAgICB9KS5jYXRjaCgoZXJyKSA9PiB7XG4gICAgICAgICAgb2JzZXJ2ZXIuZXJyb3IoZXJyKTtcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICAgIHF1ZXJ5TWFuYWdlcltcImluRmxpZ2h0TGlua09ic2VydmFibGVzXCJdLmxvb2t1cEFycmF5KFxuICAgICAgICBjYWNoZUtleUFyclxuICAgICAgKS5vYnNlcnZhYmxlID0gb2JzZXJ2YWJsZTtcbiAgICAgIHF1ZXJ5TWFuYWdlcltcImZldGNoQ2FuY2VsRm5zXCJdLnNldChcbiAgICAgICAgY2FjaGVLZXksXG4gICAgICAgIGZldGNoQ2FuY2VsRm4gPSAocmVhc29uKSA9PiB7XG4gICAgICAgICAgY29uc3QgeyByZWplY3QgfSA9IHRoaXMuc2ltdWxhdGVkU3RyZWFtaW5nUXVlcmllcy5nZXQoaWQpID8/IHt9O1xuICAgICAgICAgIGlmIChyZWplY3QpIHtcbiAgICAgICAgICAgIHJlamVjdChyZWFzb24pO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjbGVhbnVwKCk7XG4gICAgICAgIH1cbiAgICAgICk7XG4gICAgfVxuICB9XG4gIG9uUXVlcnlQcm9ncmVzcyA9IChldmVudCkgPT4ge1xuICAgIGNvbnN0IHF1ZXJ5SW5mbyA9IHRoaXMuc2ltdWxhdGVkU3RyZWFtaW5nUXVlcmllcy5nZXQoZXZlbnQuaWQpO1xuICAgIGlmIChldmVudC50eXBlID09PSBcImRhdGFcIikge1xuICAgICAgcXVlcnlJbmZvPy5yZXNvbHZlPy4oe1xuICAgICAgICBkYXRhOiBldmVudC5yZXN1bHQuZGF0YVxuICAgICAgfSk7XG4gICAgICBjb25zdCBvcHRpb25zID0gdGhpcy50cmFuc3BvcnRlZFF1ZXJ5T3B0aW9ucy5nZXQoZXZlbnQuaWQpO1xuICAgICAgaWYgKG9wdGlvbnMpIHtcbiAgICAgICAgdGhpcy5jYWNoZS53cml0ZVF1ZXJ5KHtcbiAgICAgICAgICBxdWVyeTogb3B0aW9ucy5xdWVyeSxcbiAgICAgICAgICBkYXRhOiBldmVudC5yZXN1bHQuZGF0YSxcbiAgICAgICAgICB2YXJpYWJsZXM6IG9wdGlvbnMudmFyaWFibGVzXG4gICAgICAgIH0pO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoZXZlbnQudHlwZSA9PT0gXCJlcnJvclwiKSB7XG4gICAgICBpZiAocXVlcnlJbmZvKSB7XG4gICAgICAgIHRoaXMuc2ltdWxhdGVkU3RyZWFtaW5nUXVlcmllcy5kZWxldGUoZXZlbnQuaWQpO1xuICAgICAgICB7XG4gICAgICAgICAgaW52YXJpYW50LmRlYnVnKFxuICAgICAgICAgICAgXCJRdWVyeSBmYWlsZWQgdXBzdHJlYW0sIHdpbGwgZmFpbCBpdCBkdXJpbmcgU1NSIGFuZCByZXJ1biBpdCBpbiB0aGUgYnJvd3NlcjpcIixcbiAgICAgICAgICAgIHF1ZXJ5SW5mby5vcHRpb25zXG4gICAgICAgICAgKTtcbiAgICAgICAgICBxdWVyeUluZm8/LnJlamVjdD8uKG5ldyBFcnJvcihcIlF1ZXJ5IGZhaWxlZCB1cHN0cmVhbS5cIikpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICB0aGlzLnRyYW5zcG9ydGVkUXVlcnlPcHRpb25zLmRlbGV0ZShldmVudC5pZCk7XG4gICAgfSBlbHNlIGlmIChldmVudC50eXBlID09PSBcImNvbXBsZXRlXCIpIHtcbiAgICAgIHRoaXMudHJhbnNwb3J0ZWRRdWVyeU9wdGlvbnMuZGVsZXRlKGV2ZW50LmlkKTtcbiAgICB9XG4gIH07XG4gIC8qKlxuICAgKiBDYW4gYmUgY2FsbGVkIHdoZW4gdGhlIHN0cmVhbSBjbG9zZWQgdW5leHBlY3RlZGx5IHdoaWxlIHRoZXJlIG1pZ2h0IHN0aWxsIGJlIHVucmVzb2x2ZWRcbiAgICogc2ltdWxhdGVkIHNlcnZlci1zaWRlIHF1ZXJpZXMgZ29pbmcgb24uXG4gICAqIFRob3NlIHF1ZXJpZXMgd2lsbCBiZSBjYW5jZWxsZWQgYW5kIHRoZW4gcmUtcnVuIGluIHRoZSBicm93c2VyLlxuICAgKi9cbiAgcmVydW5TaW11bGF0ZWRRdWVyaWVzID0gKCkgPT4ge1xuICAgIGZvciAoY29uc3QgW2lkLCBxdWVyeUluZm9dIG9mIHRoaXMuc2ltdWxhdGVkU3RyZWFtaW5nUXVlcmllcykge1xuICAgICAgdGhpcy5zaW11bGF0ZWRTdHJlYW1pbmdRdWVyaWVzLmRlbGV0ZShpZCk7XG4gICAgICBpbnZhcmlhbnQuZGVidWcoXG4gICAgICAgIFwic3RyZWFtaW5nIGNvbm5lY3Rpb24gY2xvc2VkIGJlZm9yZSBzZXJ2ZXIgcXVlcnkgY291bGQgYmUgZnVsbHkgdHJhbnNwb3J0ZWQsIHJlcnVubmluZzpcIixcbiAgICAgICAgcXVlcnlJbmZvLm9wdGlvbnNcbiAgICAgICk7XG4gICAgICB0aGlzLnJlcnVuU2ltdWxhdGVkUXVlcnkocXVlcnlJbmZvKTtcbiAgICB9XG4gIH07XG4gIHJlcnVuU2ltdWxhdGVkUXVlcnkgPSAocXVlcnlJbmZvKSA9PiB7XG4gICAgY29uc3QgcXVlcnlNYW5hZ2VyID0gZ2V0UXVlcnlNYW5hZ2VyKHRoaXMpO1xuICAgIGNvbnN0IHF1ZXJ5SWQgPSBxdWVyeU1hbmFnZXIuZ2VuZXJhdGVRdWVyeUlkKCk7XG4gICAgcXVlcnlNYW5hZ2VyLmZldGNoUXVlcnkocXVlcnlJZCwge1xuICAgICAgLi4ucXVlcnlJbmZvLm9wdGlvbnMsXG4gICAgICBxdWVyeTogcXVlcnlNYW5hZ2VyLnRyYW5zZm9ybShxdWVyeUluZm8ub3B0aW9ucy5xdWVyeSksXG4gICAgICBjb250ZXh0OiB7XG4gICAgICAgIC4uLnF1ZXJ5SW5mby5vcHRpb25zLmNvbnRleHQsXG4gICAgICAgIHF1ZXJ5RGVkdXBsaWNhdGlvbjogZmFsc2VcbiAgICAgIH1cbiAgICB9KS5maW5hbGx5KCgpID0+IHF1ZXJ5TWFuYWdlci5zdG9wUXVlcnkocXVlcnlJZCkpLnRoZW4ocXVlcnlJbmZvLnJlc29sdmUsIHF1ZXJ5SW5mby5yZWplY3QpO1xuICB9O1xufTtcbnZhciBBcG9sbG9DbGllbnRTU1JJbXBsID0gY2xhc3MgZXh0ZW5kcyBBcG9sbG9DbGllbnRDbGllbnRCYXNlSW1wbCB7XG4gIGZvcndhcmRlZFF1ZXJpZXMgPSBuZXcgKGdldFRyaWVDb25zdHJ1Y3Rvcih0aGlzKSkoKTtcbiAgd2F0Y2hRdWVyeVF1ZXVlID0gY3JlYXRlQmFja3ByZXNzdXJlZENhbGxiYWNrKCk7XG4gIHdhdGNoUXVlcnkob3B0aW9ucykge1xuICAgIGNvbnN0IHsgY2FjaGVLZXlBcnIgfSA9IHRoaXMuaWRlbnRpZnlVbmlxdWVRdWVyeShvcHRpb25zKTtcbiAgICBpZiAob3B0aW9ucy5mZXRjaFBvbGljeSAhPT0gXCJjYWNoZS1vbmx5XCIgJiYgb3B0aW9ucy5mZXRjaFBvbGljeSAhPT0gXCJzdGFuZGJ5XCIgJiYgIXRoaXMuZm9yd2FyZGVkUXVlcmllcy5wZWVrQXJyYXkoY2FjaGVLZXlBcnIpKSB7XG4gICAgICB0aGlzLmZvcndhcmRlZFF1ZXJpZXMubG9va3VwQXJyYXkoY2FjaGVLZXlBcnIpO1xuICAgICAgY29uc3Qgb2JzZXJ2YWJsZVF1ZXJ5ID0gc3VwZXIud2F0Y2hRdWVyeShvcHRpb25zKTtcbiAgICAgIGNvbnN0IHF1ZXJ5SW5mbyA9IG9ic2VydmFibGVRdWVyeVtcInF1ZXJ5SW5mb1wiXTtcbiAgICAgIGNvbnN0IGlkID0gcXVlcnlJbmZvLnF1ZXJ5SWQ7XG4gICAgICBjb25zdCBzdHJlYW1PYnNlcnZhYmxlID0gbmV3IE9ic2VydmFibGUkMSgoc3Vic2NyaWJlcikgPT4ge1xuICAgICAgICBjb25zdCB7IG1hcmtSZXN1bHQsIG1hcmtFcnJvciwgbWFya1JlYWR5IH0gPSBxdWVyeUluZm87XG4gICAgICAgIHF1ZXJ5SW5mby5tYXJrUmVzdWx0ID0gZnVuY3Rpb24ocmVzdWx0KSB7XG4gICAgICAgICAgc3Vic2NyaWJlci5uZXh0KHtcbiAgICAgICAgICAgIHR5cGU6IFwiZGF0YVwiLFxuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICByZXN1bHRcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gbWFya1Jlc3VsdC5hcHBseShxdWVyeUluZm8sIGFyZ3VtZW50cyk7XG4gICAgICAgIH07XG4gICAgICAgIHF1ZXJ5SW5mby5tYXJrRXJyb3IgPSBmdW5jdGlvbigpIHtcbiAgICAgICAgICBzdWJzY3JpYmVyLm5leHQoe1xuICAgICAgICAgICAgdHlwZTogXCJlcnJvclwiLFxuICAgICAgICAgICAgaWRcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBzdWJzY3JpYmVyLmNvbXBsZXRlKCk7XG4gICAgICAgICAgcmV0dXJuIG1hcmtFcnJvci5hcHBseShxdWVyeUluZm8sIGFyZ3VtZW50cyk7XG4gICAgICAgIH07XG4gICAgICAgIHF1ZXJ5SW5mby5tYXJrUmVhZHkgPSBmdW5jdGlvbigpIHtcbiAgICAgICAgICBzdWJzY3JpYmVyLm5leHQoe1xuICAgICAgICAgICAgdHlwZTogXCJjb21wbGV0ZVwiLFxuICAgICAgICAgICAgaWRcbiAgICAgICAgICB9KTtcbiAgICAgICAgICBzdWJzY3JpYmVyLmNvbXBsZXRlKCk7XG4gICAgICAgICAgcmV0dXJuIG1hcmtSZWFkeS5hcHBseShxdWVyeUluZm8sIGFyZ3VtZW50cyk7XG4gICAgICAgIH07XG4gICAgICB9KTtcbiAgICAgIHRoaXMud2F0Y2hRdWVyeVF1ZXVlLnB1c2goe1xuICAgICAgICBldmVudDoge1xuICAgICAgICAgIHR5cGU6IFwic3RhcnRlZFwiLFxuICAgICAgICAgIG9wdGlvbnM6IHNlcmlhbGl6ZU9wdGlvbnMob3B0aW9ucyksXG4gICAgICAgICAgaWRcbiAgICAgICAgfSxcbiAgICAgICAgb2JzZXJ2YWJsZTogc3RyZWFtT2JzZXJ2YWJsZVxuICAgICAgfSk7XG4gICAgICByZXR1cm4gb2JzZXJ2YWJsZVF1ZXJ5O1xuICAgIH1cbiAgICByZXR1cm4gc3VwZXIud2F0Y2hRdWVyeShvcHRpb25zKTtcbiAgfVxuICBvblF1ZXJ5U3RhcnRlZChldmVudCkge1xuICAgIGNvbnN0IGh5ZHJhdGVkT3B0aW9ucyA9IGRlc2VyaWFsaXplT3B0aW9ucyhldmVudC5vcHRpb25zKTtcbiAgICBjb25zdCB7IGNhY2hlS2V5QXJyIH0gPSB0aGlzLmlkZW50aWZ5VW5pcXVlUXVlcnkoaHlkcmF0ZWRPcHRpb25zKTtcbiAgICB0aGlzLmZvcndhcmRlZFF1ZXJpZXMubG9va3VwQXJyYXkoY2FjaGVLZXlBcnIpO1xuICAgIHN1cGVyLm9uUXVlcnlTdGFydGVkKGV2ZW50KTtcbiAgfVxufTtcbnZhciBBcG9sbG9DbGllbnRJbXBsZW1lbnRhdGlvbiA9IEFwb2xsb0NsaWVudFNTUkltcGwgO1xudmFyIEFwb2xsb0NsaWVudCA9IGNsYXNzIGV4dGVuZHMgQXBvbGxvQ2xpZW50SW1wbGVtZW50YXRpb24ge1xufTtcblxuLy8gc3JjL0RhdGFUcmFuc3BvcnRBYnN0cmFjdGlvbi9zeW1ib2xzLnRzXG52YXIgQXBvbGxvQ2xpZW50U2luZ2xldG9uID0gLyogQF9fUFVSRV9fICovIFN5bWJvbC5mb3IoXG4gIFwiQXBvbGxvQ2xpZW50U2luZ2xldG9uXCJcbik7XG5cbi8vIHNyYy9EYXRhVHJhbnNwb3J0QWJzdHJhY3Rpb24vdGVzdEhlbHBlcnMudHNcbmZ1bmN0aW9uIHJlc2V0QXBvbGxvU2luZ2xldG9ucygpIHtcbiAgZGVsZXRlIHdpbmRvd1tBcG9sbG9DbGllbnRTaW5nbGV0b25dO1xufVxuZnVuY3Rpb24gV3JhcEFwb2xsb1Byb3ZpZGVyKFRyYW5zcG9ydFByb3ZpZGVyKSB7XG4gIGNvbnN0IFdyYXBwZWRBcG9sbG9Qcm92aWRlcjMgPSAoe1xuICAgIG1ha2VDbGllbnQsXG4gICAgY2hpbGRyZW4sXG4gICAgLi4uZXh0cmFQcm9wc1xuICB9KSA9PiB7XG4gICAgY29uc3QgY2xpZW50UmVmID0gdXNlUmVmKHZvaWQgMCk7XG4gICAgaWYgKCFjbGllbnRSZWYuY3VycmVudCkge1xuICAgICAge1xuICAgICAgICBjbGllbnRSZWYuY3VycmVudCA9IG1ha2VDbGllbnQoKTtcbiAgICAgIH1cbiAgICAgIGFzc2VydEluc3RhbmNlKFxuICAgICAgICBjbGllbnRSZWYuY3VycmVudCxcbiAgICAgICAgV3JhcHBlZEFwb2xsb1Byb3ZpZGVyMy5pbmZvLFxuICAgICAgICBcIkFwb2xsb0NsaWVudFwiXG4gICAgICApO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIFJlYWN0LmNyZWF0ZUVsZW1lbnQoQXBvbGxvUHJvdmlkZXIsIHsgY2xpZW50OiBjbGllbnRSZWYuY3VycmVudCB9LCAvKiBAX19QVVJFX18gKi8gUmVhY3QuY3JlYXRlRWxlbWVudChcbiAgICAgIFRyYW5zcG9ydFByb3ZpZGVyLFxuICAgICAge1xuICAgICAgICBvblF1ZXJ5RXZlbnQ6IChldmVudCkgPT4gZXZlbnQudHlwZSA9PT0gXCJzdGFydGVkXCIgPyBjbGllbnRSZWYuY3VycmVudC5vblF1ZXJ5U3RhcnRlZChldmVudCkgOiBjbGllbnRSZWYuY3VycmVudC5vblF1ZXJ5UHJvZ3Jlc3MoZXZlbnQpLFxuICAgICAgICByZXJ1blNpbXVsYXRlZFF1ZXJpZXM6IGNsaWVudFJlZi5jdXJyZW50LnJlcnVuU2ltdWxhdGVkUXVlcmllcyxcbiAgICAgICAgcmVnaXN0ZXJEaXNwYXRjaFJlcXVlc3RTdGFydGVkOiBjbGllbnRSZWYuY3VycmVudC53YXRjaFF1ZXJ5UXVldWU/LnJlZ2lzdGVyLFxuICAgICAgICAuLi5leHRyYVByb3BzXG4gICAgICB9LFxuICAgICAgY2hpbGRyZW5cbiAgICApKTtcbiAgfTtcbiAgV3JhcHBlZEFwb2xsb1Byb3ZpZGVyMy5pbmZvID0gYnVuZGxlO1xuICByZXR1cm4gV3JhcHBlZEFwb2xsb1Byb3ZpZGVyMztcbn1cbmNvbnN0IGJ1aWx0X2Zvcl9zc3IgPSB0cnVlO1xuXG5leHBvcnQgeyBBcG9sbG9DbGllbnQsIERhdGFUcmFuc3BvcnRDb250ZXh0LCBBY2N1bXVsYXRlTXVsdGlwYXJ0UmVzcG9uc2VzTGluayBhcyBEZWJvdW5jZU11bHRpcGFydFJlc3BvbnNlc0xpbmssIEluTWVtb3J5Q2FjaGUsIFJlbW92ZU11bHRpcGFydERpcmVjdGl2ZXNMaW5rLCBTU1JNdWx0aXBhcnRMaW5rLCBXcmFwQXBvbGxvUHJvdmlkZXIsIGJ1aWx0X2Zvcl9zc3IsIHJlc2V0QXBvbGxvU2luZ2xldG9ucyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b3V0LmpzLm1hcFxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguc3NyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.ssr.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/manual-transport.ssr.js":
/*!*******************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/manual-transport.ssr.js ***!
  \*******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildManualDataTransport: () => (/* binding */ buildManualDataTransport),\n/* harmony export */   built_for_ssr: () => (/* binding */ built_for_ssr),\n/* harmony export */   resetManualSSRApolloSingletons: () => (/* binding */ resetManualSSRApolloSingletons)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client-react-streaming */ \"(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.ssr.js\");\n/* harmony import */ var ts_invariant__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ts-invariant */ \"(ssr)/../node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\");\n\n\n\n\n// src/ManualDataTransport/ManualDataTransport.tsx\n\n// src/ManualDataTransport/ApolloRehydrateSymbols.tsx\nvar ApolloSSRDataTransport = /* @__PURE__ */ Symbol.for(\n  \"ApolloSSRDataTransport\"\n);\nvar ApolloHookRehydrationCache = /* @__PURE__ */ Symbol.for(\n  \"apollo.hookRehydrationCache\"\n);\n\n// src/ManualDataTransport/htmlescape.ts\nvar ESCAPE_LOOKUP = {\n  \"&\": \"\\\\u0026\",\n  \">\": \"\\\\u003e\",\n  \"<\": \"\\\\u003c\",\n  \"\\u2028\": \"\\\\u2028\",\n  \"\\u2029\": \"\\\\u2029\"\n};\nvar ESCAPE_REGEX = /[&><\\u2028\\u2029]/g;\nfunction htmlEscapeJsonString(str) {\n  return str.replace(ESCAPE_REGEX, (match) => ESCAPE_LOOKUP[match]);\n}\n\n// src/ManualDataTransport/dataTransport.ts\nfunction transportDataToJS(data, stringify2) {\n  const key = Symbol.keyFor(ApolloSSRDataTransport);\n  return `(window[Symbol.for(\"${key}\")] ??= []).push(${htmlEscapeJsonString(\n    stringify2(data)\n  )})`;\n}\nfunction buildApolloRehydrationContext({\n  insertHtml,\n  stringify: stringify2,\n  extraScriptProps\n}) {\n  function ensureInserted() {\n    if (!rehydrationContext.currentlyInjected) {\n      rehydrationContext.currentlyInjected = true;\n      insertHtml(() => /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(rehydrationContext.RehydrateOnClient, null));\n    }\n  }\n  const rehydrationContext = {\n    currentlyInjected: false,\n    transportValueData: getTransportObject(ensureInserted),\n    transportedValues: {},\n    incomingEvents: getTransportArray(ensureInserted),\n    RehydrateOnClient() {\n      rehydrationContext.currentlyInjected = false;\n      if (!Object.keys(rehydrationContext.transportValueData).length && !Object.keys(rehydrationContext.incomingEvents).length)\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null);\n      ts_invariant__WEBPACK_IMPORTED_MODULE_2__.invariant.debug(\n        \"transporting data\",\n        rehydrationContext.transportValueData\n      );\n      ts_invariant__WEBPACK_IMPORTED_MODULE_2__.invariant.debug(\"transporting events\", rehydrationContext.incomingEvents);\n      const __html = transportDataToJS(\n        {\n          rehydrate: Object.fromEntries(\n            Object.entries(rehydrationContext.transportValueData).filter(\n              ([key, value]) => rehydrationContext.transportedValues[key] !== value\n            )\n          ),\n          events: rehydrationContext.incomingEvents\n        },\n        stringify2\n      );\n      Object.assign(\n        rehydrationContext.transportedValues,\n        rehydrationContext.transportValueData\n      );\n      rehydrationContext.transportValueData = getTransportObject(ensureInserted);\n      rehydrationContext.incomingEvents = getTransportArray(ensureInserted);\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"script\",\n        {\n          ...extraScriptProps,\n          dangerouslySetInnerHTML: {\n            __html\n          }\n        }\n      );\n    }\n  };\n  return rehydrationContext;\n}\nfunction getTransportObject(ensureInserted) {\n  return new Proxy(\n    {},\n    {\n      set(...args) {\n        ensureInserted();\n        return Reflect.set(...args);\n      }\n    }\n  );\n}\nfunction getTransportArray(ensureInserted) {\n  return new Proxy([], {\n    get(...args) {\n      if (args[1] === \"push\") {\n        return (...values) => {\n          ensureInserted();\n          return args[0].push(...values);\n        };\n      }\n      return Reflect.get(...args);\n    }\n  });\n}\n\n// src/ManualDataTransport/serialization.ts\nfunction stringify(value) {\n  let undefinedPlaceholder = \"$apollo.undefined$\";\n  const stringified = JSON.stringify(value);\n  while (stringified.includes(JSON.stringify(undefinedPlaceholder))) {\n    undefinedPlaceholder = \"$\" + undefinedPlaceholder;\n  }\n  return JSON.stringify(\n    value,\n    (_, v) => v === void 0 ? undefinedPlaceholder : v\n  ).replaceAll(JSON.stringify(undefinedPlaceholder), \"undefined\");\n}\n\n// src/ManualDataTransport/ManualDataTransport.tsx\nvar buildManualDataTransportSSRImpl = ({\n  useInsertHtml,\n  stringifyForStream = stringify\n}) => function ManualDataTransportSSRImpl({\n  extraScriptProps,\n  children,\n  registerDispatchRequestStarted\n}) {\n  const insertHtml = useInsertHtml();\n  const rehydrationContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n  if (!rehydrationContext.current) {\n    rehydrationContext.current = buildApolloRehydrationContext({\n      insertHtml,\n      extraScriptProps,\n      stringify: stringifyForStream\n    });\n  }\n  registerDispatchRequestStarted(({ event, observable }) => {\n    rehydrationContext.current.incomingEvents.push(event);\n    observable.subscribe({\n      next(event2) {\n        rehydrationContext.current.incomingEvents.push(event2);\n      }\n    });\n  });\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => ({\n      useStaticValueRef: function useStaticValueRef(value) {\n        const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n        rehydrationContext.current.transportValueData[id] = value;\n        return { current: value };\n      }\n    }),\n    []\n  );\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_1__.DataTransportContext.Provider, { value: contextValue }, children);\n};\nvar buildManualDataTransport = buildManualDataTransportSSRImpl ;\nfunction resetManualSSRApolloSingletons() {\n  (0,_apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_1__.resetApolloSingletons)();\n  delete window[ApolloHookRehydrationCache];\n  delete window[ApolloSSRDataTransport];\n}\nconst built_for_ssr = true;\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=manual-transport.ssr.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhcG9sbG8rY2xpZW50LXJlYWN0LXN0cmVhbV8wNDE2NmQwZDMzZThhZGZjMzM1YWE2NDZjNjg4NDk5Ny9ub2RlX21vZHVsZXMvQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nL2Rpc3QvbWFudWFsLXRyYW5zcG9ydC5zc3IuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXNEO0FBQ3VDO0FBQ3BEOztBQUV6Qzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLElBQUksbUJBQW1CO0FBQ3ZEO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLGdEQUFtQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0JBQStCLGdEQUFtQixDQUFDLDJDQUFjO0FBQ2pFLE1BQU0sbURBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQSxNQUFNLG1EQUFTO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGdEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSw2QkFBNkIsNkNBQU07QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLG9DQUFvQyxtQkFBbUI7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0gsdUJBQXVCLDhDQUFPO0FBQzlCO0FBQ0E7QUFDQSxtQkFBbUIsNENBQUs7QUFDeEI7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHlCQUF5QixnREFBbUIsQ0FBQyxnRkFBb0IsYUFBYSxxQkFBcUI7QUFDbkc7QUFDQTtBQUNBO0FBQ0EsRUFBRSxxRkFBcUI7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7O0FBRW1GO0FBQ25GO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGFwb2xsbytjbGllbnQtcmVhY3Qtc3RyZWFtXzA0MTY2ZDBkMzNlOGFkZmMzMzVhYTY0NmM2ODg0OTk3XFxub2RlX21vZHVsZXNcXEBhcG9sbG9cXGNsaWVudC1yZWFjdC1zdHJlYW1pbmdcXGRpc3RcXG1hbnVhbC10cmFuc3BvcnQuc3NyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VSZWYsIHVzZU1lbW8sIHVzZUlkIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgcmVzZXRBcG9sbG9TaW5nbGV0b25zLCBEYXRhVHJhbnNwb3J0Q29udGV4dCB9IGZyb20gJ0BhcG9sbG8vY2xpZW50LXJlYWN0LXN0cmVhbWluZyc7XG5pbXBvcnQgeyBpbnZhcmlhbnQgfSBmcm9tICd0cy1pbnZhcmlhbnQnO1xuXG4vLyBzcmMvTWFudWFsRGF0YVRyYW5zcG9ydC9NYW51YWxEYXRhVHJhbnNwb3J0LnRzeFxuXG4vLyBzcmMvTWFudWFsRGF0YVRyYW5zcG9ydC9BcG9sbG9SZWh5ZHJhdGVTeW1ib2xzLnRzeFxudmFyIEFwb2xsb1NTUkRhdGFUcmFuc3BvcnQgPSAvKiBAX19QVVJFX18gKi8gU3ltYm9sLmZvcihcbiAgXCJBcG9sbG9TU1JEYXRhVHJhbnNwb3J0XCJcbik7XG52YXIgQXBvbGxvSG9va1JlaHlkcmF0aW9uQ2FjaGUgPSAvKiBAX19QVVJFX18gKi8gU3ltYm9sLmZvcihcbiAgXCJhcG9sbG8uaG9va1JlaHlkcmF0aW9uQ2FjaGVcIlxuKTtcblxuLy8gc3JjL01hbnVhbERhdGFUcmFuc3BvcnQvaHRtbGVzY2FwZS50c1xudmFyIEVTQ0FQRV9MT09LVVAgPSB7XG4gIFwiJlwiOiBcIlxcXFx1MDAyNlwiLFxuICBcIj5cIjogXCJcXFxcdTAwM2VcIixcbiAgXCI8XCI6IFwiXFxcXHUwMDNjXCIsXG4gIFwiXFx1MjAyOFwiOiBcIlxcXFx1MjAyOFwiLFxuICBcIlxcdTIwMjlcIjogXCJcXFxcdTIwMjlcIlxufTtcbnZhciBFU0NBUEVfUkVHRVggPSAvWyY+PFxcdTIwMjhcXHUyMDI5XS9nO1xuZnVuY3Rpb24gaHRtbEVzY2FwZUpzb25TdHJpbmcoc3RyKSB7XG4gIHJldHVybiBzdHIucmVwbGFjZShFU0NBUEVfUkVHRVgsIChtYXRjaCkgPT4gRVNDQVBFX0xPT0tVUFttYXRjaF0pO1xufVxuXG4vLyBzcmMvTWFudWFsRGF0YVRyYW5zcG9ydC9kYXRhVHJhbnNwb3J0LnRzXG5mdW5jdGlvbiB0cmFuc3BvcnREYXRhVG9KUyhkYXRhLCBzdHJpbmdpZnkyKSB7XG4gIGNvbnN0IGtleSA9IFN5bWJvbC5rZXlGb3IoQXBvbGxvU1NSRGF0YVRyYW5zcG9ydCk7XG4gIHJldHVybiBgKHdpbmRvd1tTeW1ib2wuZm9yKFwiJHtrZXl9XCIpXSA/Pz0gW10pLnB1c2goJHtodG1sRXNjYXBlSnNvblN0cmluZyhcbiAgICBzdHJpbmdpZnkyKGRhdGEpXG4gICl9KWA7XG59XG5mdW5jdGlvbiBidWlsZEFwb2xsb1JlaHlkcmF0aW9uQ29udGV4dCh7XG4gIGluc2VydEh0bWwsXG4gIHN0cmluZ2lmeTogc3RyaW5naWZ5MixcbiAgZXh0cmFTY3JpcHRQcm9wc1xufSkge1xuICBmdW5jdGlvbiBlbnN1cmVJbnNlcnRlZCgpIHtcbiAgICBpZiAoIXJlaHlkcmF0aW9uQ29udGV4dC5jdXJyZW50bHlJbmplY3RlZCkge1xuICAgICAgcmVoeWRyYXRpb25Db250ZXh0LmN1cnJlbnRseUluamVjdGVkID0gdHJ1ZTtcbiAgICAgIGluc2VydEh0bWwoKCkgPT4gLyogQF9fUFVSRV9fICovIFJlYWN0LmNyZWF0ZUVsZW1lbnQocmVoeWRyYXRpb25Db250ZXh0LlJlaHlkcmF0ZU9uQ2xpZW50LCBudWxsKSk7XG4gICAgfVxuICB9XG4gIGNvbnN0IHJlaHlkcmF0aW9uQ29udGV4dCA9IHtcbiAgICBjdXJyZW50bHlJbmplY3RlZDogZmFsc2UsXG4gICAgdHJhbnNwb3J0VmFsdWVEYXRhOiBnZXRUcmFuc3BvcnRPYmplY3QoZW5zdXJlSW5zZXJ0ZWQpLFxuICAgIHRyYW5zcG9ydGVkVmFsdWVzOiB7fSxcbiAgICBpbmNvbWluZ0V2ZW50czogZ2V0VHJhbnNwb3J0QXJyYXkoZW5zdXJlSW5zZXJ0ZWQpLFxuICAgIFJlaHlkcmF0ZU9uQ2xpZW50KCkge1xuICAgICAgcmVoeWRyYXRpb25Db250ZXh0LmN1cnJlbnRseUluamVjdGVkID0gZmFsc2U7XG4gICAgICBpZiAoIU9iamVjdC5rZXlzKHJlaHlkcmF0aW9uQ29udGV4dC50cmFuc3BvcnRWYWx1ZURhdGEpLmxlbmd0aCAmJiAhT2JqZWN0LmtleXMocmVoeWRyYXRpb25Db250ZXh0LmluY29taW5nRXZlbnRzKS5sZW5ndGgpXG4gICAgICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8gUmVhY3QuY3JlYXRlRWxlbWVudChSZWFjdC5GcmFnbWVudCwgbnVsbCk7XG4gICAgICBpbnZhcmlhbnQuZGVidWcoXG4gICAgICAgIFwidHJhbnNwb3J0aW5nIGRhdGFcIixcbiAgICAgICAgcmVoeWRyYXRpb25Db250ZXh0LnRyYW5zcG9ydFZhbHVlRGF0YVxuICAgICAgKTtcbiAgICAgIGludmFyaWFudC5kZWJ1ZyhcInRyYW5zcG9ydGluZyBldmVudHNcIiwgcmVoeWRyYXRpb25Db250ZXh0LmluY29taW5nRXZlbnRzKTtcbiAgICAgIGNvbnN0IF9faHRtbCA9IHRyYW5zcG9ydERhdGFUb0pTKFxuICAgICAgICB7XG4gICAgICAgICAgcmVoeWRyYXRlOiBPYmplY3QuZnJvbUVudHJpZXMoXG4gICAgICAgICAgICBPYmplY3QuZW50cmllcyhyZWh5ZHJhdGlvbkNvbnRleHQudHJhbnNwb3J0VmFsdWVEYXRhKS5maWx0ZXIoXG4gICAgICAgICAgICAgIChba2V5LCB2YWx1ZV0pID0+IHJlaHlkcmF0aW9uQ29udGV4dC50cmFuc3BvcnRlZFZhbHVlc1trZXldICE9PSB2YWx1ZVxuICAgICAgICAgICAgKVxuICAgICAgICAgICksXG4gICAgICAgICAgZXZlbnRzOiByZWh5ZHJhdGlvbkNvbnRleHQuaW5jb21pbmdFdmVudHNcbiAgICAgICAgfSxcbiAgICAgICAgc3RyaW5naWZ5MlxuICAgICAgKTtcbiAgICAgIE9iamVjdC5hc3NpZ24oXG4gICAgICAgIHJlaHlkcmF0aW9uQ29udGV4dC50cmFuc3BvcnRlZFZhbHVlcyxcbiAgICAgICAgcmVoeWRyYXRpb25Db250ZXh0LnRyYW5zcG9ydFZhbHVlRGF0YVxuICAgICAgKTtcbiAgICAgIHJlaHlkcmF0aW9uQ29udGV4dC50cmFuc3BvcnRWYWx1ZURhdGEgPSBnZXRUcmFuc3BvcnRPYmplY3QoZW5zdXJlSW5zZXJ0ZWQpO1xuICAgICAgcmVoeWRyYXRpb25Db250ZXh0LmluY29taW5nRXZlbnRzID0gZ2V0VHJhbnNwb3J0QXJyYXkoZW5zdXJlSW5zZXJ0ZWQpO1xuICAgICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBSZWFjdC5jcmVhdGVFbGVtZW50KFxuICAgICAgICBcInNjcmlwdFwiLFxuICAgICAgICB7XG4gICAgICAgICAgLi4uZXh0cmFTY3JpcHRQcm9wcyxcbiAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgX19odG1sXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICApO1xuICAgIH1cbiAgfTtcbiAgcmV0dXJuIHJlaHlkcmF0aW9uQ29udGV4dDtcbn1cbmZ1bmN0aW9uIGdldFRyYW5zcG9ydE9iamVjdChlbnN1cmVJbnNlcnRlZCkge1xuICByZXR1cm4gbmV3IFByb3h5KFxuICAgIHt9LFxuICAgIHtcbiAgICAgIHNldCguLi5hcmdzKSB7XG4gICAgICAgIGVuc3VyZUluc2VydGVkKCk7XG4gICAgICAgIHJldHVybiBSZWZsZWN0LnNldCguLi5hcmdzKTtcbiAgICAgIH1cbiAgICB9XG4gICk7XG59XG5mdW5jdGlvbiBnZXRUcmFuc3BvcnRBcnJheShlbnN1cmVJbnNlcnRlZCkge1xuICByZXR1cm4gbmV3IFByb3h5KFtdLCB7XG4gICAgZ2V0KC4uLmFyZ3MpIHtcbiAgICAgIGlmIChhcmdzWzFdID09PSBcInB1c2hcIikge1xuICAgICAgICByZXR1cm4gKC4uLnZhbHVlcykgPT4ge1xuICAgICAgICAgIGVuc3VyZUluc2VydGVkKCk7XG4gICAgICAgICAgcmV0dXJuIGFyZ3NbMF0ucHVzaCguLi52YWx1ZXMpO1xuICAgICAgICB9O1xuICAgICAgfVxuICAgICAgcmV0dXJuIFJlZmxlY3QuZ2V0KC4uLmFyZ3MpO1xuICAgIH1cbiAgfSk7XG59XG5cbi8vIHNyYy9NYW51YWxEYXRhVHJhbnNwb3J0L3NlcmlhbGl6YXRpb24udHNcbmZ1bmN0aW9uIHN0cmluZ2lmeSh2YWx1ZSkge1xuICBsZXQgdW5kZWZpbmVkUGxhY2Vob2xkZXIgPSBcIiRhcG9sbG8udW5kZWZpbmVkJFwiO1xuICBjb25zdCBzdHJpbmdpZmllZCA9IEpTT04uc3RyaW5naWZ5KHZhbHVlKTtcbiAgd2hpbGUgKHN0cmluZ2lmaWVkLmluY2x1ZGVzKEpTT04uc3RyaW5naWZ5KHVuZGVmaW5lZFBsYWNlaG9sZGVyKSkpIHtcbiAgICB1bmRlZmluZWRQbGFjZWhvbGRlciA9IFwiJFwiICsgdW5kZWZpbmVkUGxhY2Vob2xkZXI7XG4gIH1cbiAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KFxuICAgIHZhbHVlLFxuICAgIChfLCB2KSA9PiB2ID09PSB2b2lkIDAgPyB1bmRlZmluZWRQbGFjZWhvbGRlciA6IHZcbiAgKS5yZXBsYWNlQWxsKEpTT04uc3RyaW5naWZ5KHVuZGVmaW5lZFBsYWNlaG9sZGVyKSwgXCJ1bmRlZmluZWRcIik7XG59XG5cbi8vIHNyYy9NYW51YWxEYXRhVHJhbnNwb3J0L01hbnVhbERhdGFUcmFuc3BvcnQudHN4XG52YXIgYnVpbGRNYW51YWxEYXRhVHJhbnNwb3J0U1NSSW1wbCA9ICh7XG4gIHVzZUluc2VydEh0bWwsXG4gIHN0cmluZ2lmeUZvclN0cmVhbSA9IHN0cmluZ2lmeVxufSkgPT4gZnVuY3Rpb24gTWFudWFsRGF0YVRyYW5zcG9ydFNTUkltcGwoe1xuICBleHRyYVNjcmlwdFByb3BzLFxuICBjaGlsZHJlbixcbiAgcmVnaXN0ZXJEaXNwYXRjaFJlcXVlc3RTdGFydGVkXG59KSB7XG4gIGNvbnN0IGluc2VydEh0bWwgPSB1c2VJbnNlcnRIdG1sKCk7XG4gIGNvbnN0IHJlaHlkcmF0aW9uQ29udGV4dCA9IHVzZVJlZih2b2lkIDApO1xuICBpZiAoIXJlaHlkcmF0aW9uQ29udGV4dC5jdXJyZW50KSB7XG4gICAgcmVoeWRyYXRpb25Db250ZXh0LmN1cnJlbnQgPSBidWlsZEFwb2xsb1JlaHlkcmF0aW9uQ29udGV4dCh7XG4gICAgICBpbnNlcnRIdG1sLFxuICAgICAgZXh0cmFTY3JpcHRQcm9wcyxcbiAgICAgIHN0cmluZ2lmeTogc3RyaW5naWZ5Rm9yU3RyZWFtXG4gICAgfSk7XG4gIH1cbiAgcmVnaXN0ZXJEaXNwYXRjaFJlcXVlc3RTdGFydGVkKCh7IGV2ZW50LCBvYnNlcnZhYmxlIH0pID0+IHtcbiAgICByZWh5ZHJhdGlvbkNvbnRleHQuY3VycmVudC5pbmNvbWluZ0V2ZW50cy5wdXNoKGV2ZW50KTtcbiAgICBvYnNlcnZhYmxlLnN1YnNjcmliZSh7XG4gICAgICBuZXh0KGV2ZW50Mikge1xuICAgICAgICByZWh5ZHJhdGlvbkNvbnRleHQuY3VycmVudC5pbmNvbWluZ0V2ZW50cy5wdXNoKGV2ZW50Mik7XG4gICAgICB9XG4gICAgfSk7XG4gIH0pO1xuICBjb25zdCBjb250ZXh0VmFsdWUgPSB1c2VNZW1vKFxuICAgICgpID0+ICh7XG4gICAgICB1c2VTdGF0aWNWYWx1ZVJlZjogZnVuY3Rpb24gdXNlU3RhdGljVmFsdWVSZWYodmFsdWUpIHtcbiAgICAgICAgY29uc3QgaWQgPSB1c2VJZCgpO1xuICAgICAgICByZWh5ZHJhdGlvbkNvbnRleHQuY3VycmVudC50cmFuc3BvcnRWYWx1ZURhdGFbaWRdID0gdmFsdWU7XG4gICAgICAgIHJldHVybiB7IGN1cnJlbnQ6IHZhbHVlIH07XG4gICAgICB9XG4gICAgfSksXG4gICAgW11cbiAgKTtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBSZWFjdC5jcmVhdGVFbGVtZW50KERhdGFUcmFuc3BvcnRDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBjb250ZXh0VmFsdWUgfSwgY2hpbGRyZW4pO1xufTtcbnZhciBidWlsZE1hbnVhbERhdGFUcmFuc3BvcnQgPSBidWlsZE1hbnVhbERhdGFUcmFuc3BvcnRTU1JJbXBsIDtcbmZ1bmN0aW9uIHJlc2V0TWFudWFsU1NSQXBvbGxvU2luZ2xldG9ucygpIHtcbiAgcmVzZXRBcG9sbG9TaW5nbGV0b25zKCk7XG4gIGRlbGV0ZSB3aW5kb3dbQXBvbGxvSG9va1JlaHlkcmF0aW9uQ2FjaGVdO1xuICBkZWxldGUgd2luZG93W0Fwb2xsb1NTUkRhdGFUcmFuc3BvcnRdO1xufVxuY29uc3QgYnVpbHRfZm9yX3NzciA9IHRydWU7XG5cbmV4cG9ydCB7IGJ1aWxkTWFudWFsRGF0YVRyYW5zcG9ydCwgYnVpbHRfZm9yX3NzciwgcmVzZXRNYW51YWxTU1JBcG9sbG9TaW5nbGV0b25zIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1vdXQuanMubWFwXG4vLyMgc291cmNlTWFwcGluZ1VSTD1tYW51YWwtdHJhbnNwb3J0LnNzci5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/manual-transport.ssr.js\n");

/***/ })

};
;