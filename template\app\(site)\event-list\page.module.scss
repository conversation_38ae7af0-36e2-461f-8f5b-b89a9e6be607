@use "@/styles/lib/mixins.scss" as *;

.sidebar {
  @include breakpoint(large up) {
    width: 344px;
    padding-right: 40px;
  }
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @include breakpoint(medium up) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 56px;
  margin-block: 24px;

  @include breakpoint(medium up) {
    grid-template-columns: repeat(3, 1fr);
    gap: 56px 24px;
  }

  @include breakpoint(large up) {
    gap: 72px 32px;
    margin-block: 32px;
  }
}
