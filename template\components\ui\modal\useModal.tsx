"use client";

import { Dialog, DialogOverlay, DialogPortal, DialogTrigger, DialogTriggerProps } from "@radix-ui/react-dialog";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "./Modal.module.scss";

interface ModalOptions {
  defaultOpen?: boolean;
}

/**
 * Assign a modal to a single trigger element.
 */
export default function useModal(modal: React.ReactNode, options?: ModalOptions) {
  const { defaultOpen } = options ?? {};
  const [open, setOpen] = useState(defaultOpen ?? false);

  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Close the modal when navigating between pages.
  useEffect(() => {
    setOpen(false);
  }, [pathname, searchParams]);

  function Trigger({ ...props }: DialogTriggerProps) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild aria-expanded={undefined} aria-controls={undefined} {...props} />
        <DialogPortal>
          <DialogOverlay className={styles.overlay} />
          <div className={styles.modalContainer}>{modal}</div>
        </DialogPortal>
      </Dialog>
    );
  }

  return { Trigger };
}
