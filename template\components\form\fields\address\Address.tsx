"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Select from "@/components/ui/select/Select";
import Textfield from "@/components/ui/textfield/Textfield";
import { AddressField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";
import styles from "./Address.module.scss";

type AddressProps = Omit<AddressField, "__typename">;

export default function Address({
  name,
  label,
  description,
  required,
  condition,
  validationMessage,
  city,
  country,
  street1,
  street2,
  state,
  zip,
  columnSpan,
}: AddressProps) {
  const { register, control } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const errorId = useId();

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label description={description ?? undefined} required={required}>
          {label}
        </Label>
        <div className={styles.wrapper}>
          {street1 && (
            <Textfield
              placeholder={street1.placeholder ?? undefined}
              {...register(street1.name, { required: street1.required })}
            />
          )}
          {street2 && (
            <Textfield
              placeholder={street2.placeholder ?? undefined}
              {...register(street2.name, { required: street2.required })}
            />
          )}
          <div className={styles.layoutCityZip}>
            {city && (
              <Textfield
                placeholder={city.placeholder ?? undefined}
                {...register(city.name, { required: city.required })}
              />
            )}
            {zip && (
              <Textfield
                placeholder={zip.placeholder ?? undefined}
                {...register(zip.name, { required: zip.required })}
              />
            )}
          </div>
          <div className={styles.layoutStateCountry}>
            {state && (
              <Textfield
                placeholder={state.placeholder ?? undefined}
                {...register(state.name, { required: state.required })}
              />
            )}
            {country && (
              <Controller
                control={control}
                name={country.name}
                rules={{ required: country.required ? "Ce champ est obligatoire" : false }}
                defaultValue={country.defaultValue ?? ""}
                render={({ field: { onChange, value } }) => (
                  <Select
                    value={value}
                    onChange={(event) => onChange(event.target.value)}
                    placeholder={country.placeholder ?? "Sélectionnez un pays"}
                    required={country.required}
                    error={!!error}
                  >
                    {country.choices
                      ?.filter((choice) => choice !== null)
                      .map((choice) => (
                        <option key={choice!.value} value={choice!.value ?? ""}>
                          {choice!.label}
                        </option>
                      ))}
                  </Select>
                )}
              />
            )}
          </div>
        </div>

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
