import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import GoogleTranslate from "./GoogleTranslate";

const meta: Meta<typeof GoogleTranslate> = {
  title: "Components/GoogleTranslate",
  component: GoogleTranslate,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof GoogleTranslate>;

export const Default: Story = {
  render: () => (
    <div style={{ padding: "10rem" }}>
      <p>Ceci est un texte en français</p>
      <GoogleTranslate pageLanguage="fr" includedLanguages={["fr", "en", "de", "it", "es", "jp"]} />
    </div>
  ),
};
