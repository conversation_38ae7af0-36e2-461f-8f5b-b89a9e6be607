import { describe, expect, test } from "vitest";
import paginate, { DOTS } from "./paginate";

describe("paginate", () => {
  test("desktop pagination", () => {
    expect(paginate(1, 100)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 100]);
    expect(paginate(2, 100)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 100]);
    expect(paginate(3, 100)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 100]);
    expect(paginate(4, 100)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 100]);
    expect(paginate(5, 100)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 100]);
    expect(paginate(6, 100)).toEqual([1, DOTS, 4, 5, 6, 7, 8, DOTS, 100]);
    expect(paginate(7, 100)).toEqual([1, DOTS, 5, 6, 7, 8, 9, DOTS, 100]);
    expect(paginate(8, 100)).toEqual([1, DOTS, 6, 7, 8, 9, 10, DOTS, 100]);
    expect(paginate(9, 100)).toEqual([1, DOTS, 7, 8, 9, 10, 11, DOTS, 100]);
    expect(paginate(10, 100)).toEqual([1, DOTS, 8, 9, 10, 11, 12, DOTS, 100]);
    expect(paginate(100, 100)).toEqual([1, DOTS, 94, 95, 96, 97, 98, 99, 100]);

    expect(paginate(1, 10)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 10]);
    expect(paginate(2, 10)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 10]);
    expect(paginate(3, 10)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 10]);
    expect(paginate(4, 10)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 10]);
    expect(paginate(5, 10)).toEqual([1, 2, 3, 4, 5, 6, 7, DOTS, 10]);
    expect(paginate(6, 10)).toEqual([1, DOTS, 4, 5, 6, 7, 8, 9, 10]);
    expect(paginate(7, 10)).toEqual([1, DOTS, 4, 5, 6, 7, 8, 9, 10]);
    expect(paginate(8, 10)).toEqual([1, DOTS, 4, 5, 6, 7, 8, 9, 10]);
    expect(paginate(9, 10)).toEqual([1, DOTS, 4, 5, 6, 7, 8, 9, 10]);
    expect(paginate(10, 10)).toEqual([1, DOTS, 4, 5, 6, 7, 8, 9, 10]);

    expect(paginate(1, 1)).toEqual([1]);
    expect(paginate(1, 7)).toEqual([1, 2, 3, 4, 5, 6, 7]);
    expect(paginate(7, 7)).toEqual([1, 2, 3, 4, 5, 6, 7]);
    expect(paginate(1, 9)).toEqual([1, 2, 3, 4, 5, 6, 7, 8, 9]);
  });

  test("tablet pagination", () => {
    expect(paginate(1, 100, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 100]);
    expect(paginate(2, 100, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 100]);
    expect(paginate(3, 100, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 100]);
    expect(paginate(4, 100, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 100]);
    expect(paginate(5, 100, 1)).toEqual([1, DOTS, 4, 5, 6, DOTS, 100]);
    expect(paginate(6, 100, 1)).toEqual([1, DOTS, 5, 6, 7, DOTS, 100]);
    expect(paginate(7, 100, 1)).toEqual([1, DOTS, 6, 7, 8, DOTS, 100]);
    expect(paginate(8, 100, 1)).toEqual([1, DOTS, 7, 8, 9, DOTS, 100]);
    expect(paginate(9, 100, 1)).toEqual([1, DOTS, 8, 9, 10, DOTS, 100]);
    expect(paginate(10, 100, 1)).toEqual([1, DOTS, 9, 10, 11, DOTS, 100]);

    expect(paginate(1, 10, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 10]);
    expect(paginate(2, 10, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 10]);
    expect(paginate(3, 10, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 10]);
    expect(paginate(4, 10, 1)).toEqual([1, 2, 3, 4, 5, DOTS, 10]);
    expect(paginate(5, 10, 1)).toEqual([1, DOTS, 4, 5, 6, DOTS, 10]);
    expect(paginate(6, 10, 1)).toEqual([1, DOTS, 5, 6, 7, DOTS, 10]);
    expect(paginate(7, 10, 1)).toEqual([1, DOTS, 6, 7, 8, 9, 10]);

    expect(paginate(8, 10, 1)).toEqual([1, DOTS, 6, 7, 8, 9, 10]);
    expect(paginate(9, 10, 1)).toEqual([1, DOTS, 6, 7, 8, 9, 10]);
    expect(paginate(10, 10, 1)).toEqual([1, DOTS, 6, 7, 8, 9, 10]);
    expect(paginate(1, 1, 1)).toEqual([1]);
    expect(paginate(1, 7, 1)).toEqual([1, 2, 3, 4, 5, 6, 7]);
    expect(paginate(7, 7, 1)).toEqual([1, 2, 3, 4, 5, 6, 7]);
  });
});
