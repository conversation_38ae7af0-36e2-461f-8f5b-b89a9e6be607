import { EventAccessibilityStatus } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import PublicWidget from "./PublicWidget";

const meta: Meta<typeof PublicWidget> = {
  title: "Components/Widget/PublicWidget",
  component: PublicWidget,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof PublicWidget>;

export const Default: Story = {
  args: {
    audience: ["Tout public", "Enfants à partir de 6 ans"],
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.SUPPORTED,
      visualImpairment: EventAccessibilityStatus.SUPPORTED,
      mentalImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      intellectualImpairment: EventAccessibilityStatus.UNKNOWN,
      signLanguageReception: EventAccessibilityStatus.SUPPORTED,
      strollers: EventAccessibilityStatus.SUPPORTED,
      reducedMobility: EventAccessibilityStatus.SUPPORTED,
    },
  },
};

export const UnknownAudience: Story = {
  args: {
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.SUPPORTED,
      visualImpairment: EventAccessibilityStatus.SUPPORTED,
      mentalImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      intellectualImpairment: EventAccessibilityStatus.UNKNOWN,
      signLanguageReception: EventAccessibilityStatus.SUPPORTED,
      strollers: EventAccessibilityStatus.SUPPORTED,
      reducedMobility: EventAccessibilityStatus.SUPPORTED,
    },
  },
};
