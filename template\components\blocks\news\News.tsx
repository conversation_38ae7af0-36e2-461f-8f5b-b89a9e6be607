"use client";

import Hx from "@/components/ui/title/Hx";
import type { NewsBlock } from "@/generated/graphql/graphql";
import { SubtitleLevelProvider } from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import styles from "./News.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));
const NewsBriefList = dynamic(() => import("./NewsBriefList"));
const NewsFocus = dynamic(() => import("./NewsFocus"));
const NewsList = dynamic(() => import("./NewsList"));
const Tags = dynamic(() => import("@/components/ui/tag-bar/TagBar"));

export type NewsProps = Partial<Omit<NewsBlock, "__typename" | "innerBlocks">> & {
  // TODO: Remove once implemented in BE
  proposeUrl?: boolean;
};

/**
 * A block that displays a selection of news.
 */
export default function News({
  anchor,
  briefNews,
  focusedNews,
  listUrl,
  news,
  proposeUrl,
  tags,
  title,
  titleLevel,
}: NewsProps) {
  return (
    <section id={anchor ?? undefined} className={clsx("block-news contained", styles.news)}>
      {(title || (tags ?? []).length > 0) && (
        <div className={styles.titleWrapper}>
          {title && (
            <Hx level={titleLevel} className={styles.title}>
              {title}
            </Hx>
          )}
          {tags && <Tags className={styles.tags} items={tags} aria-roledescription="Liste des thématiques" />}
        </div>
      )}
      <SubtitleLevelProvider level={titleLevel}>
        {focusedNews && <NewsFocus news={focusedNews} />}
        {news && news.length > 0 && <NewsList items={news} />}
        {briefNews && briefNews.length > 0 && <NewsBriefList items={briefNews} />}
        {(listUrl || proposeUrl) && (
          <div className={styles.actions}>
            {listUrl && (
              <Button asChild variant="contained" size="lg" startIcon="fas fa-plus">
                <Link href={listUrl}>Toutes les actualités</Link>
              </Button>
            )}
            {proposeUrl && (
              <Button asChild variant="outlined" size="lg" startIcon="far fa-lightbulb-on">
                <Link href="#">Proposer une actualité</Link>
              </Button>
            )}
          </div>
        )}
      </SubtitleLevelProvider>
    </section>
  );
}
