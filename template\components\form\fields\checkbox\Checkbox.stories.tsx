import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Checkbox from "./Checkbox";

const meta: Meta<typeof Checkbox> = {
  title: "Form/Checkbox",
  component: Checkbox,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

export const Default: Story = {
  args: {
    label: "Légende",
    description: "Texte de description additionel",
    name: "input_1",
    choices: [
      {
        label: "Libellé",
        value: "1",
        defaultSelected: false,
        icon: null,
      },
      {
        label: "Libellé",
        value: "2",
        defaultSelected: false,
        icon: null,
      },
      {
        label: "Libellé",
        value: "3",
        defaultSelected: false,
        icon: null,
      },
    ],
  },
};
