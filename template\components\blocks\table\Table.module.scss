@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.container {
  width: 100%;
  padding-block: 24px;
  margin-block: 12px;

  @include breakpoint(large up) {
    margin-block: 16px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.wrapper {
  -webkit-overflow-scrolling: touch;
  width: 100%;
  overflow-x: auto;
}

.caption {
  margin-bottom: 12px;
  font-size: 1.6rem;
  line-height: 130%;
  color: $color-neutral-700;
  text-align: left;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 16px;
    font-size: 2.4rem;
  }
}

.table {
  width: 100%;
  max-width: 100%;
  font-size: 1.4rem;
  line-height: 130%;
  border-spacing: 0;
  border-collapse: separate;
  border-radius: 4px;

  th,
  td {
    min-width: 200px;
    padding: 8px 12px;
    text-align: left;

    ul,
    p {
      font-size: inherit;
    }
  }

  th {
    font-weight: 700;
    color: $color-white;
    background-color: $color-primary-500;
    border-top: 1px solid $color-primary-500;
    border-left: 1px solid $color-primary-500;
  }

  thead {
    min-height: 52px;

    th {
      min-height: 52px;
      border: none;

      &:first-of-type {
        border-top-left-radius: 4px;
      }

      &:last-child {
        border-top-right-radius: 4px;
      }
    }
  }

  tbody {
    background-color: $color-white;

    tr:nth-child(even) {
      background-color: $color-neutral-100;
    }

    th,
    td {
      border-top: 1px solid $color-neutral-300;
      border-left: 1px solid $color-neutral-300;

      &:last-child {
        border-right: 1px solid $color-neutral-300;
      }
    }
  }

  tfoot {
    td,
    th {
      border-top: 1px solid $color-tertiary-500;
      border-bottom: 1px solid $color-tertiary-500;
      border-left: 1px solid $color-tertiary-500;

      &:first-child {
        border-bottom-left-radius: 4px;
      }

      &:last-child {
        border-right: 1px solid $color-tertiary-500;
        border-bottom-right-radius: 4px;
      }
    }

    td {
      font-weight: 700;
      background-color: $color-tertiary-300;
    }

    th {
      color: inherit;
      background-color: $color-tertiary-500;
    }
  }

  // Add border radius if it has no header
  &:not(:has(thead)) {
    tbody {
      tr:first-child {
        td,
        th {
          &:first-child {
            border-top-left-radius: 4px;
          }

          &:last-child {
            border-top-right-radius: 4px;
          }
        }
      }
    }
  }

  // Add border radius if the first header element is an empty cell
  // It means the table has a column header
  &:has(thead td:empty:first-child) {
    tbody {
      tr:first-child {
        th:first-child {
          border-top-left-radius: 4px;
        }
      }
    }
  }

  // Add border radius if it has no footer
  &:not(:has(tfoot)) {
    tbody {
      tr:last-child {
        td,
        th {
          border-bottom: 1px solid $color-neutral-300;

          &:first-child {
            border-bottom-left-radius: 4px;
          }

          &:last-child {
            border-bottom-right-radius: 4px;
          }
        }
      }
    }
  }

  b,
  strong {
    font-weight: 700;
  }

  i,
  em {
    font-style: italic;
  }

  a {
    text-decoration: underline;
  }
}
