"use client";

import type { TableBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import striptags from "striptags";
import styles from "./Table.module.scss";

type TextAlign = "left" | "center" | "right";

const allowedTags = ["b", "strong", "i", "em", "a"];

type TableProps = Omit<TableBlock, "__typename" | "innerBlocks">;

export default function Table({
  anchor,
  caption,
  header,
  footer,
  cells,
  columnAlign,
  fixedLayout,
  firstColumnHeader,
}: TableProps) {
  return (
    <div id={anchor ?? undefined} className={clsx("block-table contained", styles.container)}>
      <div className={styles.wrapper}>
        <table
          className={styles.table}
          style={{ tableLayout: fixedLayout ? "fixed" : "auto" }}
          role={!header && !firstColumnHeader ? "presentation" : undefined}
        >
          {caption && <caption className={styles.caption}>{caption}</caption>}

          {header && (
            <thead>
              <tr>
                {header.map((cell, index) => {
                  const isObjectCell = typeof cell === "object" && cell !== null && "html" in cell;
                  const content = isObjectCell ? cell.html : cell;
                  const colSpan = isObjectCell && cell.colspan ? cell.colspan : undefined;
                  const rowSpan = isObjectCell && cell.rowspan ? cell.rowspan : undefined;
                  const style = { textAlign: columnAlign?.[index] as TextAlign | undefined };

                  return content || !firstColumnHeader ? (
                    <th
                      key={index}
                      scope="col"
                      style={style}
                      colSpan={colSpan}
                      rowSpan={rowSpan}
                      dangerouslySetInnerHTML={{ __html: striptags(content, allowedTags) }}
                    />
                  ) : (
                    <td key={index} colSpan={colSpan} rowSpan={rowSpan} />
                  );
                })}
              </tr>
            </thead>
          )}

          {cells && (
            <tbody>
              {cells.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {row?.map((cell, cellIndex) => {
                    const isObjectCell = typeof cell === "object" && cell !== null && "html" in cell;
                    const content = isObjectCell ? cell.html : cell;
                    const colSpan = isObjectCell && cell.colspan ? cell.colspan : undefined;
                    const rowSpan = isObjectCell && cell.rowspan ? cell.rowspan : undefined;

                    const Cell = cellIndex === 0 && firstColumnHeader ? "th" : "td";

                    return (
                      <Cell
                        key={cellIndex}
                        colSpan={colSpan}
                        rowSpan={rowSpan}
                        style={{
                          textAlign: columnAlign?.[cellIndex] as TextAlign | undefined,
                        }}
                        scope={cellIndex === 0 && firstColumnHeader ? "row" : undefined}
                        dangerouslySetInnerHTML={{ __html: striptags(content, allowedTags) }}
                      />
                    );
                  })}
                </tr>
              ))}
            </tbody>
          )}

          {footer && (
            <tfoot>
              <tr>
                {footer.map((cell, index) => {
                  const isObjectCell = typeof cell === "object" && cell !== null && "html" in cell;
                  const content = isObjectCell ? cell.html : cell;
                  const colSpan = isObjectCell && cell.colspan ? cell.colspan : undefined;
                  const rowSpan = isObjectCell && cell.rowspan ? cell.rowspan : undefined;
                  const Cell = index === 0 && firstColumnHeader ? "th" : "td";
                  const style = { textAlign: columnAlign?.[index] as TextAlign | undefined };

                  return (
                    <Cell
                      key={index}
                      style={style}
                      colSpan={colSpan}
                      rowSpan={rowSpan}
                      scope={index === 0 && firstColumnHeader ? "row" : undefined}
                      dangerouslySetInnerHTML={{ __html: striptags(content, allowedTags) }}
                    />
                  );
                })}
              </tr>
            </tfoot>
          )}
        </table>
      </div>
    </div>
  );
}
