@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.discoverItem {
  display: flex;
  flex-direction: column-reverse;

  @include breakpoint(medium up) {
    min-height: 200px;

    &.imageLeft {
      flex-direction: row-reverse;
    }

    &.imageRight {
      flex-direction: row;
    }
  }

  @include breakpoint(large up) {
    min-height: 340px;
  }
}

.image,
.content {
  @include breakpoint(medium up) {
    width: 50%;
  }
}

.image {
  position: relative;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;

    @include breakpoint(medium up) {
      position: absolute;
    }
  }
}

.content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  justify-content: center;
  padding: 16px;
  background-color: $color-neutral-100;

  &::before {
    @include triangle(top, $color-neutral-100, 79px, 29px);

    position: absolute;
    top: 0;
    left: 50%;
    content: "";
    transform: translate(-50%, -100%);
  }

  @include breakpoint(medium up) {
    padding: 24px 56px;

    &::before {
      .discoverItem.imageLeft & {
        @include triangle(left, $color-neutral-100, 29px, 79px);

        top: 50%;
        left: 0;
        transform: translate(-100%, -50%);
      }

      .discoverItem.imageRight & {
        @include triangle(right, $color-neutral-100, 29px, 79px);

        top: 50%;
        right: 0;
        transform: translate(100%, -50%);
      }
    }
  }

  @include breakpoint(large up) {
    padding-block: 72px;
  }

  .surtitle {
    order: -1;
    font-size: 1.6rem;
    line-height: 130%;
    color: $color-primary-500;

    @include breakpoint(medium up) {
      font-size: 1.8rem;
    }

    @include breakpoint(large up) {
      font-size: 2.4rem;
    }
  }

  .title {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 110%;

    @include breakpoint(medium up) {
      font-size: 2rem;
    }

    @include breakpoint(large up) {
      font-size: 3.2rem;
    }
  }
}
