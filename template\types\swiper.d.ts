declare module "swiper/types" {
  interface Swiper {
    virtualSize?: number;
    visibleSlides?: HTMLElement[];
  }

  interface AutoplayOptions {
    enabled?: boolean;
  }

  interface A11yMethods {
    clicked?: boolean;
  }

  interface A11yOptions {
    /**
     * Message for screen readers when previous slide is shown
     *
     * @default 'Previous slide'
     */
    prevSlideMessage?: string;

    /**
     * Message for screen readers when next slide is shown
     *
     * @default 'Next slide'
     */
    nextSlideMessage?: string;

    /**
     * Message for screen readers for previous button
     *
     * @default null
     */
    prevButtonMessage?: string | null;

    /**
     * Message for screen readers for next button
     *
     * @default null
     */
    nextButtonMessage?: string | null;

    /**
     * Set the non-visible slides as `inert`.
     *
     * @default false
     */
    inertInvisibleSlides?: boolean;
  }
}

export {};
