# Google Translate Widget Integration Guide

## Description

This guide explains how to integrate the Google Translate widget into your React app for dynamic translation. The setup involves creating components for loading the Google Translate script, providing a context for managing the current language, and a dropdown UI for selecting the language.

## Qu'est-ce que c'est ?

Le widget Google Translate permet aux utilisateurs de traduire dynamiquement le contenu de votre site dans plusieurs langues. Il est intégré à une application React à l'aide de plusieurs composants. Un composant charge le script Google Translate, un autre permet de sélectionner la langue et un dernier gère la logique du contexte pour suivre la langue actuelle.

## Utilisation

### 1. Installer les dépendances :

Vous devez vous assurer que le script Google Translate est chargé dynamiquement.

### 2. Composant `GoogleTranslateProvider` :

Ce composant fournit un contexte pour gérer la langue par défaut, les langues disponibles et permet de changer la langue. Il charge le script Google Translate et gère la mise à jour des cookies pour la langue active.

### 3. Composant `GoogleTranslate` :

Ce composant fournit une interface utilisateur pour sélectionner une langue parmi celles disponibles.  
Il utilise un bouton avec un menu déroulant pour sélectionner la langue. Le composant se charge dynamiquement d'afficher les langues disponibles en fonction des détails des langues passées dans `languagesDetails`.

### 4. Composant `GoogleTranslateWrapper` :

Ce composant est une enveloppe qui gère l'intégration du `GoogleTranslateProvider` avec des paramètres par défaut pour les langues et la langue initiale.

## Ignorer la traduction

Pour empêcher Google Translate de traduire certains éléments, comme des titres ou des menus qui utilisent une traduction propre (par exemple, une solution i18n), vous pouvez utiliser la classe `notranslate`.

## Explication des fonctionnalités

- `GoogleTranslateProvider` : Gère l'état de la langue, initialise le script de Google Translate, et garde la langue courante synchronisée avec le cookie. Ce composant permet de configurer les langues disponibles et de gérer l'état global de l'application.

- `GoogleTranslate` : Affiche une interface de sélection de langue pour l'utilisateur, permettant de changer la langue de la page dynamiquement. Ce composant interagit avec le contexte pour mettre à jour la langue active et afficher les options disponibles dans un menu déroulant.

- `GoogleTranslateWrapper` : Fournit un contexte de traduction autour de votre application ou une partie de votre page pour faciliter la gestion de l'état de la langue. Ce composant est souvent utilisé comme un conteneur pour intégrer le contexte dans l'ensemble de l'application.
