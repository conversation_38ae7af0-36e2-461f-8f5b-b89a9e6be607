import Button from "@/components/ui/button/Button";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import Hx from "@/components/ui/title/Hx";
import { DateRangeFilter } from "@/generated/graphql/graphql";
import { FilterValues } from "@/lib/filters";
import clsx from "clsx";
import Form from "next/form";
import { useId } from "react";
import HiddenInputs from "../HiddenInputs";
import styles from "./DateRange.module.scss";

interface DateRangeProps {
  className?: string;
  defaultValue?: string[];
  filter: Omit<DateRangeFilter, "__typename">;
  filterValues?: FilterValues;
  url: string;
}

export default function DateRange({
  className,
  defaultValue,
  filter: { attribute, label },
  filterValues,
  url,
}: DateRangeProps) {
  const startId = useId();
  const endId = useId();

  return (
    <details className={clsx(styles.root, className)} open={true}>
      <summary className={styles.summary}>
        <i className="far fa-caret-down" aria-hidden={true} />

        <Hx level={2}>{label ?? "Date"}</Hx>
      </summary>

      <div className={styles.content}>
        <Form
          action={url}
          className={styles.form}
          scroll={false}
          aria-label="Recherche par dates"
          data-filter={attribute}
        >
          <div className={styles.control}>
            <Label htmlFor={startId}>Date de début</Label>

            <Textfield id={startId} name={attribute} type="date" defaultValue={defaultValue?.[0]} />
          </div>

          <div className={styles.control}>
            <Label htmlFor={endId}>Date de fin</Label>

            <Textfield id={endId} name={attribute} type="date" defaultValue={defaultValue?.[1]} />
          </div>

          {filterValues && <HiddenInputs filterValues={filterValues} omit={[attribute]} />}

          <Button type="submit" className={styles.submit} startIcon="far fa-check" variant="outlined">
            Appliquer la date
          </Button>
        </Form>
      </div>
    </details>
  );
}
