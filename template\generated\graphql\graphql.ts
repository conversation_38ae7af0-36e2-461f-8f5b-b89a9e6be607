/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** An absolute URL */
  AbsoluteURL: { input: any; output: any; }
  /** The value of a CSS property */
  CSSValue: { input: any; output: any; }
  /** An ISO 8601-formatted date */
  DateTime: { input: any; output: any; }
  /** HTML code */
  HTML: { input: string; output: string; }
  /** A relative or absolute URL */
  MediaURL: { input: any; output: any; }
  /** An object with an arbitrary structure */
  Record: { input: any; output: any; }
  /** The content of the post */
  StructuredContent: { input: any; output: any; }
  /** A relative or absolute URL */
  URL: { input: string; output: string; }
};

/** The data for ACF blocks */
export type AcfBlock = {
  __typename?: 'ACFBlock';
  /** The data of the block */
  data: Scalars['Record']['output'];
};

/** The data for a citeopolis/accordion block */
export type AccordionBlock = BlockInterface & {
  __typename?: 'AccordionBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** If enabled, multiple collapsibles can be opened at the same time. */
  multiple: Scalars['Boolean']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/accordion-item block */
export type AccordionItemBlock = BlockInterface & {
  __typename?: 'AccordionItemBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether the block is open by default. */
  open: Scalars['Boolean']['output'];
  /** The title of the collapsible. */
  title: Maybe<Scalars['String']['output']>;
  /** The level of the heading. From 1 to 6. */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** An address */
export type Address = {
  __typename?: 'Address';
  /** The city of the address */
  city: Maybe<Scalars['String']['output']>;
  /** The country of the address */
  country: Maybe<Scalars['String']['output']>;
  /** The street address */
  street: Array<Scalars['String']['output']>;
  /** The post code of the address */
  zip: Maybe<Scalars['String']['output']>;
};

/** An address field in a form */
export type AddressField = FieldInterface & {
  __typename?: 'AddressField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The input for the city */
  city: Maybe<TextField>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** The input for the country */
  country: Maybe<SelectField>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The input for the state */
  state: Maybe<TextField>;
  /** The input for the first line of the address */
  street1: Maybe<TextField>;
  /** The input for the second line of the address */
  street2: Maybe<TextField>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
  /** The input for the post code */
  zip: Maybe<TextField>;
};

/** A representation of the post type album */
export type Album = {
  __typename?: 'Album';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** Album items */
  media: Array<AlbumMedia>;
  /** Number of items */
  mediaCount: Scalars['Int']['output'];
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** Number of images */
  photoCount: Scalars['Int']['output'];
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
  /** Number of videos */
  videoCount: Scalars['Int']['output'];
};

/** An input type for filtering posts */
export type AlbumFilterInput = {
  /** Filter: AlbumFilterInput */
  album_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: AlbumFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type album */
export type AlbumList = {
  __typename?: 'AlbumList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** An album item */
export type AlbumMedia = AlbumPhoto | AlbumVideo;

/** A picture in an album */
export type AlbumPhoto = {
  __typename?: 'AlbumPhoto';
  /** The alt text of the image */
  alt: Maybe<Scalars['String']['output']>;
  /** The caption of the image */
  caption: Maybe<Scalars['String']['output']>;
  /** The height of the image in pixels */
  height: Maybe<Scalars['Int']['output']>;
  /** The URL of the image */
  src: Maybe<Scalars['MediaURL']['output']>;
  /** The width of the image in pixels */
  width: Maybe<Scalars['Int']['output']>;
};

/** Search response for posts */
export type AlbumSearchResponse = {
  __typename?: 'AlbumSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Album>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type AlbumSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** A video in an album */
export type AlbumVideo = {
  __typename?: 'AlbumVideo';
  /** A caption describing the video */
  caption: Maybe<Scalars['String']['output']>;
  /** The OEmbed provider of the video */
  provider: Maybe<Scalars['String']['output']>;
  /** The video thumbnail */
  thumbnail: Maybe<Image>;
  /** The URL of the video */
  url: Maybe<Scalars['URL']['output']>;
};

/** The data for a citeopolis/albums block */
export type AlbumsBlock = BlockInterface & {
  __typename?: 'AlbumsBlock';
  /** The albums to display */
  albums: Array<Album>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The URL to the list of albums */
  listUrl: Maybe<Scalars['URL']['output']>;
  /** The title of the block */
  title: Maybe<Scalars['String']['output']>;
  /** The heading level of the title of the block */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A representation of the post type alert */
export type Alert = {
  __typename?: 'Alert';
  /** The redirect link */
  action: Maybe<Link>;
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The alert&#039;s description */
  description: Maybe<Scalars['String']['output']>;
  /** The end date */
  endDate: Maybe<Scalars['DateTime']['output']>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The start date */
  startDate: Maybe<Scalars['DateTime']['output']>;
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
  /** The type of the alert */
  variant: AlertVariant;
};

/** An input type for filtering posts */
export type AlertFilterInput = {
  /** Filter: AlertFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** Search response for posts */
export type AlertSearchResponse = {
  __typename?: 'AlertSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Alert>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type AlertSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** All possible alert types */
export enum AlertVariant {
  POPUP = 'POPUP',
  STICKY = 'STICKY'
}

/** An Altcha field in a form */
export type AltchaField = FieldInterface & {
  __typename?: 'AltchaField';
  /** Auto-verification mode */
  auto: Scalars['String']['output'];
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** Challenge data */
  challengejson: Maybe<Scalars['String']['output']>;
  /** The URL to generate a challenge after the JSON has expired */
  challengeurl: Scalars['String']['output'];
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** Whether debug mode is enabled */
  debug: Scalars['Boolean']['output'];
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** Artificial delay (in milliseconds) */
  delay: Maybe<Scalars['Int']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Expiration delay (in milliseconds) */
  expire: Maybe<Scalars['Int']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** Whether error mocking is enabled */
  mockerror: Scalars['Boolean']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The data for a core/archives block */
export type ArchivesBlock = BlockInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'ArchivesBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The number of items for each link */
  counts: Array<Scalars['Int']['output']>;
  /** Whether to display the list as a dropdown */
  displayAsDropdown: Scalars['Boolean']['output'];
  /** The label of the dropdown */
  dropdownLabel: Maybe<Scalars['String']['output']>;
  /** The placeholder (empty value) of the dropdown */
  dropdownPlaceholder: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The links to display */
  links: Array<Link>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** Show the post count for each group */
  showPostCounts: Scalars['Boolean']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A representation of the post type associations */
export type Associations = {
  __typename?: 'Associations';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type AssociationsFilterInput = {
  /** Filter: AssociationsFilterInput */
  associations_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: AssociationsFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type associations */
export type AssociationsList = {
  __typename?: 'AssociationsList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** A representation of the map for the post type associations */
export type AssociationsMap = {
  __typename?: 'AssociationsMap';
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the map view */
  filters: Array<FilterInterface>;
  /** The metadata of the map view */
  metadata: Maybe<Metadata>;
  /** The full text search filter */
  searchFilter: Maybe<FilterInterface>;
  /** The title of the map page */
  title: Scalars['String']['output'];
  /** The URL of the map view */
  url: Maybe<Scalars['URL']['output']>;
};

/** Search response for posts */
export type AssociationsSearchResponse = {
  __typename?: 'AssociationsSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Associations>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type AssociationsSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** A representation of the post type attachment */
export type Attachment = {
  __typename?: 'Attachment';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type AttachmentFilterInput = {
  /** Filter: AttachmentFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** Search response for posts */
export type AttachmentSearchResponse = {
  __typename?: 'AttachmentSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Attachment>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type AttachmentSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The data for a core/audio block */
export type AudioBlock = BlockInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'AudioBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** Whether the audio should automatically play */
  autoplay: Scalars['Boolean']['output'];
  /** The caption of the audio file */
  caption: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether the audio should loop */
  loop: Scalars['Boolean']['output'];
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The preloading mode of the audio player */
  preload: Maybe<Scalars['String']['output']>;
  /** The URL of the audio file */
  src: Scalars['MediaURL']['output'];
  /** The transcription of the audio file */
  transcription: Maybe<Scalars['String']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a core/avatar block */
export type AvatarBlock = BlockInterface & BlockWithBorderInterface & BlockWithDuotoneInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'AvatarBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** Colors used for the duotone filter */
  duotone: Array<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The link on the image */
  link: Maybe<Link>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The size of the image in pixels */
  size: Scalars['Int']['output'];
  /** The URL of the image */
  src: Scalars['String']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/banner block */
export type BannerBlock = BlockInterface & BlockWithAlignmentInterface & {
  __typename?: 'BannerBlock';
  /** Link displayed in the content part */
  action: Maybe<Link>;
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** Color of the background of the text part */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** Description of the content part */
  description: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** The image of the banner */
  image: Maybe<Image>;
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** Color of the text part */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** Title of the content part */
  title: Maybe<Scalars['String']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The configuration of the administration&#039;s bar */
export type BarConfig = {
  __typename?: 'BarConfig';
  /** The currently logged-in user */
  currentUser: Maybe<User>;
  /** Menu items */
  entries: Array<BarMenuEntry>;
  /** The icon displayed on the left hand side of the bar. */
  icon: Maybe<Icon>;
  /** The user&#039;s logout URL. */
  logoutUrl: Maybe<Scalars['URL']['output']>;
};

/** A menu item of the admin bar */
export type BarMenuEntry = {
  __typename?: 'BarMenuEntry';
  /** The children of the menu item */
  children: Array<BarMenuEntry>;
  /** The icon of the menu item */
  icon: Maybe<Icon>;
  /** The level of the menu item in the tree */
  level: Scalars['Int']['output'];
  /** The title for screen readers */
  screenReaderTitle: Maybe<Scalars['String']['output']>;
  /** The title of the menu item */
  title: Scalars['String']['output'];
  /** The URL of the menu item */
  url: Scalars['URL']['output'];
};

/** Common fields for blocks */
export type BlockInterface = {
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The display modes for a block, in the page content column. */
export enum BlockLayout {
  CONTAINED = 'CONTAINED',
  FULLWIDTH = 'FULLWIDTH'
}

/** Common fields for block with alignment settings */
export type BlockWithAlignmentInterface = {
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for block with background settings */
export type BlockWithBackgroundInterface = {
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for block with block gap settings */
export type BlockWithBlockGapInterface = {
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with border settings */
export type BlockWithBorderInterface = {
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with color settings */
export type BlockWithColorInterface = {
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for block with duotone settings */
export type BlockWithDuotoneInterface = {
  /** Colors used for the duotone filter */
  duotone: Array<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with layout settings */
export type BlockWithLayoutInterface = {
  /** The column count of a grid layout */
  columnCount: Maybe<Scalars['Int']['output']>;
  /** The size of the content */
  contentSize: Maybe<Scalars['CSSValue']['output']>;
  /** The wrapping mode of a flex layout */
  flexWrap: Maybe<Scalars['CSSValue']['output']>;
  /** The justification mode of a flex layout */
  justifyContent: Maybe<Scalars['CSSValue']['output']>;
  /** The layout type of the block */
  layoutType: Maybe<Scalars['String']['output']>;
  /** The minimum column width of a grid layout */
  minimumColumnWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The size of the outer container */
  wideSize: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for block with margin settings */
export type BlockWithMarginInterface = {
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with minimum height settings */
export type BlockWithMinHeightInterface = {
  /** The minimum height of the block */
  minHeight: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for block with padding settings */
export type BlockWithPaddingInterface = {
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with shadow settings */
export type BlockWithShadowInterface = {
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
};

/** Common fields for blocks with typography settings */
export type BlockWithTypographyInterface = {
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The hierarchy of links for a page */
export type Breadcrumbs = {
  __typename?: 'Breadcrumbs';
  /** Items of the breadcrumb trail */
  items: Array<Crumb>;
};

/** The data for a core/button block */
export type ButtonBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithPaddingInterface & BlockWithShadowInterface & BlockWithTypographyInterface & {
  __typename?: 'ButtonBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** Extra CSS classes for the button */
  class: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The text of the button */
  html: Maybe<Scalars['HTML']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The rel attribute of the button */
  rel: Maybe<Scalars['String']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** The target of the button */
  target: Maybe<Scalars['String']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the button */
  url: Scalars['URL']['output'];
  /** The variant of the block */
  variant: Maybe<ButtonBlockVariant>;
  /** The width of the button (in percents) */
  width: Maybe<Scalars['Int']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** All styles for the core/button block */
export enum ButtonBlockVariant {
  PRIMARY = 'PRIMARY',
  SECONDARY = 'SECONDARY',
  TERTIARY = 'TERTIARY'
}

/** The data for a core/buttons block */
export type ButtonsBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithLayoutInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'ButtonsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The column count of a grid layout */
  columnCount: Maybe<Scalars['Int']['output']>;
  /** The size of the content */
  contentSize: Maybe<Scalars['CSSValue']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The wrapping mode of a flex layout */
  flexWrap: Maybe<Scalars['CSSValue']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The justification mode of a flex layout */
  justifyContent: Maybe<Scalars['CSSValue']['output']>;
  /** The layout type of the block */
  layoutType: Maybe<Scalars['String']['output']>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The minimum column width of a grid layout */
  minimumColumnWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The buttons&#039; orientation */
  orientation: Maybe<Orientation>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The buttons&#039; vertical alignment relative to their container */
  verticalAlignment: Maybe<Scalars['CSSValue']['output']>;
  /** The size of the outer container */
  wideSize: Maybe<Scalars['CSSValue']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A category of a post */
export type Category = {
  __typename?: 'Category';
  /** The children of the category */
  children: Array<Category>;
  /** The short description of the category */
  description: Maybe<Scalars['String']['output']>;
  /** The parent category */
  parent: Maybe<Category>;
  /** The relative URL of the category */
  relativeUrl: Scalars['String']['output'];
  /** The title of the category */
  title: Scalars['String']['output'];
};

/** A checkbox field in a form */
export type CheckboxField = FieldInterface & FieldWithChoicesInterface & {
  __typename?: 'CheckboxField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The choices of the input */
  choices: Array<Option>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The client information */
export type ClientInfo = {
  __typename?: 'ClientInfo';
  /** The client address */
  address: Maybe<Scalars['String']['output']>;
  /** The hours when the client can be contacted */
  openingHours: Maybe<Scalars['String']['output']>;
  /** The client phone number */
  tel: Maybe<Scalars['String']['output']>;
};

/** The data for a core/code block */
export type CodeBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'CodeBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The code */
  code: Scalars['String']['output'];
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The data for a core/column block */
export type ColumnBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithPaddingInterface & BlockWithShadowInterface & BlockWithTypographyInterface & {
  __typename?: 'ColumnBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The width of the column */
  width: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The data for a core/columns block */
export type ColumnsBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithShadowInterface & BlockWithTypographyInterface & {
  __typename?: 'ColumnsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The number of columns */
  columnCount: Scalars['Int']['output'];
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether columns should be stacked on mobile devices */
  isStackedOnMobile: Scalars['Boolean']['output'];
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A condition to display a form field */
export type Condition = {
  __typename?: 'Condition';
  /** The action to execute */
  action: ConditionAction;
  /** The operator for the group of rules */
  operator: ConditionMatchType;
  /** The rules of the group */
  rules: Array<ConditionRule>;
};

/** All actions that can be run after a condition is matched */
export enum ConditionAction {
  HIDE = 'HIDE',
  SHOW = 'SHOW'
}

/** All possible operators for rules of a condition */
export enum ConditionMatchType {
  AND = 'AND',
  OR = 'OR'
}

/** A rule of a conditional logic group */
export type ConditionRule = {
  __typename?: 'ConditionRule';
  /** The field affected by the condition */
  field: Scalars['String']['output'];
  /** The comparison between the field&#039;s value and the rule&#039;s value */
  operator: ConditionRuleOperator;
  /** The value to compare the field&#039;s value to */
  value: Scalars['String']['output'];
};

/** All possible operators for a rule of a condition */
export enum ConditionRuleOperator {
  CONTAINS = 'CONTAINS',
  ENDS_WITH = 'ENDS_WITH',
  GREATER_THAN = 'GREATER_THAN',
  IS = 'IS',
  IS_NOT = 'IS_NOT',
  LESS_THAN = 'LESS_THAN',
  STARTS_WITH = 'STARTS_WITH'
}

/** The configuration of a site */
export type ContentPager = {
  __typename?: 'ContentPager';
  /** The list view */
  list: Maybe<Link>;
  /** The next post */
  next: Maybe<Link>;
  /** The previous post */
  prev: Maybe<Link>;
};

/** The data for a core/cover block */
export type CoverBlock = BlockInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithDuotoneInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithShadowInterface & BlockWithTypographyInterface & {
  __typename?: 'CoverBlock';
  /** The alternative text of the image */
  alt: Scalars['String']['output'];
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The horizontal position of the content */
  contentPositionX: Scalars['String']['output'];
  /** The vertical position of the content */
  contentPositionY: Scalars['String']['output'];
  /** Colors used for the duotone filter */
  duotone: Array<Scalars['CSSValue']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** Whether to enable a parallax effect on the block */
  hasParallax: Scalars['Boolean']['output'];
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether the background image should be repeated */
  isRepeated: Scalars['Boolean']['output'];
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the image in the block */
  src: Maybe<Scalars['MediaURL']['output']>;
  /** The tag used for the element of the banner */
  tagName: Scalars['String']['output'];
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** An element of the breadcrumb trail for a page */
export type Crumb = {
  __typename?: 'Crumb';
  /** The breadcrumb elements at the same level */
  siblings: Array<Crumb>;
  /** The title of the breadcrumb */
  title: Scalars['String']['output'];
  /** The URL of the breadcrumb */
  url: Scalars['URL']['output'];
};

/** A date field in a form */
export type DateField = FieldInterface & {
  __typename?: 'DateField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A date range filter */
export type DateRangeFilter = FilterInterface & {
  __typename?: 'DateRangeFilter';
  /** The name of the targeted attribute */
  attribute: Scalars['String']['output'];
  /** The label of the field */
  label: Maybe<Scalars['String']['output']>;
};

/** The data for a core/details block */
export type DetailsBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'DetailsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the block is open */
  open: Scalars['Boolean']['output'];
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The summary of the block */
  summary: Scalars['String']['output'];
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/directories block */
export type DirectoriesBlock = BlockInterface & {
  __typename?: 'DirectoriesBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The directories to display */
  directories: Array<Directory>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The title of the block */
  title: Maybe<Scalars['String']['output']>;
  /** The heading level of the title of the block */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A representation of the post types associations, personnes */
export type Directory = {
  __typename?: 'Directory';
  /** Accessibility information */
  accessibility: Maybe<DirectoryAccessibility>;
  /** The company&#039;s activity sector */
  activitySector: Maybe<Scalars['String']['output']>;
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** Whether consent has been given to share the person&#039;s identity */
  consentGiven: Scalars['Boolean']['output'];
  /** Whether consent has been given to share the contact&#039;s identity */
  contactConsentGiven: Scalars['Boolean']['output'];
  /** The contact&#039;s email */
  contactEmail: Maybe<Scalars['String']['output']>;
  /** The contact&#039;s first name */
  contactFirstName: Maybe<Scalars['String']['output']>;
  /** The contact&#039;s last name */
  contactLastName: Maybe<Scalars['String']['output']>;
  /** The email */
  email: Maybe<Scalars['String']['output']>;
  /** The person&#039;s first name */
  firstName: Maybe<Scalars['String']['output']>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The person&#039;s last name */
  lastName: Maybe<Scalars['String']['output']>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The location of the place */
  location: Maybe<Location>;
  /** The name of the mayor */
  mayorName: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The town&#039;s area */
  municipalityArea: Maybe<Scalars['String']['output']>;
  /** The town&#039;s population */
  municipalityPopulation: Maybe<Scalars['Int']['output']>;
  /** The person&#039;s occupied offices */
  offices: Array<Scalars['String']['output']>;
  /** The opening hours of the establishment */
  openingHours: Array<Scalars['String']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The phone numbers */
  phones: Array<Phone>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The association&#039;s RNA number */
  rnaNumber: Maybe<Scalars['String']['output']>;
  /** The company&#039;s SIRET number */
  siretNumber: Maybe<Scalars['String']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The social links */
  socialLinks: Array<SocialLink>;
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The type of the directory */
  type: Scalars['String']['output'];
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
  /** How the post should be displayed */
  viewMode: DirectoryViewMode;
  /** The website */
  website: Maybe<Scalars['String']['output']>;
  /** The company&#039;s headcount */
  workforce: Maybe<Scalars['Int']['output']>;
};

/** The accessibility information of a directory */
export type DirectoryAccessibility = {
  __typename?: 'DirectoryAccessibility';
  /** Status for hearing impairment */
  hearingImpairment: Maybe<DirectoryAccessibilityStatus>;
  /** Status for intellectual impairment */
  intellectualImpairment: Maybe<DirectoryAccessibilityStatus>;
  /** Status for mental impairment */
  mentalImpairment: Maybe<DirectoryAccessibilityStatus>;
  /** Status for reduced mobility */
  reducedMobility: Maybe<DirectoryAccessibilityStatus>;
  /** Status for sign language reception */
  signLanguageReception: Maybe<DirectoryAccessibilityStatus>;
  /** Status for strollers */
  strollers: Maybe<DirectoryAccessibilityStatus>;
  /** Status for visual impairment */
  visualImpairment: Maybe<DirectoryAccessibilityStatus>;
};

/** All possible accessibility status types */
export enum DirectoryAccessibilityStatus {
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  SUPPORTED = 'SUPPORTED',
  UNKNOWN = 'UNKNOWN'
}

/** An input type for filtering posts */
export type DirectoryFilterInput = {
  /** Filter: DirectoryFilterInput */
  associations_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: DirectoryFilterInput */
  categories?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: DirectoryFilterInput */
  personnes_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: DirectoryFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post types associations, personnes */
export type DirectoryList = {
  __typename?: 'DirectoryList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The type of directory posts */
  type: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
  /** The rendering style of a single post */
  viewMode: DirectoryViewMode;
};

/** A representation of the map for the post types associations, personnes */
export type DirectoryMap = {
  __typename?: 'DirectoryMap';
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the map view */
  filters: Array<FilterInterface>;
  /** The metadata of the map view */
  metadata: Maybe<Metadata>;
  /** The full text search filter */
  searchFilter: Maybe<FilterInterface>;
  /** The title of the map page */
  title: Scalars['String']['output'];
  /** The types of directory posts */
  types: Array<Scalars['String']['output']>;
  /** The URL of the map view */
  url: Maybe<Scalars['URL']['output']>;
};

/** Search response for posts */
export type DirectorySearchResponse = {
  __typename?: 'DirectorySearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Directory>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type DirectorySortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** How a directory post should be displayed. */
export enum DirectoryViewMode {
  LOCATION = 'LOCATION',
  PERSON = 'PERSON'
}

/** The data for a citeopolis/discover block */
export type DiscoverBlock = BlockInterface & BlockWithAlignmentInterface & {
  __typename?: 'DiscoverBlock';
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The items in the block */
  items: Array<DiscoverBlockItem>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A discover block item */
export type DiscoverBlockItem = {
  __typename?: 'DiscoverBlockItem';
  /** Button displayed in the content part */
  action: Maybe<Link>;
  /** Image in the image part */
  image: Image;
  /** The position of the image in the block */
  imagePosition: Scalars['String']['output'];
  /** Surtitle of the content part */
  leadText: Scalars['String']['output'];
  /** Title of the content part */
  title: Scalars['String']['output'];
};

/** The data for a citeopolis/download block */
export type DownloadBlock = BlockInterface & {
  __typename?: 'DownloadBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The list of files. */
  files: Array<DownloadBlockFile>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/download block file. */
export type DownloadBlockFile = {
  __typename?: 'DownloadBlockFile';
  /** The size of the file in Bytes. */
  downloadUrl: Scalars['String']['output'];
  /** The extension name of the file. */
  extname: Scalars['String']['output'];
  /** The label of the file. */
  label: Maybe<Scalars['String']['output']>;
  /** The content type of the file. */
  mime: Scalars['String']['output'];
  /** The size of the file in Bytes. */
  size: Scalars['Int']['output'];
};

/** An email field in a form */
export type EmailField = FieldInterface & {
  __typename?: 'EmailField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** The confirmation input */
  confirmation: Maybe<TextField>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The data for a core/embed block */
export type EmbedBlock = BlockInterface & BlockWithMarginInterface & {
  __typename?: 'EmbedBlock';
  /** Whether the embedded content should be resized on smaller devices */
  allowResponsive: Scalars['Boolean']['output'];
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The caption of the embedded content */
  caption: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The HTML code to embed */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The name of the oEmbed provider */
  provider: Maybe<Scalars['String']['output']>;
  /** Whether the embedded content can be resized */
  responsive: Scalars['Boolean']['output'];
  /** The OEmbed thumbnail */
  thumbnail: Maybe<Image>;
  /** The transcription of the video */
  transcription: Maybe<Scalars['String']['output']>;
  /** The type of media to embed */
  type: Maybe<OEmbedResourceType>;
  /** The source URL of the embedded content */
  url: Scalars['String']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The duration of the video in seconds */
  videoDuration: Maybe<Scalars['Int']['output']>;
  /** Available video formats */
  videoFormats: Array<EmbedBlockVideoFormat>;
};

/** A format description for a video */
export type EmbedBlockVideoFormat = {
  __typename?: 'EmbedBlockVideoFormat';
  /** The name of the format */
  name: Scalars['String']['output'];
  /** The weight of the video (in bytes) */
  weight: Scalars['Int']['output'];
};

/** A representation of the post type event */
export type Event = {
  __typename?: 'Event';
  /** Accessibility information */
  accessibility: Maybe<EventAccessibility>;
  /** Target audiences for this event */
  audience: Array<Scalars['String']['output']>;
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The end date */
  endDate: Maybe<Scalars['DateTime']['output']>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** Unused. Kept for compatibility. Will always return null. */
  location: Maybe<Location>;
  /** Locations associated to the news */
  locations: Array<Locatable>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** THe list of all periods for the event */
  periods: Maybe<EventPeriodResponse>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The label describing the recursion periods */
  recurrenceSummary: Maybe<Scalars['String']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The start date */
  startDate: Maybe<Scalars['DateTime']['output']>;
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};


/** A representation of the post type event */
export type EventPeriodsArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<EventPeriodFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
};

/** The accessibility information of an event */
export type EventAccessibility = {
  __typename?: 'EventAccessibility';
  /** Status for hearing impairment */
  hearingImpairment: Maybe<EventAccessibilityStatus>;
  /** Status for intellectual impairment */
  intellectualImpairment: Maybe<EventAccessibilityStatus>;
  /** Status for mental impairment */
  mentalImpairment: Maybe<EventAccessibilityStatus>;
  /** Status for reduced mobility */
  reducedMobility: Maybe<EventAccessibilityStatus>;
  /** Status for sign language reception */
  signLanguageReception: Maybe<EventAccessibilityStatus>;
  /** Status for strollers */
  strollers: Maybe<EventAccessibilityStatus>;
  /** Status for visual impairment */
  visualImpairment: Maybe<EventAccessibilityStatus>;
};

/** All possible accessibility status types */
export enum EventAccessibilityStatus {
  NOT_SUPPORTED = 'NOT_SUPPORTED',
  SUPPORTED = 'SUPPORTED',
  UNKNOWN = 'UNKNOWN'
}

/** An input type for filtering posts */
export type EventFilterInput = {
  /** Filter: EventFilterInput */
  date_range?: InputMaybe<FilterRangeTypeInput>;
  /** Filter: EventFilterInput */
  event_audience?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: EventFilterInput */
  event_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: EventFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type event */
export type EventList = {
  __typename?: 'EventList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** A representation of the map for the post type event */
export type EventMap = {
  __typename?: 'EventMap';
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the map view */
  filters: Array<FilterInterface>;
  /** The metadata of the map view */
  metadata: Maybe<Metadata>;
  /** The full text search filter */
  searchFilter: Maybe<FilterInterface>;
  /** The title of the map page */
  title: Scalars['String']['output'];
  /** The URL of the map view */
  url: Maybe<Scalars['URL']['output']>;
};

/** An event period */
export type EventPeriod = {
  __typename?: 'EventPeriod';
  /** The ending date of the period */
  endDate: Scalars['DateTime']['output'];
  /** Whether the period takes the whole day */
  fullday: Scalars['Boolean']['output'];
  /** The starting date of the period */
  startDate: Scalars['DateTime']['output'];
};

export type EventPeriodFilterInput = {
  /** Start date of the filter period */
  from?: InputMaybe<Scalars['DateTime']['input']>;
  /** End date of the filter period */
  to?: InputMaybe<Scalars['DateTime']['input']>;
  /** Whether to return results that are upcoming */
  upcoming?: InputMaybe<Scalars['Boolean']['input']>;
};

/** A response to an event period query */
export type EventPeriodResponse = {
  __typename?: 'EventPeriodResponse';
  /** Periods */
  items: Array<EventPeriod>;
  /** Pagination information */
  pageInfo: PageInfo;
  /** Total number of items */
  totalCount: Scalars['Int']['output'];
};

/** Search response for posts */
export type EventSearchResponse = {
  __typename?: 'EventSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Event>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type EventSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The data for a citeopolis/events block */
export type EventsBlock = BlockInterface & {
  __typename?: 'EventsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The events to display */
  events: Array<Event>;
  /** The focused event */
  focusedEvent: Maybe<Event>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The URL to the list of news */
  listUrl: Maybe<Scalars['URL']['output']>;
  /** Additional links */
  tags: Array<Link>;
  /** The title of the block */
  title: Maybe<Scalars['String']['output']>;
  /** The heading level of the title of the block */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** All form fields */
export type Field = AddressField | AltchaField | CheckboxField | DateField | EmailField | Fieldset | FileUploadField | GenericCaptchaField | HCaptchaField | HtmlField | ListField | NameField | NumberField | PhoneField | RadioField | ReCaptchaField | SelectField | TextAreaField | TextField | TimeField | WebsiteField;

/** A form field */
export type FieldInterface = {
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** All possible sizes for a form field */
export enum FieldSize {
  LARGE = 'LARGE',
  MEDIUM = 'MEDIUM',
  SMALL = 'SMALL'
}

/** A form field with choices */
export type FieldWithChoicesInterface = {
  /** The choices of the input */
  choices: Array<Option>;
};

/** A fieldset in a form */
export type Fieldset = {
  __typename?: 'Fieldset';
  /** The conditional logic to show or hide the fieldset */
  condition: Maybe<Condition>;
  /** The description of the fieldset */
  description: Maybe<Scalars['String']['output']>;
  /** Inner fields */
  fields: Array<Field>;
  /** Whether the fieldset is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The title of the fieldset */
  title: Scalars['String']['output'];
  /** The type of the field */
  type: Scalars['String']['output'];
};

/** A file */
export type File = {
  __typename?: 'File';
  /** The URL to download the file */
  downloadUrl: Maybe<Scalars['String']['output']>;
  /** The extension of the file */
  extname: Maybe<Scalars['String']['output']>;
  /** The label of the file */
  label: Maybe<Scalars['String']['output']>;
  /** The MIME type of the file */
  mime: Maybe<Scalars['String']['output']>;
  /** The size of the file, in bytes */
  size: Maybe<Scalars['Int']['output']>;
  /** The URL to view the file */
  viewUrl: Maybe<Scalars['String']['output']>;
};

/** The data for a core/file block */
export type FileBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'FileBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The link on the filename */
  buttonLink: Link;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** Whether to preview the file */
  displayPreview: Scalars['Boolean']['output'];
  /** The link on the button */
  fileLink: Link;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The height of the preview, in pixels */
  previewHeight: Maybe<Scalars['Int']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A file upload field in a form */
export type FileUploadField = FieldInterface & {
  __typename?: 'FileUploadField';
  /** List of allowed extensions */
  allowedExtensions: Array<Scalars['String']['output']>;
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The maximum number of files that can be uploaded */
  maxFiles: Maybe<Scalars['Int']['output']>;
  /** The maximum size of uploaded files, in bytes */
  maxSize: Maybe<Scalars['Int']['output']>;
  /** Whether multiple files are allowed */
  multiple: Scalars['Boolean']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** Equal match filter */
export type FilterEqualTypeInput = {
  /** Values to match */
  in?: InputMaybe<Array<Scalars['String']['input']>>;
};

/** Common fields for search filters */
export type FilterInterface = {
  /** The name of the targeted attribute */
  attribute: Scalars['String']['output'];
  /** The label of the field */
  label: Maybe<Scalars['String']['output']>;
};

/** Fuzzy match filter */
export type FilterMatchTypeInput = {
  /** String on which to perform the filter */
  match?: InputMaybe<Scalars['String']['input']>;
};

/** Range filter */
export type FilterRangeTypeInput = {
  /** The lowest possible value in the range. */
  from?: InputMaybe<Scalars['String']['input']>;
  /** The highest possible value in the range. */
  to?: InputMaybe<Scalars['String']['input']>;
};

/** A representation of the post type news-flash */
export type FlashInfo = {
  __typename?: 'FlashInfo';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The news flash&#039;s description */
  description: Maybe<Scalars['String']['output']>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The redirect link */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type FlashInfoFilterInput = {
  /** Filter: FlashInfoFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** Search response for posts */
export type FlashInfoSearchResponse = {
  __typename?: 'FlashInfoSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<FlashInfo>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type FlashInfoSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The configuration of a site */
export type FooterConfig = {
  __typename?: 'FooterConfig';
  /** The button links */
  buttons: Array<Link>;
  /** The name of the site */
  clientInfo: Maybe<ClientInfo>;
  /** The logo for dark backgrounds */
  logoDark: Maybe<Image>;
  /** The logo for light backgrounds */
  logoLight: Maybe<Image>;
  /** The quick access level 1 links */
  quickAccess1: Array<Link>;
  /** The quick access level 2 links */
  quickAccess2: Array<Link>;
  /** The site title */
  title: Scalars['String']['output'];
  /** Top section of the footer */
  top: Maybe<FooterTop>;
};

/** A content section over the footer */
export type FooterTop = {
  __typename?: 'FooterTop';
  /** The button on the right side of the block */
  action: Maybe<Link>;
  /** A long description under the title */
  description: Maybe<Scalars['String']['output']>;
  /** The title of the section. */
  title: Maybe<Scalars['String']['output']>;
};

/** A form */
export type Form = {
  __typename?: 'Form';
  /** The ID of the form */
  id: Scalars['ID']['output'];
  /** All steps in the form */
  steps: Array<FormStep>;
  /** The submit button of the form */
  submitButton: FormAction;
  /** The number of steps in the form */
  totalSteps: Scalars['Int']['output'];
};

/** A form action */
export type FormAction = {
  __typename?: 'FormAction';
  /** The conditional logic to display the button */
  condition: Maybe<Condition>;
  /** The icon of the button */
  icon: Maybe<Icon>;
  /** The label of the button */
  label: Maybe<Scalars['String']['output']>;
};

/** The data for a gravityforms/form block */
export type FormBlock = BlockInterface & {
  __typename?: 'FormBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The form */
  form: Maybe<Form>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

export type FormDataEntryInput = {
  name?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['String']['input']>;
};

/** An error in a form */
export type FormError = {
  __typename?: 'FormError';
  /** The name of the field for which the error applies. If absent, applies to the whole form. */
  fieldName: Maybe<Scalars['String']['output']>;
  /** Error messages */
  messages: Array<Scalars['String']['output']>;
};

/** A step of a form */
export type FormStep = {
  __typename?: 'FormStep';
  /** The condition to show the step */
  condition: Maybe<Condition>;
  /** The description of the step */
  description: Maybe<Scalars['String']['output']>;
  /** The fields in the step */
  fields: Array<Field>;
  /** The Next button */
  next: Maybe<FormAction>;
  /** The Previous button */
  prev: Maybe<FormAction>;
  /** The number of the step */
  stepNumber: Scalars['Int']['output'];
  /** The title of the step */
  title: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/frame block */
export type FrameBlock = BlockInterface & BlockWithAlignmentInterface & {
  __typename?: 'FrameBlock';
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The HTML code of the block text */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** Colors used for the block */
  variant: Scalars['String']['output'];
};

/** The data for a core/gallery block */
export type GalleryBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'GalleryBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The caption of the gallery */
  caption: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The number of columns of the gallery */
  columns: Scalars['Int']['output'];
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Whether to crop images so they fit */
  imageCrop: Scalars['Boolean']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether images should be displayed in a random order */
  randomOrder: Scalars['Boolean']['output'];
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A generic captcha field in a form */
export type GenericCaptchaField = FieldInterface & {
  __typename?: 'GenericCaptchaField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The raw HTML of the field */
  html: Scalars['HTML']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The global map page configuration. */
export type GlobalMap = {
  __typename?: 'GlobalMap';
  /** Short description of the page */
  leadText: Maybe<Scalars['String']['output']>;
  /** Links to other maps */
  mapLinks: Array<Link>;
  /** The metadata of the map view */
  metadata: Maybe<Metadata>;
  /** The title of the page */
  title: Scalars['String']['output'];
  /** URL to the page */
  url: Maybe<Scalars['URL']['output']>;
};

/** Global search page configuration */
export type GlobalSearch = {
  __typename?: 'GlobalSearch';
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The main search filter */
  searchFilter: Maybe<FilterInterface>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** An input type for filtering posts */
export type GlobalSearchFilterInput = {
  /** Filter: GlobalSearchFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
  /** Filter: GlobalSearchFilterInput */
  taxonomies?: InputMaybe<FilterEqualTypeInput>;
};

/** Search response for the global search */
export type GlobalSearchResponse = {
  __typename?: 'GlobalSearchResponse';
  /** The items of the search posts */
  items: Array<Searchable>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type GlobalSearchSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The data for a core/group block */
export type GroupBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithMinHeightInterface & BlockWithPaddingInterface & BlockWithShadowInterface & BlockWithTypographyInterface & {
  __typename?: 'GroupBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The minimum height of the block */
  minHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the group should stick to the top of the page */
  sticky: Scalars['Boolean']['output'];
  /** The tag of the container element */
  tagName: Scalars['String']['output'];
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** An hCaptcha field in a form */
export type HCaptchaField = FieldInterface & {
  __typename?: 'HCaptchaField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The hCaptcha site key */
  siteKey: Scalars['String']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The header configuration */
export type HeaderConfig = {
  __typename?: 'HeaderConfig';
  /** List of buttons displayed in the header */
  buttons: Array<Link>;
  /** The logo for dark backgrounds */
  logoDark: Maybe<Image>;
  /** The logo for light backgrounds */
  logoLight: Maybe<Image>;
};

/** The data for a core/heading block */
export type HeadingBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'HeadingBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML content of the block */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The heading level */
  level: Scalars['Int']['output'];
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The data for a citeopolis/hero block */
export type HeroBlock = BlockInterface & {
  __typename?: 'HeroBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The slides of the block */
  slides: Array<HeroBlockSlide>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a citeopolis/hero block slide. */
export type HeroBlockSlide = {
  __typename?: 'HeroBlockSlide';
  /** URL of the image */
  imageSrc: Maybe<Scalars['String']['output']>;
  /** Surtitle of the slide */
  leadText: Maybe<Scalars['String']['output']>;
  /** Link of the slide */
  link: Maybe<Scalars['String']['output']>;
  /** Title of the slide */
  title: Maybe<Scalars['String']['output']>;
  /** URL of the video */
  videoSrc: Maybe<Scalars['String']['output']>;
};

/** The data for a core/html block */
export type HtmlBlock = BlockInterface & {
  __typename?: 'HtmlBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The raw HTML to insert into the page */
  html: Maybe<Scalars['HTML']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** An html field in a form */
export type HtmlField = {
  __typename?: 'HtmlField';
  /** Whether to disable default margins */
  disableMargins: Scalars['Boolean']['output'];
  /** The HTML to display */
  html: Scalars['HTML']['output'];
  /** The type of the field */
  type: Scalars['String']['output'];
};

/** An icon */
export type Icon = {
  __typename?: 'Icon';
  /** The content of the icon */
  src: Scalars['String']['output'];
  /** The type of the icon */
  type: IconType;
};

/** All possible icon types */
export enum IconType {
  FONT = 'FONT',
  SVG = 'SVG',
  URL = 'URL'
}

/** The data for a citeopolis/iframe block */
export type IframeBlock = {
  __typename?: 'IframeBlock';
  /** The height of the iframe in pixels */
  height: Scalars['Int']['output'];
  /** Whether to add a border to the iframe */
  outlined: Scalars['Boolean']['output'];
  /** The title used as an alternative text for accessibility reasons */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the page to display in the iframe */
  url: Maybe<Scalars['URL']['output']>;
};

/** An image */
export type Image = {
  __typename?: 'Image';
  /** The alternative text of the image */
  alt: Maybe<Scalars['String']['output']>;
  /** The height of the image in pixels */
  height: Scalars['Int']['output'];
  /** The URL of the image */
  url: Scalars['MediaURL']['output'];
  /** The width of the image in pixels */
  width: Scalars['Int']['output'];
};

/** The data for a core/image block */
export type ImageBlock = BlockInterface & BlockWithAlignmentInterface & BlockWithBorderInterface & BlockWithDuotoneInterface & BlockWithMarginInterface & BlockWithShadowInterface & {
  __typename?: 'ImageBlock';
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The alternative text of the image */
  alt: Maybe<Scalars['String']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The desired aspect ratio of the image */
  aspectRatio: Maybe<Scalars['Float']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The caption of the image */
  caption: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** Colors used for the duotone filter */
  duotone: Array<Scalars['CSSValue']['output']>;
  /** The display height of the image, in pixels */
  height: Maybe<Scalars['Int']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** Whether clicking on the image should display it in full */
  lightbox: Scalars['Boolean']['output'];
  /** The link on the image */
  link: Maybe<Link>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The display mode of the image within its container */
  scale: Maybe<Scalars['String']['output']>;
  /** The shadow mode of the block */
  shadow: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the image */
  src: Scalars['MediaURL']['output'];
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** The title of the image */
  title: Maybe<Scalars['String']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The displayed width of the image, in pixels */
  width: Maybe<Scalars['Int']['output']>;
};

/** A collection of images. */
export type ImageCollection = {
  __typename?: 'ImageCollection';
  /** The image with the size 1536x1536 */
  _1536x1536: Maybe<Image>;
  /** The image with the size 2048x2048 */
  _2048x2048: Maybe<Image>;
  /** The image with the size gform-image-choice-lg */
  gformimagechoicelg: Maybe<Image>;
  /** The image with the size gform-image-choice-md */
  gformimagechoicemd: Maybe<Image>;
  /** The image with the size gform-image-choice-sm */
  gformimagechoicesm: Maybe<Image>;
  /** The image with the size large */
  large: Maybe<Image>;
  /** The image with the size medium */
  medium: Maybe<Image>;
  /** The image with the size medium_large */
  medium_large: Maybe<Image>;
  /** The full-sized image */
  original: Maybe<Image>;
  /** The image with the size ratio_1x1 */
  ratio_1x1: Maybe<Image>;
  /** The image with the size ratio_2x3 */
  ratio_2x3: Maybe<Image>;
  /** The image with the size ratio_3x2 */
  ratio_3x2: Maybe<Image>;
  /** The image with the size ratio_3x4 */
  ratio_3x4: Maybe<Image>;
  /** The image with the size ratio_4x3 */
  ratio_4x3: Maybe<Image>;
  /** The image with the size ratio_9x16 */
  ratio_9x16: Maybe<Image>;
  /** The image with the size ratio_16x5 */
  ratio_16x5: Maybe<Image>;
  /** The image with the size ratio_16x9 */
  ratio_16x9: Maybe<Image>;
  /** The image with the size ratio_21x9 */
  ratio_21x9: Maybe<Image>;
  /** The image with the size ratio_A4_landscape */
  ratio_A4_landscape: Maybe<Image>;
  /** The image with the size ratio_A4_portrait */
  ratio_A4_portrait: Maybe<Image>;
  /** The image with the size thumbnail */
  thumbnail: Maybe<Image>;
};

/** A link */
export type Link = {
  __typename?: 'Link';
  /** Extra CSS classes for the link */
  class: Maybe<Scalars['String']['output']>;
  /** The icon of the link */
  icon: Maybe<Icon>;
  /** The rel attribute of the link */
  rel: Maybe<Scalars['String']['output']>;
  /** The target of the link */
  target: Maybe<Scalars['String']['output']>;
  /** The text of the link */
  text: Scalars['String']['output'];
  /** The URL of the link */
  url: Scalars['URL']['output'];
};

/** The data for a core/list block */
export type ListBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'ListBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the list is ordered */
  ordered: Scalars['Boolean']['output'];
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the ordered list has reversed numbering */
  reversed: Scalars['Boolean']['output'];
  /** The item number the list starts at */
  startAt: Maybe<Scalars['Int']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The type of marker used for the list */
  type: Maybe<Scalars['String']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A list field in a form */
export type ListField = FieldInterface & {
  __typename?: 'ListField';
  /** Add Item icon */
  addItemIcon: Maybe<Icon>;
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** Delete Item icon */
  deleteItemIcon: Maybe<Icon>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** Inputs for each row */
  inputs: Array<TextField>;
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The maximum number of rows */
  maxRows: Maybe<Scalars['Int']['output']>;
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** The data for a core/list-item block */
export type ListItemBlock = BlockInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'ListItemBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML of the list item */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A locatable object */
export type Locatable = Directory | Location;

/** A location */
export type Location = {
  __typename?: 'Location';
  /** The address of the location */
  address: Maybe<Address>;
  /** The latitude of the location */
  latitude: Maybe<Scalars['Float']['output']>;
  /** The longitude of the location */
  longitude: Maybe<Scalars['Float']['output']>;
  /** The name of the location */
  title: Maybe<Scalars['String']['output']>;
};

/** The data for an acf/citeopolis-locate block */
export type LocationsBlock = BlockInterface & BlockWithAlignmentInterface & {
  __typename?: 'LocationsBlock';
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** List of locations to render */
  locations: Array<Locatable>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a core/media-text block */
export type MediaTextBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'MediaTextBlock';
  /** The alt text of the image */
  alt: Maybe<Scalars['String']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The horizontal position (0 to 1) of the focal point */
  focalPointX: Maybe<Scalars['Float']['output']>;
  /** The vertical position (0 to 1) of the focal point */
  focalPointY: Maybe<Scalars['Float']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Whether to crop the image in order to fill its container */
  imageFill: Scalars['Boolean']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether the media and text should be stacked on mobile devices */
  isStackedOnMobile: Scalars['Boolean']['output'];
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The link on the image */
  link: Maybe<Link>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The link to the page of the media */
  mediaLink: Maybe<Scalars['URL']['output']>;
  /** The position of the media relative to the text */
  mediaPosition: Maybe<Scalars['String']['output']>;
  /** The type of media (image or video) */
  mediaType: Maybe<Scalars['String']['output']>;
  /** The URL of the media */
  mediaURL: Maybe<Scalars['MediaURL']['output']>;
  /** The width (in percentage) of the media relative to the container */
  mediaWidth: Maybe<Scalars['String']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The vertical alignment mode of inner blocks relative to the media */
  verticalAlignment: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A menu */
export type Menu = {
  __typename?: 'Menu';
  /** The items of the menu */
  items: Array<MenuItem>;
};

/** A menu item */
export type MenuItem = {
  __typename?: 'MenuItem';
  /** The children of the menu item */
  children: Array<MenuItem>;
  /** Extra CSS class for the link */
  className: Maybe<Scalars['String']['output']>;
  /** The description of the menu item */
  description: Maybe<Scalars['String']['output']>;
  /** The level of the menu item in the tree */
  level: Scalars['Int']['output'];
  /** The title attribute of the link */
  linkTitle: Maybe<Scalars['String']['output']>;
  /** The target of the link */
  target: Maybe<Scalars['String']['output']>;
  /** The title of the menu item */
  title: Scalars['String']['output'];
  /** The URL of the menu item */
  url: Scalars['URL']['output'];
  /** XFN relation with the linked site&#039;s owner */
  xfn: Maybe<Scalars['String']['output']>;
};

/** The metadata of a page */
export type Metadata = {
  __typename?: 'Metadata';
  /** The description of the page */
  description: Maybe<Scalars['String']['output']>;
  /** The title of the page */
  title: Scalars['String']['output'];
};

/** A name field in a form */
export type NameField = FieldInterface & {
  __typename?: 'NameField';
  /** The Additional Name field */
  additionalName: Maybe<TextField>;
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** The First Name field */
  firstName: Maybe<TextField>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The Last Name field */
  lastName: Maybe<TextField>;
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Selector of honorific prefixes */
  prefix: Maybe<SelectField>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The Suffix field */
  suffix: Maybe<TextField>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A representation of the post type news */
export type News = {
  __typename?: 'News';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** Unused. Kept for compatibility. Will always return null. */
  location: Maybe<Location>;
  /** Locations associated to the news */
  locations: Array<Locatable>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** The data for a citeopolis/news block */
export type NewsBlock = BlockInterface & {
  __typename?: 'NewsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The brief news to display */
  briefNews: Array<News>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The focused news */
  focusedNews: Maybe<News>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The URL to the list of news */
  listUrl: Maybe<Scalars['URL']['output']>;
  /** The news to display */
  news: Array<News>;
  /** Additional links */
  tags: Array<Link>;
  /** The title of the block */
  title: Maybe<Scalars['String']['output']>;
  /** The heading level of the title of the block */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** An input type for filtering posts */
export type NewsFilterInput = {
  /** Filter: NewsFilterInput */
  news_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: NewsFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type news */
export type NewsList = {
  __typename?: 'NewsList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** A representation of the map for the post type news */
export type NewsMap = {
  __typename?: 'NewsMap';
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the map view */
  filters: Array<FilterInterface>;
  /** The metadata of the map view */
  metadata: Maybe<Metadata>;
  /** The full text search filter */
  searchFilter: Maybe<FilterInterface>;
  /** The title of the map page */
  title: Scalars['String']['output'];
  /** The URL of the map view */
  url: Maybe<Scalars['URL']['output']>;
};

/** Search response for posts */
export type NewsSearchResponse = {
  __typename?: 'NewsSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<News>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type NewsSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** A number field in a form */
export type NumberField = FieldInterface & {
  __typename?: 'NumberField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The maximum value of the field */
  max: Maybe<Scalars['Int']['output']>;
  /** The minimum value of the field */
  min: Maybe<Scalars['Int']['output']>;
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** Type of the embedded content */
export enum OEmbedResourceType {
  LINK = 'LINK',
  PHOTO = 'PHOTO',
  RICH = 'RICH',
  VIDEO = 'VIDEO'
}

/** An option of a select input */
export type Option = {
  __typename?: 'Option';
  /** Whether the option is selected by default */
  defaultSelected: Maybe<Scalars['Boolean']['output']>;
  /** The icon of the choice */
  icon: Maybe<Icon>;
  /** The label of the option */
  label: Maybe<Scalars['String']['output']>;
  /** The value of the option */
  value: Maybe<Scalars['String']['output']>;
};

/** All possible orientations */
export enum Orientation {
  HORIZONTAL = 'HORIZONTAL',
  VERTICAL = 'VERTICAL'
}

/** A representation of the post type page */
export type Page = {
  __typename?: 'Page';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type PageFilterInput = {
  /** Filter: PageFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** The pagination information */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** Current page */
  currentPage: Scalars['Int']['output'];
  /** The number of items in each page */
  pageSize: Scalars['Int']['output'];
  /** The maximum number of pages for the paginated content */
  totalPages: Scalars['Int']['output'];
};

/** Search response for posts */
export type PageSearchResponse = {
  __typename?: 'PageSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Page>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type PageSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The data for a core/paragraph block */
export type ParagraphBlock = BlockInterface & BlockWithAlignmentInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'ParagraphBlock';
  /** The block&#039;s alignment setting */
  align: Maybe<Scalars['CSSValue']['output']>;
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** Whether a drop cap is enabled for the text */
  dropCap: Maybe<Scalars['Boolean']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML content of the paragraph */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The display mode for a block, in the page content column. */
  layout: Maybe<BlockLayout>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The text alignment set for the block */
  textAlign: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A representation of the post type personnes */
export type Personnes = {
  __typename?: 'Personnes';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type PersonnesFilterInput = {
  /** Filter: PersonnesFilterInput */
  personnes_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: PersonnesFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type personnes */
export type PersonnesList = {
  __typename?: 'PersonnesList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** Search response for posts */
export type PersonnesSearchResponse = {
  __typename?: 'PersonnesSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Personnes>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type PersonnesSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** A phone number */
export type Phone = {
  __typename?: 'Phone';
  /** The country code in ISO 3166-1 alpha-2 format. */
  country: Maybe<Scalars['String']['output']>;
  /** The type of device this number is attributed to. */
  deviceType: Maybe<PhoneDeviceType>;
  /** The number in international format. */
  internationalNumber: Scalars['String']['output'];
  /** The number&#039;s label. */
  label: Maybe<Scalars['String']['output']>;
  /** The number in local format. */
  number: Scalars['String']['output'];
};

/** All possible types of phone */
export enum PhoneDeviceType {
  FAX = 'FAX',
  LANDLINE = 'LANDLINE',
  MOBILE = 'MOBILE',
  OTHER = 'OTHER'
}

/** A phone field in a form */
export type PhoneField = FieldInterface & {
  __typename?: 'PhoneField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A representation of the post type post */
export type Post = {
  __typename?: 'Post';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type PostFilterInput = {
  /** Filter: PostFilterInput */
  category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: PostFilterInput */
  post_format?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: PostFilterInput */
  post_tag?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: PostFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** Search response for posts */
export type PostSearchResponse = {
  __typename?: 'PostSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Post>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type PostSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** All possible post statuses */
export enum PostStatus {
  ACF_DISABLED = 'ACF_DISABLED',
  AUTO_DRAFT = 'AUTO_DRAFT',
  DRAFT = 'DRAFT',
  EXPIRED = 'EXPIRED',
  FUTURE = 'FUTURE',
  INHERIT = 'INHERIT',
  PENDING = 'PENDING',
  PRIVATE = 'PRIVATE',
  PUBLISHED = 'PUBLISHED',
  REQUEST_COMPLETED = 'REQUEST_COMPLETED',
  REQUEST_CONFIRMED = 'REQUEST_CONFIRMED',
  REQUEST_FAILED = 'REQUEST_FAILED',
  REQUEST_PENDING = 'REQUEST_PENDING',
  TRASH = 'TRASH'
}

/** The data for a core/preformatted block */
export type PreformattedBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'PreformattedBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML content of the preformatted block */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A representation of the post type publication */
export type Publication = {
  __typename?: 'Publication';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The files attached to the publication */
  files: Array<File>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The subtitle of the post */
  subtitle: Maybe<Scalars['String']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type PublicationFilterInput = {
  /** Filter: PublicationFilterInput */
  publication_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: PublicationFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type publication */
export type PublicationList = {
  __typename?: 'PublicationList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** Search response for posts */
export type PublicationSearchResponse = {
  __typename?: 'PublicationSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Publication>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type PublicationSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The data for a citeopolis/publications block */
export type PublicationsBlock = BlockInterface & {
  __typename?: 'PublicationsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The URL to the list of news */
  listUrl: Maybe<Scalars['URL']['output']>;
  /** The publications to display */
  publications: Array<Publication>;
  /** The title of the block */
  title: Maybe<Scalars['String']['output']>;
  /** The heading level of the title of the block */
  titleLevel: Scalars['Int']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a core/pullquote block */
export type PullquoteBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithMinHeightInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'PullquoteBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML of the quote */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The minimum height of the block */
  minHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML of the source */
  sourceHTML: Maybe<Scalars['HTML']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The data for a citeopolis/quick-accesses block */
export type QuickAccessBlock = BlockInterface & {
  __typename?: 'QuickAccessBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The focused item in the block */
  focus: Maybe<QuickAccessBlockFocus>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The links in the block */
  items: Array<Link>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for the focused item of a citeopolis/quick-accesses block */
export type QuickAccessBlockFocus = {
  __typename?: 'QuickAccessBlockFocus';
  /** The icon of the focused item */
  icon: Maybe<Icon>;
  /** The title of the focused item */
  title: Scalars['String']['output'];
  /** The URL of the focused item */
  url: Scalars['URL']['output'];
};

/** The data for a core/quote block */
export type QuoteBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBlockGapInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithMinHeightInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'QuoteBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The horizontal spacing between inner blocks */
  blockGapH: Maybe<Scalars['CSSValue']['output']>;
  /** The vertical spacing between inner blocks */
  blockGapV: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The minimum height of the block */
  minHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML of the source */
  sourceHTML: Scalars['String']['output'];
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The global RGAA configuration */
export type RgaaConfig = {
  __typename?: 'RGAAConfig';
  /** The accessibility level of the website */
  status: RgaaStatus;
};

/** All possible RGAA statuses */
export enum RgaaStatus {
  COMPLIANT = 'COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  NO_DISPLAY = 'NO_DISPLAY',
  PARTIAL = 'PARTIAL'
}

/** A radio field in a form */
export type RadioField = FieldInterface & FieldWithChoicesInterface & {
  __typename?: 'RadioField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The choices of the input */
  choices: Array<Option>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The field for a custom choice */
  other: Maybe<TextField>;
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A reCAPTCHA field in a form */
export type ReCaptchaField = FieldInterface & {
  __typename?: 'ReCaptchaField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The mode for reCAPTCHA V2 */
  mode: ReCaptchaMode;
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The reCAPTCHA site key */
  siteKey: Scalars['String']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
  /** reCAPTCHA version */
  version: Scalars['Int']['output'];
};

/** reCAPTCHA V2 modes */
export enum ReCaptchaMode {
  CHECKBOX = 'CHECKBOX',
  INVISIBLE = 'INVISIBLE'
}

/** A redirect */
export type Redirect = {
  __typename?: 'Redirect';
  /** The HTTP code of the redirect */
  redirectCode: Maybe<Scalars['Int']['output']>;
  /** The URL to redirect to */
  relativeUrl: Scalars['URL']['output'];
};

/** A representation of the post type resolution */
export type Resolution = {
  __typename?: 'Resolution';
  /** The breadcrumb trail of the post */
  breadcrumbs: Breadcrumbs;
  /** List of categories of the post */
  categories: Array<Category>;
  /** The number of files */
  fileCount: Scalars['Int']['output'];
  /** The files attached to the resolution */
  files: Array<File>;
  /** The ID of the post */
  id: Scalars['Int']['output'];
  /** The featured image in various sizes */
  images: Maybe<ImageCollection>;
  /** The issue date of the resolution */
  issueDate: Maybe<Scalars['DateTime']['output']>;
  /** The lead text of the post */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the post */
  metadata: Maybe<Metadata>;
  /** The date at which the post was last modified */
  modifiedDate: Maybe<Scalars['DateTime']['output']>;
  /** The navigation between posts relative to this post */
  pager: Maybe<ContentPager>;
  /** The date at which the post was published */
  publicationDate: Maybe<Scalars['DateTime']['output']>;
  /** The slug of the post */
  slug: Scalars['String']['output'];
  /** The status of the post */
  status: PostStatus;
  /** The content of the post (in reality a [BlockInterface!]) */
  structuredContent: Maybe<Scalars['StructuredContent']['output']>;
  /** The surtitle of the post */
  surtitle: Maybe<Scalars['String']['output']>;
  /** The title of the post */
  title: Maybe<Scalars['String']['output']>;
  /** The URL of the post */
  url: Maybe<Scalars['URL']['output']>;
};

/** An input type for filtering posts */
export type ResolutionFilterInput = {
  /** Filter: ResolutionFilterInput */
  issueYear?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: ResolutionFilterInput */
  resolution_category?: InputMaybe<FilterEqualTypeInput>;
  /** Filter: ResolutionFilterInput */
  s?: InputMaybe<FilterMatchTypeInput>;
};

/** A representation of the list for the post type resolution */
export type ResolutionList = {
  __typename?: 'ResolutionList';
  /** The background image */
  backgroundImage: Maybe<Image>;
  /** The breadcrumbs of the list view */
  breadcrumbs: Maybe<Breadcrumbs>;
  /** The default page size */
  defaultPageSize: Scalars['Int']['output'];
  /** The filters on the list view */
  filters: Array<FilterInterface>;
  /** The lead text on the list view */
  leadText: Maybe<Scalars['String']['output']>;
  /** The metadata of the list view */
  metadata: Maybe<Metadata>;
  /** The URL to the proposal page */
  proposeUrl: Maybe<Scalars['URL']['output']>;
  /** The URL of the RSS feed */
  rssUrl: Maybe<Scalars['AbsoluteURL']['output']>;
  /** The title of the list page */
  title: Scalars['String']['output'];
  /** The URL of the list view */
  url: Scalars['URL']['output'];
};

/** Search response for posts */
export type ResolutionSearchResponse = {
  __typename?: 'ResolutionSearchResponse';
  /** Filters that can be applied on search results */
  filters: Array<FilterInterface>;
  /** The items of the search posts */
  items: Array<Resolution>;
  /** The pagination information */
  pageInfo: PageInfo;
  /** The total number of posts */
  totalCount: Scalars['Int']['output'];
};

/** An input type for sorting posts */
export type ResolutionSortInput = {
  /** How to sort by publication date */
  publicationDate?: InputMaybe<SortDirection>;
  /** How to sort by title */
  title?: InputMaybe<SortDirection>;
};

/** The root mutation */
export type RootMutation = {
  __typename?: 'RootMutation';
  submitForm: SubmitFormResponse;
};


/** The root mutation */
export type RootMutationSubmitFormArgs = {
  formData: Array<FormDataEntryInput>;
  formId: Scalars['Int']['input'];
};

/** Schema root */
export type RootQuery = {
  __typename?: 'RootQuery';
  /** Fetch a single album */
  album: Maybe<Album>;
  /** List view of album */
  albumSearch: Maybe<AlbumSearchResponse>;
  /** Fetch a single alert */
  alert: Maybe<Alert>;
  /** List view of alert */
  alertSearch: Maybe<AlertSearchResponse>;
  /** Fetch a single associations */
  associations: Maybe<Associations>;
  /** List view of associations */
  associationsSearch: Maybe<AssociationsSearchResponse>;
  /** Fetch a single attachment */
  attachment: Maybe<Attachment>;
  /** List view of attachment */
  attachmentSearch: Maybe<AttachmentSearchResponse>;
  /** Retrieve the configuration of the admin bar */
  barConfig: Maybe<BarConfig>;
  /** Fetch directory posts */
  directory: Maybe<Directory>;
  /** List view of associations, personnes */
  directorySearch: Maybe<DirectorySearchResponse>;
  /** Fetch a single event */
  event: Maybe<Event>;
  /** List view of event */
  eventSearch: Maybe<EventSearchResponse>;
  /** Fetch a single news-flash */
  flashInfo: Maybe<FlashInfo>;
  /** List view of news-flash */
  flashInfoSearch: Maybe<FlashInfoSearchResponse>;
  /** Retrieve a menu */
  menu: Maybe<Menu>;
  /** Fetch a single news */
  news: Maybe<News>;
  /** List view of news */
  newsSearch: Maybe<NewsSearchResponse>;
  /** Fetch a single page */
  page: Maybe<Page>;
  /** List view of page */
  pageSearch: Maybe<PageSearchResponse>;
  /** Fetch a single personnes */
  personnes: Maybe<Personnes>;
  /** List view of personnes */
  personnesSearch: Maybe<PersonnesSearchResponse>;
  /** Fetch a single post */
  post: Maybe<Post>;
  /** List view of post */
  postSearch: Maybe<PostSearchResponse>;
  /** Fetch a single publication */
  publication: Maybe<Publication>;
  /** List view of publication */
  publicationSearch: Maybe<PublicationSearchResponse>;
  /** Fetch a single resolution */
  resolution: Maybe<Resolution>;
  /** List view of resolution */
  resolutionSearch: Maybe<ResolutionSearchResponse>;
  /** Retrieve a routable object */
  route: Maybe<Routable>;
  /** Global search */
  search: Maybe<GlobalSearchResponse>;
  /** Retrieve the configuration of the site */
  siteConfig: Maybe<SiteConfig>;
};


/** Schema root */
export type RootQueryAlbumArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryAlbumSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<AlbumFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<AlbumSortInput>;
};


/** Schema root */
export type RootQueryAlertArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryAlertSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<AlertFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<AlertSortInput>;
};


/** Schema root */
export type RootQueryAssociationsArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryAssociationsSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<AssociationsFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<AssociationsSortInput>;
};


/** Schema root */
export type RootQueryAttachmentArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryAttachmentSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<AttachmentFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<AttachmentSortInput>;
};


/** Schema root */
export type RootQueryBarConfigArgs = {
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryDirectoryArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryDirectorySearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<DirectoryFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<DirectorySortInput>;
  type: Scalars['String']['input'];
};


/** Schema root */
export type RootQueryEventArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryEventSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<EventFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<EventSortInput>;
};


/** Schema root */
export type RootQueryFlashInfoArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryFlashInfoSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<FlashInfoFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<FlashInfoSortInput>;
};


/** Schema root */
export type RootQueryMenuArgs = {
  position: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryNewsArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryNewsSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<NewsFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<NewsSortInput>;
};


/** Schema root */
export type RootQueryPageArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryPageSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<PageFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<PageSortInput>;
};


/** Schema root */
export type RootQueryPersonnesArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryPersonnesSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<PersonnesFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<PersonnesSortInput>;
};


/** Schema root */
export type RootQueryPostArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryPostSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<PostFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<PostSortInput>;
};


/** Schema root */
export type RootQueryPublicationArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryPublicationSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<PublicationFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<PublicationSortInput>;
};


/** Schema root */
export type RootQueryResolutionArgs = {
  id: InputMaybe<Scalars['Int']['input']>;
  slug: InputMaybe<Scalars['String']['input']>;
  url: InputMaybe<Scalars['String']['input']>;
};


/** Schema root */
export type RootQueryResolutionSearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<ResolutionFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<ResolutionSortInput>;
};


/** Schema root */
export type RootQueryRouteArgs = {
  url: InputMaybe<Scalars['URL']['input']>;
};


/** Schema root */
export type RootQuerySearchArgs = {
  currentPage: InputMaybe<Scalars['Int']['input']>;
  filter: InputMaybe<GlobalSearchFilterInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  sort: InputMaybe<GlobalSearchSortInput>;
};

/** A routable object */
export type Routable = Album | AlbumList | Alert | Associations | AssociationsList | AssociationsMap | Attachment | Directory | DirectoryList | DirectoryMap | Event | EventList | EventMap | FlashInfo | GlobalMap | GlobalSearch | News | NewsList | NewsMap | Page | Personnes | PersonnesList | Post | Publication | PublicationList | Redirect | Resolution | ResolutionList;

/** A searchable object */
export type Searchable = Album | Associations | Attachment | Directory | Event | News | Page | Personnes | Post | Publication | Resolution;

/** A select field in a form */
export type SelectField = FieldInterface & FieldWithChoicesInterface & {
  __typename?: 'SelectField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The choices of the input */
  choices: Array<Option>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether to enable the enhanced UI on the field */
  enhancedUI: Scalars['Boolean']['output'];
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A select filter */
export type SelectFilter = FilterInterface & {
  __typename?: 'SelectFilter';
  /** The name of the targeted attribute */
  attribute: Scalars['String']['output'];
  /** The label of the field */
  label: Maybe<Scalars['String']['output']>;
  /** Whether the filter accepts multiple values */
  multiple: Maybe<Scalars['Boolean']['output']>;
  /** The options of the filter */
  options: Array<SelectFilterOption>;
  /** The placeholder of the filter */
  placeholder: Maybe<Scalars['String']['output']>;
};

/** A select filter option */
export type SelectFilterOption = {
  __typename?: 'SelectFilterOption';
  /** The child options of the option */
  children: Array<SelectFilterOption>;
  /** The number of matches for this option, if known */
  count: Maybe<Scalars['Int']['output']>;
  /** The label of the option */
  label: Maybe<Scalars['String']['output']>;
  /** The value of the option */
  value: Scalars['String']['output'];
};

/** The data for a core/separator block */
export type SeparatorBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithMarginInterface & {
  __typename?: 'SeparatorBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The configuration of a site */
export type SiteConfig = {
  __typename?: 'SiteConfig';
  /** The footer configuration data */
  footer: FooterConfig;
  /** The header configuration data */
  header: HeaderConfig;
  /** The site&#039;s logo */
  logo: Maybe<Image>;
  /** The RGAA configuration */
  rgaa: Maybe<RgaaConfig>;
  /** The URL of the search page */
  searchPage: Scalars['URL']['output'];
  /** The name of the site */
  siteName: Scalars['String']['output'];
  /** The social networks links */
  socialLinks: Array<SocialLink>;
  /** Whether to enable social sharing */
  socialShare: Scalars['Boolean']['output'];
};

/** The data for a citeopolis/sitemap block */
export type SitemapBlock = BlockInterface & {
  __typename?: 'SitemapBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Site map entries */
  sitemap: Array<SitemapEntry>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** An entry of the sitemap */
export type SitemapEntry = {
  __typename?: 'SitemapEntry';
  /** Sub-entries */
  children: Array<SitemapEntry>;
  /** The level of the entry */
  level: Scalars['Int']['output'];
  /** The title of the entry */
  title: Scalars['String']['output'];
  /** The URL of the entry */
  url: Scalars['URL']['output'];
};

/** A link to a social network page */
export type SocialLink = {
  __typename?: 'SocialLink';
  /** The network */
  network: SocialNetwork;
  /** The page title */
  text: Scalars['String']['output'];
  /** The page URL */
  url: Scalars['URL']['output'];
};

/** All known social networks */
export enum SocialNetwork {
  FACEBOOK = 'FACEBOOK',
  INSTAGRAM = 'INSTAGRAM',
  LINKEDIN = 'LINKEDIN',
  TWITTER = 'TWITTER',
  YOUTUBE = 'YOUTUBE'
}

/** Sort directions */
export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

/** The data for a core/spacer block */
export type SpacerBlock = BlockInterface & BlockWithMarginInterface & {
  __typename?: 'SpacerBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The height of the spacer */
  height: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The response to a form submission */
export type SubmitFormResponse = {
  __typename?: 'SubmitFormResponse';
  /** Submission errors */
  errors: Array<FormError>;
  /** The success message */
  message: Maybe<Scalars['String']['output']>;
  /** The HTTP code of the redirect */
  redirectCode: Maybe<Scalars['Int']['output']>;
  /** The URL to redirect to */
  relativeUrl: Maybe<Scalars['URL']['output']>;
  /** Whether the form submission was successful */
  success: Maybe<Scalars['Boolean']['output']>;
};

/** The data for a citeopolis/tab block */
export type TabBlock = BlockInterface & {
  __typename?: 'TabBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The title of the tab. */
  title: Maybe<Scalars['String']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** The data for a core/table block */
export type TableBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithBorderInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'TableBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the border */
  borderColor: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s border */
  borderRadius: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom left angle */
  borderRadiusBottomLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s bottom right angle */
  borderRadiusLeftRight: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top left angle */
  borderRadiusTopLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The radius of the block&#039;s top right angle */
  borderRadiusTopRight: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the border */
  borderStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The border size of the block */
  borderWidth: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom border size of the block */
  borderWidthBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left border size of the block */
  borderWidthLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right border size of the block */
  borderWidthRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top border size of the block */
  borderWidthTop: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The caption of the table */
  caption: Maybe<Scalars['String']['output']>;
  /** The data in the table */
  cells: Array<Array<TableCell>>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The alignment of each column */
  columnAlign: Array<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** Whether the first column should be a header column */
  firstColumnHeader: Scalars['Boolean']['output'];
  /** Whether the table has fixed-width columns */
  fixedLayout: Scalars['Boolean']['output'];
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The footer cells of the table */
  footer: Array<TableCell>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The header cells of the table */
  header: Array<TableCell>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** A table cell */
export type TableCell = {
  __typename?: 'TableCell';
  /** The number of columns the cell spans */
  colspan: Maybe<Scalars['Int']['output']>;
  /** The cell&#039;s content */
  html: Scalars['HTML']['output'];
  /** The number of rows the cell spans */
  rowspan: Maybe<Scalars['Int']['output']>;
};

/** The data for a citeopolis/tabs block */
export type TabsBlock = BlockInterface & {
  __typename?: 'TabsBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Tab orientation */
  orientation: Scalars['String']['output'];
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A textarea field in a form */
export type TextAreaField = FieldInterface & {
  __typename?: 'TextAreaField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
  /** Enable the RTE on the field */
  wysiwyg: Scalars['Boolean']['output'];
};

/** A text field in a form */
export type TextField = FieldInterface & {
  __typename?: 'TextField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** A text filter */
export type TextFilter = FilterInterface & {
  __typename?: 'TextFilter';
  /** The name of the targeted attribute */
  attribute: Scalars['String']['output'];
  /** The label of the field */
  label: Maybe<Scalars['String']['output']>;
};

/** A time field in a form */
export type TimeField = FieldInterface & {
  __typename?: 'TimeField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** The time format of the field */
  format: TimeFormat;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The hours input */
  hours: Maybe<NumberField>;
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The AM/PM selector */
  meridiem: Maybe<SelectField>;
  /** The minutes field */
  minutes: Maybe<NumberField>;
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

/** Time formats */
export enum TimeFormat {
  H12 = 'H12',
  H24 = 'H24'
}

/** A user */
export type User = {
  __typename?: 'User';
  /** The avatar of the user */
  avatar: Maybe<Scalars['URL']['output']>;
  /** The display name of the user */
  displayName: Scalars['String']['output'];
  /** The URL to edit the user&#039;s profile */
  editProfileUrl: Maybe<Scalars['URL']['output']>;
  /** The unique identifier of the user */
  id: Scalars['ID']['output'];
};

/** The data for a core/verse block */
export type VerseBlock = BlockInterface & BlockWithBackgroundInterface & BlockWithColorInterface & BlockWithMarginInterface & BlockWithMinHeightInterface & BlockWithPaddingInterface & BlockWithTypographyInterface & {
  __typename?: 'VerseBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** The background color of the block */
  backgroundColor: Maybe<Scalars['CSSValue']['output']>;
  /** The URL of the background image */
  backgroundImage: Maybe<Scalars['String']['output']>;
  /** The sizing mode of the background image */
  backgroundSize: Maybe<Scalars['CSSValue']['output']>;
  /** The background color of buttons in the block */
  buttonBackground: Maybe<Scalars['CSSValue']['output']>;
  /** The background gradient of buttons in the block */
  buttonGradient: Maybe<Scalars['CSSValue']['output']>;
  /** The color of button text in the block */
  buttonTextColor: Maybe<Scalars['CSSValue']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** The ratio (0 to 100) at which to dim the picture */
  dimRatio: Maybe<Scalars['Int']['output']>;
  /** The font of the text in the block */
  fontFamily: Maybe<Scalars['CSSValue']['output']>;
  /** The font size of the text in the block */
  fontSize: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the font of the text */
  fontStyle: Maybe<Scalars['CSSValue']['output']>;
  /** The weight of the font of the text */
  fontWeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 1 headings in the block */
  h1Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 2 headings in the block */
  h2Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 3 headings in the block */
  h3Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 4 headings in the block */
  h4Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 5 headings in the block */
  h5Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of level 6 headings in the block */
  h6Color: Maybe<Scalars['CSSValue']['output']>;
  /** The color of headings in the block */
  headingColor: Maybe<Scalars['CSSValue']['output']>;
  /** The HTML of the verse */
  html: Scalars['HTML']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** The letter spacing of the text */
  letterSpacing: Maybe<Scalars['CSSValue']['output']>;
  /** The line height of the text */
  lineHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of links in the block */
  linkColor: Maybe<Scalars['CSSValue']['output']>;
  /** The color of hovered links in the block */
  linkHoverColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** The minimum height of the block */
  minHeight: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the overlay of the block */
  overlayColor: Maybe<Scalars['CSSValue']['output']>;
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** The color of the text in the block */
  textColor: Maybe<Scalars['CSSValue']['output']>;
  /** The decoration style of the text */
  textDecoration: Maybe<Scalars['CSSValue']['output']>;
  /** The transformation mode of the text */
  textTransform: Maybe<Scalars['CSSValue']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
  /** The writing mode of the text */
  writingMode: Maybe<Scalars['CSSValue']['output']>;
};

/** The data for a core/video block */
export type VideoBlock = BlockInterface & BlockWithMarginInterface & BlockWithPaddingInterface & {
  __typename?: 'VideoBlock';
  /** The anchor name of the block (i.e. its ID) */
  anchor: Maybe<Scalars['String']['output']>;
  /** Whether the video should automatically play */
  autoplay: Scalars['Boolean']['output'];
  /** The caption of the video file */
  caption: Maybe<Scalars['String']['output']>;
  /** The class names of the block */
  className: Maybe<Scalars['String']['output']>;
  /** Whether to show controls of the video player */
  controls: Scalars['Boolean']['output'];
  /** The ID of the block within its parent post */
  id: Scalars['ID']['output'];
  /** Nested blocks */
  innerBlocks: Array<BlockInterface>;
  /** Whether the video should loop */
  loop: Scalars['Boolean']['output'];
  /** The bottom margin of the block */
  marginBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left margin of the block */
  marginLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right margin of the block */
  marginRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top margin of the block */
  marginTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the video should be muted by default */
  muted: Scalars['Boolean']['output'];
  /** The bottom padding of the block */
  paddingBottom: Maybe<Scalars['CSSValue']['output']>;
  /** The left padding of the block */
  paddingLeft: Maybe<Scalars['CSSValue']['output']>;
  /** The right padding of the block */
  paddingRight: Maybe<Scalars['CSSValue']['output']>;
  /** The top padding of the block */
  paddingTop: Maybe<Scalars['CSSValue']['output']>;
  /** Whether the video should play inline on mobile devices */
  playsinline: Scalars['Boolean']['output'];
  /** The URL to the cover image for the video */
  poster: Maybe<Scalars['MediaURL']['output']>;
  /** The preloading mode of the audio player */
  preload: Maybe<Scalars['String']['output']>;
  /** The URL of the video file */
  src: Scalars['MediaURL']['output'];
  /** Text tracks for the video */
  tracks: Array<Scalars['Record']['output']>;
  /** The style of the block */
  variant: Maybe<Scalars['String']['output']>;
};

/** A URL field in a form */
export type WebsiteField = FieldInterface & {
  __typename?: 'WebsiteField';
  /** The autocomplete attribute of the field */
  autocomplete: Maybe<Scalars['String']['output']>;
  /** The width of the field, in columns (max 12) */
  columnSpan: Scalars['Int']['output'];
  /** The conditional logic to show or hide the field */
  condition: Maybe<Condition>;
  /** Custom CSS classes for the field */
  cssClass: Maybe<Scalars['String']['output']>;
  /** The default value of the field */
  defaultValue: Maybe<Scalars['String']['output']>;
  /** The description of the field */
  description: Maybe<Scalars['String']['output']>;
  /** The position at which to show the field&#039;s description */
  descriptionPlacement: Maybe<Scalars['String']['output']>;
  /** Whether the field is hidden */
  hidden: Scalars['Boolean']['output'];
  /** The label of the field */
  label: Scalars['String']['output'];
  /** The name of the field&#039;s input */
  name: Scalars['String']['output'];
  /** The input mask of the field */
  pattern: Maybe<Scalars['String']['output']>;
  /** The placeholder value of the field */
  placeholder: Maybe<Scalars['String']['output']>;
  /** Whether the field is required */
  required: Scalars['Boolean']['output'];
  /** Whether to show the label of the field */
  showLabel: Scalars['Boolean']['output'];
  /** The display size of the field */
  size: Maybe<FieldSize>;
  /** The type of the field */
  type: Scalars['String']['output'];
  /** The error message to display when the field fails validation */
  validationMessage: Maybe<Scalars['String']['output']>;
};

export type GetAlbumListConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetAlbumListConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList', defaultPageSize: number, leadText: string | null, url: string, title: string, rssUrl: any | null, proposeUrl: string | null, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetAlbumListQueryVariables = Exact<{
  filter: InputMaybe<AlbumFilterInput>;
  sort: InputMaybe<AlbumSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetAlbumListQuery = { __typename?: 'RootQuery', albumSearch: { __typename?: 'AlbumSearchResponse', totalCount: number, items: Array<{ __typename?: 'Album', id: number, leadText: string | null, url: string | null, title: string | null, videoCount: number, photoCount: number, mediaCount: number, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, media: Array<{ __typename?: 'AlbumPhoto', alt: string | null, caption: string | null } | { __typename?: 'AlbumVideo', caption: string | null, provider: string | null, thumbnail: { __typename?: 'Image', url: any, width: number, height: number, alt: string | null } | null }>, categories: Array<{ __typename?: 'Category', title: string }> }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type SelectFilterOptionFragmentFragment = { __typename?: 'SelectFilterOption', label: string | null, value: string, count: number | null } & { ' $fragmentName'?: 'SelectFilterOptionFragmentFragment' };

export type GetAlbumQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetAlbumQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album', id: number, mediaCount: number, photoCount: number, videoCount: number, title: string | null, status: PostStatus, leadText: string | null, structuredContent: any | null, publicationDate: any | null, modifiedDate: any | null, media: Array<{ __typename?: 'AlbumPhoto', alt: string | null, caption: string | null, src: any | null, height: number | null, width: number | null } | { __typename?: 'AlbumVideo', url: string | null, caption: string | null, provider: string | null, thumbnail: { __typename?: 'Image', url: any, width: number, height: number, alt: string | null } | null }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string, description: string | null, parent: { __typename: 'Category' } | null }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string, siblings: Array<{ __typename?: 'Crumb', title: string, url: string }> }> }, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetDirectoryDetailsQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetDirectoryDetailsQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList', defaultPageSize: number, leadText: string | null, proposeUrl: string | null, rssUrl: any | null, title: string, url: string, viewMode: DirectoryViewMode, type: string, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetDirectoryListQueryVariables = Exact<{
  type: Scalars['String']['input'];
  filter: InputMaybe<DirectoryFilterInput>;
  sort: InputMaybe<DirectorySortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetDirectoryListQuery = { __typename?: 'RootQuery', directorySearch: { __typename?: 'DirectorySearchResponse', totalCount: number, items: Array<{ __typename?: 'Directory', id: number, openingHours: Array<string>, offices: Array<string>, title: string | null, url: string | null, viewMode: DirectoryViewMode, website: string | null, email: string | null, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null, ratio_1x1: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, location: { __typename?: 'Location', address: { __typename?: 'Address', city: string | null, country: string | null, street: Array<string>, zip: string | null } | null } | null, phones: Array<{ __typename?: 'Phone', deviceType: PhoneDeviceType | null, number: string }>, accessibility: { __typename?: 'DirectoryAccessibility', hearingImpairment: DirectoryAccessibilityStatus | null, intellectualImpairment: DirectoryAccessibilityStatus | null, mentalImpairment: DirectoryAccessibilityStatus | null, reducedMobility: DirectoryAccessibilityStatus | null, signLanguageReception: DirectoryAccessibilityStatus | null, strollers: DirectoryAccessibilityStatus | null, visualImpairment: DirectoryAccessibilityStatus | null } | null }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type GetDirectoryQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetDirectoryQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory', viewMode: DirectoryViewMode, title: string | null, leadText: string | null, structuredContent: any | null, publicationDate: any | null, modifiedDate: any | null, email: string | null, website: string | null, openingHours: Array<string>, offices: Array<string>, municipalityArea: string | null, municipalityPopulation: number | null, mayorName: string | null, contactFirstName: string | null, contactLastName: string | null, contactEmail: string | null, phones: Array<{ __typename?: 'Phone', number: string, deviceType: PhoneDeviceType | null, country: string | null, internationalNumber: string, label: string | null }>, accessibility: { __typename?: 'DirectoryAccessibility', hearingImpairment: DirectoryAccessibilityStatus | null, intellectualImpairment: DirectoryAccessibilityStatus | null, mentalImpairment: DirectoryAccessibilityStatus | null, reducedMobility: DirectoryAccessibilityStatus | null, signLanguageReception: DirectoryAccessibilityStatus | null, strollers: DirectoryAccessibilityStatus | null, visualImpairment: DirectoryAccessibilityStatus | null } | null, images: { __typename?: 'ImageCollection', ratio_1x1: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, socialLinks: Array<{ __typename?: 'SocialLink', network: SocialNetwork, text: string, url: string }>, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string, description: string | null, parent: { __typename: 'Category' } | null }>, location: { __typename?: 'Location', longitude: number | null, latitude: number | null, address: { __typename?: 'Address', street: Array<string>, city: string | null, zip: string | null } | null } | null, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> }, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetEventListConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetEventListConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList', defaultPageSize: number, leadText: string | null, url: string, title: string, rssUrl: any | null, proposeUrl: string | null, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetEventListQueryVariables = Exact<{
  filter: InputMaybe<EventFilterInput>;
  period: InputMaybe<EventPeriodFilterInput>;
  sort: InputMaybe<EventSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetEventListQuery = { __typename?: 'RootQuery', eventSearch: { __typename?: 'EventSearchResponse', totalCount: number, items: Array<{ __typename?: 'Event', endDate: any | null, id: number, leadText: string | null, startDate: any | null, title: string | null, url: string | null, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, location: { __typename?: 'Location', title: string | null, address: { __typename?: 'Address', city: string | null } | null } | null, categories: Array<{ __typename?: 'Category', title: string }>, periods: { __typename?: 'EventPeriodResponse', items: Array<{ __typename?: 'EventPeriod', startDate: any }> } | null }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type GetEventQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetEventQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event', id: number, title: string | null, status: PostStatus, leadText: string | null, structuredContent: any | null, endDate: any | null, startDate: any | null, publicationDate: any | null, modifiedDate: any | null, recurrenceSummary: string | null, audience: Array<string>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, accessibility: { __typename?: 'EventAccessibility', hearingImpairment: EventAccessibilityStatus | null, intellectualImpairment: EventAccessibilityStatus | null, mentalImpairment: EventAccessibilityStatus | null, reducedMobility: EventAccessibilityStatus | null, signLanguageReception: EventAccessibilityStatus | null, strollers: EventAccessibilityStatus | null, visualImpairment: EventAccessibilityStatus | null } | null, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string, description: string | null, parent: { __typename: 'Category' } | null }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string, siblings: Array<{ __typename?: 'Crumb', title: string, url: string }> }> }, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetHomepageQueryVariables = Exact<{ [key: string]: never; }>;


export type GetHomepageQuery = { __typename?: 'RootQuery', page: { __typename?: 'Page', structuredContent: any | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | null };

export type GetNewsListConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetNewsListConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList', defaultPageSize: number, leadText: string | null, proposeUrl: string | null, rssUrl: any | null, title: string, url: string, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetNewsListQueryVariables = Exact<{
  filter: InputMaybe<NewsFilterInput>;
  sort: InputMaybe<NewsSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetNewsListQuery = { __typename?: 'RootQuery', newsSearch: { __typename?: 'NewsSearchResponse', totalCount: number, items: Array<{ __typename?: 'News', id: number, leadText: string | null, slug: string, status: PostStatus, title: string | null, url: string | null, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, categories: Array<{ __typename?: 'Category', description: string | null, relativeUrl: string, title: string }> }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type GetNewsQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetNewsQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename: 'News', structuredContent: any | null, title: string | null, leadText: string | null, publicationDate: any | null, modifiedDate: any | null, url: string | null, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', url: any, width: number, height: number, alt: string | null } | null } | null, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, categories: Array<{ __typename?: 'Category', description: string | null, relativeUrl: string, title: string, parent: { __typename: 'Category' } | null }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> }, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetPageQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetPageQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page', title: string | null, status: PostStatus, structuredContent: any | null, leadText: string | null, surtitle: string | null, publicationDate: any | null, modifiedDate: any | null, images: { __typename?: 'ImageCollection', ratio_21x9: { __typename?: 'Image', url: any, width: number, height: number, alt: string | null } | null } | null, categories: Array<{ __typename?: 'Category', description: string | null, relativeUrl: string, title: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> }, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetPublicationListConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetPublicationListConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList', defaultPageSize: number, leadText: string | null, proposeUrl: string | null, rssUrl: any | null, title: string, url: string, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetPublicationListQueryVariables = Exact<{
  filter: InputMaybe<PublicationFilterInput>;
  sort: InputMaybe<PublicationSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetPublicationListQuery = { __typename?: 'RootQuery', publicationSearch: { __typename?: 'PublicationSearchResponse', totalCount: number, items: Array<{ __typename?: 'Publication', id: number, leadText: string | null, modifiedDate: any | null, publicationDate: any | null, slug: string, status: PostStatus, title: string | null, url: string | null, categories: Array<{ __typename?: 'Category', description: string | null, relativeUrl: string, title: string }>, files: Array<{ __typename?: 'File', downloadUrl: string | null, extname: string | null, label: string | null, mime: string | null, size: number | null, viewUrl: string | null }>, images: { __typename?: 'ImageCollection', original: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type GetPublicationQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetPublicationQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication', title: string | null, status: PostStatus, structuredContent: any | null, leadText: string | null, publicationDate: any | null, modifiedDate: any | null, files: Array<{ __typename?: 'File', label: string | null, downloadUrl: string | null, extname: string | null, mime: string | null, size: number | null, viewUrl: string | null }>, images: { __typename?: 'ImageCollection', ratio_A4_portrait: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string, parent: { __typename: 'Category' } | null }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string, siblings: Array<{ __typename?: 'Crumb', title: string, url: string }> }> }, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetResolutionListConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetResolutionListConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList', defaultPageSize: number, leadText: string | null, url: string, title: string, rssUrl: any | null, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }>, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null } | null };

export type GetResolutionListQueryVariables = Exact<{
  filter: InputMaybe<ResolutionFilterInput>;
  sort: InputMaybe<ResolutionSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetResolutionListQuery = { __typename?: 'RootQuery', resolutionSearch: { __typename?: 'ResolutionSearchResponse', totalCount: number, items: Array<{ __typename?: 'Resolution', id: number, title: string | null, fileCount: number, issueDate: any | null, publicationDate: any | null, url: string | null, categories: Array<{ __typename?: 'Category', title: string }> }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string, label: string | null } | { __typename: 'SelectFilter', placeholder: string | null, attribute: string, label: string | null, options: Array<(
        { __typename?: 'SelectFilterOption', children: Array<(
          { __typename?: 'SelectFilterOption', children: Array<(
            { __typename?: 'SelectFilterOption' }
            & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
          )> }
          & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'SelectFilterOptionFragmentFragment': SelectFilterOptionFragmentFragment } }
      )> } | { __typename: 'TextFilter', attribute: string, label: string | null }> } | null };

export type GetResolutionQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetResolutionQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution', issueDate: any | null, leadText: string | null, modifiedDate: any | null, publicationDate: any | null, structuredContent: any | null, surtitle: string | null, title: string | null, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string, siblings: Array<{ __typename?: 'Crumb', title: string, url: string }> }> }, categories: Array<{ __typename?: 'Category', relativeUrl: string, title: string, parent: { __typename: 'Category' } | null }>, files: Array<{ __typename?: 'File', downloadUrl: string | null, extname: string | null, label: string | null, mime: string | null, size: number | null, viewUrl: string | null }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, pager: { __typename?: 'ContentPager', list: { __typename?: 'Link', text: string, url: string } | null, next: { __typename?: 'Link', text: string, url: string } | null, prev: { __typename?: 'Link', text: string, url: string } | null } | null, metadata: { __typename?: 'Metadata', description: string | null, title: string } | null } | { __typename?: 'ResolutionList' } | null, siteConfig: { __typename?: 'SiteConfig', socialShare: boolean } | null };

export type GetSearchConfigQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetSearchConfigQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename: 'GlobalSearch', defaultPageSize: number, title: string, url: string, breadcrumbs: { __typename?: 'Breadcrumbs', items: Array<{ __typename?: 'Crumb', title: string, url: string }> } | null, filters: Array<{ __typename?: 'DateRangeFilter', attribute: string, label: string | null } | { __typename?: 'SelectFilter', attribute: string, label: string | null, placeholder: string | null, options: Array<{ __typename?: 'SelectFilterOption', label: string | null, value: string, count: number | null, children: Array<{ __typename?: 'SelectFilterOption', label: string | null, value: string, count: number | null, children: Array<{ __typename?: 'SelectFilterOption', label: string | null, value: string, count: number | null, children: Array<{ __typename?: 'SelectFilterOption', label: string | null, value: string, count: number | null }> }> }> }> } | { __typename?: 'TextFilter', label: string | null, attribute: string }>, searchFilter: { __typename?: 'DateRangeFilter', attribute: string } | { __typename?: 'SelectFilter', attribute: string } | { __typename?: 'TextFilter', attribute: string } | null } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetSearchListQueryVariables = Exact<{
  filter: InputMaybe<GlobalSearchFilterInput>;
  sort: InputMaybe<GlobalSearchSortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetSearchListQuery = { __typename?: 'RootQuery', search: { __typename?: 'GlobalSearchResponse', totalCount: number, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, items: Array<{ __typename: 'Album', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'Associations' } | { __typename: 'Attachment' } | { __typename: 'Directory', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'Event', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'News', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'Page', title: string | null, surtitle: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'Personnes' } | { __typename: 'Post' } | { __typename: 'Publication', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null } | { __typename: 'Resolution', title: string | null, leadText: string | null, url: string | null, modifiedDate: any | null, categories: Array<{ __typename?: 'Category', title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null }> } | null };

export type GetDirectoriesLocationQueryVariables = Exact<{
  type: Scalars['String']['input'];
  filter: InputMaybe<DirectoryFilterInput>;
  sort: InputMaybe<DirectorySortInput>;
  pageSize: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetDirectoriesLocationQuery = { __typename?: 'RootQuery', directorySearch: { __typename?: 'DirectorySearchResponse', totalCount: number, items: Array<{ __typename?: 'Directory', id: number, title: string | null, location: { __typename?: 'Location', latitude: number | null, longitude: number | null } | null, categories: Array<{ __typename?: 'Category', title: string, relativeUrl: string, description: string | null }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null }>, pageInfo: { __typename?: 'PageInfo', currentPage: number, pageSize: number, totalPages: number }, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }> } | null };

export type GetRouteAndTypeQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetRouteAndTypeQuery = { __typename?: 'RootQuery', route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap', types: Array<string>, url: string | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null, filters: Array<{ __typename: 'DateRangeFilter', attribute: string } | { __typename: 'SelectFilter', attribute: string } | { __typename: 'TextFilter', attribute: string }> } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap' } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetDirectoriesAndMapLinksQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetDirectoriesAndMapLinksQuery = { __typename?: 'RootQuery', menu: { __typename?: 'Menu', items: Array<(
      { __typename?: 'MenuItem', children: Array<(
        { __typename?: 'MenuItem', children: Array<(
          { __typename?: 'MenuItem' }
          & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
      )> }
      & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
    )> } | null, siteConfig: { __typename?: 'SiteConfig', siteName: string, header: { __typename?: 'HeaderConfig', logoDark: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null }, footer: { __typename?: 'FooterConfig', quickAccess2: Array<{ __typename?: 'Link', rel: string | null, target: string | null, text: string, url: string }> } } | null, route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename?: 'GlobalMap', mapLinks: Array<{ __typename?: 'Link', url: string, text: string, icon: { __typename?: 'Icon', src: string, type: IconType } | null }> } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetSiteAndMapDetailsQueryVariables = Exact<{
  url: InputMaybe<Scalars['URL']['input']>;
}>;


export type GetSiteAndMapDetailsQuery = { __typename?: 'RootQuery', menu: { __typename?: 'Menu', items: Array<(
      { __typename?: 'MenuItem', children: Array<(
        { __typename?: 'MenuItem', children: Array<(
          { __typename?: 'MenuItem' }
          & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
      )> }
      & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
    )> } | null, siteConfig: { __typename?: 'SiteConfig', siteName: string, header: { __typename?: 'HeaderConfig', logoDark: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null }, footer: { __typename?: 'FooterConfig', quickAccess2: Array<{ __typename?: 'Link', rel: string | null, target: string | null, text: string, url: string }> } } | null, route: { __typename?: 'Album' } | { __typename?: 'AlbumList' } | { __typename?: 'Alert' } | { __typename?: 'Associations' } | { __typename?: 'AssociationsList' } | { __typename?: 'AssociationsMap' } | { __typename?: 'Attachment' } | { __typename?: 'Directory' } | { __typename?: 'DirectoryList' } | { __typename?: 'DirectoryMap' } | { __typename?: 'Event' } | { __typename?: 'EventList' } | { __typename?: 'EventMap' } | { __typename?: 'FlashInfo' } | { __typename: 'GlobalMap', title: string, leadText: string | null, metadata: { __typename?: 'Metadata', title: string, description: string | null } | null, mapLinks: Array<{ __typename?: 'Link', url: string, text: string, icon: { __typename?: 'Icon', src: string } | null }> } | { __typename?: 'GlobalSearch' } | { __typename?: 'News' } | { __typename?: 'NewsList' } | { __typename?: 'NewsMap' } | { __typename?: 'Page' } | { __typename?: 'Personnes' } | { __typename?: 'PersonnesList' } | { __typename?: 'Post' } | { __typename?: 'Publication' } | { __typename?: 'PublicationList' } | { __typename?: 'Redirect' } | { __typename?: 'Resolution' } | { __typename?: 'ResolutionList' } | null };

export type GetAdminBarQueryVariables = Exact<{
  url: InputMaybe<Scalars['String']['input']>;
}>;


export type GetAdminBarQuery = { __typename?: 'RootQuery', barConfig: { __typename?: 'BarConfig', currentUser: { __typename?: 'User', displayName: string, avatar: string | null } | null, entries: Array<(
      { __typename?: 'BarMenuEntry', children: Array<(
        { __typename?: 'BarMenuEntry', children: Array<(
          { __typename?: 'BarMenuEntry' }
          & { ' $fragmentRefs'?: { 'BarMenuEntryFragmentFragment': BarMenuEntryFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'BarMenuEntryFragmentFragment': BarMenuEntryFragmentFragment } }
      )> }
      & { ' $fragmentRefs'?: { 'BarMenuEntryFragmentFragment': BarMenuEntryFragmentFragment } }
    )> } | null };

export type BarMenuEntryFragmentFragment = { __typename?: 'BarMenuEntry', title: string, url: string, screenReaderTitle: string | null, icon: { __typename?: 'Icon', type: IconType, src: string } | null } & { ' $fragmentName'?: 'BarMenuEntryFragmentFragment' };

export type GetAlertsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAlertsQuery = { __typename?: 'RootQuery', alertSearch: { __typename?: 'AlertSearchResponse', totalCount: number, items: Array<{ __typename?: 'Alert', id: number, variant: AlertVariant, title: string | null, description: string | null, modifiedDate: any | null, action: { __typename?: 'Link', url: string, text: string } | null }> } | null };

export type GetDirectoryMarkersDetailsQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type GetDirectoryMarkersDetailsQuery = { __typename?: 'RootQuery', directory: { __typename?: 'Directory', email: string | null, title: string | null, website: string | null, accessibility: { __typename?: 'DirectoryAccessibility', hearingImpairment: DirectoryAccessibilityStatus | null, intellectualImpairment: DirectoryAccessibilityStatus | null, mentalImpairment: DirectoryAccessibilityStatus | null, reducedMobility: DirectoryAccessibilityStatus | null, signLanguageReception: DirectoryAccessibilityStatus | null, strollers: DirectoryAccessibilityStatus | null, visualImpairment: DirectoryAccessibilityStatus | null } | null, categories: Array<{ __typename?: 'Category', description: string | null, relativeUrl: string, title: string }>, images: { __typename?: 'ImageCollection', ratio_3x2: { __typename?: 'Image', alt: string | null, height: number, url: any, width: number } | null } | null, location: { __typename?: 'Location', title: string | null, address: { __typename?: 'Address', city: string | null, country: string | null, street: Array<string>, zip: string | null } | null } | null, phones: Array<{ __typename?: 'Phone', country: string | null, deviceType: PhoneDeviceType | null, internationalNumber: string, label: string | null, number: string }> } | null };

export type GetEventPeriodsQueryVariables = Exact<{
  id: Scalars['Int']['input'];
  pageSize: InputMaybe<Scalars['Int']['input']>;
  currentPage: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetEventPeriodsQuery = { __typename?: 'RootQuery', event: { __typename?: 'Event', recurrenceSummary: string | null, periods: { __typename?: 'EventPeriodResponse', totalCount: number, items: Array<{ __typename?: 'EventPeriod', startDate: any, fullday: boolean, endDate: any }>, pageInfo: { __typename?: 'PageInfo', currentPage: number } } | null } | null };

export type GetFooterQueryVariables = Exact<{ [key: string]: never; }>;


export type GetFooterQuery = { __typename?: 'RootQuery', siteConfig: { __typename?: 'SiteConfig', footer: { __typename?: 'FooterConfig', title: string, top: { __typename?: 'FooterTop', description: string | null, title: string | null, action: { __typename?: 'Link', text: string, url: string, class: string | null, rel: string | null, target: string | null, icon: { __typename?: 'Icon', src: string, type: IconType } | null } | null } | null, logoLight: { __typename?: 'Image', url: any, width: number, height: number } | null, buttons: Array<{ __typename?: 'Link', class: string | null, rel: string | null, target: string | null, text: string, url: string, icon: { __typename?: 'Icon', src: string, type: IconType } | null }>, clientInfo: { __typename?: 'ClientInfo', address: string | null, openingHours: string | null, tel: string | null } | null, quickAccess1: Array<{ __typename?: 'Link', class: string | null, rel: string | null, target: string | null, text: string, url: string, icon: { __typename?: 'Icon', src: string, type: IconType } | null }>, quickAccess2: Array<{ __typename?: 'Link', class: string | null, rel: string | null, target: string | null, text: string, url: string, icon: { __typename?: 'Icon', src: string, type: IconType } | null }> }, socialLinks: Array<{ __typename?: 'SocialLink', network: SocialNetwork, text: string, url: string }> } | null };

export type GetHeaderQueryVariables = Exact<{ [key: string]: never; }>;


export type GetHeaderQuery = { __typename?: 'RootQuery', menu: { __typename?: 'Menu', items: Array<(
      { __typename?: 'MenuItem', children: Array<(
        { __typename?: 'MenuItem', children: Array<(
          { __typename?: 'MenuItem' }
          & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
        )> }
        & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
      )> }
      & { ' $fragmentRefs'?: { 'MenuItemFragmentFragment': MenuItemFragmentFragment } }
    )> } | null, siteConfig: { __typename?: 'SiteConfig', siteName: string, searchPage: string, header: { __typename?: 'HeaderConfig', logoLight: { __typename?: 'Image', url: any } | null }, socialLinks: Array<{ __typename?: 'SocialLink', network: SocialNetwork, text: string, url: string }> } | null, flashInfoSearch: { __typename?: 'FlashInfoSearchResponse', items: Array<{ __typename?: 'FlashInfo', id: number, url: string | null, description: string | null, title: string | null, modifiedDate: any | null }> } | null };

export type MenuItemFragmentFragment = { __typename?: 'MenuItem', url: string, title: string, target: string | null, linkTitle: string | null, level: number, description: string | null, className: string | null } & { ' $fragmentName'?: 'MenuItemFragmentFragment' };

export type GetRouteQueryVariables = Exact<{
  url: Scalars['URL']['input'];
}>;


export type GetRouteQuery = { __typename?: 'RootQuery', route: { __typename: 'Album' } | { __typename: 'AlbumList' } | { __typename: 'Alert' } | { __typename: 'Associations' } | { __typename: 'AssociationsList' } | { __typename: 'AssociationsMap' } | { __typename: 'Attachment' } | { __typename: 'Directory' } | { __typename: 'DirectoryList' } | { __typename: 'DirectoryMap' } | { __typename: 'Event' } | { __typename: 'EventList' } | { __typename: 'EventMap' } | { __typename: 'FlashInfo' } | { __typename: 'GlobalMap' } | { __typename: 'GlobalSearch' } | { __typename: 'News' } | { __typename: 'NewsList' } | { __typename: 'NewsMap' } | { __typename: 'Page' } | { __typename: 'Personnes' } | { __typename: 'PersonnesList' } | { __typename: 'Post' } | { __typename: 'Publication' } | { __typename: 'PublicationList' } | { __typename: 'Redirect', redirectCode: number | null, relativeUrl: string } | { __typename: 'Resolution' } | { __typename: 'ResolutionList' } | null };

export const SelectFilterOptionFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<SelectFilterOptionFragmentFragment, unknown>;
export const BarMenuEntryFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"BarMenuEntryFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"BarMenuEntry"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"src"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"screenReaderTitle"}}]}}]} as unknown as DocumentNode<BarMenuEntryFragmentFragment, unknown>;
export const MenuItemFragmentFragmentDoc = {"kind":"Document","definitions":[{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MenuItemFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"MenuItem"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"linkTitle"}},{"kind":"Field","name":{"kind":"Name","value":"level"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"className"}}]}}]} as unknown as DocumentNode<MenuItemFragmentFragment, unknown>;
export const GetAlbumListConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAlbumListConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"proposeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetAlbumListConfigQuery, GetAlbumListConfigQueryVariables>;
export const GetAlbumListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAlbumList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"albumSearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"videoCount"}},{"kind":"Field","name":{"kind":"Name","value":"photoCount"}},{"kind":"Field","name":{"kind":"Name","value":"mediaCount"}},{"kind":"Field","name":{"kind":"Name","value":"media"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumPhoto"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"caption"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumVideo"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"caption"}},{"kind":"Field","name":{"kind":"Name","value":"provider"}},{"kind":"Field","name":{"kind":"Name","value":"thumbnail"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"alt"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetAlbumListQuery, GetAlbumListQueryVariables>;
export const GetAlbumDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAlbum"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Album"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"mediaCount"}},{"kind":"Field","name":{"kind":"Name","value":"photoCount"}},{"kind":"Field","name":{"kind":"Name","value":"videoCount"}},{"kind":"Field","name":{"kind":"Name","value":"media"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumVideo"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"caption"}},{"kind":"Field","name":{"kind":"Name","value":"provider"}},{"kind":"Field","name":{"kind":"Name","value":"thumbnail"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"alt"}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"AlbumPhoto"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"caption"}},{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"siblings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetAlbumQuery, GetAlbumQueryVariables>;
export const GetDirectoryDetailsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectoryDetails"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"DirectoryList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"proposeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"viewMode"}},{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetDirectoryDetailsQuery, GetDirectoryDetailsQueryVariables>;
export const GetDirectoryListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectoryList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DirectoryFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DirectorySortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"directorySearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ratio_1x1"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"location"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"address"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"street"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"openingHours"}},{"kind":"Field","name":{"kind":"Name","value":"offices"}},{"kind":"Field","name":{"kind":"Name","value":"phones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deviceType"}},{"kind":"Field","name":{"kind":"Name","value":"number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"viewMode"}},{"kind":"Field","name":{"kind":"Name","value":"website"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"accessibility"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hearingImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"intellectualImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"mentalImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"reducedMobility"}},{"kind":"Field","name":{"kind":"Name","value":"signLanguageReception"}},{"kind":"Field","name":{"kind":"Name","value":"strollers"}},{"kind":"Field","name":{"kind":"Name","value":"visualImpairment"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetDirectoryListQuery, GetDirectoryListQueryVariables>;
export const GetDirectoryDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectory"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Directory"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"viewMode"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"website"}},{"kind":"Field","name":{"kind":"Name","value":"openingHours"}},{"kind":"Field","name":{"kind":"Name","value":"offices"}},{"kind":"Field","name":{"kind":"Name","value":"municipalityArea"}},{"kind":"Field","name":{"kind":"Name","value":"municipalityPopulation"}},{"kind":"Field","name":{"kind":"Name","value":"mayorName"}},{"kind":"Field","name":{"kind":"Name","value":"contactFirstName"}},{"kind":"Field","name":{"kind":"Name","value":"contactLastName"}},{"kind":"Field","name":{"kind":"Name","value":"contactEmail"}},{"kind":"Field","name":{"kind":"Name","value":"phones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"number"}},{"kind":"Field","name":{"kind":"Name","value":"deviceType"}},{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"internationalNumber"}},{"kind":"Field","name":{"kind":"Name","value":"label"}}]}},{"kind":"Field","name":{"kind":"Name","value":"accessibility"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hearingImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"intellectualImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"mentalImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"reducedMobility"}},{"kind":"Field","name":{"kind":"Name","value":"signLanguageReception"}},{"kind":"Field","name":{"kind":"Name","value":"strollers"}},{"kind":"Field","name":{"kind":"Name","value":"visualImpairment"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_1x1"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"socialLinks"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"network"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"location"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"address"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"street"}},{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}}]}},{"kind":"Field","name":{"kind":"Name","value":"longitude"}},{"kind":"Field","name":{"kind":"Name","value":"latitude"}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetDirectoryQuery, GetDirectoryQueryVariables>;
export const GetEventListConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEventListConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"EventList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"proposeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetEventListConfigQuery, GetEventListConfigQueryVariables>;
export const GetEventListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEventList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"EventFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"period"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"EventPeriodFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"EventSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"eventSearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"endDate"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"startDate"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"location"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"address"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"city"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"periods"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"period"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"startDate"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetEventListQuery, GetEventListQueryVariables>;
export const GetEventDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEvent"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Event"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"endDate"}},{"kind":"Field","name":{"kind":"Name","value":"startDate"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"recurrenceSummary"}},{"kind":"Field","name":{"kind":"Name","value":"accessibility"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hearingImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"intellectualImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"mentalImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"reducedMobility"}},{"kind":"Field","name":{"kind":"Name","value":"signLanguageReception"}},{"kind":"Field","name":{"kind":"Name","value":"strollers"}},{"kind":"Field","name":{"kind":"Name","value":"visualImpairment"}}]}},{"kind":"Field","name":{"kind":"Name","value":"audience"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"siblings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetEventQuery, GetEventQueryVariables>;
export const GetHomepageDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetHomepage"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"page"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"StringValue","value":"/","block":false}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]} as unknown as DocumentNode<GetHomepageQuery, GetHomepageQueryVariables>;
export const GetNewsListConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetNewsListConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"NewsList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"proposeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetNewsListConfigQuery, GetNewsListConfigQueryVariables>;
export const GetNewsListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetNewsList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"NewsFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"NewsSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"newsSearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetNewsListQuery, GetNewsListQueryVariables>;
export const GetNewsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetNews"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"News"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"alt"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetNewsQuery, GetNewsQueryVariables>;
export const GetPageDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPage"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Page"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"surtitle"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_21x9"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"alt"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetPageQuery, GetPageQueryVariables>;
export const GetPublicationListConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPublicationListConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"PublicationList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"proposeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetPublicationListConfigQuery, GetPublicationListConfigQueryVariables>;
export const GetPublicationListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPublicationList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"PublicationFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"PublicationSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"publicationSearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"files"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"downloadUrl"}},{"kind":"Field","name":{"kind":"Name","value":"extname"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"mime"}},{"kind":"Field","name":{"kind":"Name","value":"size"}},{"kind":"Field","name":{"kind":"Name","value":"viewUrl"}}]}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"original"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"slug"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"placeholder"}},{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetPublicationListQuery, GetPublicationListQueryVariables>;
export const GetPublicationDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPublication"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Publication"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"status"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"files"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"downloadUrl"}},{"kind":"Field","name":{"kind":"Name","value":"extname"}},{"kind":"Field","name":{"kind":"Name","value":"mime"}},{"kind":"Field","name":{"kind":"Name","value":"size"}},{"kind":"Field","name":{"kind":"Name","value":"viewUrl"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_A4_portrait"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"siblings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetPublicationQuery, GetPublicationQueryVariables>;
export const GetResolutionListConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetResolutionListConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"ResolutionList"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"rssUrl"}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetResolutionListConfigQuery, GetResolutionListConfigQueryVariables>;
export const GetResolutionListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetResolutionList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ResolutionFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ResolutionSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"resolutionSearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"fileCount"}},{"kind":"Field","name":{"kind":"Name","value":"issueDate"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"SelectFilterOptionFragment"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"SelectFilterOptionFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilterOption"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]} as unknown as DocumentNode<GetResolutionListQuery, GetResolutionListQueryVariables>;
export const GetResolutionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetResolution"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Resolution"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"siblings"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"parent"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"files"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"downloadUrl"}},{"kind":"Field","name":{"kind":"Name","value":"extname"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"mime"}},{"kind":"Field","name":{"kind":"Name","value":"size"}},{"kind":"Field","name":{"kind":"Name","value":"viewUrl"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"issueDate"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"publicationDate"}},{"kind":"Field","name":{"kind":"Name","value":"structuredContent"}},{"kind":"Field","name":{"kind":"Name","value":"surtitle"}},{"kind":"Field","name":{"kind":"Name","value":"pager"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"list"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"next"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"prev"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"socialShare"}}]}}]}}]} as unknown as DocumentNode<GetResolutionQuery, GetResolutionQueryVariables>;
export const GetSearchConfigDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSearchConfig"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"GlobalSearch"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"defaultPageSize"}},{"kind":"Field","name":{"kind":"Name","value":"breadcrumbs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"SelectFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"options"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"value"}},{"kind":"Field","name":{"kind":"Name","value":"count"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"placeholder"}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TextFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"searchFilter"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}}]} as unknown as DocumentNode<GetSearchConfigQuery, GetSearchConfigQueryVariables>;
export const GetSearchListDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSearchList"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"GlobalSearchFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"GlobalSearchSortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"search"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Page"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"surtitle"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"News"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Event"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Publication"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Directory"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Album"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Resolution"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetSearchListQuery, GetSearchListQueryVariables>;
export const GetDirectoriesLocationDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectoriesLocation"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"type"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"filter"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DirectoryFilterInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sort"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DirectorySortInput"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"directorySearch"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"type"}}},{"kind":"Argument","name":{"kind":"Name","value":"filter"},"value":{"kind":"Variable","name":{"kind":"Name","value":"filter"}}},{"kind":"Argument","name":{"kind":"Name","value":"sort"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sort"}}},{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"location"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"latitude"}},{"kind":"Field","name":{"kind":"Name","value":"longitude"}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}},{"kind":"Field","name":{"kind":"Name","value":"pageSize"}},{"kind":"Field","name":{"kind":"Name","value":"totalPages"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}}]}}]}}]} as unknown as DocumentNode<GetDirectoriesLocationQuery, GetDirectoriesLocationQueryVariables>;
export const GetRouteAndTypeDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetRouteAndType"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"DirectoryMap"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"types"}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}},{"kind":"Field","name":{"kind":"Name","value":"filters"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"attribute"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"TextFilter"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"attribute"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}}]} as unknown as DocumentNode<GetRouteAndTypeQuery, GetRouteAndTypeQueryVariables>;
export const GetDirectoriesAndMapLinksDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectoriesAndMapLinks"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"menu"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"position"},"value":{"kind":"StringValue","value":"header","block":false}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"siteName"}},{"kind":"Field","name":{"kind":"Name","value":"header"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logoDark"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"footer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"quickAccess2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"GlobalMap"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"mapLinks"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MenuItemFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"MenuItem"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"linkTitle"}},{"kind":"Field","name":{"kind":"Name","value":"level"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"className"}}]}}]} as unknown as DocumentNode<GetDirectoriesAndMapLinksQuery, GetDirectoriesAndMapLinksQueryVariables>;
export const GetSiteAndMapDetailsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSiteAndMapDetails"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"menu"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"position"},"value":{"kind":"StringValue","value":"header","block":false}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"siteName"}},{"kind":"Field","name":{"kind":"Name","value":"header"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logoDark"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"footer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"quickAccess2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"GlobalMap"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"leadText"}},{"kind":"Field","name":{"kind":"Name","value":"metadata"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}}]}},{"kind":"Field","name":{"kind":"Name","value":"mapLinks"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}}]}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MenuItemFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"MenuItem"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"linkTitle"}},{"kind":"Field","name":{"kind":"Name","value":"level"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"className"}}]}}]} as unknown as DocumentNode<GetSiteAndMapDetailsQuery, GetSiteAndMapDetailsQueryVariables>;
export const GetAdminBarDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAdminBar"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"barConfig"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentUser"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"displayName"}},{"kind":"Field","name":{"kind":"Name","value":"avatar"}}]}},{"kind":"Field","name":{"kind":"Name","value":"entries"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"BarMenuEntryFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"BarMenuEntryFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"BarMenuEntryFragment"}}]}}]}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"BarMenuEntryFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"BarMenuEntry"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"type"}},{"kind":"Field","name":{"kind":"Name","value":"src"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"screenReaderTitle"}}]}}]} as unknown as DocumentNode<GetAdminBarQuery, GetAdminBarQueryVariables>;
export const GetAlertsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetAlerts"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alertSearch"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"variant"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}},{"kind":"Field","name":{"kind":"Name","value":"action"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"text"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetAlertsQuery, GetAlertsQueryVariables>;
export const GetDirectoryMarkersDetailsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetDirectoryMarkersDetails"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"directory"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"accessibility"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hearingImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"intellectualImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"mentalImpairment"}},{"kind":"Field","name":{"kind":"Name","value":"reducedMobility"}},{"kind":"Field","name":{"kind":"Name","value":"signLanguageReception"}},{"kind":"Field","name":{"kind":"Name","value":"strollers"}},{"kind":"Field","name":{"kind":"Name","value":"visualImpairment"}}]}},{"kind":"Field","name":{"kind":"Name","value":"categories"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"images"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ratio_3x2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"alt"}},{"kind":"Field","name":{"kind":"Name","value":"height"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"location"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"address"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"city"}},{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"street"}},{"kind":"Field","name":{"kind":"Name","value":"zip"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"phones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"country"}},{"kind":"Field","name":{"kind":"Name","value":"deviceType"}},{"kind":"Field","name":{"kind":"Name","value":"internationalNumber"}},{"kind":"Field","name":{"kind":"Name","value":"label"}},{"kind":"Field","name":{"kind":"Name","value":"number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"website"}}]}}]}}]} as unknown as DocumentNode<GetDirectoryMarkersDetailsQuery, GetDirectoryMarkersDetailsQueryVariables>;
export const GetEventPeriodsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetEventPeriods"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"event"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"recurrenceSummary"}},{"kind":"Field","name":{"kind":"Name","value":"periods"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"pageSize"},"value":{"kind":"Variable","name":{"kind":"Name","value":"pageSize"}}},{"kind":"Argument","name":{"kind":"Name","value":"currentPage"},"value":{"kind":"Variable","name":{"kind":"Name","value":"currentPage"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"startDate"}},{"kind":"Field","name":{"kind":"Name","value":"fullday"}},{"kind":"Field","name":{"kind":"Name","value":"endDate"}}]}},{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currentPage"}}]}}]}}]}}]}}]} as unknown as DocumentNode<GetEventPeriodsQuery, GetEventPeriodsQueryVariables>;
export const GetFooterDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetFooter"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"footer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"top"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"action"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"class"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}}]}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"title"}}]}},{"kind":"Field","name":{"kind":"Name","value":"logoLight"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"width"}},{"kind":"Field","name":{"kind":"Name","value":"height"}}]}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"buttons"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"class"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"clientInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"openingHours"}},{"kind":"Field","name":{"kind":"Name","value":"tel"}}]}},{"kind":"Field","name":{"kind":"Name","value":"quickAccess1"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"class"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"quickAccess2"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"class"}},{"kind":"Field","name":{"kind":"Name","value":"icon"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"src"}},{"kind":"Field","name":{"kind":"Name","value":"type"}}]}},{"kind":"Field","name":{"kind":"Name","value":"rel"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"socialLinks"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"network"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}}]}}]} as unknown as DocumentNode<GetFooterQuery, GetFooterQueryVariables>;
export const GetHeaderDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetHeader"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"menu"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"position"},"value":{"kind":"StringValue","value":"header","block":false}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}},{"kind":"Field","name":{"kind":"Name","value":"children"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"FragmentSpread","name":{"kind":"Name","value":"MenuItemFragment"}}]}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"siteConfig"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"siteName"}},{"kind":"Field","name":{"kind":"Name","value":"header"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logoLight"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"socialLinks"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"network"}},{"kind":"Field","name":{"kind":"Name","value":"text"}},{"kind":"Field","name":{"kind":"Name","value":"url"}}]}},{"kind":"Field","name":{"kind":"Name","value":"searchPage"}}]}},{"kind":"Field","name":{"kind":"Name","value":"flashInfoSearch"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"modifiedDate"}}]}}]}}]}},{"kind":"FragmentDefinition","name":{"kind":"Name","value":"MenuItemFragment"},"typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"MenuItem"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"url"}},{"kind":"Field","name":{"kind":"Name","value":"title"}},{"kind":"Field","name":{"kind":"Name","value":"target"}},{"kind":"Field","name":{"kind":"Name","value":"linkTitle"}},{"kind":"Field","name":{"kind":"Name","value":"level"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"className"}}]}}]} as unknown as DocumentNode<GetHeaderQuery, GetHeaderQueryVariables>;
export const GetRouteDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetRoute"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"url"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"URL"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"route"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"url"},"value":{"kind":"Variable","name":{"kind":"Name","value":"url"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"__typename"}},{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"Redirect"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"redirectCode"}},{"kind":"Field","name":{"kind":"Name","value":"relativeUrl"}}]}}]}}]}}]} as unknown as DocumentNode<GetRouteQuery, GetRouteQueryVariables>;