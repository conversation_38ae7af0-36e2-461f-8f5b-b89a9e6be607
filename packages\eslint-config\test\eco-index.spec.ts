import { ESLint } from "eslint";
import { fileURLToPath } from "node:url";
import { expect, test } from "vitest";

const root = fileURLToPath(new URL("../", import.meta.url));

test("return an eco index error if an image has no src", async () => {
  const code = `
    export default function Image() {
      return <img alt="" />;
    }
  `;

  const eslint = new ESLint({ cwd: root });
  const result = await eslint.lintText(code, { filePath: "file.tsx" });

  // Here we expect the linter to return an error
  // because the `<img>` tag is missing a `src` attribute.
  // Also it won't return an a11y error because we still have an `alt` attr
  expect(result.length).toBe(1);

  expect(result[0].messages).toEqual(
    expect.arrayContaining([expect.objectContaining({ ruleId: "@creedengo/no-empty-image-src-attribute" })])
  );
});
