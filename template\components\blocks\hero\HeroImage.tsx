"use client";

import type { HeroB<PERSON> } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Image from "next/image";
import HeroContent from "./HeroContent";
import styles from "./HeroImage.module.scss";

/**
 * Render the Hero block with a single image.
 */
export default function HeroImage({ slides }: Pick<HeroBlock, "slides">) {
  const [firstSlide] = slides;

  if (!firstSlide.imageSrc) {
    return null;
  }

  return (
    <section className={clsx("block-hero", styles.heroImage)}>
      <HeroContent title={firstSlide.title} surtitle={firstSlide.leadText} url={firstSlide.link} />

      <Image src={firstSlide.imageSrc} alt={firstSlide.title ?? ""} fill priority />
    </section>
  );
}
