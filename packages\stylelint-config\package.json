{"name": "@citeopolis/stylelint-config", "type": "module", "version": "0.1.2", "scripts": {"format": "prettier -w ."}, "files": ["./stylelint.config.js"], "lint-staged": {"*.{js,json,md}": "prettier --write"}, "dependencies": {"stylelint-config-recess-order": "^6.0.0", "stylelint-config-standard-scss": "^14.0.0"}, "devDependencies": {"@citeopolis/prettier-config": "workspace:^", "prettier": "^3", "stylelint": "^16"}, "peerDependencies": {"stylelint": "^16"}, "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/stylelint-config"}, "exports": {".": {"import": "./stylelint.config.js"}}}