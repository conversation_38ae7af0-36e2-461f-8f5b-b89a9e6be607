import type { Event } from "@/generated/graphql/graphql";
import EventsItem from "./EventsItem";
import styles from "./EventsList.module.scss";

interface EventsListProps {
  items: Event[];
}

export default function EventsList({ items }: EventsListProps) {
  return (
    <ul className={styles.eventList}>
      {items.map((event, index) => (
        <li key={index}>
          <EventsItem event={event} />
        </li>
      ))}
    </ul>
  );
}
