import { ToastProvider, ToastViewport } from "@radix-ui/react-toast";
import type { Meta, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import Toast from "./Toast";

const meta: Meta<typeof Toast> = {
  title: "Components/Toast",
  component: Toast,
  tags: ["autodocs"],
  args: {
    open: true,
    onOpenChange: fn(),
  },
  decorators: [
    (Story) => (
      <ToastProvider>
        <Story />
        <ToastViewport />
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Toast>;

export const Info: Story = {
  args: {
    variant: "info",
    children: "Votre mot de passe expire dans 12j.",
  },
};

export const Success: Story = {
  args: {
    variant: "success",
    children: "Opération réussie !",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    children: "Une erreur s'est produite.",
  },
};

export const Warning: Story = {
  args: {
    variant: "warning",
    children: "Attention, vérifiez vos données.",
  },
};

export const WithAction: Story = {
  args: {
    children: "Attention, vérifiez vos données.",
    actionText: "Vérifier",
    actionProps: {
      altText: "Vérifier les données",
    },
  },
};

export const WithLongText: Story = {
  args: {
    children:
      "It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout. The point of using Lorem Ipsum is that it has a more-or-less normal distribution of letters...",
  },
};
