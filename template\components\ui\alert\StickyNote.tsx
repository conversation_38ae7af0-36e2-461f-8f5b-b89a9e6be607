"use client";

import { Alert } from "@/generated/graphql/graphql";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useId, useState } from "react";
import { PartialDeep } from "type-fest";
import { useIsClient, useLocalStorage } from "usehooks-ts";
import styles from "./StickyNote.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));

export const STICKY_NOTE_CLOSED_KEY = "citeopolis:sticky-note:closed";

function getStickyNoteKey({ id, modifiedDate }: Pick<Partial<Alert>, "id" | "modifiedDate">) {
  return `sticky-note|${id}|${modifiedDate}`;
}

interface StickyNoteProps {
  alert: PartialDeep<Alert, { recurseIntoArrays: true }>;
}

export default function StickyNote({ alert: { id, title, description, action, modifiedDate } }: StickyNoteProps) {
  const [isClosed, setIsClosed] = useState(false);
  const [closedStickyNoteKey, setClosedStickyNoteKey] = useLocalStorage<string>(STICKY_NOTE_CLOSED_KEY, "");

  const titleId = useId();
  const alertKey = getStickyNoteKey({ id, modifiedDate });

  const isClient = useIsClient();

  if (!isClient || closedStickyNoteKey === alertKey || isClosed) {
    return null;
  }

  const handleCloseAlert = () => {
    setClosedStickyNoteKey(alertKey);
    setIsClosed(true);
    const siteWrapper = document.querySelector(".site-wrapper") as HTMLElement;

    siteWrapper?.focus();
  };

  const activeColorVariant = "primary";

  return (
    <div className={styles.stickyNote} role="dialog" aria-labelledby={titleId} aria-live="assertive" tabIndex={-1}>
      <div className={clsx(styles.container, styles[`color-${activeColorVariant}`])}>
        <div className={styles.wrapper}>
          <i className={clsx("far fa-triangle-exclamation", styles.topIcon)} aria-hidden="true"></i>
          <div className={styles.content}>
            <h2 id={titleId} className="sr-only">
              Alerte
            </h2>

            <div className={styles.header}>
              {title && <h3 className={styles.title}>{title}</h3>}
              {description && <p className={styles.description}>{description}</p>}
            </div>
            {action?.url && (
              <div className={styles.action}>
                <Button
                  asChild
                  variant="outlined"
                  color={`${activeColorVariant}-inverted`}
                  startIcon="far fa-check"
                  size="xs"
                >
                  <Link href={action?.url} className={styles.itemLink}>
                    {action.text || "En savoir plus"}
                  </Link>
                </Button>
              </div>
            )}
          </div>
          <button
            className={clsx(styles.close, styles[`color-${activeColorVariant}`])}
            onClick={handleCloseAlert}
            aria-label="Fermer"
          >
            <i className="fas fa-xmark" aria-hidden="true"></i>
          </button>
        </div>
      </div>
    </div>
  );
}
