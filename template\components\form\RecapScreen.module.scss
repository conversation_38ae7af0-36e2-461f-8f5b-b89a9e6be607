@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.recapScreen {
  display: flex;
  flex-direction: column;
  grid-column: span 12;
  gap: 16px 0;
  width: 100%;
}

.header {
  display: flex;
  flex-direction: column;
  width: 100%;
  text-align: center;

  @include breakpoint(medium up) {
    text-align: left;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.title {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  color: $color-black;

  @include breakpoint(medium up) {
    font-size: 28px;
  }
}

.description {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
  color: $color-neutral-700;
}

.emptyState {
  padding: 48px 16px;
  text-align: center;

  p {
    margin: 0;
    font-size: 1.6rem;
    color: $color-neutral-500;
  }
}

.fieldsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.field {
  display: block;
  text-align: left;
}

.fieldLabel {
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-black;
  text-transform: none;
  letter-spacing: normal;
}

.fieldValue {
  font-size: 1.8rem;
  font-weight: 600;
  line-height: 1.5;
  color: $color-neutral-700;
  hyphens: auto;
  word-break: break-word;
  overflow-wrap: break-word;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;

  @include breakpoint(medium up) {
    justify-content: flex-start;
  }
}

.submitButton {
  min-width: 200px;
  font-weight: 600;

  @include breakpoint(medium up) {
    min-width: 240px;
  }
}
