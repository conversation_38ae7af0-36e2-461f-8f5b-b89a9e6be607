"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { PhoneField } from "@/generated/graphql/graphql";
import { useId } from "react";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type PhoneProps = Omit<PhoneField, "__typename">;

export default function Phone({
  name,
  autocomplete,
  defaultValue,
  label,
  description,
  placeholder,
  required,
  condition,
  validationMessage,
  columnSpan,
}: PhoneProps) {
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const inputId = useId();
  const errorId = useId();

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <Textfield
          id={inputId}
          type="tel"
          inputMode="tel"
          startIcon="far fa-phone"
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          placeholder={placeholder ?? undefined}
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
        />

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
