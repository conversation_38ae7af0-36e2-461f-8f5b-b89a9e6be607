import fs from "fs-extra";
import { globby } from "globby";
import { readFile } from "node:fs/promises";
import { tmpdir } from "node:os";
import path from "node:path";
import { expect, test } from "vitest";
import { createProject } from "../src/commands/create";

test("create an empty project", async () => {
  const temporary = await fs.mkdtemp(path.join(tmpdir(), "citeopolist-test-"));

  await createProject(temporary);

  expect(fs.existsSync(path.join(temporary))).toBe(true);

  expect(fs.existsSync(path.join(temporary, "package.json"))).toBe(true);

  await fs.remove(temporary);
});

test("create a local project", async () => {
  const temporary = await fs.mkdtemp(path.join(tmpdir(), "citeopolist-test-"));
  const template = path.resolve(process.cwd(), "../../template");

  await createProject(temporary, { template });

  expect(fs.existsSync(path.join(temporary))).toBe(true);
  expect(fs.existsSync(path.join(temporary, "package.json"))).toBe(true);

  const configFile = path.resolve(template, "butcher.json");
  const config = JSON.parse(await readFile(configFile, "utf8"));
  const filesList = config.flatMap((v: { files: string[] }) => v.files ?? []);
  const files = await globby(filesList ?? [], { cwd: temporary });

  expect(files.length).toBe(0);

  await fs.remove(temporary);
});

test("create a local project", async () => {
  const temporary = await fs.mkdtemp(path.join(tmpdir(), "citeopolist-test-"));
  const template = path.resolve(process.cwd(), "../../template");
  const features = ["news"];

  await createProject(temporary, { template, features });

  expect(fs.existsSync(path.join(temporary))).toBe(true);
  expect(fs.existsSync(path.join(temporary, "package.json"))).toBe(true);

  const configFile = path.resolve(template, "butcher.json");
  const config = JSON.parse(await readFile(configFile, "utf8"));
  const filesList = config.flatMap((v: { files: string[] }) => v.files ?? []);
  const files = await globby(filesList ?? [], { cwd: temporary });

  expect(files.length).not.toBe(0);

  await fs.remove(temporary);
});
