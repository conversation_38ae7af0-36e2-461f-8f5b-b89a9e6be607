import { kebabCase } from "change-case";
import { NextResponse, type NextRequest } from "next/server";
import { graphql } from "./generated/graphql";
import { query } from "./lib/graphql";

const ROUTE_QUERY = graphql(`
  query GetRoute($url: URL!) {
    route(url: $url) {
      __typename

      ... on Redirect {
        redirectCode
        relativeUrl
      }
    }
  }
`);

export async function middleware(request: NextRequest) {
  // TODO: Handle errors
  const { data } = await query({
    query: ROUTE_QUERY,
    variables: { url: request.nextUrl.pathname + request.nextUrl.search },
    errorPolicy: "all",
  });

  // Triggers a 404 manually by rewriting to an unknown internal route.
  if (!data.route) {
    return NextResponse.rewrite(new URL("/not-found", request.url));
  }

  const headersList = new Headers(request.headers);
  const { route } = data;

  // Pass the URL pathname + search to the inner headers,
  // so it can be used in list view queries.
  headersList.set("X-Current-Path", request.nextUrl.pathname + request.nextUrl.search);

  // TODO: The BE should return a post with the `DRAFT` or `PREVIEW` status,
  //  so we don't invalidate the cache for non preview/draft pages.
  if ("status" in route) {
    if (route?.status === "DRAFT" || request.nextUrl.searchParams.has("preview")) {
      // Disable caching on this request.
      // TODO: Maybe use `draftMode` if this does not work
      headersList.set("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");

      // Forward the `preview_nonce` param in headers, and use it in graphql SSR client.
      headersList.set("X-Preview-Token", request.nextUrl.searchParams.get("preview_nonce") ?? "");
    }
  }

  const innerPath =
    request.nextUrl.pathname === "/" && data.route.__typename === "Page"
      ? "homepage"
      : kebabCase(data.route.__typename);

  return NextResponse.rewrite(new URL(`/${innerPath}${request.nextUrl.search}`, request.url), {
    request: {
      headers: headersList,
    },
  });
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt, manifest.json, .well-known (metadata files)
     * - images (from public dir)
     */
    "/((?!api|_next/static|_next/image|images|favicon.ico|sitemap.xml|robots.txt|manifest.json|.well-known).*)",
  ],
};
