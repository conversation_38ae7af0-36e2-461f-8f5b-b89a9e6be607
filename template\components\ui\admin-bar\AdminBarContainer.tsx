import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import dynamic from "next/dynamic";

const AdminBar = dynamic(() => import("./AdminBar"));

const ADMIN_BAR_QUERY = graphql(`
  query GetAdminBar($url: String) {
    barConfig(url: $url) {
      currentUser {
        displayName
        avatar
      }
      entries {
        ...BarMenuEntryFragment
        children {
          ...BarMenuEntryFragment
          children {
            ...BarMenuEntryFragment
          }
        }
      }
    }
  }

  fragment BarMenuEntryFragment on BarMenuEntry {
    icon {
      type
      src
    }
    title
    url
    screenReaderTitle
  }
`);

// FIXME: This is not working anymore since it's a server component
//  At least it does not expose this endpoint everytime
export default async function AdminBarContainer() {
  const pathname = await getCurrentServerUrl();

  // TODO: Rerun query when the pathname changes
  const { data } = await query({
    query: ADMIN_BAR_QUERY,
    variables: { url: (process.env.NEXT_PUBLIC_BACKEND_URL ?? "") + pathname },
  });

  if (!data?.barConfig) {
    return;
  }

  const { entries } = data.barConfig;

  return <AdminBar entries={entries} />;
}
