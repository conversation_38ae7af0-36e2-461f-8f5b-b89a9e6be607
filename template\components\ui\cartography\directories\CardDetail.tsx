import Accessibility from "@/components/ui/accessibility/Accessibility";
import Button from "@/components/ui/button/Button";
import { Directory, PhoneDeviceType } from "@/generated/graphql/graphql";
import formatPhone from "@/utils/formatPhone";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./CardDetail.module.scss";

interface CardDetailProps {
  data: PartialDeep<Directory, { recurseIntoArrays: true }>;
}

const deviceIcons: Partial<Record<PhoneDeviceType, string>> = {
  LANDLINE: "fa-phone",
  MOBILE: "fa-mobile",
  FAX: "fa-fax",
};

export default function CardDetail({ data }: CardDetailProps) {
  if (!data) return null;

  const { title, accessibility, phones, url, website, email, images, location } = data;
  const image = images?.ratio_3x2 ?? null;

  const { address } = location ?? {};
  const { street, city, zip, country } = address ?? {};

  return (
    <div className={styles.root}>
      <div className={clsx(styles.row, styles.gap12)}>
        <p className={styles.subtitle}>Thématique</p>

        {title && <h2 className={clsx(styles.title, styles["size-lg"])}>{title}</h2>}

        {image?.url && (
          <div className={styles.imageWrapper}>
            <Image
              className={styles.poster}
              src={image?.url}
              alt={image?.alt ?? ""}
              width={image.width}
              height={image.height}
            />
          </div>
        )}
      </div>

      <div className={styles.row}>
        <div className={styles.location}>
          {location?.title && (
            <h2 className={clsx(styles.title, styles["size-sm"], styles["color-primary"])} aria-roledescription="Ville">
              {location.title}
            </h2>
          )}
          {address && (
            <>
              <p className={styles.place} aria-roledescription="Lieu">
                {street && street?.length > 0 && (
                  <>
                    <span>{street.join(" ")}, </span>
                    {city && <span>{city} </span>}
                  </>
                )}
              </p>
              <p className={styles.place} aria-roledescription="Lieu">
                {zip && <span>{zip}, </span>}
                {country && <span>{country}</span>}
              </p>
            </>
          )}
        </div>

        <h2 className={clsx(styles.title, styles["size-sm"], styles["color-primary"])}>Accessibilité</h2>

        {accessibility && <Accessibility size="sm" accessibility={accessibility} />}
      </div>

      <div className={clsx(styles.row, styles.gap16)}>
        <h2 className={clsx(styles.title, styles["size-md"], styles["color-primary"])}>
          <i className={clsx("far", "fa-paperclip", styles["color-secondary"])} aria-hidden={true} /> Plus d'infos
        </h2>

        {((phones && phones.some((phone) => phone?.number)) || email || website) && (
          <ul className={styles.actions}>
            {phones && phones.some((phone) => phone?.number) && (
              <li className={styles.item}>
                {phones?.map((phone, index) => {
                  return (
                    phone?.number && (
                      <Link href={`tel:${phone.number}`} key={index}>
                        {phone.deviceType && deviceIcons[phone.deviceType] && (
                          <i className={clsx("far", deviceIcons[phone.deviceType])} aria-hidden="true"></i>
                        )}
                        <span>{formatPhone(phone.number)}</span>
                      </Link>
                    )
                  );
                })}
              </li>
            )}

            {email && (
              <li className={styles.item}>
                <Link href={`mailto:${email}`}>
                  <i className="far fa-envelope" aria-hidden="true" />
                  <span>{email}</span>
                </Link>
              </li>
            )}

            {website && (
              <li className={styles.item}>
                <Link href={website} target="_blank" rel="noreferrer">
                  <i className="far fa-arrow-up-right-from-square" aria-hidden="true" />
                  <span>{website}</span>
                </Link>
              </li>
            )}
          </ul>
        )}
      </div>

      {url && (
        <div className={styles.row}>
          <Button asChild color="primary" variant="text" startIcon="far fa-arrow-right">
            <Link href={url}>Voir la fiche détaillée</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
