@use "mixins.scss" as *;
@use "variables.scss" as *;

// Visually hidden pattern
%visually-hidden {
  @include visually-hidden;
}

// Inherit font styles
%font-inherit {
  font-family: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
}

// Clear default styles for ul/ol elements
%clear-list-styles {
  padding: 0;
  margin: 0;
}

// Default extender for clear-fix;
%clear-fix {
  &::after {
    clear: both;
    display: block;
    visibility: hidden;
    height: 0;
    overflow: hidden;
    content: "";
  }
}

// Clear default styles for buttons
%clear-button-styles {
  padding: 0;
  background-color: transparent;
  border: 0;

  @include on-event {
    cursor: pointer;
  }
}

%underline-context {
  text-decoration: none;

  @include on-event {
    outline: none !important;

    .underline {
      background-size: 100% 100% !important;
    }
  }
}

%text-wrap {
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
}

%underline {
  @include multiline-underline;

  @include on-event {
    outline: none !important;
    background-size: 100% 100% !important;
  }
}

%link-block {
  text-decoration: none;

  &::after {
    @include absolute(0, 0);
    @include size(100%);

    z-index: $layer-link-block;
    display: block;
    content: "";
  }

  &:focus-visible {
    &::after {
      outline: $focus-outline;
      outline-offset: $focus-outline-offset;
    }
  }
}
