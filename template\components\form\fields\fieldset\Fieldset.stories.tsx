import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Text from "../text/Text";
import Fieldset from "./Fieldset";

const meta: Meta<typeof Fieldset> = {
  title: "Form/Fieldset",
  component: Fieldset,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Fieldset>;

export const Default: Story = {
  args: {
    title: "Vous connaître",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    children: (
      <>
        <Text
          autocomplete={null}
          columnSpan={12}
          condition={null}
          cssClass={null}
          defaultValue={null}
          descriptionPlacement={null}
          description="Texte de description additionel"
          hidden={false}
          label="Nom"
          name="input_1"
          pattern={null}
          placeholder=""
          required={false}
          showLabel={true}
          size={null}
          type=""
          validationMessage={null}
        />
        <Text
          autocomplete={null}
          columnSpan={12}
          condition={null}
          cssClass={null}
          defaultValue={null}
          descriptionPlacement={null}
          description="Texte de description additionel"
          hidden={false}
          label="Prénom"
          name="input_2"
          pattern={null}
          placeholder=""
          required={false}
          showLabel={true}
          size={null}
          type=""
          validationMessage={null}
        />
      </>
    ),
  },
};
