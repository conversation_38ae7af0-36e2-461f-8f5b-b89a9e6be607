import StoryForm<PERSON>rovider from "@/stories/StoryFormProvider";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Address from "./Address";

const meta: Meta<typeof Address> = {
  title: "Form/Address",
  component: Address,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Address>;

export const Default: Story = {
  args: {
    name: "address_1",
    label: "Adresse de résidence",
    description: "Votre adresse complète",
    street1: {
      required: true,
      placeholder: "Rue, avenue, boulevard...",
      autocomplete: null,
      columnSpan: 12,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Adresse 1",
      name: "street1",
    },
    city: {
      required: true,
      placeholder: "Ville",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Ville",
      name: "city",
    },
    zip: {
      required: true,
      placeholder: "Code postal",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Code postal",
      name: "zip",
    },
    country: {
      required: false,
      placeholder: "Sélectionnez un pays",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      showLabel: true,
      size: null,
      validationMessage: null,
      label: "Pays",
      name: "country",
      pattern: null,
      type: "select",
      enhancedUI: false,
      choices: [
        { value: "FR", label: "France", defaultSelected: true, icon: null },
        { value: "BE", label: "Belgique", defaultSelected: false, icon: null },
        { value: "CH", label: "Suisse", defaultSelected: false, icon: null },
        { value: "CA", label: "Canada", defaultSelected: false, icon: null },
        { value: "US", label: "États-Unis", defaultSelected: false, icon: null },
        { value: "UK", label: "Royaume-Uni", defaultSelected: false, icon: null },
        { value: "DE", label: "Allemagne", defaultSelected: false, icon: null },
        { value: "IT", label: "Italie", defaultSelected: false, icon: null },
        { value: "ES", label: "Espagne", defaultSelected: false, icon: null },
      ],
    },
  },
};

export const Complete: Story = {
  args: {
    name: "address_2",
    label: "Adresse complète",
    description: "Tous les champs d'adresse",
    street1: {
      required: true,
      placeholder: "Adresse ligne 1",
      autocomplete: null,
      columnSpan: 12,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Adresse 1",
      name: "street1",
    },
    street2: {
      required: false,
      placeholder: "Complément d'adresse",
      autocomplete: null,
      columnSpan: 12,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Adresse 2",
      name: "street2",
    },
    city: {
      required: true,
      placeholder: "Ville",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Ville",
      name: "city",
    },
    zip: {
      required: true,
      placeholder: "Code postal",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Code postal",
      name: "zip",
    },
    state: {
      required: false,
      placeholder: "État/Région",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Province",
      name: "state",
    },
    country: {
      required: true,
      placeholder: "Sélectionnez un pays",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      showLabel: true,
      size: null,
      validationMessage: null,
      label: "Pays",
      name: "country",
      pattern: null,
      type: "select",
      enhancedUI: false,
      choices: [
        { value: "FR", label: "France", defaultSelected: true, icon: null },
        { value: "BE", label: "Belgique", defaultSelected: false, icon: null },
        { value: "CH", label: "Suisse", defaultSelected: false, icon: null },
        { value: "CA", label: "Canada", defaultSelected: false, icon: null },
        { value: "US", label: "États-Unis", defaultSelected: false, icon: null },
        { value: "UK", label: "Royaume-Uni", defaultSelected: false, icon: null },
        { value: "DE", label: "Allemagne", defaultSelected: false, icon: null },
        { value: "IT", label: "Italie", defaultSelected: false, icon: null },
        { value: "ES", label: "Espagne", defaultSelected: false, icon: null },
      ],
    },
  },
};

export const Minimal: Story = {
  args: {
    name: "address_3",
    label: "Adresse simplifiée",
    description: "Champs essentiels uniquement",
    street1: {
      required: true,
      placeholder: "Adresse",
      autocomplete: null,
      columnSpan: 12,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Adresse 1",
      name: "street1",
    },
    city: {
      required: true,
      placeholder: "Ville",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Ville",
      name: "city",
    },
    zip: {
      required: true,
      placeholder: "Code postal",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Code postal",
      name: "zip",
    },
  },
};
