@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.adminBar {
  position: sticky;
  top: 0;
  z-index: $layer-adminbar;
  display: flex;
  gap: 24px;
  align-items: center;
  width: 100%;
  padding-inline: 12px;
  font-size: 1.4rem;
  color: #fffffe;
  background-color: $color-neutral-700;

  @include breakpoint(large up) {
    gap: 82px;
    padding-inline: 32px;
  }
}

.logo {
  flex-shrink: 0;
  max-height: 20px;

  @include breakpoint(small only) {
    display: none;
  }
}

.menu {
  display: flex;
  flex-wrap: wrap;
}

.menuitem {
  display: flex;
  gap: 8px;
  align-items: center;
  min-height: 40px;
  padding-inline: 16px;
  font-size: 1.4rem;
  font-weight: 700;
  color: $color-neutral-300;
  cursor: pointer;
  transition:
    background-color 100ms ease,
    color 100ms ease;

  img {
    width: 100%;
    max-width: 18px;
    max-height: 18px;
  }

  i {
    font-size: 1.8rem;
    color: $color-neutral-500;

    @include breakpoint(small only) {
      color: $color-neutral-300;
    }
  }

  &:hover {
    color: $color-black;
    background-color: $color-neutral-200;

    i {
      color: inherit;
    }
  }
}

.text {
  @include breakpoint(small only) {
    display: none;
  }
}
