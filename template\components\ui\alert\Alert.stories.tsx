import { <PERSON>ertVariant } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Alert from "./AlertRenderer";
import { POP_UP_ALERT_CLOSED_KEY } from "./PopUpAlert";
import { STICKY_NOTE_CLOSED_KEY } from "./StickyNote";

const meta: Meta<typeof Alert> = {
  title: "Components/Alert",
  component: Alert,
  tags: ["autodocs"],
  parameters: {
    localStorage: {
      [POP_UP_ALERT_CLOSED_KEY]: "",
      [STICKY_NOTE_CLOSED_KEY]: "",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Alert>;

export const Popup: Story = {
  args: {
    entries: [
      {
        id: 1,
        title: "Travaux rue de la République du 25 au 28 juin, circulation perturbée.",
        description:
          "Des travaux de réfection de la chaussée auront lieu cette semaine. Prévoir des ralentissements et privilégier les itinéraires alternatifs.",
        modifiedDate: "2023-10-01",
        variant: AlertVariant.POPUP,
        action: {
          url: "#",
          text: "J'ai compris",
        },
      },
    ],
  },
};

export const StickyNote: Story = {
  args: {
    entries: [
      {
        id: 20,
        title: "Route départementale D23 coupée à la circulation pour cause d’éboulement.",
        description:
          "Suite aux intempéries, un éboulement a eu lieu sur la D23 entre La Rivière et Montclaire. La route est fermée jusqu’à nouvel ordre. Déviations mises en place.",
        modifiedDate: "2023-10-01",
        variant: AlertVariant.STICKY,
        action: {
          url: "#",
          text: "J'ai compris",
        },
      },
    ],
  },
};
