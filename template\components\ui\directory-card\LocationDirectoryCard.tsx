"use client";

import { formatAddress } from "@/components/ui/address/Address";
import Button from "@/components/ui/button/Button";
import Hx from "@/components/ui/title/Hx";
import type { Directory, PhoneDeviceType } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import formatPhone from "@/utils/formatPhone";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { PartialDeep } from "type-fest";
import Accessibility from "../accessibility/Accessibility";
import styles from "./LocationDirectoryCard.module.scss";

const deviceIcons: Partial<Record<PhoneDeviceType, string>> = {
  LANDLINE: "fa-phone",
  MOBILE: "fa-mobile",
};

interface LocationDirectoryCardProps {
  directory: PartialDeep<Directory, { recurseIntoArrays: true }> & Pick<Directory, "viewMode">;
}

export default function LocationDirectoryCard({
  directory: { title, images, phones, website, email, openingHours, categories, location, url, accessibility },
}: LocationDirectoryCardProps) {
  const image = images?.ratio_3x2 ?? null;
  const [category] = categories ?? [];
  const titleLevel = useTitleLevel();
  const formattedAddress = formatAddress(location?.address);

  return (
    <article className={styles.directory}>
      <Hx level={titleLevel} className={styles.title}>
        {category && (
          <span className={styles.category}>
            {category.title}
            <span className="sr-only">:</span>
          </span>
        )}
        {url ? (
          <Link href={url} className={styles.titleLink}>
            {title}
          </Link>
        ) : (
          title
        )}
      </Hx>
      <Accessibility className={styles.accessibility} accessibility={accessibility ?? undefined} />
      <div className={styles.infos}>
        {formattedAddress && (
          <p className={styles.info}>
            <strong className={styles.label}>Adresse :</strong>
            {formattedAddress}
          </p>
        )}
        {openingHours && openingHours.length > 0 && (
          <p className={styles.info}>
            <strong className={styles.label}>Horaires :</strong>
            {openingHours?.map((time, index) => (
              <React.Fragment key={index}>
                {index > 0 && <br />}
                {time}
              </React.Fragment>
            ))}
          </p>
        )}
      </div>
      {((phones && phones.some((phone) => phone?.number)) || email || website) && (
        <div className={styles.actions}>
          {phones?.map((phone, index) => {
            return (
              phone?.number && (
                <Button
                  key={index}
                  asChild
                  variant="outlined"
                  startIcon={
                    phone.deviceType &&
                    deviceIcons[phone.deviceType] && (
                      <i className={clsx("far", deviceIcons[phone.deviceType])} aria-hidden="true"></i>
                    )
                  }
                >
                  <a href={`tel:${phone.number}`}>{formatPhone(phone.number)}</a>
                </Button>
              )
            );
          })}
          {email && (
            <Button asChild variant="outlined" startIcon="far fa-at">
              <a href={`mailto:${email}`}>Courriel</a>
            </Button>
          )}
          {website && (
            <Button asChild variant="contained" startIcon="fas fa-arrow-up-right-from-square">
              <a href={website} target="_blank" rel="noreferrer">
                Site internet
              </a>
            </Button>
          )}
        </div>
      )}
      {image?.url && (
        <div className={styles.imageWrapper}>
          <Image className={styles.img} src={image.url} alt={image.alt ?? ""} width={247} height={165} />
        </div>
      )}
    </article>
  );
}
