"use client";

import MapComponent from "@/components/ui/map/Map";
import { Marker } from "@/components/ui/map/types";
import { Directory, LocationsBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import maplibregl from "maplibre-gl";
import { useCallback, useMemo, useRef, useState } from "react";
import { useMediaQuery } from "usehooks-ts";
import styles from "./Locations.module.scss";
import Popup from "./Popup";

type LocationsProps = Omit<LocationsBlock, "__typename" | "innerBlocks">;

export type DirectoryMarker = Marker<{
  title?: string;
  address?: string;
  directory?: string;
}>;

export default function Locations({ locations }: LocationsProps) {
  const mapRef = useRef<maplibregl.Map>(null);

  const [selectedMarkers, setSelectedMarkers] = useState<DirectoryMarker[]>([]);

  const isMobileViewport = useMediaQuery("(max-width: 768px)");

  const markers: DirectoryMarker[] = useMemo(
    () =>
      locations
        .filter((item): item is Directory => item.__typename === "Directory")
        .filter((item) => !!item.location)
        .map((item, index) => ({
          id: index,
          coordinates: [item.location!.longitude!, item.location!.latitude!],
          data: {
            title: item.title ?? "",
            address: item.location!.address ? JSON.stringify(item.location!.address) : undefined,
            directory: JSON.stringify(item),
          },
        })),
    [locations]
  );

  const handleSelectMarker = useCallback(
    (markers: DirectoryMarker[]) => {
      setSelectedMarkers(markers);

      if (!mapRef.current || markers.length === 0) {
        return;
      }

      const point = mapRef.current.project(markers[0].coordinates);

      // If the cursor is under the popup, center it on the right side
      // TODO: Needs better calculation (for responsive)
      if (point.x < 400 && !isMobileViewport) {
        mapRef.current.panTo(markers[0].coordinates, { offset: [200, 0] });
      }
    },
    [mapRef, isMobileViewport]
  );

  const handleClose = useCallback(() => {
    setSelectedMarkers([]);
  }, []);

  return (
    <section className={clsx("block-locations contained", styles.locations)}>
      <MapComponent
        className={styles.map}
        markers={markers}
        selectedMarkerIds={selectedMarkers.map((m) => m.id)}
        onSelectionChange={handleSelectMarker}
        controls={{ fullscreen: false }}
        onMapLoad={(m) => (mapRef.current = m)}
        scrollOverlay={true}
      />

      {selectedMarkers.length > 0 && <Popup onClose={handleClose} selectedMarkers={selectedMarkers} />}
    </section>
  );
}
