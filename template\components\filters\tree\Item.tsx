import clsx from "clsx";
import Link from "next/link";
import { useCallback, useState } from "react";
import List from "./List";
import styles from "./Tree.module.scss";
import { TreeNode, useLink, useTreeContext } from "./TreeContext";

interface ItemProps {
  root?: boolean;
  node: TreeNode;
}

export default function Item({ root, node: { value, label, count, status, children, defaultExpanded } }: ItemProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded ?? false);
  const hasChildren = children && children.length > 0;
  const handleToggle = useCallback(() => setIsExpanded(!isExpanded), [isExpanded]);

  const { attribute, tree } = useTreeContext();
  const { link } = useLink(tree, attribute, value);

  return (
    <li className={styles.item}>
      <div className={styles.label}>
        <Link href={link} className={styles.link} scroll={false}>
          <span
            className={clsx(styles.box, {
              [styles.active]: status === "ACTIVE",
              [styles.partial]: status === "PARTIAL",
            })}
          >
            {status && (
              <i
                className={clsx(styles.boxIcon, "far", {
                  "fa-check": status === "ACTIVE",
                  "fa-minus": status === "PARTIAL",
                })}
              />
            )}
          </span>
          <span className={clsx(styles.text, { [styles.root]: root })}>{label ?? value}</span>
          <span className={styles.count}>({count ?? 0})</span>
        </Link>

        {hasChildren && (
          <button
            type="button"
            className={styles.toggle}
            onClick={handleToggle}
            aria-expanded={isExpanded}
            aria-label={isExpanded ? "Replier les options" : "Déplier les options"}
          >
            <i className={clsx("far", isExpanded ? "fa-minus" : "fa-plus")} aria-hidden={true} />
          </button>
        )}
      </div>

      {hasChildren && isExpanded && <List nodes={children} />}
    </li>
  );
}
