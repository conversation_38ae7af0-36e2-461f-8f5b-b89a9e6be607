import { DirectoryAccessibility, EventAccessibility } from "@/generated/graphql/graphql";

export type AccessibilityKey = Exclude<keyof EventAccessibility | keyof DirectoryAccessibility, "__typename">;

export type AccessibilityStatus = "SUPPORTED" | "NOT_SUPPORTED" | "UNKNOWN";

export interface AccessibilityOption {
  tooltip: Record<AccessibilityStatus, string>;
  icon: React.FC<React.SVGProps<unknown>>;
}
