"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import type { FlashInfo } from "@/generated/graphql/graphql";
import A11y from "@/lib/swiper/modules/a11y";
import clsx from "clsx";
import { useCallback, useId, useRef, useState } from "react";
import SwiperCore from "swiper";
import "swiper/css";
import "swiper/css/a11y";
import "swiper/css/navigation";
import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import styles from "./FlashInfoCarousel.module.scss";
import FlashInfoContent from "./FlashInfoContent";

interface FlashInfoCarouselProps {
  flashInfos: Partial<FlashInfo>[];
}

export default function FlashInfoCarousel({ flashInfos }: FlashInfoCarouselProps) {
  const previousId = useId();
  const nextId = useId();
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const swiperRef = useRef<SwiperCore | null>(null);

  const togglePlayPause = useCallback(() => {
    if (!swiperRef.current) return;

    if (isPlaying) {
      swiperRef.current.autoplay.stop();
    } else {
      swiperRef.current.autoplay.start();
    }

    setIsPlaying((prev) => !prev);
  }, [isPlaying]);

  const playPauseLabel = isPlaying ? "Arrêter le défilement automatique" : "Lancer le défilement automatique";

  return (
    <>
      <div role="group" className={styles.swiperControls} aria-label="Contrôle du carrousel">
        <p className={styles.paginationInfo} aria-hidden="true">
          {activeIndex + 1}/{flashInfos.length}
        </p>

        <Tooltip content="Info précédente">
          <div id={previousId} role="button" tabIndex={0} className={styles.prevButton}>
            <i className="far fa-chevron-left" aria-hidden="true"></i>
            <span className="sr-only">Info précédente</span>
          </div>
        </Tooltip>

        <Tooltip content="Info suivante">
          <div id={nextId} role="button" tabIndex={0} className={styles.nextButton}>
            <i className="far fa-chevron-right" aria-hidden="true"></i>
            <span className="sr-only">Info suivante</span>
          </div>
        </Tooltip>

        <Tooltip content={playPauseLabel}>
          <button type="button" onClick={togglePlayPause} className={styles.playButton} aria-label={playPauseLabel}>
            <i className={clsx("far", isPlaying ? "fa-pause" : "fa-play")} aria-hidden="true" />
          </button>
        </Tooltip>
      </div>
      <Swiper
        className={styles.swiper}
        modules={[Navigation, Autoplay, A11y]}
        slidesPerView={1}
        navigation={{
          prevEl: "#" + CSS.escape(previousId),
          nextEl: "#" + CSS.escape(nextId),
        }}
        loop
        autoplay={{ delay: 6000 }}
        effect="slide"
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        onSlideChange={(swiper) => setActiveIndex(swiper.realIndex)}
        a11y={{
          nextSlideMessage: "Info suivante",
          prevSlideMessage: "Info précédente",
          firstSlideMessage: "Première info",
          lastSlideMessage: "Dernière info",
          slideLabelMessage: "{{index}} sur {{slidesLength}}",
          inertInvisibleSlides: true,
        }}
      >
        {flashInfos.map((item: Partial<FlashInfo>, index) => (
          <SwiperSlide key={index} className={styles.wrapper}>
            <FlashInfoContent item={item} />
          </SwiperSlide>
        ))}
      </Swiper>
    </>
  );
}
