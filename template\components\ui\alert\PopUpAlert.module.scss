@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.overlay {
  position: fixed;
  inset: 0;
  z-index: $layer-alert - 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(0 0 0 / 40%);
}

.modalContainer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: $layer-alert;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  padding: 16px;

  @include breakpoint(medium up) {
    padding: 24px;
  }
}

.popupAlert {
  display: flex;
  flex-direction: column;
  max-width: 720px;
  color: $color-white;
  background-color: $color-primary-500;
  border-radius: 4px;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);
}

.header {
  display: flex;
  justify-content: space-between;
}

.closeButton {
  width: 48px;
  aspect-ratio: 1;
  margin-left: auto;
}

.content {
  padding: 0 16px 48px;

  @include breakpoint(medium up) {
    padding: 0 48px 48px;
  }

  @include breakpoint(large up) {
    padding: 0 56px 56px;
  }
}

.details {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.topIcon {
  font-size: 3.2rem;
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.description {
  font-size: 1.6rem;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.actions {
  margin-top: 24px;

  @include breakpoint(large up) {
    margin-top: 32px;
  }
}
