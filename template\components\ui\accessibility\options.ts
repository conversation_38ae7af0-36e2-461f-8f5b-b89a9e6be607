import HearingImpairmentIcon from "./icons/hearing-impairment.svg";
import IntellectualImpairmentIcon from "./icons/intellectual-impairment.svg";
import MentalImpairmentIcon from "./icons/mental-impairment.svg";
import ReducedMobilityIcon from "./icons/reduced-mobility.svg";
import SignLanguageReceptionIcon from "./icons/sign-language-reception.svg";
import StrollersIcon from "./icons/strollers.svg";
import VisualImpairmentIcon from "./icons/visual-impairment.svg";
import { AccessibilityKey, AccessibilityOption } from "./types";

export const accessibilityOptions: Record<AccessibilityKey, AccessibilityOption> = {
  hearingImpairment: {
    icon: HearingImpairmentIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les personnes déficientes auditives",
      NOT_SUPPORTED: "Non accessible pour les personnes déficientes auditives",
      UNKNOWN: "Non renseigné pour les personnes déficientes auditives",
    },
  },
  visualImpairment: {
    icon: VisualImpairmentIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les personnes déficientes visuelles",
      NOT_SUPPORTED: "Non accessible pour les personnes déficientes visuelles",
      UNKNOWN: "Non renseigné pour les personnes déficientes visuelles",
    },
  },
  mentalImpairment: {
    icon: MentalImpairmentIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les personnes déficientes mentales",
      NOT_SUPPORTED: "Non accessible pour les personnes déficientes mentales",
      UNKNOWN: "Non renseigné pour les personnes déficientes mentales",
    },
  },
  intellectualImpairment: {
    icon: IntellectualImpairmentIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les personnes déficientes psychiques",
      NOT_SUPPORTED: "Non accessible pour les personnes déficientes psychiques",
      UNKNOWN: "Non renseigné pour les personnes déficientes psychiques",
    },
  },
  signLanguageReception: {
    icon: SignLanguageReceptionIcon,
    tooltip: {
      SUPPORTED: "Accueil en langue des signes accessible",
      NOT_SUPPORTED: "Accueil en langue des signes non accessible",
      UNKNOWN: "Accueil en langue des signes non renseigné",
    },
  },
  strollers: {
    icon: StrollersIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les poussettes",
      NOT_SUPPORTED: "Non accessible pour les poussettes",
      UNKNOWN: "Non renseigné pour les poussettes",
    },
  },
  reducedMobility: {
    icon: ReducedMobilityIcon,
    tooltip: {
      SUPPORTED: "Accessible pour les personnes à mobilité réduite",
      NOT_SUPPORTED: "Non accessible pour les personnes à mobilité réduite",
      UNKNOWN: "Non renseigné pour les personnes à mobilité réduite",
    },
  },
};
