import { execa } from "execa";
import fs from "fs-extra";
import path from "node:path";
import { fileURLToPath } from "node:url";

const docs = fileURLToPath(new URL("../docs", import.meta.url));
const themeDir = path.join(docs, "themes/hugo-book");

async function preflight() {
  await fs.ensureDir(themeDir);

  // Skip if the dir exists already
  if (fs.exists(themeDir) && fs.readdirSync(themeDir).length > 0) {
    return;
  }

  // Clone the theme repository
  await execa("git", [
    "clone",
    "https://github.com/alex-shpak/hugo-book",
    "--depth=1",
    themeDir,
  ]);
}

preflight();
