import Address from "@/components/ui/address/Address";
import Button from "@/components/ui/button/Button";
import MapComponent from "@/components/ui/map/Map";
import type { Location, Phone, PhoneDeviceType } from "@/generated/graphql/graphql";
import formatPhone from "@/utils/formatPhone";
import clsx from "clsx";
import Link from "next/link";
import type { PartialDeep } from "type-fest";
import styles from "./LocationContactWidget.module.scss";

interface LocationContactWidgetProps {
  phones?: Phone[] | null;
  email?: string | null;
  website?: string | null;
  location?: PartialDeep<Location, { recurseIntoArrays: true }> | null;
}

const deviceIcons: Partial<Record<PhoneDeviceType, string>> = {
  LANDLINE: "fa-phone",
  MOBILE: "fa-mobile",
  FAX: "fa-fax",
};

export default function LocationContactWidget({ phones, email, website, location }: LocationContactWidgetProps) {
  if ((!phones || phones.length === 0) && !email && !website && !location) {
    return null;
  }

  return (
    <div className={clsx("widget widget-location-contact", styles.locationContactWidget)}>
      <h3 className={styles.title}>
        <i className="far fa-location-dot" aria-hidden="true"></i> Coordonnées
      </h3>
      <div className={styles.actions}>
        {phones?.map((phone, index) => {
          return (
            <Button
              key={index}
              asChild
              variant="outlined"
              startIcon={
                deviceIcons[phone.deviceType ?? "LANDLINE"] && (
                  <i className={clsx("far", deviceIcons[phone.deviceType ?? "LANDLINE"])} aria-hidden="true"></i>
                )
              }
            >
              <Link href={`tel:${phone.number}`}>{formatPhone(phone.number)}</Link>
            </Button>
          );
        })}

        {email && (
          <Button asChild variant="outlined" startIcon="far fa-at">
            <Link href={`mailto:${email}`}>Courriel</Link>
          </Button>
        )}

        {website && (
          <Button asChild variant="contained" startIcon="fas fa-arrow-up-right-from-square">
            <Link href={`tel:${email}`}>Site internet</Link>
          </Button>
        )}
      </div>

      {location?.latitude && location?.longitude && (
        <MapComponent
          defaultZoom={18}
          className={styles.map}
          defaultCenter={[location.longitude, location.latitude]}
          fitBoundsToMarkers={false}
          markers={[{ id: 1, coordinates: [location.longitude, location.latitude] }]}
          selectedMarkerIds={[1]}
          interactive={false}
        />
      )}

      {location?.address && <Address className={styles.address} address={location.address} />}
    </div>
  );
}
