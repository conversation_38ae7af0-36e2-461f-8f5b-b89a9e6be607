import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import ListItem from "../list-item/ListItem";
import List from "./List";

const meta: Meta<typeof List> = {
  title: "Blocks/List",
  component: List,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof List>;

export const Unordered: Story = {
  args: {
    ordered: false,
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const TwoLevels: Story = {
  args: {
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum">
          <List>
            <ListItem html="Lorem ipsum"></ListItem>
            <ListItem html="Lorem ipsum"></ListItem>
            <ListItem html="Lorem ipsum"></ListItem>
          </List>
        </ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const ThreeLevels: Story = {
  args: {
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum">
          <List>
            <ListItem html="Lorem ipsum"></ListItem>
            <ListItem html="Lorem ipsum"></ListItem>
            <ListItem html="Lorem ipsum">
              <List>
                <ListItem html="Lorem ipsum"></ListItem>
                <ListItem html="Lorem ipsum"></ListItem>
                <ListItem html="Lorem ipsum"></ListItem>
              </List>
            </ListItem>
          </List>
        </ListItem>
      </>
    ),
  },
};

export const Ordered: Story = {
  args: {
    ordered: true,
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const OrderedStartAt: Story = {
  args: {
    ordered: true,
    startAt: 8,
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const OrderedType: Story = {
  args: {
    ordered: true,
    type: "A",
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const OrderedReversed: Story = {
  args: {
    ordered: true,
    reversed: true,
    children: (
      <>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
        <ListItem html="Lorem ipsum"></ListItem>
      </>
    ),
  },
};

export const RichContent: Story = {
  args: {
    ordered: false,
    children: (
      <>
        <ListItem html="<b>Lorem</b> ipsum"></ListItem>
        <ListItem html="Lorem <em>ipsum</em>"></ListItem>
        <ListItem html="<a href='#'>Lorem ipsum</a>"></ListItem>
      </>
    ),
  },
};
