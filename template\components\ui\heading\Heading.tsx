import dynamic from "next/dynamic";
import Link from "next/link";
import striptags from "striptags";
import styles from "./Heading.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));
const Tag = dynamic(() => import("@/components/ui/tag/Tag"));

interface HeadingProps {
  leadText?: string | null;
  modifiedDate?: string;
  publicationDate?: string;
  rssUrl?: string | null;
  surtitle?: string;
  tagsRoleDescription?: string;
  tags?: { url?: string; text: string }[];
  title: string;
}

/**
 * The base heading, used in list views and content pages.
 */
export default function Heading({
  children,
  leadText,
  modifiedDate,
  publicationDate,
  rssUrl,
  surtitle,
  tags,
  tagsRoleDescription,
  title,
}: React.PropsWithChildren<HeadingProps>) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.titleWrapper}>
          <h1 className={styles.title}>
            {surtitle && (
              <span className={styles.surtitle}>
                {surtitle}
                <span className="sr-only">:</span>
              </span>
            )}
            {title}
          </h1>
          {rssUrl && (
            <div className={styles.actions}>
              <Button asChild variant="outlined" size="xs" startIcon="fas fa-rss">
                <Link href={rssUrl}>
                  <span className="text">S’abonner</span>
                </Link>
              </Button>
            </div>
          )}
        </div>
        {leadText && <p className={styles.teaser}>{striptags(leadText)}</p>}
        {children}
        {tags && tags.length > 0 && (
          <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
            {tags.map((tag, index) => (
              <li key={index}>
                {tag.url ? (
                  <Tag variant="primary" asChild>
                    <Link href={tag.url}>{tag.text}</Link>
                  </Tag>
                ) : (
                  <Tag variant="primary">{tag.text}</Tag>
                )}
              </li>
            ))}
          </ul>
        )}
        {(publicationDate || modifiedDate) && (
          <p className={styles.dates}>
            {publicationDate && (
              <>
                <span>Publié le </span>
                <time dateTime={publicationDate}>
                  {new Date(publicationDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
            {modifiedDate && (
              <>
                <span>Mis à jour le </span>
                <time dateTime={modifiedDate}>
                  {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
          </p>
        )}
      </div>
    </header>
  );
}
