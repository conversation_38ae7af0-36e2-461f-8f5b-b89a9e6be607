"use client";

import { graphql } from "@/generated/graphql";
import { Directory } from "@/generated/graphql/graphql";
import { useQuery } from "@apollo/client";
import CardDetail from "./CardDetail";

interface MarkerDetailsContainerProps {
  id: number;
}

const DIRECTORY_MARKERS_DETAILS_QUERY = graphql(`
  query GetDirectoryMarkersDetails($id: Int!) {
    directory(id: $id) {
      accessibility {
        hearingImpairment
        intellectualImpairment
        mentalImpairment
        reducedMobility
        signLanguageReception
        strollers
        visualImpairment
      }
      categories {
        description
        relativeUrl
        title
      }
      email
      images {
        ratio_3x2 {
          alt
          height
          url
          width
        }
      }
      location {
        title
        address {
          city
          country
          street
          zip
        }
      }
      phones {
        country
        deviceType
        internationalNumber
        label
        number
      }
      title
      website
    }
  }
`);

export default function MarkerDetailsContainer({ id }: MarkerDetailsContainerProps) {
  const { data } = useQuery(DIRECTORY_MARKERS_DETAILS_QUERY, {
    variables: { id },
    skip: !id,
  });

  return <CardDetail data={data?.directory as Directory} />;
}
