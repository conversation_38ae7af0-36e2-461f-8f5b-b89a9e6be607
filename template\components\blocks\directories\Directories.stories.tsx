import { DirectoryViewMode, PhoneDeviceType, type Directory } from "@/generated/graphql/graphql";
import { OneThirdColumnsDecorator, TwoThirdColumnsDecorator } from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Directories from "./Directories";

const meta: Meta<typeof Directories> = {
  title: "Blocks/Directories",
  component: Directories,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Directories>;

const directoryData: Directory = {
  viewMode: DirectoryViewMode.LOCATION,
  title:
    "120 caractères ullam mauris egestas quamac urna eu felis dapibus condimentum sit amet a augue ed non neque elit sd auris",
  // @ts-expect-error Incomplete image collection
  images: {
    ratio_3x2: {
      url: "/assets/placeholder-contact.png",
      alt: "Medical Center",
      height: 400,
      width: 600,
    },
  },
  phones: [
    // @ts-expect-error Incomplete phone
    {
      deviceType: PhoneDeviceType.LANDLINE,
      number: "**********",
    },
    // @ts-expect-error Incomplete phone
    {
      deviceType: PhoneDeviceType.MOBILE,
      number: "**********",
    },
  ],
  website: "#",
  email: "<EMAIL>",
  openingHours: ["Lundi 15h-18h", "Mardi 9h30-12h30", "Jeudi 9h30-12h30 / 14h30-18h30"],
  categories: [
    // @ts-expect-error Incomplete category
    {
      title: "Thématique",
      relativeUrl: "#",
    },
  ],
  location: {
    // @ts-expect-error Incomplete address
    address: {
      street: ["123 Main Street"],
      zip: "75000",
      city: "Paris",
    },
  },
  url: "#",
};

export const Default: Story = {
  args: {
    title: "Annuaire de structures",
    directories: [directoryData],
  },
};

export const TwoColumns: Story = {
  args: {
    directories: [directoryData],
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    directories: [directoryData],
  },
  decorators: [OneThirdColumnsDecorator],
};
