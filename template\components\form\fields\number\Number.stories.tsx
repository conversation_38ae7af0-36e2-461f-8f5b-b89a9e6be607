import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Number from "./Number";

const meta: Meta<typeof Number> = {
  title: "Form/Number",
  component: Number,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Number>;

export const Default: Story = {
  args: {
    name: "input_1",
    label: "Quantity",
    description: "Texte de description additionel",
    placeholder: "0",
  },
};
