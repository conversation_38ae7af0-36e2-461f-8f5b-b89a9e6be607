@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.stepIndicator {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.title {
  .currentStep {
    display: block;
    font-size: 1.2rem;
    line-height: 110%;
    color: $color-primary-500;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }
  }
}

.nextStepInfo {
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}
