import { AsChildProps } from "@/components/utils/asChild";
import { Slot, Slottable } from "@radix-ui/react-slot";
import clsx from "clsx";
import React from "react";
import styles from "./Tag.module.scss";

type TagProps = AsChildProps<React.HTMLAttributes<HTMLDivElement>> & {
  variant?: "primary" | "secondary" | "tertiary";
  size?: "sm" | "md" | "lg";
  rounded?: boolean;
  dismissable?: boolean;
  onDismiss?: () => void;
  dismissLabel?: string;
  invert?: boolean;
};

export default function Tag({
  dismissable,
  onDismiss,
  variant,
  rounded,
  dismissLabel,
  asChild,
  invert,
  children,
  className,
  size = "md",
  ...restProps
}: React.PropsWithChildren<TagProps> & React.HTMLAttributes<HTMLDivElement>) {
  const Comp = asChild ? Slot : "div";

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    onDismiss?.();
  };

  return (
    <Comp
      className={clsx(
        styles.tag,
        invert && styles.invert,
        rounded && styles.rounded,
        size && styles[`size-${size}`],
        variant && styles[`variant-${variant}`],
        className
      )}
      {...restProps}
    >
      <Slottable>{children}</Slottable>

      {dismissable && (
        <button type="button" className={styles.dismissButton} onClick={handleClick} aria-label={dismissLabel}>
          <i className="far fa-close fa-fw" aria-hidden="true" />
        </button>
      )}
    </Comp>
  );
}
