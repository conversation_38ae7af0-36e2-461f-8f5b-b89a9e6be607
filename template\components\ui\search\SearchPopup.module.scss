@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.searchPopup {
  width: 100%;
  max-width: 900px;
}

.searchForm {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  padding-top: 5px;

  @include breakpoint(medium up) {
    flex-direction: row;
  }
}

.bottom {
  margin-top: 24px;
}

.subtitle {
  margin-bottom: 12px;
  font-size: 1.6rem;
  line-height: 130%;

  @include breakpoint(large up) {
    margin-bottm: 16px;
  }
}
