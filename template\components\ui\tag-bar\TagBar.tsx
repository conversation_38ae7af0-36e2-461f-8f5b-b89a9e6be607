import Tag from "@/components/ui/tag/Tag";
import { Link } from "@/generated/graphql/graphql";
import clsx from "clsx";
import NextLink from "next/link";
import React from "react";
import styles from "./TagBar.module.scss";

interface TagBarProps {
  items: Partial<Link>[];
}

/**
 * @deprecated Use directly the `Tag` element
 */
export default function TagBar({
  items,
  className,
  ...restProps
}: TagBarProps & React.HTMLAttributes<HTMLUListElement>) {
  if (items.length === 0) {
    return null;
  }

  return (
    <ul className={clsx(styles.tagBar, className)} {...restProps}>
      {items.map(
        (item, index) =>
          item.url && (
            <li key={index}>
              <Tag asChild>
                <NextLink href={item.url} rel={item.rel ?? undefined} target={item.target ?? undefined}>
                  {item.text}
                </NextLink>
              </Tag>
            </li>
          )
      )}
    </ul>
  );
}
