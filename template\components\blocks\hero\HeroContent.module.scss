@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heroContent {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: 100%;
  min-height: calc(100vh - var(--header-height, 0));
  padding-block: 72px;
  padding-inline: 32px;
  color: $color-white;

  @include breakpoint(medium up) {
    padding-inline: 72px;
    padding-bottom: 120px;
  }

  @include breakpoint(large up) {
    padding-inline: 120px;
    padding-bottom: 144px;
  }
}

.surtitle {
  display: block;
  width: 100%;
  margin-bottom: 2px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  letter-spacing: initial;

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 40px;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;

  @include breakpoint(medium up) {
    font-size: 64px;
    letter-spacing: -1.28px;
  }

  @include breakpoint(large up) {
    max-width: 800px;
  }
}

.plusIcon {
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-top: 32px;
  font-size: 1.6rem;
  color: $color-black;
  background-color: $color-secondary-500;
  border-radius: $rounded-xs;
  transition: background-color 200ms ease-in-out;

  &:hover {
    background-color: $color-secondary-300;
  }

  @include breakpoint(medium up) {
    width: 64px;
    height: 64px;
    font-size: 2.4rem;
  }
}
