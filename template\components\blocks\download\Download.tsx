import PublicationFile from "@/components/ui/publication-file/PublicationFile";
import type { DownloadBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./Download.module.scss";

type DownloadProps = Partial<Omit<DownloadBlock, "__typename" | "innerBlocks">>;

export default function Download({ files }: DownloadProps) {
  return (
    <ul
      className={clsx("block-download contained", styles.download)}
      aria-roledescription="Liste de fichiers à télécharger"
    >
      {files?.map((file, index) => (
        <PublicationFile
          key={index}
          label={file.label ?? ""}
          extname={file.extname}
          size={file.size}
          downloadUrl={file.downloadUrl}
        />
      ))}
    </ul>
  );
}
