import type {
  DateRangeFilter,
  FilterEqualTypeInput,
  FilterInterface,
  FilterMatchTypeInput,
  FilterRangeTypeInput,
  SelectFilter,
  TextFilter,
} from "@/generated/graphql/graphql";

/**
 * An union of all the supported filters.
 */
export type Filter = SelectFilter | TextFilter | DateRangeFilter;

/**
 * Search parameters infered from a `FilterInput`.
 */
export type FilterInputParams<T extends Record<string, unknown>> = Partial<Record<keyof T, string | string[]>>;

/**
 * A map of all the filter values, extracted from the query params.
 */
export type FilterValues = Map<string, string[] | null>;

/**
 * Generates a type safe filter input for a `*Search` GraphQL query.
 * TODO: Infer return type from filters and params.
 */
export function createFilterInput<T extends Record<string, unknown>>({
  params,
  filters,
}: {
  params: FilterInputParams<T>;
  filters: { __typename?: string; attribute: string }[];
}): Partial<T> {
  const filterInput: Record<string, unknown> = {};

  for (const filter of filters) {
    const parameter = params[filter.attribute];

    if (parameter) {
      switch (filter.__typename) {
        case "SelectFilter": {
          filterInput[filter.attribute] = {
            in: Array.isArray(parameter) ? parameter : [parameter],
          } satisfies FilterEqualTypeInput;
          break;
        }
        case "TextFilter": {
          if (typeof parameter === "string") {
            filterInput[filter.attribute] = {
              match: parameter as string,
            } satisfies FilterMatchTypeInput;
          }

          break;
        }
        case "DateRangeFilter": {
          if (Array.isArray(parameter) && (parameter as string[]).some(Boolean)) {
            filterInput[filter.attribute] = {
              from: parameter?.[0] && parameter?.[0] !== "" ? parameter?.[0] : null,
              to: parameter?.[1] && parameter?.[1] !== "" ? parameter?.[1] : null,
            } satisfies FilterRangeTypeInput;
          }

          break;
        }
      }
    }
  }

  return filterInput as Partial<T>;
}

export function isSelectFilter(filter: FilterInterface & { __typename?: string }): filter is SelectFilter {
  return filter.__typename === "SelectFilter";
}

export function isDateRangeFilter(filter: FilterInterface & { __typename?: string }): filter is DateRangeFilter {
  return filter.__typename === "DateRangeFilter";
}

export function createPeriodFilterInput(filterInput: Record<string, unknown>): {
  upcoming: boolean;
  from: string | null;
  to: string | null;
} {
  const dateRange = filterInput.date_range as { from?: string; to?: string } | undefined;
  const from = dateRange?.from ? new Date(dateRange.from).toISOString() : null;
  const to = dateRange?.to ? new Date(dateRange.to).toISOString() : null;

  return {
    upcoming: true,
    from,
    to,
  };
}
