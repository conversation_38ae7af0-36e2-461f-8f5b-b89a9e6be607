@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.tag {
  display: inline-flex;
  gap: 8px;
  align-items: center;
  font-size: 1.2rem;
  line-height: normal;
  line-height: 120%;
  color: $color-neutral-700;
  background-color: $color-neutral-100;
  border: 1px solid $color-neutral-300;
  border-radius: 4px;

  &.size-sm {
    gap: 4px;
    padding: 4px 6px;
  }

  &.size-md {
    padding: 8px 12px;
  }

  &.size-lg {
    padding: 12px 24px;
    font-size: 1.4rem;
  }

  &.rounded {
    border-radius: 1000px;
  }

  &.variant-primary {
    color: $color-primary-500;
    background-color: $color-primary-50;
    border-color: $color-primary-400;

    &.invert {
      color: $color-white;
      background-color: $color-primary-500;
      border-color: $color-primary-500;
    }
  }

  &.variant-secondary {
    color: $color-neutral-700;
    background-color: $color-secondary-100;
    border-color: $color-secondary-300;
  }

  &.variant-tertiary {
    color: $color-neutral-700;
    background-color: $color-tertiary-100;
    border-color: $color-tertiary-300;
  }
}

.dismissButton {
  cursor: pointer;

  i {
    vertical-align: middle;
  }
}
