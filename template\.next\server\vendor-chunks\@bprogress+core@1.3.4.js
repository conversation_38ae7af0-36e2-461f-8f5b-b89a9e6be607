"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@bprogress+core@1.3.4";
exports.ids = ["vendor-chunks/@bprogress+core@1.3.4"];
exports.modules = {

/***/ "(ssr)/../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.css":
/*!***********************************************************************************************!*\
  !*** ../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.css ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"a7eae7fc0cf7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BicHJvZ3Jlc3MrY29yZUAxLjMuNC9ub2RlX21vZHVsZXMvQGJwcm9ncmVzcy9jb3JlL2Rpc3QvaW5kZXguY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBicHJvZ3Jlc3MrY29yZUAxLjMuNFxcbm9kZV9tb2R1bGVzXFxAYnByb2dyZXNzXFxjb3JlXFxkaXN0XFxpbmRleC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJhN2VhZTdmYzBjZjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.css\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.js":
/*!**********************************************************************************************!*\
  !*** ../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BProgress: () => (/* binding */ BProgress),\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   getAnchorProperty: () => (/* binding */ getAnchorProperty),\n/* harmony export */   isSameURL: () => (/* binding */ isSameURL),\n/* harmony export */   isSameURLWithoutSearch: () => (/* binding */ isSameURLWithoutSearch)\n/* harmony export */ });\n// src/utils/clamp.ts\nfunction clamp(n, min, max) {\n  return Math.max(min, Math.min(n, max));\n}\n\n// src/utils/to-bar-perc.ts\nfunction toBarPerc(n, direction) {\n  if (direction === \"rtl\") return (1 - n) * 100;\n  return (-1 + n) * 100;\n}\n\n// src/utils/to-css.ts\nfunction toCss(element, properties, value) {\n  if (typeof properties === \"string\") {\n    if (value !== void 0) {\n      element.style[properties] = value;\n    }\n  } else {\n    for (const prop in properties) {\n      if (properties.hasOwnProperty(prop)) {\n        const val = properties[prop];\n        if (val !== void 0) {\n          element.style[prop] = val;\n        }\n      }\n    }\n  }\n}\n\n// src/utils/class.ts\nfunction addClass(element, name) {\n  element.classList.add(name);\n}\nfunction removeClass(element, name) {\n  element.classList.remove(name);\n}\n\n// src/utils/element.ts\nfunction removeElement(element) {\n  if (element && element.parentNode) {\n    element.parentNode.removeChild(element);\n  }\n}\n\n// src/progress.ts\nvar defaultSettings = {\n  minimum: 0.08,\n  maximum: 1,\n  // If template is null, the user can insert their own template in the DOM.\n  template: `<div class=\"bar\"><div class=\"peg\"></div></div>\n             <div class=\"spinner\"><div class=\"spinner-icon\"></div></div>\n             <div class=\"indeterminate\"><div class=\"inc\"></div><div class=\"dec\"></div></div>`,\n  easing: \"linear\",\n  positionUsing: \"\",\n  speed: 200,\n  trickle: true,\n  trickleSpeed: 200,\n  showSpinner: true,\n  indeterminate: false,\n  indeterminateSelector: \".indeterminate\",\n  barSelector: \".bar\",\n  spinnerSelector: \".spinner\",\n  parent: \"body\",\n  direction: \"ltr\"\n};\nvar BProgress = class {\n  static settings = defaultSettings;\n  static status = null;\n  // Queue for animation functions\n  static pending = [];\n  static isPaused = false;\n  // Reset the progress\n  static reset() {\n    this.status = null;\n    this.isPaused = false;\n    this.pending = [];\n    this.settings = defaultSettings;\n    return this;\n  }\n  // Configure BProgress with new options\n  static configure(options) {\n    Object.assign(this.settings, options);\n    return this;\n  }\n  // Check if BProgress has started\n  static isStarted() {\n    return typeof this.status === \"number\";\n  }\n  /**\n   * Set the progress status.\n   * This method updates the progress status for every progress element present in the DOM.\n   * If a template is provided, it will create a new progress element if one does not already exist.\n   * If the template is null, it relies on user-inserted elements.\n   */\n  static set(n) {\n    if (this.isPaused) return this;\n    const started = this.isStarted();\n    n = clamp(n, this.settings.minimum, this.settings.maximum);\n    this.status = n === this.settings.maximum ? null : n;\n    const progressElements = this.render(!started);\n    const speed = this.settings.speed;\n    const ease = this.settings.easing;\n    progressElements.forEach((progress) => progress.offsetWidth);\n    this.queue((next) => {\n      progressElements.forEach((progress) => {\n        if (!this.settings.indeterminate) {\n          const bar = progress.querySelector(\n            this.settings.barSelector\n          );\n          toCss(bar, this.barPositionCSS({ n, speed, ease }));\n        }\n      });\n      if (n === this.settings.maximum) {\n        progressElements.forEach((progress) => {\n          toCss(progress, { transition: \"none\", opacity: \"1\" });\n          progress.offsetWidth;\n        });\n        setTimeout(() => {\n          progressElements.forEach((progress) => {\n            toCss(progress, {\n              transition: `all ${speed}ms ${ease}`,\n              opacity: \"0\"\n            });\n          });\n          setTimeout(() => {\n            progressElements.forEach((progress) => {\n              this.remove(progress);\n              if (this.settings.template === null) {\n                toCss(progress, { transition: \"none\", opacity: \"1\" });\n              }\n            });\n            next();\n          }, speed);\n        }, speed);\n      } else {\n        setTimeout(next, speed);\n      }\n    });\n    return this;\n  }\n  // Start the progress bar\n  static start() {\n    if (!this.status) this.set(0);\n    const work = () => {\n      if (this.isPaused) return;\n      setTimeout(() => {\n        if (!this.status) return;\n        this.trickle();\n        work();\n      }, this.settings.trickleSpeed);\n    };\n    if (this.settings.trickle) work();\n    return this;\n  }\n  // Complete the progress\n  static done(force) {\n    if (!force && !this.status) return this;\n    return this.inc(0.3 + 0.5 * Math.random()).set(1);\n  }\n  // Increment the progress\n  static inc(amount) {\n    if (this.isPaused || this.settings.indeterminate) return this;\n    let n = this.status;\n    if (!n) {\n      return this.start();\n    } else if (n > 1) {\n      return this;\n    } else {\n      if (typeof amount !== \"number\") {\n        if (n >= 0 && n < 0.2) {\n          amount = 0.1;\n        } else if (n >= 0.2 && n < 0.5) {\n          amount = 0.04;\n        } else if (n >= 0.5 && n < 0.8) {\n          amount = 0.02;\n        } else if (n >= 0.8 && n < 0.99) {\n          amount = 5e-3;\n        } else {\n          amount = 0;\n        }\n      }\n      n = clamp(n + amount, 0, 0.994);\n      return this.set(n);\n    }\n  }\n  // Decrement the progress\n  static dec(amount) {\n    if (this.isPaused || this.settings.indeterminate) return this;\n    let n = this.status;\n    if (typeof n !== \"number\") return this;\n    if (typeof amount !== \"number\") {\n      if (n > 0.8) {\n        amount = 0.1;\n      } else if (n > 0.5) {\n        amount = 0.05;\n      } else if (n > 0.2) {\n        amount = 0.02;\n      } else {\n        amount = 0.01;\n      }\n    }\n    n = clamp(n - amount, 0, 0.994);\n    return this.set(n);\n  }\n  // Advance the progress (trickle)\n  static trickle() {\n    if (this.isPaused || this.settings.indeterminate) return this;\n    return this.inc();\n  }\n  // Handle jQuery promises (for compatibility)\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  static promise($promise) {\n    if (!$promise || $promise.state() === \"resolved\") {\n      return this;\n    }\n    let initial = 0, current = 0;\n    if (current === 0) {\n      this.start();\n    }\n    initial++;\n    current++;\n    $promise.always(() => {\n      current--;\n      if (current === 0) {\n        initial = 0;\n        this.done();\n      } else {\n        this.set((initial - current) / initial);\n      }\n    });\n    return this;\n  }\n  /**\n   * Renders the BProgress component.\n   * If a template is provided, it will create a progress element if none exists in the parent.\n   * If the template is null, it relies on the user to insert their own elements marked with the \"bprogress\" class.\n   * When using indeterminate mode with a custom template, the template should include the indeterminate element.\n   */\n  static render(fromStart = false) {\n    const parent = typeof this.settings.parent === \"string\" ? document.querySelector(this.settings.parent) : this.settings.parent;\n    const progressElements = parent ? Array.from(parent.querySelectorAll(\".bprogress\")) : [];\n    if (this.settings.template !== null && progressElements.length === 0) {\n      addClass(document.documentElement, \"bprogress-busy\");\n      const progress = document.createElement(\"div\");\n      addClass(progress, \"bprogress\");\n      progress.innerHTML = this.settings.template;\n      if (parent !== document.body) {\n        addClass(parent, \"bprogress-custom-parent\");\n      }\n      parent.appendChild(progress);\n      progressElements.push(progress);\n    }\n    progressElements.forEach((progress) => {\n      if (this.settings.template === null) {\n        progress.style.display = \"\";\n      }\n      addClass(document.documentElement, \"bprogress-busy\");\n      if (parent !== document.body) {\n        addClass(parent, \"bprogress-custom-parent\");\n      }\n      if (!this.settings.indeterminate) {\n        const bar = progress.querySelector(\n          this.settings.barSelector\n        );\n        const perc = fromStart ? toBarPerc(0, this.settings.direction) : toBarPerc(this.status || 0, this.settings.direction);\n        toCss(\n          bar,\n          this.barPositionCSS({\n            n: this.status || 0,\n            speed: this.settings.speed,\n            ease: this.settings.easing,\n            perc\n          })\n        );\n        const indeterminateElem = progress.querySelector(\n          this.settings.indeterminateSelector\n        );\n        if (indeterminateElem) {\n          indeterminateElem.style.display = \"none\";\n        }\n      } else {\n        const bar = progress.querySelector(\n          this.settings.barSelector\n        );\n        if (bar) {\n          bar.style.display = \"none\";\n        }\n        const indeterminateElem = progress.querySelector(\n          this.settings.indeterminateSelector\n        );\n        if (indeterminateElem) {\n          indeterminateElem.style.display = \"\";\n        }\n      }\n      if (this.settings.template === null) {\n        const spinner = progress.querySelector(\n          this.settings.spinnerSelector\n        );\n        if (spinner) {\n          spinner.style.display = this.settings.showSpinner ? \"block\" : \"none\";\n        }\n      } else {\n        if (!this.settings.showSpinner) {\n          const spinner = progress.querySelector(\n            this.settings.spinnerSelector\n          );\n          if (spinner) removeElement(spinner);\n        }\n      }\n    });\n    return progressElements;\n  }\n  /**\n   * Remove the progress element from the DOM.\n   * If a progress element is provided, only that element is removed;\n   * otherwise, all progress elements and associated classes are removed.\n   * For user-provided templates (when settings.template === null), the element\n   * is hidden instead of being removed.\n   */\n  static remove(progressElement) {\n    if (progressElement) {\n      if (this.settings.template === null) {\n        progressElement.style.display = \"none\";\n      } else {\n        removeElement(progressElement);\n      }\n    } else {\n      removeClass(document.documentElement, \"bprogress-busy\");\n      const parent = typeof this.settings.parent === \"string\" ? document.querySelectorAll(this.settings.parent) : [this.settings.parent];\n      parent.forEach((p) => {\n        removeClass(p, \"bprogress-custom-parent\");\n      });\n      const progresses = document.querySelectorAll(\".bprogress\");\n      progresses.forEach((progress) => {\n        const elem = progress;\n        if (this.settings.template === null) {\n          elem.style.display = \"none\";\n        } else {\n          removeElement(elem);\n        }\n      });\n    }\n  }\n  // Pause the progress\n  static pause() {\n    if (!this.isStarted() || this.settings.indeterminate) return this;\n    this.isPaused = true;\n    return this;\n  }\n  // Resume the progress\n  static resume() {\n    if (!this.isStarted() || this.settings.indeterminate) return this;\n    this.isPaused = false;\n    if (this.settings.trickle) {\n      const work = () => {\n        if (this.isPaused) return;\n        setTimeout(() => {\n          if (!this.status) return;\n          this.trickle();\n          work();\n        }, this.settings.trickleSpeed);\n      };\n      work();\n    }\n    return this;\n  }\n  // Check if BProgress is rendered in the DOM\n  static isRendered() {\n    return document.querySelectorAll(\".bprogress\").length > 0;\n  }\n  // Determine the CSS positioning method to use\n  static getPositioningCSS() {\n    const bodyStyle = document.body.style;\n    const vendorPrefix = \"WebkitTransform\" in bodyStyle ? \"Webkit\" : \"MozTransform\" in bodyStyle ? \"Moz\" : \"msTransform\" in bodyStyle ? \"ms\" : \"OTransform\" in bodyStyle ? \"O\" : \"\";\n    if (`${vendorPrefix}Perspective` in bodyStyle) {\n      return \"translate3d\";\n    } else if (`${vendorPrefix}Transform` in bodyStyle) {\n      return \"translate\";\n    } else {\n      return \"margin\";\n    }\n  }\n  // Queue function for animations\n  static queue(fn) {\n    this.pending.push(fn);\n    if (this.pending.length === 1) this.next();\n  }\n  static next() {\n    const fn = this.pending.shift();\n    if (fn) fn(this.next.bind(this));\n  }\n  static initPositionUsing() {\n    if (this.settings.positionUsing === \"\") {\n      this.settings.positionUsing = this.getPositioningCSS();\n    }\n  }\n  // Compute the CSS for positioning the bar\n  static barPositionCSS({\n    n,\n    speed,\n    ease,\n    perc\n  }) {\n    this.initPositionUsing();\n    let barCSS = {};\n    const computedPerc = perc ?? toBarPerc(n, this.settings.direction);\n    if (this.settings.positionUsing === \"translate3d\") {\n      barCSS = {\n        transform: `translate3d(${computedPerc}%,0,0)`\n      };\n    } else if (this.settings.positionUsing === \"translate\") {\n      barCSS = {\n        transform: `translate(${computedPerc}%,0)`\n      };\n    } else if (this.settings.positionUsing === \"width\") {\n      barCSS = {\n        width: `${this.settings.direction === \"rtl\" ? 100 - computedPerc : computedPerc + 100}%`,\n        ...this.settings.direction === \"rtl\" ? { right: \"0\", left: \"auto\" } : {}\n      };\n    } else if (this.settings.positionUsing === \"margin\") {\n      barCSS = this.settings.direction === \"rtl\" ? { \"margin-left\": `${-computedPerc}%` } : { \"margin-right\": `${-computedPerc}%` };\n    }\n    barCSS.transition = `all ${speed}ms ${ease}`;\n    return barCSS;\n  }\n};\n\n// src/lib/css.ts\nvar css = ({\n  color = \"#29d\",\n  height = \"2px\",\n  spinnerPosition = \"top-right\"\n}) => `\n:root {\n  --bprogress-color: ${color};\n  --bprogress-height: ${height};\n  --bprogress-spinner-size: 18px;\n  --bprogress-spinner-animation-duration: 400ms;\n  --bprogress-spinner-border-size: 2px;\n  --bprogress-box-shadow: 0 0 10px ${color}, 0 0 5px ${color};\n  --bprogress-z-index: 99999;\n  --bprogress-spinner-top: ${spinnerPosition === \"top-right\" || spinnerPosition === \"top-left\" ? \"15px\" : \"auto\"};\n  --bprogress-spinner-bottom: ${spinnerPosition === \"bottom-right\" || spinnerPosition === \"bottom-left\" ? \"15px\" : \"auto\"};\n  --bprogress-spinner-right: ${spinnerPosition === \"top-right\" || spinnerPosition === \"bottom-right\" ? \"15px\" : \"auto\"};\n  --bprogress-spinner-left: ${spinnerPosition === \"top-left\" || spinnerPosition === \"bottom-left\" ? \"15px\" : \"auto\"};\n}\n\n.bprogress {\n  width: 0;\n  height: 0;\n  pointer-events: none;\n  z-index: var(--bprogress-z-index);\n}\n\n.bprogress .bar {\n  background: var(--bprogress-color);\n  position: fixed;\n  z-index: var(--bprogress-z-index);\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: var(--bprogress-height);\n}\n\n/* Fancy blur effect */\n.bprogress .peg {\n  display: block;\n  position: absolute;\n  right: 0;\n  width: 100px;\n  height: 100%;\n  box-shadow: var(--bprogress-box-shadow);\n  opacity: 1.0;\n  transform: rotate(3deg) translate(0px, -4px);\n}\n\n/* Remove these to get rid of the spinner */\n.bprogress .spinner {\n  display: block;\n  position: fixed;\n  z-index: var(--bprogress-z-index);\n  top: var(--bprogress-spinner-top);\n  bottom: var(--bprogress-spinner-bottom);\n  right: var(--bprogress-spinner-right);\n  left: var(--bprogress-spinner-left);\n}\n\n.bprogress .spinner-icon {\n  width: var(--bprogress-spinner-size);\n  height: var(--bprogress-spinner-size);\n  box-sizing: border-box;\n  border: solid var(--bprogress-spinner-border-size) transparent;\n  border-top-color: var(--bprogress-color);\n  border-left-color: var(--bprogress-color);\n  border-radius: 50%;\n  -webkit-animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;\n  animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;\n}\n\n.bprogress-custom-parent {\n  overflow: hidden;\n  position: relative;\n}\n\n.bprogress-custom-parent .bprogress .spinner,\n.bprogress-custom-parent .bprogress .bar {\n  position: absolute;\n}\n\n.bprogress .indeterminate {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: var(--bprogress-height);\n  overflow: hidden;\n}\n\n.bprogress .indeterminate .inc,\n.bprogress .indeterminate .dec {\n  position: absolute;\n  top: 0;\n  height: 100%;\n  background-color: var(--bprogress-color);\n}\n\n.bprogress .indeterminate .inc {\n  animation: bprogress-indeterminate-increase 2s infinite;\n}\n\n.bprogress .indeterminate .dec {\n  animation: bprogress-indeterminate-decrease 2s 0.5s infinite;\n}\n\n@-webkit-keyframes bprogress-spinner {\n  0%   { -webkit-transform: rotate(0deg); transform: rotate(0deg); }\n  100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}\n\n@keyframes bprogress-spinner {\n  0%   { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes bprogress-indeterminate-increase {\n  from { left: -5%; width: 5%; }\n  to { left: 130%; width: 100%; }\n}\n\n@keyframes bprogress-indeterminate-decrease {\n  from { left: -80%; width: 80%; }\n  to { left: 110%; width: 10%; }\n}\n`;\n\n// src/lib/same-url.ts\nfunction isSameURL(target, current) {\n  const cleanTarget = target.protocol + \"//\" + target.host + target.pathname + target.search;\n  const cleanCurrent = current.protocol + \"//\" + current.host + current.pathname + current.search;\n  return cleanTarget === cleanCurrent;\n}\nfunction isSameURLWithoutSearch(target, current) {\n  const cleanTarget = target.protocol + \"//\" + target.host + target.pathname;\n  const cleanCurrent = current.protocol + \"//\" + current.host + current.pathname;\n  return cleanTarget === cleanCurrent;\n}\n\n// src/lib/get-anchor-property.ts\nfunction parsePath(path) {\n  const hashIndex = path.indexOf(\"#\");\n  const queryIndex = path.indexOf(\"?\");\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex);\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : void 0) : \"\",\n      hash: hashIndex > -1 ? path.slice(hashIndex) : \"\"\n    };\n  }\n  return { pathname: path, query: \"\", hash: \"\" };\n}\nfunction addPathPrefix(path, prefix) {\n  if (!path.startsWith(\"/\") || !prefix) {\n    return path;\n  }\n  const { pathname, query, hash } = parsePath(path);\n  return `${prefix}${pathname}${query}${hash}`;\n}\nfunction getAnchorProperty(a, key) {\n  if (typeof key === \"string\" && key === \"data-disable-progress\") {\n    const dataKey = key.substring(5).replace(/-([a-z])/g, (_, c) => c.toUpperCase());\n    return a.dataset[dataKey];\n  }\n  const prop = a[key];\n  if (prop instanceof SVGAnimatedString) {\n    const value = prop.baseVal;\n    if (key === \"href\") {\n      return addPathPrefix(value, location.origin);\n    }\n    return value;\n  }\n  return prop;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.js\n");

/***/ })

};
;