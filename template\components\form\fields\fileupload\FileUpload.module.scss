@use "sass:color";
@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.inputTypeFile {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 1.6rem 0 0;
  overflow: hidden;
  font-size: 1.4rem;
  line-height: 130%;
  color: $color-neutral-500;
  appearance: none;
  background-color: $color-white;
  background-color: white;
  border: 1px solid $color-neutral-500;
  border-radius: 4px;

  @include breakpoint(large up) {
    font-size: 1.6rem;
  }

  &::placeholder {
    color: $color-neutral-500;
  }

  &::file-selector-button {
    padding: 1.2rem 2.4rem;
    margin-right: 1.6rem;
    font-size: 1.4rem;
    font-weight: 700;
    color: white;
    cursor: pointer;
    background-color: $color-primary-500;
    border: none;
    border-right: 1px solid $color-neutral-500;

    @include breakpoint(large up) {
      font-size: 1.6rem;
    }
  }
}
