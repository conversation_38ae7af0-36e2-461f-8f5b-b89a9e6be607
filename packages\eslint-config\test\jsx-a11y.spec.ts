import { ESLint } from "eslint";
import { fileURLToPath } from "node:url";
import { expect, test } from "vitest";

const root = fileURLToPath(new URL("../", import.meta.url));

test("return an a11y error if a link has no href", async () => {
  const code = `
    export default function Button() {
      return <a>Click me!</a>;
    }
  `;

  const eslint = new ESLint({ cwd: root });
  const result = await eslint.lintText(code, { filePath: "file.tsx" });

  // Here we expect the linter to return an error
  // because the `<a>` tag is missing an `href` attribute.
  expect(result.length).toBe(1);

  expect(result[0].messages).toEqual(
    expect.arrayContaining([expect.objectContaining({ ruleId: "jsx-a11y/anchor-is-valid" })])
  );
});
