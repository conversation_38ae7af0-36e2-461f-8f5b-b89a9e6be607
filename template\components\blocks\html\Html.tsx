import type { HtmlBlock } from "@/generated/graphql/graphql";

type HtmlProps = Partial<Omit<HtmlBlock, "__typename" | "innerBlocks">>;

export default function Html({ html }: HtmlProps) {
  if (!html) {
    return;
  }

  // eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- This content is provided by the contributor
  return <div className="block-html contained" dangerouslySetInnerHTML={{ __html: html }} />;
}
