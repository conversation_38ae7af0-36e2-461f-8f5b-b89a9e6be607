import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import HeadingAgenda from "@/components/ui/heading/HeadingAgenda";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";
import SidebarContent from "./SidebarContent";
import styles from "./SingleEventPage.module.scss";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const EVENT_QUERY = graphql(`
  query GetEvent($url: URL!) {
    route(url: $url) {
      ... on Event {
        id
        title
        status
        leadText
        structuredContent
        endDate
        startDate
        publicationDate
        modifiedDate
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        recurrenceSummary
        accessibility {
          hearingImpairment
          intellectualImpairment
          mentalImpairment
          reducedMobility
          signLanguageReception
          strollers
          visualImpairment
        }
        audience
        categories {
          relativeUrl
          title
          description
          parent {
            __typename
          }
        }
        breadcrumbs {
          items {
            title
            url
            siblings {
              title
              url
            }
          }
        }
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: EVENT_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "Event");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: EVENT_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "all",
  });

  assert.ok(data.route?.__typename === "Event");

  const {
    id,
    title,
    leadText,
    categories,
    pager,
    breadcrumbs,
    startDate,
    endDate,
    images,
    modifiedDate,
    publicationDate,
    structuredContent,
  } = data.route;

  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");

  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  const hasStructuredContent = structuredContent.length > 0;

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <HeadingAgenda
        surtitle={surtitle}
        title={title ?? "Sans titre"}
        leadText={leadText}
        tags={tags}
        tagsRoleDescription="Thématiques"
        imageSrc={images?.ratio_3x2?.url}
        startDate={startDate}
        endDate={endDate}
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />

      <div className="layout-2columns-right">
        <div className="column main">
          {hasStructuredContent ? (
            <BlockRenderer structuredContent={structuredContent} />
          ) : (
            <div className={styles.aside}>
              <SidebarContent eventData={data.route} eventId={id} />
            </div>
          )}
          {socialShare && <SocialShare />}
        </div>

        {hasStructuredContent && (
          <aside className={clsx("column sidebar", styles.sidebar)}>
            <SidebarContent eventData={data.route} eventId={id} />
          </aside>
        )}
      </div>

      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
