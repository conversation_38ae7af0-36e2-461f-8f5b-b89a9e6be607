import { Publication } from "@/generated/graphql/graphql";
import PublicationsItem from "./PublicationsItem";
import styles from "./PublicationsList.module.scss";

interface PublicationListProps {
  items: Publication[];
}

export default function PublicationsList({ items }: PublicationListProps) {
  return (
    <ul className={styles.publicationsList}>
      {items.map((publication, index) => (
        <li key={index}>
          <PublicationsItem publication={publication} />
        </li>
      ))}
    </ul>
  );
}
