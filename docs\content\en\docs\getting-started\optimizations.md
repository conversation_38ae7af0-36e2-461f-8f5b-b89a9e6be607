---
title: "Optimizations"
weight: 1
---

Here are listed some ideas to keep in mind to make some optimizations.

You can learn more about optimizing a Next.js app on the [Next.js documentation](https://nextjs.org/docs/pages/building-your-application/optimizing).

# Code Splitting

Sometimes, when constructing a component, splitting some parts of the components is necessary.

## Use case: HeroBlock

The `HeroBlock` is a content block that can either render:

- A single video
- A single image
- A carousel of images

Following the code splitting principle, the final component looks like this:

```tsx
const HeroImageCarousel = dynamic(() => import("./HeroImageCarousel"));
const HeroImage = dynamic(() => import("./HeroImage"));
const HeroVideo = dynamic(() => import("./HeroVideo"));

interface HeroProps {
  variant: "CAROUSEL" | "IMAGE" | "VIDEO";
}

export default function Hero({ variant, ...props }: HeroProps) {
  switch (variant) {
    case "CAROUSEL":
      return <HeroImageCarousel {...props} />;
    case "IMAGE":
      return <HeroImage {...props} />;
    case "VIDEO":
      return <HeroVideo {...props} />;
  }
}
```

The `HeroImageCarousel` component, contains js and styles from `Swiper`.

With this method, the whole `Swiper` bundle is not sent to the browser if the `HeroBlock` is not a carousel.

## CSS Modules

This also works for CSS modules, sometimes the whole stylesheet is not necessary, if parts of the components should not be sent to the browser.

{{% hint info %}}
Note that the whole stylesheet of the component (example: `Button.module.scss`) will be integrated in the bundle, even the classes that have no match.
{{% /hint %}}
