"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import type { HeroBlock } from "@/generated/graphql/graphql";
import A11y from "@/lib/swiper/modules/a11y";
import clsx from "clsx";
import "css.escape";
import Image from "next/image";
import { useCallback, useId, useRef, useState } from "react";
import SwiperCore from "swiper";
import "swiper/css";
import "swiper/css/a11y";
import "swiper/css/effect-fade";
import "swiper/css/navigation";
import { Autoplay, EffectFade, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import HeroContent from "./HeroContent";
import styles from "./HeroImageCarousel.module.scss";
import PauseIcon from "./pause.svg";
import PlayIcon from "./play.svg";

/**
 * Render the Hero block as a carousel of images.
 */
export default function HeroImageCarousel({ slides }: Pick<HeroBlock, "slides">) {
  const [isPlaying, setIsPlaying] = useState(true);
  const [activeIndex, setActiveIndex] = useState(0);
  const swiperRef = useRef<SwiperCore | null>(null);
  const titleId = useId();
  const carouselId = useId();
  const paginationId = useId();

  const togglePlayPause = useCallback(() => {
    if (!swiperRef.current) return;

    if (isPlaying) {
      swiperRef.current.autoplay.stop();
    } else {
      swiperRef.current.autoplay.start();
    }

    setIsPlaying((prev) => !prev);
  }, [isPlaying]);

  return (
    <section
      id={carouselId}
      className={clsx("block-hero", styles.heroCarousel)}
      aria-labelledby={titleId}
      aria-roledescription="diaporama de type onglets"
    >
      <h2 className="sr-only" id={titleId}>
        Articles mis en avant
      </h2>

      <div className={styles.controls} role="group" aria-label="Contrôle du diaporama">
        <Tooltip content={isPlaying ? "Arrêter le diaporama" : "Lancer le diaporama"}>
          <button
            type="button"
            onClick={togglePlayPause}
            className={styles.playButton}
            aria-label={isPlaying ? "Arrêter le diaporama" : "Lancer le diaporama"}
          >
            {isPlaying ? <PauseIcon aria-hidden="true" /> : <PlayIcon aria-hidden="true" />}
          </button>
        </Tooltip>

        <div id={paginationId} role="tablist" className={styles.pagination} aria-labelledby={carouselId}></div>
      </div>

      <Swiper
        autoHeight={true}
        modules={[Autoplay, A11y, EffectFade, Pagination]}
        slidesPerView={1}
        loop
        autoplay={{ delay: 4000 }}
        effect="fade"
        fadeEffect={{ crossFade: true }}
        speed={1000}
        onSwiper={(swiper) => {
          swiperRef.current = swiper;
        }}
        pagination={{
          el: "#" + CSS.escape(paginationId),
          clickable: true,
          bulletClass: styles.bullet,
          bulletActiveClass: styles.active,
          renderBullet(index, className) {
            return `<button role="tab" class="${className}" aria-controls="${carouselId + "-slide-" + index}" aria-label="Image ${index + 1}"></button>`;
          },
        }}
        a11y={{
          slideRole: "tabpanel",
          inertInvisibleSlides: true,
          slideLabelMessage: "{{index}} sur {{slidesLength}}",
          prevSlideMessage: "Image précédente",
          nextSlideMessage: "Image suivante",
          lastSlideMessage: "Dérnière image",
          firstSlideMessage: "Première image",
        }}
        onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
      >
        {slides.map(({ imageSrc, title, leadText, link }, index) => (
          <SwiperSlide key={index} id={`${carouselId}-slide-${index}`} tabIndex={index === activeIndex ? 0 : -1}>
            <HeroContent title={title} surtitle={leadText} url={link} headingLevel={3} />

            {imageSrc && (
              <div className={styles.imageWrapper}>
                <Image src={imageSrc} alt="" fill priority className={styles.image} />
              </div>
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </section>
  );
}
