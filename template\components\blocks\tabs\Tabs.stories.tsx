import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Tab from "../tab/Tab";
import Tabs from "./Tabs";

const meta: Meta<typeof Tabs> = {
  title: "Components/Tabs",
  component: Tabs,
  tags: ["autodocs"],
  argTypes: {
    orientation: {
      control: { type: "radio" },
      options: ["horizontal", "vertical"],
    },
  },
};

export default meta;

type Story = StoryObj<typeof Tabs>;

const tabContent = (n: number) => {
  return (
    <Tab key={`tab-${n}`} id={String(n)} title={`Tab ${n}`}>
      <h3>Tab content {n}</h3>
      <p>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Maiores neque earum laborum blanditiis praesentium
        tempora molestiae ipsa esse labore reprehenderit similique modi aut nam impedit eligendi, corrupti itaque
        quibusdam. Eos!
      </p>
    </Tab>
  );
};

export const Horizontal: Story = {
  render: (args) => <Tabs {...args}>{Array.from({ length: 3 }).map((_, index) => tabContent(index + 1))}</Tabs>,
};

export const HorizontalMany: Story = {
  render: (args) => <Tabs {...args}>{Array.from({ length: 7 }).map((_, index) => tabContent(index + 1))}</Tabs>,
};

export const Vertical: Story = {
  args: {
    orientation: "vertical",
  },
  render: (args) => <Tabs {...args}>{Array.from({ length: 3 }).map((_, index) => tabContent(index + 1))}</Tabs>,
};
