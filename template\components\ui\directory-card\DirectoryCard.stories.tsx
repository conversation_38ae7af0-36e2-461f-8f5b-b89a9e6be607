import { DirectoryAccessibilityStatus, DirectoryViewMode, PhoneDeviceType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import DirectoryCard from "./DirectoryCard";

const meta: Meta<typeof DirectoryCard> = {
  title: "Components/DirectoryCard",
  component: DirectoryCard,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ containerType: "inline-size" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof DirectoryCard>;

export const Location: Story = {
  args: {
    directory: {
      viewMode: DirectoryViewMode.LOCATION,
      title:
        "120 caractères ullam mauris egestas quamac urna eu felis dapibus condimentum sit amet a augue ed non neque elit sd auris",
      images: {
        ratio_3x2: {
          url: "/assets/placeholder-contact.png",
          alt: "Medical Center",
          height: 400,
          width: 600,
        },
      },
      phones: [
        {
          deviceType: PhoneDeviceType.LANDLINE,
          number: "**********",
        },
        {
          deviceType: PhoneDeviceType.MOBILE,
          number: "**********",
        },
      ],
      website: "#",
      email: "<EMAIL>",
      openingHours: ["Lundi 15h-18h", "Mardi 9h30-12h30", "Jeudi 9h30-12h30 / 14h30-18h30"],
      categories: [
        {
          title: "Thématique",
          relativeUrl: "#",
        },
      ],
      location: {
        address: {
          street: ["123 Main Street"],
          zip: "75000",
          city: "Paris",
        },
      },
      url: "#",
      accessibility: {
        hearingImpairment: DirectoryAccessibilityStatus.SUPPORTED,
        strollers: DirectoryAccessibilityStatus.NOT_SUPPORTED,
      },
    },
  },
};

export const Person: Story = {
  args: {
    directory: {
      viewMode: DirectoryViewMode.PERSON,
      title:
        "120 caractères ullam mauris egestas quamac urna eu felis dapibus condimentum sit amet a augue ed non neque elit sd auris",
      images: {
        ratio_1x1: {
          url: "/assets/placeholder-176x176.png",
          alt: "Medical Center",
          height: 176,
          width: 176,
        },
      },
      phones: [
        {
          deviceType: PhoneDeviceType.LANDLINE,
          number: "**********",
        },
        {
          deviceType: PhoneDeviceType.MOBILE,
          number: "**********",
        },
      ],
      website: "#",
      email: "<EMAIL>",
      openingHours: ["Lundi 15h-18h", "Mardi 9h30-12h30", "Jeudi 9h30-12h30 / 14h30-18h30"],
      categories: [
        {
          title: "Thématique",
          relativeUrl: "#",
        },
      ],
      location: {
        address: {
          street: ["123 Main Street"],
          zip: "75000",
          city: "Paris",
        },
      },
      url: "#",
      offices: [
        "Directrice de la communication institutionnelle",
        "Responsable des relations médias",
        "Porte-parole officielle",
      ],
    },
  },
};
