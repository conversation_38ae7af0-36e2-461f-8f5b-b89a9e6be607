@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.socialMediaWidget {
  @extend %text-wrap;
}

.title {
  margin-bottom: 24px;
  font-size: 1.8rem;
  font-weight: $fw-bold;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 3.2rem;
  }

  i {
    margin-right: 6px;
    font-size: 2.4rem;
    color: $color-secondary-500;
  }
}

.socials {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
}
