---
title: "Font Icon"
weight: 1
---

## Font Awesome

By default, [FontAwesome](https://code.stratis.fr/citeopolis-5/fontawesome) should be setup in your project.

It is a special build, created by the design team and carried out through all the projects.

## Usage

The convention for font icons is to use the `<i>` element.

It is also recommended to use the short version of the font family class. Like:

- `far`
- `fas`
- `fab`

Example:

```tsx
export default function Button() {
  return (
    <button>
      <i className="far fa-user-circle" aria-hidden="true"></i>
      Push me!
    </button>
  );
}
```
