import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Button from "@/components/ui/button/Button";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { EventFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, createPeriodFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import Link from "next/link";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

const EVENT_LIST_QUERY = graphql(`
  query GetEventListConfig($url: URL) {
    route(url: $url) {
      ... on EventList {
        defaultPageSize
        leadText
        url
        title
        rssUrl
        proposeUrl
        filters {
          __typename
          attribute
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const EVENT_SEARCH_QUERY = graphql(`
  query GetEventList(
    $filter: EventFilterInput
    $period: EventPeriodFilterInput
    $sort: EventSortInput
    $pageSize: Int
    $currentPage: Int
  ) {
    eventSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        endDate
        id
        leadText
        startDate
        title
        url
        location {
          title
          address {
            city
          }
        }
        categories {
          title
        }
        periods(filter: $period) {
          items {
            startDate
          }
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
          placeholder
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: EVENT_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "EventList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<FilterInputParams<EventFilterInput> & { p?: string }>;
}) {
  const { data: eventList } = await query({
    query: EVENT_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(eventList.route?.__typename === "EventList");

  const {
    title,
    leadText,
    breadcrumbs,
    filters: defaultFilters,
    rssUrl,
    proposeUrl,
    url,
    defaultPageSize,
  } = eventList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<EventFilterInput>({
    params,
    filters: defaultFilters,
  });
  const filterPeriodInput = createPeriodFilterInput(filterInput);

  const { data: eventSearch } = await query({
    query: EVENT_SEARCH_QUERY,
    errorPolicy: "all",
    variables: {
      pageSize,
      currentPage,
      period: filterPeriodInput,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = eventSearch?.eventSearch ?? {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Événements" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les événements" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />

            {proposeUrl && (
              <Button asChild variant="outlined" startIcon="far fa-lightbulb-on">
                <Link href={proposeUrl}>Suggérer un événement</Link>
              </Button>
            )}
          </div>

          {items.length > 0 && (
            <ol className={styles.grid}>
              {items.map((event) => (
                <li key={event.id}>
                  <Item event={event} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
