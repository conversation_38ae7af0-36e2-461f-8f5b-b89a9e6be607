@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.captcha {
  width: 304px;
  max-width: 100%;
  margin-left: auto;

  :global {
    .altcha {
      max-width: none;
    }

    #google-recaptcha-checkbox-container > div {
      display: flex;
      justify-content: flex-end;
    }

    iframe {
      cursor: pointer;
    }
  }
}

.verified {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 1.6rem;
  line-height: 120%;
  color: $color-positive-500;
  background-color: $color-positive-300;
  border-color: $color-positive-500;
  border-radius: 5px;
}

.icon {
  font-size: 2.5rem;
}
