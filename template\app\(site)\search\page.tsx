import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import SearchForm from "@/components/ui/search/SearchForm";
import Sorter from "@/components/ui/sorter/Sorter";
import { graphql } from "@/generated/graphql";
import { GlobalSearchFilterInput, Searchable, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

// TODO UPDATE FILTER QUERY
const SEARCH_PAGE_QUERY = graphql(`
  query GetSearchConfig($url: URL) {
    route(url: $url) {
      ... on GlobalSearch {
        __typename
        defaultPageSize
        breadcrumbs {
          items {
            title
            url
          }
        }
        filters {
          attribute
          label
          ... on SelectFilter {
            attribute
            label
            options {
              label
              value
              count
              children {
                label
                value
                count
                children {
                  label
                  value
                  count
                  children {
                    label
                    value
                    count
                  }
                }
              }
            }
            placeholder
          }
          ... on TextFilter {
            label
            attribute
          }
        }
        searchFilter {
          attribute
        }
        title
        url
      }
    }
  }
`);

const SEARCH_RESULTS_QUERY = graphql(`
  query GetSearchList(
    $filter: GlobalSearchFilterInput
    $sort: GlobalSearchSortInput
    $pageSize: Int
    $currentPage: Int
  ) {
    search(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      totalCount
      items {
        __typename
        ... on Page {
          title
          surtitle
          leadText
          url
          modifiedDate
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on News {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on Event {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on Publication {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on Directory {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on Album {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
        ... on Resolution {
          title
          leadText
          url
          modifiedDate
          categories {
            title
          }
          images {
            ratio_3x2 {
              alt
              height
              url
              width
            }
          }
        }
      }
    }
  }
`);

// TODO: Add metadata
export default async function Page({
  searchParams,
}: {
  searchParams: Promise<
    Record<string, string | string[] | null> & FilterInputParams<GlobalSearchFilterInput> & { p?: string }
  >;
}) {
  const { data: searchPage } = await query({
    query: SEARCH_PAGE_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(searchPage.route?.__typename === "GlobalSearch");

  const { title, filters, breadcrumbs, url, defaultPageSize } = searchPage?.route;

  const { p = "", sort, ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<GlobalSearchFilterInput>({
    params,
    filters,
  });

  // TODO: Get these values from the list configuration
  const sortChoices = [
    { label: "Par pertinence", value: "", defaultSelected: true },
    { label: "Par titre", value: "title" },
    { label: "Par date de publication", value: "publicationDate" },
  ];

  // FIXME: We don't know in advance what input the filter requires, might break
  const sortInput: Record<string, string> = sortChoices.some((choice) => choice.value === sort && sort)
    ? {
        [sort as string]: SortDirection.DESC,
      }
    : {};

  const { data: searchResults } = await query({
    query: SEARCH_RESULTS_QUERY,
    variables: {
      pageSize,
      currentPage,
      filter: filterInput,
      sort: sortInput,
    },
  });

  const {
    items,
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
  } = searchResults?.search || {};

  // FIXME: Get `searchFilter` from the list config
  const searchValue = params.s as string;

  // List of words to highlight
  const searchWords = params.s ? (typeof params.s === "string" ? [params.s] : params.s) : [];

  // TODO: Create SearchHeading instead of passing the search form into the Heading
  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items || []} />
      <Heading surtitle="Recherche" title={title}>
        <SearchForm searchUrl={url} searchValue={searchValue} />
      </Heading>

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les résultats" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />
            <Sorter size="sm" choices={sortChoices} />
          </div>

          <FilterSelection filters={filters} url={url} />

          {items && items.length > 0 && (
            <ol className={styles.flex}>
              {items.map((detail, index) => (
                <li key={index}>
                  <Item detail={detail as Searchable} searchWords={searchWords} />
                </li>
              ))}
            </ol>
          )}
          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
