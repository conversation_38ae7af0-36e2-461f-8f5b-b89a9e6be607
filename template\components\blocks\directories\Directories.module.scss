@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.directories {
  width: 100%;
  container: block-directories / inline-size;
}

.title {
  margin-bottom: 12px;
  font-size: 2rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-primary-600;

  @include breakpoint(medium up) {
    font-size: 2.4rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 16px;
    font-size: 4rem;
  }
}

.list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-block: 24px;

  @include breakpoint(large up) {
    gap: 24px;
    padding-block: 32px;
  }

  :global(.block-columns) & {
    padding-block: 0;
  }
}
