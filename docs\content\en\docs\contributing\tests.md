---
title: Tests
weight: 5
---

# Tests

## Naming convention

All the tests are made with [vitest](https://vitest.dev/), and follow the following convention:

### Unit

A unit test (related to a single file or component), must be named **exactly** the same but with `.test` before the extension.

Example:

```
✅

Button.tsx
Button.test.tsx

paginate.tsx
paginate.test.tsx
```

### Integration / E2E

An integration test (related to a behavior, or connected parts), must have a readable name with `.spec` before the extension.

Example:

```
✅

create-app.spec.tsx
```
