/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/brands.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.fab,
.fa-brands {
  font-weight: 400;
}

:root, :host {
  --fa-style-family-brands: 'Font Awesome 6 Brands';
  --fa-font-brands: normal 400 1em/1 'Font Awesome 6 Brands'; }

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url(/_next/static/media/fa-brands-400.dd7d9b55.woff2) format("woff2"), url(/_next/static/media/fa-brands-400.e6c47892.ttf) format("truetype"); }

.fa-android { --fa: "\f17b"; --fa--fa: "\f17b\f17b"; }
.fa-app-store { --fa: "\f36f"; --fa--fa: "\f36f\f36f"; }
.fa-app-store-ios { --fa: "\f370"; --fa--fa: "\f370\f370"; }
.fa-apple { --fa: "\f179"; --fa--fa: "\f179\f179"; }
.fa-apple-pay { --fa: "\f415"; --fa--fa: "\f415\f415"; }
.fa-bluesky { --fa: "\e671"; --fa--fa: "\e671\e671"; }
.fa-cc-amazon-pay { --fa: "\f42d"; --fa--fa: "\f42d\f42d"; }
.fa-cc-apple-pay { --fa: "\f416"; --fa--fa: "\f416\f416"; }
.fa-cc-paypal { --fa: "\f1f4"; --fa--fa: "\f1f4\f1f4"; }
.fa-dailymotion { --fa: "\e052"; --fa--fa: "\e052\e052"; }
.fa-facebook-f { --fa: "\f39e"; --fa--fa: "\f39e\f39e"; }
.fa-facebook-messenger { --fa: "\f39f"; --fa--fa: "\f39f\f39f"; }
.fa-facebook-square { --fa: "\f082"; --fa--fa: "\f082\f082"; }
.fa-figma { --fa: "\f799"; --fa--fa: "\f799\f799"; }
.fa-flickr { --fa: "\f16e"; --fa--fa: "\f16e\f16e"; }
.fa-google { --fa: "\f1a0"; --fa--fa: "\f1a0\f1a0"; }
.fa-google-pay { --fa: "\e079"; --fa--fa: "\e079\e079"; }
.fa-google-play { --fa: "\f3ab"; --fa--fa: "\f3ab\f3ab"; }
.fa-instagram { --fa: "\f16d"; --fa--fa: "\f16d\f16d"; }
.fa-instagram-square { --fa: "\e055"; --fa--fa: "\e055\e055"; }
.fa-linkedin { --fa: "\f08c"; --fa--fa: "\f08c\f08c"; }
.fa-linkedin-in { --fa: "\f0e1"; --fa--fa: "\f0e1\f0e1"; }
.fa-mastodon { --fa: "\f4f6"; --fa--fa: "\f4f6\f4f6"; }
.fa-paypal { --fa: "\f1ed"; --fa--fa: "\f1ed\f1ed"; }
.fa-pinterest-p { --fa: "\f231"; --fa--fa: "\f231\f231"; }
.fa-pinterest-square { --fa: "\f0d3"; --fa--fa: "\f0d3\f0d3"; }
.fa-snapchat { --fa: "\f2ab"; --fa--fa: "\f2ab\f2ab"; }
.fa-snapchat-ghost { --fa: "\f2ab"; --fa--fa: "\f2ab\f2ab"; }
.fa-snapchat-square { --fa: "\f2ad"; --fa--fa: "\f2ad\f2ad"; }
.fa-square-bluesky { --fa: "\e6a3"; --fa--fa: "\e6a3\e6a3"; }
.fa-square-facebook { --fa: "\f082"; --fa--fa: "\f082\f082"; }
.fa-square-instagram { --fa: "\e055"; --fa--fa: "\e055\e055"; }
.fa-square-pinterest { --fa: "\f0d3"; --fa--fa: "\f0d3\f0d3"; }
.fa-square-snapchat { --fa: "\f2ad"; --fa--fa: "\f2ad\f2ad"; }
.fa-square-threads { --fa: "\e619"; --fa--fa: "\e619\e619"; }
.fa-square-viadeo { --fa: "\f2aa"; --fa--fa: "\f2aa\f2aa"; }
.fa-square-vimeo { --fa: "\f194"; --fa--fa: "\f194\f194"; }
.fa-square-whatsapp { --fa: "\f40c"; --fa--fa: "\f40c\f40c"; }
.fa-square-x-twitter { --fa: "\e61a"; --fa--fa: "\e61a\e61a"; }
.fa-square-youtube { --fa: "\f431"; --fa--fa: "\f431\f431"; }
.fa-threads { --fa: "\e618"; --fa--fa: "\e618\e618"; }
.fa-tiktok { --fa: "\e07b"; --fa--fa: "\e07b\e07b"; }
.fa-twitch { --fa: "\f1e8"; --fa--fa: "\f1e8\f1e8"; }
.fa-viadeo { --fa: "\f2a9"; --fa--fa: "\f2a9\f2a9"; }
.fa-viadeo-square { --fa: "\f2aa"; --fa--fa: "\f2aa\f2aa"; }
.fa-vimeo-square { --fa: "\f194"; --fa--fa: "\f194\f194"; }
.fa-vimeo-v { --fa: "\f27d"; --fa--fa: "\f27d\f27d"; }
.fa-whatsapp { --fa: "\f232"; --fa--fa: "\f232\f232"; }
.fa-whatsapp-square { --fa: "\f40c"; --fa--fa: "\f40c\f40c"; }
.fa-windows { --fa: "\f17a"; --fa--fa: "\f17a\f17a"; }
.fa-x-twitter { --fa: "\e61b"; --fa--fa: "\e61b\e61b"; }
.fa-youtube { --fa: "\f167"; --fa--fa: "\f167\f167"; }
.fa-youtube-square { --fa: "\f431"; --fa--fa: "\f431\f431"; }

/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!../node_modules/.pnpm/@bprogress+core@1.3.4/node_modules/@bprogress/core/dist/index.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Global variables */
:root {
  --bprogress-color: #29d;
  --bprogress-height: 2px;
  --bprogress-spinner-size: 18px;
  --bprogress-spinner-animation-duration: 400ms;
  --bprogress-spinner-border-size: 2px;
  --bprogress-box-shadow: 0 0 10px var(--bprogress-color), 0 0 5px var(--bprogress-color);
  --bprogress-z-index: 99999;
  --bprogress-spinner-top: 15px;
  --bprogress-spinner-bottom: auto;
  --bprogress-spinner-right: 15px;
  --bprogress-spinner-left: auto;
}

.bprogress {
  width: 0;
  height: 0;
  pointer-events: none;
  z-index: var(--bprogress-z-index);
}

.bprogress .bar {
  background: var(--bprogress-color);
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
}

/* Fancy blur effect */
.bprogress .peg {
  display: block;
  position: absolute;
  right: 0;
  width: 100px;
  height: 100%;
  box-shadow: var(--bprogress-box-shadow);
  opacity: 1.0;
  transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
.bprogress .spinner {
  display: block;
  position: fixed;
  z-index: var(--bprogress-z-index);
  top: var(--bprogress-spinner-top);
  bottom: var(--bprogress-spinner-bottom);
  right: var(--bprogress-spinner-right);
  left: var(--bprogress-spinner-left);
}

.bprogress .spinner-icon {
  width: var(--bprogress-spinner-size);
  height: var(--bprogress-spinner-size);
  box-sizing: border-box;
  border: solid var(--bprogress-spinner-border-size) transparent;
  border-top-color: var(--bprogress-color);
  border-left-color: var(--bprogress-color);
  border-radius: 50%;
  animation: bprogress-spinner var(--bprogress-spinner-animation-duration) linear infinite;
}

/* Custom parent styles */
.bprogress-custom-parent {
  overflow: hidden;
  position: relative;
}

.bprogress-custom-parent .bprogress .spinner,
.bprogress-custom-parent .bprogress .bar {
  position: absolute;
}

/* Styles for indeterminate progress mode */
.bprogress .indeterminate {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: var(--bprogress-height);
  overflow: hidden;
}

.bprogress .indeterminate .inc,
.bprogress .indeterminate .dec {
  position: absolute;
  top: 0;
  height: 100%;
  background-color: var(--bprogress-color);
}

.bprogress .indeterminate .inc {
  animation: bprogress-indeterminate-increase 2s infinite;
}

.bprogress .indeterminate .dec {
  animation: bprogress-indeterminate-decrease 2s 0.5s infinite;
}

@keyframes bprogress-spinner {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bprogress-indeterminate-increase {
  from { left: -5%; width: 5%; }
  to { left: 130%; width: 100%; }
}

@keyframes bprogress-indeterminate-decrease {
  from { left: -80%; width: 80%; }
  to { left: 110%; width: 10%; }
}

/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/admin-bar/AdminBar.module.scss ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.AdminBar_adminBar__SWU9T {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 900;
  display: flex;
  gap: 24px;
  align-items: center;
  width: 100%;
  padding-inline: 12px;
  font-size: 1.4rem;
  color: #fffffe;
  background-color: #363636;
}
@media screen and (min-width: 1302px) {
  .AdminBar_adminBar__SWU9T {
    gap: 82px;
    padding-inline: 32px;
  }
}

.AdminBar_logo__9MXbm {
  flex-shrink: 0;
  max-height: 20px;
}
@media screen and (max-width: 767px) {
  .AdminBar_logo__9MXbm {
    display: none;
  }
}

.AdminBar_menu__EdhEv {
  display: flex;
  flex-wrap: wrap;
}

.AdminBar_menuitem__G5QJr {
  display: flex;
  gap: 8px;
  align-items: center;
  min-height: 40px;
  padding-inline: 16px;
  font-size: 1.4rem;
  font-weight: 700;
  color: #c5c5c5;
  cursor: pointer;
  transition: background-color 100ms ease, color 100ms ease;
}
.AdminBar_menuitem__G5QJr img {
  width: 100%;
  max-width: 18px;
  max-height: 18px;
}
.AdminBar_menuitem__G5QJr i {
  font-size: 1.8rem;
  color: #707070;
}
@media screen and (max-width: 767px) {
  .AdminBar_menuitem__G5QJr i {
    color: #c5c5c5;
  }
}
.AdminBar_menuitem__G5QJr:hover {
  color: #000;
  background-color: #efefef;
}
.AdminBar_menuitem__G5QJr:hover i {
  color: inherit;
}

@media screen and (max-width: 767px) {
  .AdminBar_text__tXmpU {
    display: none;
  }
}
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/fontawesome.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.fa {
  font-family: var(--fa-style-family, "Font Awesome 6 Pro");
  font-weight: var(--fa-style, 900); }

.fas,
.fass,
.far,
.fasr,
.fal,
.fasl,
.fat,
.fast,
.fad,
.fadr,
.fadl,
.fadt,
.fasds,
.fasdr,
.fasdl,
.fasdt,
.fab,
.fa-solid,
.fa-regular,
.fa-light,
.fa-thin,
.fa-brands,
.fa-classic,
.fa-duotone,
.fa-sharp,
.fa-sharp-duotone,
.fa {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: var(--fa-display, inline-block);
  font-style: normal;
  font-feature-settings: normal;
  font-variant: normal;
  line-height: 1;
  text-rendering: auto; }

.fas::before,
.fass::before,
.far::before,
.fasr::before,
.fal::before,
.fasl::before,
.fat::before,
.fast::before,
.fad::before,
.fadr::before,
.fadl::before,
.fadt::before,
.fasds::before,
.fasdr::before,
.fasdl::before,
.fasdt::before,
.fab::before,
.fa-solid::before,
.fa-regular::before,
.fa-light::before,
.fa-thin::before,
.fa-brands::before,
.fa-classic::before,
.fa-duotone::before,
.fa-sharp::before,
.fa-sharp-duotone::before,
.fa::before {
  content: var(--fa); }

.fad::after,
.fa-duotone.fa-solid::after,
.fa-duotone::after,
.fadr::after,
.fa-duotone.fa-regular::after,
.fadl::after,
.fa-duotone.fa-light::after,
.fadt::after,
.fa-duotone.fa-thin::after,
.fasds::after,
.fa-sharp-duotone.fa-solid::after,
.fa-sharp-duotone::after,
.fasdr::after,
.fa-sharp-duotone.fa-regular::after,
.fasdl::after,
.fa-sharp-duotone.fa-light::after,
.fasdt::after,
.fa-sharp-duotone.fa-thin::after {
  content: var(--fa--fa); }

.fa-classic.fa-duotone {
  font-family: 'Font Awesome 6 Duotone'; }

.fass,
.fa-sharp {
  font-weight: 900; }

.fad,
.fa-duotone {
  font-weight: 900; }

.fasds,
.fa-sharp-duotone {
  font-weight: 900; }

.fa-classic,
.fas,
.fa-solid,
.far,
.fa-regular,
.fal,
.fa-light,
.fat,
.fa-thin {
  font-family: 'Font Awesome 6 Pro'; }

.fa-duotone,
.fad,
.fadr,
.fadl,
.fadt {
  font-family: 'Font Awesome 6 Duotone'; }

.fa-brands,
.fab {
  font-family: 'Font Awesome 6 Brands'; }

.fa-sharp,
.fass,
.fasr,
.fasl,
.fast {
  font-family: 'Font Awesome 6 Sharp'; }

.fa-sharp-duotone,
.fasds,
.fasdr,
.fasdl,
.fasdt {
  font-family: 'Font Awesome 6 Sharp Duotone'; }

.fa-1x {
  font-size: 1em; }

.fa-2x {
  font-size: 2em; }

.fa-3x {
  font-size: 3em; }

.fa-4x {
  font-size: 4em; }

.fa-5x {
  font-size: 5em; }

.fa-6x {
  font-size: 6em; }

.fa-7x {
  font-size: 7em; }

.fa-8x {
  font-size: 8em; }

.fa-9x {
  font-size: 9em; }

.fa-10x {
  font-size: 10em; }

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em; }

.fa-xs {
  font-size: 0.75em;
  line-height: 0.08333em;
  vertical-align: 0.125em; }

.fa-sm {
  font-size: 0.875em;
  line-height: 0.07143em;
  vertical-align: 0.05357em; }

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em; }

.fa-xl {
  font-size: 1.5em;
  line-height: 0.04167em;
  vertical-align: -0.125em; }

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em; }

.fa-fw {
  text-align: center;
  width: 1.25em; }

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0; }
  .fa-ul > li {
    position: relative; }

.fa-li {
  left: calc(-1 * var(--fa-li-width, 2em));
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit; }

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em); }

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em); }

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em); }

.fa-beat {
  animation-name: fa-beat;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out); }

.fa-bounce {
  animation-name: fa-bounce;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1)); }

.fa-fade {
  animation-name: fa-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }

.fa-beat-fade {
  animation-name: fa-beat-fade;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }

.fa-flip {
  animation-name: fa-flip;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, ease-in-out); }

.fa-shake {
  animation-name: fa-shake;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear); }

.fa-spin {
  animation-name: fa-spin;
  animation-delay: var(--fa-animation-delay, 0s);
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 2s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, linear); }

.fa-spin-reverse {
  --fa-animation-direction: reverse; }

.fa-pulse,
.fa-spin-pulse {
  animation-name: fa-spin;
  animation-direction: var(--fa-animation-direction, normal);
  animation-duration: var(--fa-animation-duration, 1s);
  animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  animation-timing-function: var(--fa-animation-timing, steps(8)); }

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
  .fa-bounce,
  .fa-fade,
  .fa-beat-fade,
  .fa-flip,
  .fa-pulse,
  .fa-shake,
  .fa-spin,
  .fa-spin-pulse {
    animation-delay: -1ms;
    animation-duration: 1ms;
    animation-iteration-count: 1;
    transition-delay: 0s;
    transition-duration: 0s; } }

@keyframes fa-beat {
  0%, 90% {
    transform: scale(1); }
  45% {
    transform: scale(var(--fa-beat-scale, 1.25)); } }

@keyframes fa-bounce {
  0% {
    transform: scale(1, 1) translateY(0); }
  10% {
    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0); }
  30% {
    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em)); }
  50% {
    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0); }
  57% {
    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em)); }
  64% {
    transform: scale(1, 1) translateY(0); }
  100% {
    transform: scale(1, 1) translateY(0); } }

@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4); } }

@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    transform: scale(1); }
  50% {
    opacity: 1;
    transform: scale(var(--fa-beat-fade-scale, 1.125)); } }

@keyframes fa-flip {
  50% {
    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg)); } }

@keyframes fa-shake {
  0% {
    transform: rotate(-15deg); }
  4% {
    transform: rotate(15deg); }
  8%, 24% {
    transform: rotate(-18deg); }
  12%, 28% {
    transform: rotate(18deg); }
  16% {
    transform: rotate(-22deg); }
  20% {
    transform: rotate(22deg); }
  32% {
    transform: rotate(-12deg); }
  36% {
    transform: rotate(12deg); }
  40%, 100% {
    transform: rotate(0deg); } }

@keyframes fa-spin {
  0% {
    transform: rotate(0deg); }
  100% {
    transform: rotate(360deg); } }

.fa-rotate-90 {
  transform: rotate(90deg); }

.fa-rotate-180 {
  transform: rotate(180deg); }

.fa-rotate-270 {
  transform: rotate(270deg); }

.fa-flip-horizontal {
  transform: scale(-1, 1); }

.fa-flip-vertical {
  transform: scale(1, -1); }

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  transform: scale(-1, -1); }

.fa-rotate-by {
  transform: rotate(var(--fa-rotate-angle, 0)); }

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2.5em; }

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
  z-index: var(--fa-stack-z-index, auto); }

.fa-stack-1x {
  line-height: inherit; }

.fa-stack-2x {
  font-size: 2em; }

.fa-inverse {
  color: var(--fa-inverse, #fff); }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-acorn { --fa: "\f6ae"; --fa--fa: "\f6ae\f6ae"; }
.fa-add { --fa: "\2b"; --fa--fa: "\2b\2b"; }
.fa-address-book { --fa: "\f2b9"; --fa--fa: "\f2b9\f2b9"; }
.fa-address-card { --fa: "\f2bb"; --fa--fa: "\f2bb\f2bb"; }
.fa-air-conditioner { --fa: "\f8f4"; --fa--fa: "\f8f4\f8f4"; }
.fa-alarm-clock { --fa: "\f34e"; --fa--fa: "\f34e\f34e"; }
.fa-ambulance { --fa: "\f0f9"; --fa--fa: "\f0f9\f0f9"; }
.fa-analytics { --fa: "\f643"; --fa--fa: "\f643\f643"; }
.fa-anchor { --fa: "\f13d"; --fa--fa: "\f13d\f13d"; }
.fa-angle-90 { --fa: "\e08d"; --fa--fa: "\e08d\e08d"; }
.fa-angle-double-down { --fa: "\f103"; --fa--fa: "\f103\f103"; }
.fa-angle-double-left { --fa: "\f100"; --fa--fa: "\f100\f100"; }
.fa-angle-double-right { --fa: "\f101"; --fa--fa: "\f101\f101"; }
.fa-angle-double-up { --fa: "\f102"; --fa--fa: "\f102\f102"; }
.fa-angle-down { --fa: "\f107"; --fa--fa: "\f107\f107"; }
.fa-angle-left { --fa: "\f104"; --fa--fa: "\f104\f104"; }
.fa-angle-right { --fa: "\f105"; --fa--fa: "\f105\f105"; }
.fa-angle-up { --fa: "\f106"; --fa--fa: "\f106\f106"; }
.fa-angles-down { --fa: "\f103"; --fa--fa: "\f103\f103"; }
.fa-angles-left { --fa: "\f100"; --fa--fa: "\f100\f100"; }
.fa-angles-right { --fa: "\f101"; --fa--fa: "\f101\f101"; }
.fa-angles-up { --fa: "\f102"; --fa--fa: "\f102\f102"; }
.fa-angles-up-down { --fa: "\e60d"; --fa--fa: "\e60d\e60d"; }
.fa-apartment { --fa: "\e468"; --fa--fa: "\e468\e468"; }
.fa-aperture { --fa: "\e2df"; --fa--fa: "\e2df\e2df"; }
.fa-archive { --fa: "\f187"; --fa--fa: "\f187\f187"; }
.fa-archway { --fa: "\f557"; --fa--fa: "\f557\f557"; }
.fa-arrow-alt-down { --fa: "\f354"; --fa--fa: "\f354\f354"; }
.fa-arrow-alt-left { --fa: "\f355"; --fa--fa: "\f355\f355"; }
.fa-arrow-alt-right { --fa: "\f356"; --fa--fa: "\f356\f356"; }
.fa-arrow-alt-up { --fa: "\f357"; --fa--fa: "\f357\f357"; }
.fa-arrow-down { --fa: "\f063"; --fa--fa: "\f063\f063"; }
.fa-arrow-down-1-9 { --fa: "\f162"; --fa--fa: "\f162\f162"; }
.fa-arrow-down-9-1 { --fa: "\f886"; --fa--fa: "\f886\f886"; }
.fa-arrow-down-a-z { --fa: "\f15d"; --fa--fa: "\f15d\f15d"; }
.fa-arrow-down-arrow-up { --fa: "\f883"; --fa--fa: "\f883\f883"; }
.fa-arrow-down-left { --fa: "\e091"; --fa--fa: "\e091\e091"; }
.fa-arrow-down-long { --fa: "\f175"; --fa--fa: "\f175\f175"; }
.fa-arrow-down-right { --fa: "\e093"; --fa--fa: "\e093\e093"; }
.fa-arrow-down-short-wide { --fa: "\f884"; --fa--fa: "\f884\f884"; }
.fa-arrow-down-to-line { --fa: "\f33d"; --fa--fa: "\f33d\f33d"; }
.fa-arrow-down-wide-short { --fa: "\f160"; --fa--fa: "\f160\f160"; }
.fa-arrow-down-z-a { --fa: "\f881"; --fa--fa: "\f881\f881"; }
.fa-arrow-left { --fa: "\f060"; --fa--fa: "\f060\f060"; }
.fa-arrow-left-long { --fa: "\f177"; --fa--fa: "\f177\f177"; }
.fa-arrow-left-long-to-line { --fa: "\e3d4"; --fa--fa: "\e3d4\e3d4"; }
.fa-arrow-left-rotate { --fa: "\f0e2"; --fa--fa: "\f0e2\f0e2"; }
.fa-arrow-pointer { --fa: "\f245"; --fa--fa: "\f245\f245"; }
.fa-arrow-right { --fa: "\f061"; --fa--fa: "\f061\f061"; }
.fa-arrow-right-arrow-left { --fa: "\f0ec"; --fa--fa: "\f0ec\f0ec"; }
.fa-arrow-right-from-bracket { --fa: "\f08b"; --fa--fa: "\f08b\f08b"; }
.fa-arrow-right-from-file { --fa: "\f56e"; --fa--fa: "\f56e\f56e"; }
.fa-arrow-right-long { --fa: "\f178"; --fa--fa: "\f178\f178"; }
.fa-arrow-right-long-to-line { --fa: "\e3d5"; --fa--fa: "\e3d5\e3d5"; }
.fa-arrow-right-to-bracket { --fa: "\f090"; --fa--fa: "\f090\f090"; }
.fa-arrow-right-to-file { --fa: "\f56f"; --fa--fa: "\f56f\f56f"; }
.fa-arrow-rotate-back { --fa: "\f0e2"; --fa--fa: "\f0e2\f0e2"; }
.fa-arrow-rotate-backward { --fa: "\f0e2"; --fa--fa: "\f0e2\f0e2"; }
.fa-arrow-rotate-left { --fa: "\f0e2"; --fa--fa: "\f0e2\f0e2"; }
.fa-arrow-to-bottom { --fa: "\f33d"; --fa--fa: "\f33d\f33d"; }
.fa-arrow-turn-down-left { --fa: "\e2e1"; --fa--fa: "\e2e1\e2e1"; }
.fa-arrow-turn-down-right { --fa: "\e3d6"; --fa--fa: "\e3d6\e3d6"; }
.fa-arrow-up { --fa: "\f062"; --fa--fa: "\f062\f062"; }
.fa-arrow-up-1-9 { --fa: "\f163"; --fa--fa: "\f163\f163"; }
.fa-arrow-up-a-z { --fa: "\f15e"; --fa--fa: "\f15e\f15e"; }
.fa-arrow-up-arrow-down { --fa: "\e099"; --fa--fa: "\e099\e099"; }
.fa-arrow-up-from-bracket { --fa: "\e09a"; --fa--fa: "\e09a\e09a"; }
.fa-arrow-up-left { --fa: "\e09d"; --fa--fa: "\e09d\e09d"; }
.fa-arrow-up-long { --fa: "\f176"; --fa--fa: "\f176\f176"; }
.fa-arrow-up-right { --fa: "\e09f"; --fa--fa: "\e09f\e09f"; }
.fa-arrow-up-right-and-arrow-down-left-from-center { --fa: "\e0a0"; --fa--fa: "\e0a0\e0a0"; }
.fa-arrow-up-right-from-square { --fa: "\f08e"; --fa--fa: "\f08e\f08e"; }
.fa-arrow-up-short-wide { --fa: "\f885"; --fa--fa: "\f885\f885"; }
.fa-arrow-up-wide-short { --fa: "\f161"; --fa--fa: "\f161\f161"; }
.fa-arrows { --fa: "\f047"; --fa--fa: "\f047\f047"; }
.fa-arrows-h { --fa: "\f07e"; --fa--fa: "\f07e\f07e"; }
.fa-arrows-left-right { --fa: "\f07e"; --fa--fa: "\f07e\f07e"; }
.fa-arrows-maximize { --fa: "\f31d"; --fa--fa: "\f31d\f31d"; }
.fa-arrows-repeat { --fa: "\f364"; --fa--fa: "\f364\f364"; }
.fa-arrows-repeat-1 { --fa: "\f366"; --fa--fa: "\f366\f366"; }
.fa-arrows-retweet { --fa: "\f361"; --fa--fa: "\f361\f361"; }
.fa-arrows-rotate { --fa: "\f021"; --fa--fa: "\f021\f021"; }
.fa-arrows-up-down { --fa: "\f07d"; --fa--fa: "\f07d\f07d"; }
.fa-arrows-up-down-left-right { --fa: "\f047"; --fa--fa: "\f047\f047"; }
.fa-arrows-v { --fa: "\f07d"; --fa--fa: "\f07d\f07d"; }
.fa-asterisk { --fa: "\2a"; --fa--fa: "\2a\2a"; }
.fa-at { --fa: "\40"; --fa--fa: "\40\40"; }
.fa-audio-description { --fa: "\f29e"; --fa--fa: "\f29e\f29e"; }
.fa-automobile { --fa: "\f1b9"; --fa--fa: "\f1b9\f1b9"; }
.fa-backward { --fa: "\f04a"; --fa--fa: "\f04a\f04a"; }
.fa-backward-fast { --fa: "\f049"; --fa--fa: "\f049\f049"; }
.fa-backward-step { --fa: "\f048"; --fa--fa: "\f048\f048"; }
.fa-badge-check { --fa: "\f336"; --fa--fa: "\f336\f336"; }
.fa-badge-percent { --fa: "\f646"; --fa--fa: "\f646\f646"; }
.fa-bag-shopping { --fa: "\f290"; --fa--fa: "\f290\f290"; }
.fa-balance-scale { --fa: "\f24e"; --fa--fa: "\f24e\f24e"; }
.fa-ban-parking { --fa: "\f616"; --fa--fa: "\f616\f616"; }
.fa-bank { --fa: "\f19c"; --fa--fa: "\f19c\f19c"; }
.fa-barcode { --fa: "\f02a"; --fa--fa: "\f02a\f02a"; }
.fa-bars { --fa: "\f0c9"; --fa--fa: "\f0c9\f0c9"; }
.fa-bars-filter { --fa: "\e0ad"; --fa--fa: "\e0ad\e0ad"; }
.fa-bars-sort { --fa: "\e0ae"; --fa--fa: "\e0ae\e0ae"; }
.fa-bars-staggered { --fa: "\f550"; --fa--fa: "\f550\f550"; }
.fa-basket-shopping { --fa: "\f291"; --fa--fa: "\f291\f291"; }
.fa-basket-shopping-simple { --fa: "\e0af"; --fa--fa: "\e0af\e0af"; }
.fa-basketball { --fa: "\f434"; --fa--fa: "\f434\f434"; }
.fa-basketball-ball { --fa: "\f434"; --fa--fa: "\f434\f434"; }
.fa-basketball-hoop { --fa: "\f435"; --fa--fa: "\f435\f435"; }
.fa-battery-4 { --fa: "\f241"; --fa--fa: "\f241\f241"; }
.fa-battery-car { --fa: "\f5df"; --fa--fa: "\f5df\f5df"; }
.fa-battery-slash { --fa: "\f377"; --fa--fa: "\f377\f377"; }
.fa-battery-three-quarters { --fa: "\f241"; --fa--fa: "\f241\f241"; }
.fa-bed { --fa: "\f236"; --fa--fa: "\f236\f236"; }
.fa-bed-alt { --fa: "\f8f7"; --fa--fa: "\f8f7\f8f7"; }
.fa-bed-empty { --fa: "\f8f9"; --fa--fa: "\f8f9\f8f9"; }
.fa-bed-front { --fa: "\f8f7"; --fa--fa: "\f8f7\f8f7"; }
.fa-bed-pulse { --fa: "\f487"; --fa--fa: "\f487\f487"; }
.fa-bee { --fa: "\e0b2"; --fa--fa: "\e0b2\e0b2"; }
.fa-beer-foam { --fa: "\e0b3"; --fa--fa: "\e0b3\e0b3"; }
.fa-beer-mug { --fa: "\e0b3"; --fa--fa: "\e0b3\e0b3"; }
.fa-bell { --fa: "\f0f3"; --fa--fa: "\f0f3\f0f3"; }
.fa-bell-concierge { --fa: "\f562"; --fa--fa: "\f562\f562"; }
.fa-bell-slash { --fa: "\f1f6"; --fa--fa: "\f1f6\f1f6"; }
.fa-bench-tree { --fa: "\e2e7"; --fa--fa: "\e2e7\e2e7"; }
.fa-bicycle { --fa: "\f206"; --fa--fa: "\f206\f206"; }
.fa-biking { --fa: "\f84a"; --fa--fa: "\f84a\f84a"; }
.fa-bin-bottles-recycle { --fa: "\e5f6"; --fa--fa: "\e5f6\e5f6"; }
.fa-bin-recycle { --fa: "\e5f7"; --fa--fa: "\e5f7\e5f7"; }
.fa-binoculars { --fa: "\f1e5"; --fa--fa: "\f1e5\f1e5"; }
.fa-blind { --fa: "\f29d"; --fa--fa: "\f29d\f29d"; }
.fa-bolt { --fa: "\f0e7"; --fa--fa: "\f0e7\f0e7"; }
.fa-bolt-lightning { --fa: "\e0b7"; --fa--fa: "\e0b7\e0b7"; }
.fa-bolt-slash { --fa: "\e0b8"; --fa--fa: "\e0b8\e0b8"; }
.fa-bomb { --fa: "\f1e2"; --fa--fa: "\f1e2\f1e2"; }
.fa-book { --fa: "\f02d"; --fa--fa: "\f02d\f02d"; }
.fa-book-alt { --fa: "\f5d9"; --fa--fa: "\f5d9\f5d9"; }
.fa-book-blank { --fa: "\f5d9"; --fa--fa: "\f5d9\f5d9"; }
.fa-book-bookmark { --fa: "\e0bb"; --fa--fa: "\e0bb\e0bb"; }
.fa-book-open { --fa: "\f518"; --fa--fa: "\f518\f518"; }
.fa-book-open-alt { --fa: "\e0c0"; --fa--fa: "\e0c0\e0c0"; }
.fa-book-open-cover { --fa: "\e0c0"; --fa--fa: "\e0c0\e0c0"; }
.fa-book-open-reader { --fa: "\f5da"; --fa--fa: "\f5da\f5da"; }
.fa-book-reader { --fa: "\f5da"; --fa--fa: "\f5da\f5da"; }
.fa-bookmark { --fa: "\f02e"; --fa--fa: "\f02e\f02e"; }
.fa-bookmark-slash { --fa: "\e0c2"; --fa--fa: "\e0c2\e0c2"; }
.fa-books { --fa: "\f5db"; --fa--fa: "\f5db\f5db"; }
.fa-bowl-salad { --fa: "\f81e"; --fa--fa: "\f81e\f81e"; }
.fa-box-alt { --fa: "\f49a"; --fa--fa: "\f49a\f49a"; }
.fa-box-archive { --fa: "\f187"; --fa--fa: "\f187\f187"; }
.fa-box-ballot { --fa: "\f735"; --fa--fa: "\f735\f735"; }
.fa-box-check { --fa: "\f467"; --fa--fa: "\f467\f467"; }
.fa-box-open { --fa: "\f49e"; --fa--fa: "\f49e\f49e"; }
.fa-box-taped { --fa: "\f49a"; --fa--fa: "\f49a\f49a"; }
.fa-brain { --fa: "\f5dc"; --fa--fa: "\f5dc\f5dc"; }
.fa-briefcase { --fa: "\f0b1"; --fa--fa: "\f0b1\f0b1"; }
.fa-briefcase-medical { --fa: "\f469"; --fa--fa: "\f469\f469"; }
.fa-brush { --fa: "\f55d"; --fa--fa: "\f55d\f55d"; }
.fa-bug { --fa: "\f188"; --fa--fa: "\f188\f188"; }
.fa-building { --fa: "\f1ad"; --fa--fa: "\f1ad\f1ad"; }
.fa-building-columns { --fa: "\f19c"; --fa--fa: "\f19c\f19c"; }
.fa-building-user { --fa: "\e4da"; --fa--fa: "\e4da\e4da"; }
.fa-buildings { --fa: "\e0cc"; --fa--fa: "\e0cc\e0cc"; }
.fa-bullhorn { --fa: "\f0a1"; --fa--fa: "\f0a1\f0a1"; }
.fa-bullseye { --fa: "\f140"; --fa--fa: "\f140\f140"; }
.fa-bullseye-arrow { --fa: "\f648"; --fa--fa: "\f648\f648"; }
.fa-bullseye-pointer { --fa: "\f649"; --fa--fa: "\f649\f649"; }
.fa-bus { --fa: "\f207"; --fa--fa: "\f207\f207"; }
.fa-bus-alt { --fa: "\f55e"; --fa--fa: "\f55e\f55e"; }
.fa-bus-simple { --fa: "\f55e"; --fa--fa: "\f55e\f55e"; }
.fa-business-front { --fa: "\e45c"; --fa--fa: "\e45c\e45c"; }
.fa-cab { --fa: "\f1ba"; --fa--fa: "\f1ba\f1ba"; }
.fa-cable-car { --fa: "\f7da"; --fa--fa: "\f7da\f7da"; }
.fa-calculator { --fa: "\f1ec"; --fa--fa: "\f1ec\f1ec"; }
.fa-calendar { --fa: "\f133"; --fa--fa: "\f133\f133"; }
.fa-calendar-alt { --fa: "\f073"; --fa--fa: "\f073\f073"; }
.fa-calendar-check { --fa: "\f274"; --fa--fa: "\f274\f274"; }
.fa-calendar-day { --fa: "\f783"; --fa--fa: "\f783\f783"; }
.fa-calendar-days { --fa: "\f073"; --fa--fa: "\f073\f073"; }
.fa-calendar-edit { --fa: "\f333"; --fa--fa: "\f333\f333"; }
.fa-calendar-heart { --fa: "\e0d3"; --fa--fa: "\e0d3\e0d3"; }
.fa-calendar-minus { --fa: "\f272"; --fa--fa: "\f272\f272"; }
.fa-calendar-pen { --fa: "\f333"; --fa--fa: "\f333\f333"; }
.fa-calendar-plus { --fa: "\f271"; --fa--fa: "\f271\f271"; }
.fa-calendar-times { --fa: "\f273"; --fa--fa: "\f273\f273"; }
.fa-calendar-xmark { --fa: "\f273"; --fa--fa: "\f273\f273"; }
.fa-camera { --fa: "\f030"; --fa--fa: "\f030\f030"; }
.fa-camera-alt { --fa: "\f030"; --fa--fa: "\f030\f030"; }
.fa-camera-cctv { --fa: "\f8ac"; --fa--fa: "\f8ac\f8ac"; }
.fa-camera-slash { --fa: "\e0d9"; --fa--fa: "\e0d9\e0d9"; }
.fa-camera-web { --fa: "\f832"; --fa--fa: "\f832\f832"; }
.fa-camera-web-slash { --fa: "\f833"; --fa--fa: "\f833\f833"; }
.fa-candy { --fa: "\e3e7"; --fa--fa: "\e3e7\e3e7"; }
.fa-car { --fa: "\f1b9"; --fa--fa: "\f1b9\f1b9"; }
.fa-car-alt { --fa: "\f5de"; --fa--fa: "\f5de\f5de"; }
.fa-car-battery { --fa: "\f5df"; --fa--fa: "\f5df\f5df"; }
.fa-car-bolt { --fa: "\e341"; --fa--fa: "\e341\e341"; }
.fa-car-mirrors { --fa: "\e343"; --fa--fa: "\e343\e343"; }
.fa-car-rear { --fa: "\f5de"; --fa--fa: "\f5de\f5de"; }
.fa-car-side { --fa: "\f5e4"; --fa--fa: "\f5e4\f5e4"; }
.fa-car-side-bolt { --fa: "\e344"; --fa--fa: "\e344\e344"; }
.fa-car-tilt { --fa: "\f5e5"; --fa--fa: "\f5e5\f5e5"; }
.fa-caret-circle-down { --fa: "\f32d"; --fa--fa: "\f32d\f32d"; }
.fa-caret-circle-left { --fa: "\f32e"; --fa--fa: "\f32e\f32e"; }
.fa-caret-circle-right { --fa: "\f330"; --fa--fa: "\f330\f330"; }
.fa-caret-circle-up { --fa: "\f331"; --fa--fa: "\f331\f331"; }
.fa-caret-down { --fa: "\f0d7"; --fa--fa: "\f0d7\f0d7"; }
.fa-caret-left { --fa: "\f0d9"; --fa--fa: "\f0d9\f0d9"; }
.fa-caret-right { --fa: "\f0da"; --fa--fa: "\f0da\f0da"; }
.fa-caret-square-down { --fa: "\f150"; --fa--fa: "\f150\f150"; }
.fa-caret-square-left { --fa: "\f191"; --fa--fa: "\f191\f191"; }
.fa-caret-square-right { --fa: "\f152"; --fa--fa: "\f152\f152"; }
.fa-caret-square-up { --fa: "\f151"; --fa--fa: "\f151\f151"; }
.fa-caret-up { --fa: "\f0d8"; --fa--fa: "\f0d8\f0d8"; }
.fa-cars { --fa: "\f85b"; --fa--fa: "\f85b\f85b"; }
.fa-cart-arrow-down { --fa: "\f218"; --fa--fa: "\f218\f218"; }
.fa-cart-arrow-up { --fa: "\e3ee"; --fa--fa: "\e3ee\e3ee"; }
.fa-cart-minus { --fa: "\e0db"; --fa--fa: "\e0db\e0db"; }
.fa-cart-plus { --fa: "\f217"; --fa--fa: "\f217\f217"; }
.fa-cart-shopping { --fa: "\f07a"; --fa--fa: "\f07a\f07a"; }
.fa-cctv { --fa: "\f8ac"; --fa--fa: "\f8ac\f8ac"; }
.fa-chain { --fa: "\f0c1"; --fa--fa: "\f0c1\f0c1"; }
.fa-chart-mixed { --fa: "\f643"; --fa--fa: "\f643\f643"; }
.fa-chart-network { --fa: "\f78a"; --fa--fa: "\f78a\f78a"; }
.fa-chart-pie { --fa: "\f200"; --fa--fa: "\f200\f200"; }
.fa-chart-simple { --fa: "\e473"; --fa--fa: "\e473\e473"; }
.fa-check { --fa: "\f00c"; --fa--fa: "\f00c\f00c"; }
.fa-check-circle { --fa: "\f058"; --fa--fa: "\f058\f058"; }
.fa-check-square { --fa: "\f14a"; --fa--fa: "\f14a\f14a"; }
.fa-check-to-slot { --fa: "\f772"; --fa--fa: "\f772\f772"; }
.fa-chevron-double-down { --fa: "\f322"; --fa--fa: "\f322\f322"; }
.fa-chevron-double-left { --fa: "\f323"; --fa--fa: "\f323\f323"; }
.fa-chevron-double-right { --fa: "\f324"; --fa--fa: "\f324\f324"; }
.fa-chevron-double-up { --fa: "\f325"; --fa--fa: "\f325\f325"; }
.fa-chevron-down { --fa: "\f078"; --fa--fa: "\f078\f078"; }
.fa-chevron-left { --fa: "\f053"; --fa--fa: "\f053\f053"; }
.fa-chevron-right { --fa: "\f054"; --fa--fa: "\f054\f054"; }
.fa-chevron-up { --fa: "\f077"; --fa--fa: "\f077\f077"; }
.fa-chevrons-down { --fa: "\f322"; --fa--fa: "\f322\f322"; }
.fa-chevrons-left { --fa: "\f323"; --fa--fa: "\f323\f323"; }
.fa-chevrons-right { --fa: "\f324"; --fa--fa: "\f324\f324"; }
.fa-chevrons-up { --fa: "\f325"; --fa--fa: "\f325\f325"; }
.fa-child { --fa: "\f1ae"; --fa--fa: "\f1ae\f1ae"; }
.fa-child-dress { --fa: "\e59c"; --fa--fa: "\e59c\e59c"; }
.fa-child-reaching { --fa: "\e59d"; --fa--fa: "\e59d\e59d"; }
.fa-children { --fa: "\e4e1"; --fa--fa: "\e4e1\e4e1"; }
.fa-circle { --fa: "\f111"; --fa--fa: "\f111\f111"; }
.fa-circle-caret-down { --fa: "\f32d"; --fa--fa: "\f32d\f32d"; }
.fa-circle-caret-left { --fa: "\f32e"; --fa--fa: "\f32e\f32e"; }
.fa-circle-caret-right { --fa: "\f330"; --fa--fa: "\f330\f330"; }
.fa-circle-caret-up { --fa: "\f331"; --fa--fa: "\f331\f331"; }
.fa-circle-check { --fa: "\f058"; --fa--fa: "\f058\f058"; }
.fa-circle-euro { --fa: "\e5ce"; --fa--fa: "\e5ce\e5ce"; }
.fa-circle-exclamation { --fa: "\f06a"; --fa--fa: "\f06a\f06a"; }
.fa-circle-h { --fa: "\f47e"; --fa--fa: "\f47e\f47e"; }
.fa-circle-info { --fa: "\f05a"; --fa--fa: "\f05a\f05a"; }
.fa-circle-location-arrow { --fa: "\f602"; --fa--fa: "\f602\f602"; }
.fa-circle-minus { --fa: "\f056"; --fa--fa: "\f056\f056"; }
.fa-circle-notch { --fa: "\f1ce"; --fa--fa: "\f1ce\f1ce"; }
.fa-circle-play { --fa: "\f144"; --fa--fa: "\f144\f144"; }
.fa-circle-plus { --fa: "\f055"; --fa--fa: "\f055\f055"; }
.fa-circle-quarter-stroke { --fa: "\e5d3"; --fa--fa: "\e5d3\e5d3"; }
.fa-circle-quarters { --fa: "\e3f8"; --fa--fa: "\e3f8\e3f8"; }
.fa-circle-question { --fa: "\f059"; --fa--fa: "\f059\f059"; }
.fa-circle-user { --fa: "\f2bd"; --fa--fa: "\f2bd\f2bd"; }
.fa-circle-xmark { --fa: "\f057"; --fa--fa: "\f057\f057"; }
.fa-city { --fa: "\f64f"; --fa--fa: "\f64f\f64f"; }
.fa-clinic-medical { --fa: "\f7f2"; --fa--fa: "\f7f2\f7f2"; }
.fa-clipboard { --fa: "\f328"; --fa--fa: "\f328\f328"; }
.fa-clipboard-check { --fa: "\f46c"; --fa--fa: "\f46c\f46c"; }
.fa-clipboard-list-check { --fa: "\f737"; --fa--fa: "\f737\f737"; }
.fa-clock { --fa: "\f017"; --fa--fa: "\f017\f017"; }
.fa-clock-four { --fa: "\f017"; --fa--fa: "\f017\f017"; }
.fa-clock-rotate-left { --fa: "\f1da"; --fa--fa: "\f1da\f1da"; }
.fa-clone { --fa: "\f24d"; --fa--fa: "\f24d\f24d"; }
.fa-close { --fa: "\f00d"; --fa--fa: "\f00d\f00d"; }
.fa-cloud-arrow-up { --fa: "\f0ee"; --fa--fa: "\f0ee\f0ee"; }
.fa-cloud-bolt { --fa: "\f76c"; --fa--fa: "\f76c\f76c"; }
.fa-cloud-bolt-moon { --fa: "\f76d"; --fa--fa: "\f76d\f76d"; }
.fa-cloud-bolt-sun { --fa: "\f76e"; --fa--fa: "\f76e\f76e"; }
.fa-cloud-drizzle { --fa: "\f738"; --fa--fa: "\f738\f738"; }
.fa-cloud-fog { --fa: "\f74e"; --fa--fa: "\f74e\f74e"; }
.fa-cloud-hail { --fa: "\f739"; --fa--fa: "\f739\f739"; }
.fa-cloud-hail-mixed { --fa: "\f73a"; --fa--fa: "\f73a\f73a"; }
.fa-cloud-meatball { --fa: "\f73b"; --fa--fa: "\f73b\f73b"; }
.fa-cloud-moon { --fa: "\f6c3"; --fa--fa: "\f6c3\f6c3"; }
.fa-cloud-moon-rain { --fa: "\f73c"; --fa--fa: "\f73c\f73c"; }
.fa-cloud-rain { --fa: "\f73d"; --fa--fa: "\f73d\f73d"; }
.fa-cloud-rainbow { --fa: "\f73e"; --fa--fa: "\f73e\f73e"; }
.fa-cloud-showers { --fa: "\f73f"; --fa--fa: "\f73f\f73f"; }
.fa-cloud-showers-heavy { --fa: "\f740"; --fa--fa: "\f740\f740"; }
.fa-cloud-showers-water { --fa: "\e4e4"; --fa--fa: "\e4e4\e4e4"; }
.fa-cloud-slash { --fa: "\e137"; --fa--fa: "\e137\e137"; }
.fa-cloud-sleet { --fa: "\f741"; --fa--fa: "\f741\f741"; }
.fa-cloud-snow { --fa: "\f742"; --fa--fa: "\f742\f742"; }
.fa-cloud-sun { --fa: "\f6c4"; --fa--fa: "\f6c4\f6c4"; }
.fa-cloud-sun-rain { --fa: "\f743"; --fa--fa: "\f743\f743"; }
.fa-cloud-upload { --fa: "\f0ee"; --fa--fa: "\f0ee\f0ee"; }
.fa-cloud-upload-alt { --fa: "\f0ee"; --fa--fa: "\f0ee\f0ee"; }
.fa-clouds { --fa: "\f744"; --fa--fa: "\f744\f744"; }
.fa-clouds-moon { --fa: "\f745"; --fa--fa: "\f745\f745"; }
.fa-clouds-sun { --fa: "\f746"; --fa--fa: "\f746\f746"; }
.fa-code-branch { --fa: "\f126"; --fa--fa: "\f126\f126"; }
.fa-cog { --fa: "\f013"; --fa--fa: "\f013\f013"; }
.fa-cogs { --fa: "\f085"; --fa--fa: "\f085\f085"; }
.fa-comment { --fa: "\f075"; --fa--fa: "\f075\f075"; }
.fa-comment-alt { --fa: "\f27a"; --fa--fa: "\f27a\f27a"; }
.fa-comment-alt-check { --fa: "\f4a2"; --fa--fa: "\f4a2\f4a2"; }
.fa-comment-alt-dots { --fa: "\f4a3"; --fa--fa: "\f4a3\f4a3"; }
.fa-comment-alt-edit { --fa: "\f4a4"; --fa--fa: "\f4a4\f4a4"; }
.fa-comment-alt-exclamation { --fa: "\f4a5"; --fa--fa: "\f4a5\f4a5"; }
.fa-comment-alt-image { --fa: "\e1e0"; --fa--fa: "\e1e0\e1e0"; }
.fa-comment-alt-lines { --fa: "\f4a6"; --fa--fa: "\f4a6\f4a6"; }
.fa-comment-alt-medical { --fa: "\f7f4"; --fa--fa: "\f7f4\f7f4"; }
.fa-comment-alt-minus { --fa: "\f4a7"; --fa--fa: "\f4a7\f4a7"; }
.fa-comment-alt-plus { --fa: "\f4a8"; --fa--fa: "\f4a8\f4a8"; }
.fa-comment-alt-quote { --fa: "\e1e4"; --fa--fa: "\e1e4\e1e4"; }
.fa-comment-alt-slash { --fa: "\f4a9"; --fa--fa: "\f4a9\f4a9"; }
.fa-comment-alt-smile { --fa: "\f4aa"; --fa--fa: "\f4aa\f4aa"; }
.fa-comment-alt-times { --fa: "\f4ab"; --fa--fa: "\f4ab\f4ab"; }
.fa-comment-middle-alt { --fa: "\e1e1"; --fa--fa: "\e1e1\e1e1"; }
.fa-comment-slash { --fa: "\f4b3"; --fa--fa: "\f4b3\f4b3"; }
.fa-comments { --fa: "\f086"; --fa--fa: "\f086\f086"; }
.fa-comments-alt { --fa: "\f4b6"; --fa--fa: "\f4b6\f4b6"; }
.fa-compass { --fa: "\f14e"; --fa--fa: "\f14e\f14e"; }
.fa-compass-drafting { --fa: "\f568"; --fa--fa: "\f568\f568"; }
.fa-computer { --fa: "\e4e5"; --fa--fa: "\e4e5\e4e5"; }
.fa-computer-mouse-scrollwheel { --fa: "\f8cd"; --fa--fa: "\f8cd\f8cd"; }
.fa-concierge-bell { --fa: "\f562"; --fa--fa: "\f562\f562"; }
.fa-construction { --fa: "\f85d"; --fa--fa: "\f85d\f85d"; }
.fa-contact-book { --fa: "\f2b9"; --fa--fa: "\f2b9\f2b9"; }
.fa-contact-card { --fa: "\f2bb"; --fa--fa: "\f2bb\f2bb"; }
.fa-cookie { --fa: "\f563"; --fa--fa: "\f563\f563"; }
.fa-cookie-bite { --fa: "\f564"; --fa--fa: "\f564\f564"; }
.fa-copy { --fa: "\f0c5"; --fa--fa: "\f0c5\f0c5"; }
.fa-copyright { --fa: "\f1f9"; --fa--fa: "\f1f9\f1f9"; }
.fa-court-sport { --fa: "\e643"; --fa--fa: "\e643\e643"; }
.fa-crab { --fa: "\e3ff"; --fa--fa: "\e3ff\e3ff"; }
.fa-credit-card { --fa: "\f09d"; --fa--fa: "\f09d\f09d"; }
.fa-credit-card-alt { --fa: "\f09d"; --fa--fa: "\f09d\f09d"; }
.fa-croissant { --fa: "\f7f6"; --fa--fa: "\f7f6\f7f6"; }
.fa-cube { --fa: "\f1b2"; --fa--fa: "\f1b2\f1b2"; }
.fa-cut { --fa: "\f0c4"; --fa--fa: "\f0c4\f0c4"; }
.fa-cutlery { --fa: "\f2e7"; --fa--fa: "\f2e7\f2e7"; }
.fa-dashboard { --fa: "\f624"; --fa--fa: "\f624\f624"; }
.fa-database { --fa: "\f1c0"; --fa--fa: "\f1c0\f1c0"; }
.fa-deaf { --fa: "\f2a4"; --fa--fa: "\f2a4\f2a4"; }
.fa-deafness { --fa: "\f2a4"; --fa--fa: "\f2a4\f2a4"; }
.fa-desktop { --fa: "\f390"; --fa--fa: "\f390\f390"; }
.fa-desktop-alt { --fa: "\f390"; --fa--fa: "\f390\f390"; }
.fa-desktop-slash { --fa: "\e2fa"; --fa--fa: "\e2fa\e2fa"; }
.fa-dice { --fa: "\f522"; --fa--fa: "\f522\f522"; }
.fa-digging { --fa: "\f85e"; --fa--fa: "\f85e\f85e"; }
.fa-display { --fa: "\e163"; --fa--fa: "\e163\e163"; }
.fa-display-arrow-down { --fa: "\e164"; --fa--fa: "\e164\e164"; }
.fa-display-slash { --fa: "\e2fa"; --fa--fa: "\e2fa\e2fa"; }
.fa-dna { --fa: "\f471"; --fa--fa: "\f471\f471"; }
.fa-down { --fa: "\f354"; --fa--fa: "\f354\f354"; }
.fa-down-left { --fa: "\e16a"; --fa--fa: "\e16a\e16a"; }
.fa-down-right { --fa: "\e16b"; --fa--fa: "\e16b\e16b"; }
.fa-download { --fa: "\f019"; --fa--fa: "\f019\f019"; }
.fa-drafting-compass { --fa: "\f568"; --fa--fa: "\f568\f568"; }
.fa-drivers-license { --fa: "\f2c2"; --fa--fa: "\f2c2\f2c2"; }
.fa-droplet { --fa: "\f043"; --fa--fa: "\f043\f043"; }
.fa-ear-deaf { --fa: "\f2a4"; --fa--fa: "\f2a4\f2a4"; }
.fa-earth-europe { --fa: "\f7a2"; --fa--fa: "\f7a2\f7a2"; }
.fa-edit { --fa: "\f044"; --fa--fa: "\f044\f044"; }
.fa-ellipsis { --fa: "\f141"; --fa--fa: "\f141\f141"; }
.fa-ellipsis-h { --fa: "\f141"; --fa--fa: "\f141\f141"; }
.fa-ellipsis-v { --fa: "\f142"; --fa--fa: "\f142\f142"; }
.fa-ellipsis-vertical { --fa: "\f142"; --fa--fa: "\f142\f142"; }
.fa-envelope { --fa: "\f0e0"; --fa--fa: "\f0e0\f0e0"; }
.fa-envelope-open { --fa: "\f2b6"; --fa--fa: "\f2b6\f2b6"; }
.fa-envelopes { --fa: "\e170"; --fa--fa: "\e170\e170"; }
.fa-envelopes-bulk { --fa: "\f674"; --fa--fa: "\f674\f674"; }
.fa-eraser { --fa: "\f12d"; --fa--fa: "\f12d\f12d"; }
.fa-eur { --fa: "\f153"; --fa--fa: "\f153\f153"; }
.fa-euro { --fa: "\f153"; --fa--fa: "\f153\f153"; }
.fa-euro-sign { --fa: "\f153"; --fa--fa: "\f153\f153"; }
.fa-exchange { --fa: "\f0ec"; --fa--fa: "\f0ec\f0ec"; }
.fa-exclamation { --fa: "\21"; --fa--fa: "\21\21"; }
.fa-exclamation-circle { --fa: "\f06a"; --fa--fa: "\f06a\f06a"; }
.fa-exclamation-triangle { --fa: "\f071"; --fa--fa: "\f071\f071"; }
.fa-expand { --fa: "\f065"; --fa--fa: "\f065\f065"; }
.fa-expand-arrows { --fa: "\f31d"; --fa--fa: "\f31d\f31d"; }
.fa-external-link { --fa: "\f08e"; --fa--fa: "\f08e\f08e"; }
.fa-external-link-alt { --fa: "\f35d"; --fa--fa: "\f35d\f35d"; }
.fa-eye { --fa: "\f06e"; --fa--fa: "\f06e\f06e"; }
.fa-eye-dropper-half { --fa: "\e173"; --fa--fa: "\e173\e173"; }
.fa-eye-low-vision { --fa: "\f2a8"; --fa--fa: "\f2a8\f2a8"; }
.fa-eye-slash { --fa: "\f070"; --fa--fa: "\f070\f070"; }
.fa-face-confused { --fa: "\e36d"; --fa--fa: "\e36d\e36d"; }
.fa-face-diagonal-mouth { --fa: "\e47e"; --fa--fa: "\e47e\e47e"; }
.fa-face-frown { --fa: "\f119"; --fa--fa: "\f119\f119"; }
.fa-face-frown-open { --fa: "\f57a"; --fa--fa: "\f57a\f57a"; }
.fa-face-frown-slight { --fa: "\e376"; --fa--fa: "\e376\e376"; }
.fa-face-grin { --fa: "\f580"; --fa--fa: "\f580\f580"; }
.fa-face-grin-beam-sweat { --fa: "\f583"; --fa--fa: "\f583\f583"; }
.fa-face-grin-wide { --fa: "\f581"; --fa--fa: "\f581\f581"; }
.fa-face-laugh { --fa: "\f599"; --fa--fa: "\f599\f599"; }
.fa-face-laugh-beam { --fa: "\f59a"; --fa--fa: "\f59a\f59a"; }
.fa-face-laugh-wink { --fa: "\f59c"; --fa--fa: "\f59c\f59c"; }
.fa-face-smile { --fa: "\f118"; --fa--fa: "\f118\f118"; }
.fa-face-smile-beam { --fa: "\f5b8"; --fa--fa: "\f5b8\f5b8"; }
.fa-face-smile-hearts { --fa: "\e390"; --fa--fa: "\e390\e390"; }
.fa-face-smile-horns { --fa: "\e391"; --fa--fa: "\e391\e391"; }
.fa-face-smile-relaxed { --fa: "\e392"; --fa--fa: "\e392\e392"; }
.fa-face-smile-upside-down { --fa: "\e395"; --fa--fa: "\e395\e395"; }
.fa-face-smile-wink { --fa: "\f4da"; --fa--fa: "\f4da\f4da"; }
.fa-face-thinking { --fa: "\e39b"; --fa--fa: "\e39b\e39b"; }
.fa-face-weary { --fa: "\e3a1"; --fa--fa: "\e3a1\e3a1"; }
.fa-face-worried { --fa: "\e3a3"; --fa--fa: "\e3a3\e3a3"; }
.fa-family { --fa: "\e300"; --fa--fa: "\e300\e300"; }
.fa-family-dress { --fa: "\e301"; --fa--fa: "\e301\e301"; }
.fa-family-pants { --fa: "\e302"; --fa--fa: "\e302\e302"; }
.fa-fast-backward { --fa: "\f049"; --fa--fa: "\f049\f049"; }
.fa-fast-forward { --fa: "\f050"; --fa--fa: "\f050\f050"; }
.fa-faucet-drip { --fa: "\e006"; --fa--fa: "\e006\e006"; }
.fa-fax { --fa: "\f1ac"; --fa--fa: "\f1ac\f1ac"; }
.fa-feed { --fa: "\f09e"; --fa--fa: "\f09e\f09e"; }
.fa-female { --fa: "\f182"; --fa--fa: "\f182\f182"; }
.fa-ferry { --fa: "\e4ea"; --fa--fa: "\e4ea\e4ea"; }
.fa-file { --fa: "\f15b"; --fa--fa: "\f15b\f15b"; }
.fa-file-alt { --fa: "\f15c"; --fa--fa: "\f15c\f15c"; }
.fa-file-archive { --fa: "\f1c6"; --fa--fa: "\f1c6\f1c6"; }
.fa-file-arrow-down { --fa: "\f56d"; --fa--fa: "\f56d\f56d"; }
.fa-file-arrow-up { --fa: "\f574"; --fa--fa: "\f574\f574"; }
.fa-file-audio { --fa: "\f1c7"; --fa--fa: "\f1c7\f1c7"; }
.fa-file-award { --fa: "\f5f3"; --fa--fa: "\f5f3\f5f3"; }
.fa-file-binary { --fa: "\e175"; --fa--fa: "\e175\e175"; }
.fa-file-certificate { --fa: "\f5f3"; --fa--fa: "\f5f3\f5f3"; }
.fa-file-chart-column { --fa: "\f659"; --fa--fa: "\f659\f659"; }
.fa-file-chart-line { --fa: "\f659"; --fa--fa: "\f659\f659"; }
.fa-file-chart-pie { --fa: "\f65a"; --fa--fa: "\f65a\f65a"; }
.fa-file-check { --fa: "\f316"; --fa--fa: "\f316\f316"; }
.fa-file-clipboard { --fa: "\f0ea"; --fa--fa: "\f0ea\f0ea"; }
.fa-file-code { --fa: "\f1c9"; --fa--fa: "\f1c9\f1c9"; }
.fa-file-contract { --fa: "\f56c"; --fa--fa: "\f56c\f56c"; }
.fa-file-csv { --fa: "\f6dd"; --fa--fa: "\f6dd\f6dd"; }
.fa-file-doc { --fa: "\e5ed"; --fa--fa: "\e5ed\e5ed"; }
.fa-file-download { --fa: "\f56d"; --fa--fa: "\f56d\f56d"; }
.fa-file-edit { --fa: "\f31c"; --fa--fa: "\f31c\f31c"; }
.fa-file-excel { --fa: "\f1c3"; --fa--fa: "\f1c3\f1c3"; }
.fa-file-exclamation { --fa: "\f31a"; --fa--fa: "\f31a\f31a"; }
.fa-file-export { --fa: "\f56e"; --fa--fa: "\f56e\f56e"; }
.fa-file-heart { --fa: "\e176"; --fa--fa: "\e176\e176"; }
.fa-file-image { --fa: "\f1c5"; --fa--fa: "\f1c5\f1c5"; }
.fa-file-import { --fa: "\f56f"; --fa--fa: "\f56f\f56f"; }
.fa-file-invoice { --fa: "\f570"; --fa--fa: "\f570\f570"; }
.fa-file-lines { --fa: "\f15c"; --fa--fa: "\f15c\f15c"; }
.fa-file-lock { --fa: "\e3a6"; --fa--fa: "\e3a6\e3a6"; }
.fa-file-magnifying-glass { --fa: "\f865"; --fa--fa: "\f865\f865"; }
.fa-file-medical { --fa: "\f477"; --fa--fa: "\f477\f477"; }
.fa-file-medical-alt { --fa: "\f478"; --fa--fa: "\f478\f478"; }
.fa-file-minus { --fa: "\f318"; --fa--fa: "\f318\f318"; }
.fa-file-music { --fa: "\f8b6"; --fa--fa: "\f8b6\f8b6"; }
.fa-file-pdf { --fa: "\f1c1"; --fa--fa: "\f1c1\f1c1"; }
.fa-file-pen { --fa: "\f31c"; --fa--fa: "\f31c\f31c"; }
.fa-file-plus { --fa: "\f319"; --fa--fa: "\f319\f319"; }
.fa-file-plus-minus { --fa: "\e177"; --fa--fa: "\e177\e177"; }
.fa-file-powerpoint { --fa: "\f1c4"; --fa--fa: "\f1c4\f1c4"; }
.fa-file-prescription { --fa: "\f572"; --fa--fa: "\f572\f572"; }
.fa-file-search { --fa: "\f865"; --fa--fa: "\f865\f865"; }
.fa-file-shield { --fa: "\e4f0"; --fa--fa: "\e4f0\e4f0"; }
.fa-file-slash { --fa: "\e3a7"; --fa--fa: "\e3a7\e3a7"; }
.fa-file-spreadsheet { --fa: "\f65b"; --fa--fa: "\f65b\f65b"; }
.fa-file-text { --fa: "\f15c"; --fa--fa: "\f15c\f15c"; }
.fa-file-times { --fa: "\f317"; --fa--fa: "\f317\f317"; }
.fa-file-upload { --fa: "\f574"; --fa--fa: "\f574\f574"; }
.fa-file-user { --fa: "\f65c"; --fa--fa: "\f65c\f65c"; }
.fa-file-video { --fa: "\f1c8"; --fa--fa: "\f1c8\f1c8"; }
.fa-file-waveform { --fa: "\f478"; --fa--fa: "\f478\f478"; }
.fa-file-word { --fa: "\f1c2"; --fa--fa: "\f1c2\f1c2"; }
.fa-file-xmark { --fa: "\f317"; --fa--fa: "\f317\f317"; }
.fa-file-zipper { --fa: "\f1c6"; --fa--fa: "\f1c6\f1c6"; }
.fa-files { --fa: "\e178"; --fa--fa: "\e178\e178"; }
.fa-files-medical { --fa: "\f7fd"; --fa--fa: "\f7fd\f7fd"; }
.fa-filter { --fa: "\f0b0"; --fa--fa: "\f0b0\f0b0"; }
.fa-filter-list { --fa: "\e17c"; --fa--fa: "\e17c\e17c"; }
.fa-filter-slash { --fa: "\e17d"; --fa--fa: "\e17d\e17d"; }
.fa-filters { --fa: "\e17e"; --fa--fa: "\e17e\e17e"; }
.fa-fingerprint { --fa: "\f577"; --fa--fa: "\f577\f577"; }
.fa-fire { --fa: "\f06d"; --fa--fa: "\f06d\f06d"; }
.fa-flag { --fa: "\f024"; --fa--fa: "\f024\f024"; }
.fa-flask { --fa: "\f0c3"; --fa--fa: "\f0c3\f0c3"; }
.fa-floppy-disk { --fa: "\f0c7"; --fa--fa: "\f0c7\f0c7"; }
.fa-fog { --fa: "\f74e"; --fa--fa: "\f74e\f74e"; }
.fa-folder { --fa: "\f07b"; --fa--fa: "\f07b\f07b"; }
.fa-folder-arrow-down { --fa: "\e053"; --fa--fa: "\e053\e053"; }
.fa-folder-arrow-up { --fa: "\e054"; --fa--fa: "\e054\e054"; }
.fa-folder-blank { --fa: "\f07b"; --fa--fa: "\f07b\f07b"; }
.fa-folder-bookmark { --fa: "\e186"; --fa--fa: "\e186\e186"; }
.fa-folder-download { --fa: "\e053"; --fa--fa: "\e053\e053"; }
.fa-folder-heart { --fa: "\e189"; --fa--fa: "\e189\e189"; }
.fa-folder-image { --fa: "\e18a"; --fa--fa: "\e18a\e18a"; }
.fa-folder-magnifying-glass { --fa: "\e18b"; --fa--fa: "\e18b\e18b"; }
.fa-folder-medical { --fa: "\e18c"; --fa--fa: "\e18c\e18c"; }
.fa-folder-music { --fa: "\e18d"; --fa--fa: "\e18d\e18d"; }
.fa-folder-open { --fa: "\f07c"; --fa--fa: "\f07c\f07c"; }
.fa-folder-plus { --fa: "\f65e"; --fa--fa: "\f65e\f65e"; }
.fa-folder-search { --fa: "\e18b"; --fa--fa: "\e18b\e18b"; }
.fa-folder-times { --fa: "\f65f"; --fa--fa: "\f65f\f65f"; }
.fa-folder-tree { --fa: "\f802"; --fa--fa: "\f802\f802"; }
.fa-folder-upload { --fa: "\e054"; --fa--fa: "\e054\e054"; }
.fa-folder-user { --fa: "\e18e"; --fa--fa: "\e18e\e18e"; }
.fa-folder-xmark { --fa: "\f65f"; --fa--fa: "\f65f\f65f"; }
.fa-folders { --fa: "\f660"; --fa--fa: "\f660\f660"; }
.fa-font-awesome { --fa: "\f2b4"; --fa--fa: "\f2b4\f2b4"; }
.fa-font-awesome-flag { --fa: "\f2b4"; --fa--fa: "\f2b4\f2b4"; }
.fa-font-awesome-logo-full { --fa: "\f2b4"; --fa--fa: "\f2b4\f2b4"; }
.fa-fork { --fa: "\f2e3"; --fa--fa: "\f2e3\f2e3"; }
.fa-fork-knife { --fa: "\f2e6"; --fa--fa: "\f2e6\f2e6"; }
.fa-fort { --fa: "\e486"; --fa--fa: "\e486\e486"; }
.fa-forward { --fa: "\f04e"; --fa--fa: "\f04e\f04e"; }
.fa-forward-fast { --fa: "\f050"; --fa--fa: "\f050\f050"; }
.fa-forward-step { --fa: "\f051"; --fa--fa: "\f051\f051"; }
.fa-fragile { --fa: "\f4bb"; --fa--fa: "\f4bb\f4bb"; }
.fa-frown { --fa: "\f119"; --fa--fa: "\f119\f119"; }
.fa-frown-open { --fa: "\f57a"; --fa--fa: "\f57a\f57a"; }
.fa-futbol { --fa: "\f1e3"; --fa--fa: "\f1e3\f1e3"; }
.fa-futbol-ball { --fa: "\f1e3"; --fa--fa: "\f1e3\f1e3"; }
.fa-gamepad-alt { --fa: "\e5a2"; --fa--fa: "\e5a2\e5a2"; }
.fa-gamepad-modern { --fa: "\e5a2"; --fa--fa: "\e5a2\e5a2"; }
.fa-gas-pump { --fa: "\f52f"; --fa--fa: "\f52f\f52f"; }
.fa-gas-pump-slash { --fa: "\f5f4"; --fa--fa: "\f5f4\f5f4"; }
.fa-gauge { --fa: "\f624"; --fa--fa: "\f624\f624"; }
.fa-gauge-med { --fa: "\f624"; --fa--fa: "\f624\f624"; }
.fa-gear { --fa: "\f013"; --fa--fa: "\f013\f013"; }
.fa-gears { --fa: "\f085"; --fa--fa: "\f085\f085"; }
.fa-gem { --fa: "\f3a5"; --fa--fa: "\f3a5\f3a5"; }
.fa-gift { --fa: "\f06b"; --fa--fa: "\f06b\f06b"; }
.fa-glass { --fa: "\f804"; --fa--fa: "\f804\f804"; }
.fa-glasses { --fa: "\f530"; --fa--fa: "\f530\f530"; }
.fa-globe { --fa: "\f0ac"; --fa--fa: "\f0ac\f0ac"; }
.fa-globe-europe { --fa: "\f7a2"; --fa--fa: "\f7a2\f7a2"; }
.fa-graduation-cap { --fa: "\f19d"; --fa--fa: "\f19d\f19d"; }
.fa-grid { --fa: "\e195"; --fa--fa: "\e195\e195"; }
.fa-grid-2 { --fa: "\e196"; --fa--fa: "\e196\e196"; }
.fa-grid-2-plus { --fa: "\e197"; --fa--fa: "\e197\e197"; }
.fa-grid-3 { --fa: "\e195"; --fa--fa: "\e195\e195"; }
.fa-grid-4 { --fa: "\e198"; --fa--fa: "\e198\e198"; }
.fa-grid-5 { --fa: "\e199"; --fa--fa: "\e199\e199"; }
.fa-grid-round { --fa: "\e5da"; --fa--fa: "\e5da\e5da"; }
.fa-grid-round-2 { --fa: "\e5db"; --fa--fa: "\e5db\e5db"; }
.fa-grid-round-2-plus { --fa: "\e5dc"; --fa--fa: "\e5dc\e5dc"; }
.fa-grid-round-4 { --fa: "\e5dd"; --fa--fa: "\e5dd\e5dd"; }
.fa-grid-round-5 { --fa: "\e5de"; --fa--fa: "\e5de\e5de"; }
.fa-grin { --fa: "\f580"; --fa--fa: "\f580\f580"; }
.fa-grin-alt { --fa: "\f581"; --fa--fa: "\f581\f581"; }
.fa-grin-beam-sweat { --fa: "\f583"; --fa--fa: "\f583\f583"; }
.fa-grip-dots { --fa: "\e410"; --fa--fa: "\e410\e410"; }
.fa-grip-dots-vertical { --fa: "\e411"; --fa--fa: "\e411\e411"; }
.fa-guitar { --fa: "\f7a6"; --fa--fa: "\f7a6\f7a6"; }
.fa-guitar-electric { --fa: "\f8be"; --fa--fa: "\f8be\f8be"; }
.fa-hand-holding-droplet { --fa: "\f4c1"; --fa--fa: "\f4c1\f4c1"; }
.fa-hand-holding-heart { --fa: "\f4be"; --fa--fa: "\f4be\f4be"; }
.fa-hand-holding-water { --fa: "\f4c1"; --fa--fa: "\f4c1\f4c1"; }
.fa-hand-pointer { --fa: "\f25a"; --fa--fa: "\f25a\f25a"; }
.fa-handcuffs { --fa: "\e4f8"; --fa--fa: "\e4f8\e4f8"; }
.fa-hands { --fa: "\f2a7"; --fa--fa: "\f2a7\f2a7"; }
.fa-hands-heart { --fa: "\f4c3"; --fa--fa: "\f4c3\f4c3"; }
.fa-hands-helping { --fa: "\f4c4"; --fa--fa: "\f4c4\f4c4"; }
.fa-hands-holding-child { --fa: "\e4fa"; --fa--fa: "\e4fa\e4fa"; }
.fa-hands-holding-heart { --fa: "\f4c3"; --fa--fa: "\f4c3\f4c3"; }
.fa-handshake { --fa: "\f2b5"; --fa--fa: "\f2b5\f2b5"; }
.fa-handshake-alt { --fa: "\f4c6"; --fa--fa: "\f4c6\f4c6"; }
.fa-handshake-angle { --fa: "\f4c4"; --fa--fa: "\f4c4\f4c4"; }
.fa-handshake-simple { --fa: "\f4c6"; --fa--fa: "\f4c6\f4c6"; }
.fa-handshake-slash { --fa: "\e060"; --fa--fa: "\e060\e060"; }
.fa-hard-of-hearing { --fa: "\f2a4"; --fa--fa: "\f2a4\f2a4"; }
.fa-hashtag { --fa: "\23"; --fa--fa: "\23\23"; }
.fa-head-side { --fa: "\f6e9"; --fa--fa: "\f6e9\f6e9"; }
.fa-head-side-brain { --fa: "\f808"; --fa--fa: "\f808\f808"; }
.fa-head-side-cough { --fa: "\e061"; --fa--fa: "\e061\e061"; }
.fa-head-side-cough-slash { --fa: "\e062"; --fa--fa: "\e062\e062"; }
.fa-head-side-gear { --fa: "\e611"; --fa--fa: "\e611\e611"; }
.fa-head-side-goggles { --fa: "\f6ea"; --fa--fa: "\f6ea\f6ea"; }
.fa-head-side-headphones { --fa: "\f8c2"; --fa--fa: "\f8c2\f8c2"; }
.fa-head-side-heart { --fa: "\e1aa"; --fa--fa: "\e1aa\e1aa"; }
.fa-head-side-mask { --fa: "\e063"; --fa--fa: "\e063\e063"; }
.fa-head-side-medical { --fa: "\f809"; --fa--fa: "\f809\f809"; }
.fa-head-side-virus { --fa: "\e064"; --fa--fa: "\e064\e064"; }
.fa-head-vr { --fa: "\f6ea"; --fa--fa: "\f6ea\f6ea"; }
.fa-headphones { --fa: "\f025"; --fa--fa: "\f025\f025"; }
.fa-headset { --fa: "\f590"; --fa--fa: "\f590\f590"; }
.fa-heart { --fa: "\f004"; --fa--fa: "\f004\f004"; }
.fa-heart-half { --fa: "\e1ab"; --fa--fa: "\e1ab\e1ab"; }
.fa-high-definition { --fa: "\e1ae"; --fa--fa: "\e1ae\e1ae"; }
.fa-highlighter { --fa: "\f591"; --fa--fa: "\f591\f591"; }
.fa-hiking { --fa: "\f6ec"; --fa--fa: "\f6ec\f6ec"; }
.fa-history { --fa: "\f1da"; --fa--fa: "\f1da\f1da"; }
.fa-home { --fa: "\f015"; --fa--fa: "\f015\f015"; }
.fa-home-alt { --fa: "\f015"; --fa--fa: "\f015\f015"; }
.fa-home-blank { --fa: "\e487"; --fa--fa: "\e487\e487"; }
.fa-home-heart { --fa: "\f4c9"; --fa--fa: "\f4c9\f4c9"; }
.fa-home-lg { --fa: "\e3af"; --fa--fa: "\e3af\e3af"; }
.fa-home-lg-alt { --fa: "\f015"; --fa--fa: "\f015\f015"; }
.fa-home-user { --fa: "\e1b0"; --fa--fa: "\e1b0\e1b0"; }
.fa-hospital-symbol { --fa: "\f47e"; --fa--fa: "\f47e\f47e"; }
.fa-hourglass { --fa: "\f254"; --fa--fa: "\f254\f254"; }
.fa-hourglass-2 { --fa: "\f252"; --fa--fa: "\f252\f252"; }
.fa-hourglass-empty { --fa: "\f254"; --fa--fa: "\f254\f254"; }
.fa-hourglass-half { --fa: "\f252"; --fa--fa: "\f252\f252"; }
.fa-house { --fa: "\f015"; --fa--fa: "\f015\f015"; }
.fa-house-blank { --fa: "\e487"; --fa--fa: "\e487\e487"; }
.fa-house-building { --fa: "\e1b1"; --fa--fa: "\e1b1\e1b1"; }
.fa-house-chimney { --fa: "\e3af"; --fa--fa: "\e3af\e3af"; }
.fa-house-chimney-blank { --fa: "\e3b0"; --fa--fa: "\e3b0\e3b0"; }
.fa-house-chimney-crack { --fa: "\f6f1"; --fa--fa: "\f6f1\f6f1"; }
.fa-house-chimney-heart { --fa: "\e1b2"; --fa--fa: "\e1b2\e1b2"; }
.fa-house-chimney-medical { --fa: "\f7f2"; --fa--fa: "\f7f2\f7f2"; }
.fa-house-chimney-window { --fa: "\e00d"; --fa--fa: "\e00d\e00d"; }
.fa-house-crack { --fa: "\e3b1"; --fa--fa: "\e3b1\e3b1"; }
.fa-house-damage { --fa: "\f6f1"; --fa--fa: "\f6f1\f6f1"; }
.fa-house-fire { --fa: "\e50c"; --fa--fa: "\e50c\e50c"; }
.fa-house-flag { --fa: "\e50d"; --fa--fa: "\e50d\e50d"; }
.fa-house-flood { --fa: "\f74f"; --fa--fa: "\f74f\f74f"; }
.fa-house-flood-water { --fa: "\e50e"; --fa--fa: "\e50e\e50e"; }
.fa-house-heart { --fa: "\f4c9"; --fa--fa: "\f4c9\f4c9"; }
.fa-house-leave { --fa: "\e00f"; --fa--fa: "\e00f\e00f"; }
.fa-house-medical { --fa: "\e3b2"; --fa--fa: "\e3b2\e3b2"; }
.fa-house-medical-flag { --fa: "\e514"; --fa--fa: "\e514\e514"; }
.fa-house-person-arrive { --fa: "\e011"; --fa--fa: "\e011\e011"; }
.fa-house-person-depart { --fa: "\e00f"; --fa--fa: "\e00f\e00f"; }
.fa-house-person-leave { --fa: "\e00f"; --fa--fa: "\e00f\e00f"; }
.fa-house-person-return { --fa: "\e011"; --fa--fa: "\e011\e011"; }
.fa-house-return { --fa: "\e011"; --fa--fa: "\e011\e011"; }
.fa-house-tree { --fa: "\e1b3"; --fa--fa: "\e1b3\e1b3"; }
.fa-house-tsunami { --fa: "\e515"; --fa--fa: "\e515\e515"; }
.fa-house-user { --fa: "\e1b0"; --fa--fa: "\e1b0\e1b0"; }
.fa-house-water { --fa: "\f74f"; --fa--fa: "\f74f\f74f"; }
.fa-house-window { --fa: "\e3b3"; --fa--fa: "\e3b3\e3b3"; }
.fa-hurricane { --fa: "\f751"; --fa--fa: "\f751\f751"; }
.fa-i-cursor { --fa: "\f246"; --fa--fa: "\f246\f246"; }
.fa-id-badge { --fa: "\f2c1"; --fa--fa: "\f2c1\f2c1"; }
.fa-id-card { --fa: "\f2c2"; --fa--fa: "\f2c2\f2c2"; }
.fa-id-card-alt { --fa: "\f47f"; --fa--fa: "\f47f\f47f"; }
.fa-id-card-clip { --fa: "\f47f"; --fa--fa: "\f47f\f47f"; }
.fa-image { --fa: "\f03e"; --fa--fa: "\f03e\f03e"; }
.fa-image-portrait { --fa: "\f3e0"; --fa--fa: "\f3e0\f3e0"; }
.fa-image-user { --fa: "\e1b8"; --fa--fa: "\e1b8\e1b8"; }
.fa-images { --fa: "\f302"; --fa--fa: "\f302\f302"; }
.fa-images-user { --fa: "\e1b9"; --fa--fa: "\e1b9\e1b9"; }
.fa-info { --fa: "\f129"; --fa--fa: "\f129\f129"; }
.fa-info-circle { --fa: "\f05a"; --fa--fa: "\f05a\f05a"; }
.fa-info-square { --fa: "\f30f"; --fa--fa: "\f30f\f30f"; }
.fa-institution { --fa: "\f19c"; --fa--fa: "\f19c\f19c"; }
.fa-key { --fa: "\f084"; --fa--fa: "\f084\f084"; }
.fa-keyboard { --fa: "\f11c"; --fa--fa: "\f11c\f11c"; }
.fa-ladder-water { --fa: "\f5c5"; --fa--fa: "\f5c5\f5c5"; }
.fa-lamp-desk { --fa: "\e014"; --fa--fa: "\e014\e014"; }
.fa-lamp-street { --fa: "\e1c5"; --fa--fa: "\e1c5\e1c5"; }
.fa-landmark { --fa: "\f66f"; --fa--fa: "\f66f\f66f"; }
.fa-landmark-alt { --fa: "\f752"; --fa--fa: "\f752\f752"; }
.fa-landmark-dome { --fa: "\f752"; --fa--fa: "\f752\f752"; }
.fa-landmark-flag { --fa: "\e51c"; --fa--fa: "\e51c\e51c"; }
.fa-language { --fa: "\f1ab"; --fa--fa: "\f1ab\f1ab"; }
.fa-laptop { --fa: "\f109"; --fa--fa: "\f109\f109"; }
.fa-laptop-mobile { --fa: "\f87a"; --fa--fa: "\f87a\f87a"; }
.fa-laptop-slash { --fa: "\e1c7"; --fa--fa: "\e1c7\e1c7"; }
.fa-laugh { --fa: "\f599"; --fa--fa: "\f599\f599"; }
.fa-laugh-beam { --fa: "\f59a"; --fa--fa: "\f59a\f59a"; }
.fa-laugh-wink { --fa: "\f59c"; --fa--fa: "\f59c\f59c"; }
.fa-layer-group { --fa: "\f5fd"; --fa--fa: "\f5fd\f5fd"; }
.fa-leaf { --fa: "\f06c"; --fa--fa: "\f06c\f06c"; }
.fa-leaf-heart { --fa: "\f4cb"; --fa--fa: "\f4cb\f4cb"; }
.fa-leafy-green { --fa: "\e41d"; --fa--fa: "\e41d\e41d"; }
.fa-left { --fa: "\f355"; --fa--fa: "\f355\f355"; }
.fa-light-emergency-on { --fa: "\e420"; --fa--fa: "\e420\e420"; }
.fa-lightbulb { --fa: "\f0eb"; --fa--fa: "\f0eb\f0eb"; }
.fa-lightbulb-on { --fa: "\f672"; --fa--fa: "\f672\f672"; }
.fa-lightbulb-slash { --fa: "\f673"; --fa--fa: "\f673\f673"; }
.fa-link { --fa: "\f0c1"; --fa--fa: "\f0c1\f0c1"; }
.fa-list { --fa: "\f03a"; --fa--fa: "\f03a\f03a"; }
.fa-list-check { --fa: "\f0ae"; --fa--fa: "\f0ae\f0ae"; }
.fa-list-squares { --fa: "\f03a"; --fa--fa: "\f03a\f03a"; }
.fa-list-tree { --fa: "\e1d2"; --fa--fa: "\e1d2\e1d2"; }
.fa-loader { --fa: "\e1d4"; --fa--fa: "\e1d4\e1d4"; }
.fa-location { --fa: "\f601"; --fa--fa: "\f601\f601"; }
.fa-location-arrow { --fa: "\f124"; --fa--fa: "\f124\f124"; }
.fa-location-circle { --fa: "\f602"; --fa--fa: "\f602\f602"; }
.fa-location-crosshairs { --fa: "\f601"; --fa--fa: "\f601\f601"; }
.fa-location-crosshairs-slash { --fa: "\f603"; --fa--fa: "\f603\f603"; }
.fa-location-dot { --fa: "\f3c5"; --fa--fa: "\f3c5\f3c5"; }
.fa-location-dot-slash { --fa: "\f605"; --fa--fa: "\f605\f605"; }
.fa-location-exclamation { --fa: "\f608"; --fa--fa: "\f608\f608"; }
.fa-location-minus { --fa: "\f609"; --fa--fa: "\f609\f609"; }
.fa-location-pin { --fa: "\f041"; --fa--fa: "\f041\f041"; }
.fa-location-pin-slash { --fa: "\f60c"; --fa--fa: "\f60c\f60c"; }
.fa-location-plus { --fa: "\f60a"; --fa--fa: "\f60a\f60a"; }
.fa-location-question { --fa: "\f60b"; --fa--fa: "\f60b\f60b"; }
.fa-location-slash { --fa: "\f603"; --fa--fa: "\f603\f603"; }
.fa-location-smile { --fa: "\f60d"; --fa--fa: "\f60d\f60d"; }
.fa-lock { --fa: "\f023"; --fa--fa: "\f023\f023"; }
.fa-lock-alt { --fa: "\f30d"; --fa--fa: "\f30d\f30d"; }
.fa-lock-keyhole { --fa: "\f30d"; --fa--fa: "\f30d\f30d"; }
.fa-lock-keyhole-open { --fa: "\f3c2"; --fa--fa: "\f3c2\f3c2"; }
.fa-lock-open { --fa: "\f3c1"; --fa--fa: "\f3c1\f3c1"; }
.fa-lock-open-alt { --fa: "\f3c2"; --fa--fa: "\f3c2\f3c2"; }
.fa-long-arrow-down { --fa: "\f175"; --fa--fa: "\f175\f175"; }
.fa-long-arrow-left { --fa: "\f177"; --fa--fa: "\f177\f177"; }
.fa-long-arrow-right { --fa: "\f178"; --fa--fa: "\f178\f178"; }
.fa-long-arrow-up { --fa: "\f176"; --fa--fa: "\f176\f176"; }
.fa-low-vision { --fa: "\f2a8"; --fa--fa: "\f2a8\f2a8"; }
.fa-magnifying-glass { --fa: "\f002"; --fa--fa: "\f002\f002"; }
.fa-magnifying-glass-location { --fa: "\f689"; --fa--fa: "\f689\f689"; }
.fa-magnifying-glass-minus { --fa: "\f010"; --fa--fa: "\f010\f010"; }
.fa-magnifying-glass-plus { --fa: "\f00e"; --fa--fa: "\f00e\f00e"; }
.fa-mail-bulk { --fa: "\f674"; --fa--fa: "\f674\f674"; }
.fa-mail-forward { --fa: "\f064"; --fa--fa: "\f064\f064"; }
.fa-mail-reply { --fa: "\f3e5"; --fa--fa: "\f3e5\f3e5"; }
.fa-mail-reply-all { --fa: "\f122"; --fa--fa: "\f122\f122"; }
.fa-male { --fa: "\f183"; --fa--fa: "\f183\f183"; }
.fa-map-location { --fa: "\f59f"; --fa--fa: "\f59f\f59f"; }
.fa-map-marked { --fa: "\f59f"; --fa--fa: "\f59f\f59f"; }
.fa-map-marker { --fa: "\f041"; --fa--fa: "\f041\f041"; }
.fa-map-marker-alt { --fa: "\f3c5"; --fa--fa: "\f3c5\f3c5"; }
.fa-map-marker-alt-slash { --fa: "\f605"; --fa--fa: "\f605\f605"; }
.fa-map-marker-exclamation { --fa: "\f608"; --fa--fa: "\f608\f608"; }
.fa-map-marker-minus { --fa: "\f609"; --fa--fa: "\f609\f609"; }
.fa-map-marker-plus { --fa: "\f60a"; --fa--fa: "\f60a\f60a"; }
.fa-map-marker-question { --fa: "\f60b"; --fa--fa: "\f60b\f60b"; }
.fa-map-marker-slash { --fa: "\f60c"; --fa--fa: "\f60c\f60c"; }
.fa-map-marker-smile { --fa: "\f60d"; --fa--fa: "\f60d\f60d"; }
.fa-map-pin { --fa: "\f276"; --fa--fa: "\f276\f276"; }
.fa-marker { --fa: "\f5a1"; --fa--fa: "\f5a1\f5a1"; }
.fa-masks-theater { --fa: "\f630"; --fa--fa: "\f630\f630"; }
.fa-medal { --fa: "\f5a2"; --fa--fa: "\f5a2\f5a2"; }
.fa-megaphone { --fa: "\f675"; --fa--fa: "\f675\f675"; }
.fa-message { --fa: "\f27a"; --fa--fa: "\f27a\f27a"; }
.fa-message-check { --fa: "\f4a2"; --fa--fa: "\f4a2\f4a2"; }
.fa-message-dots { --fa: "\f4a3"; --fa--fa: "\f4a3\f4a3"; }
.fa-message-edit { --fa: "\f4a4"; --fa--fa: "\f4a4\f4a4"; }
.fa-message-exclamation { --fa: "\f4a5"; --fa--fa: "\f4a5\f4a5"; }
.fa-message-heart { --fa: "\e5c9"; --fa--fa: "\e5c9\e5c9"; }
.fa-message-image { --fa: "\e1e0"; --fa--fa: "\e1e0\e1e0"; }
.fa-message-lines { --fa: "\f4a6"; --fa--fa: "\f4a6\f4a6"; }
.fa-message-medical { --fa: "\f7f4"; --fa--fa: "\f7f4\f7f4"; }
.fa-message-middle { --fa: "\e1e1"; --fa--fa: "\e1e1\e1e1"; }
.fa-message-minus { --fa: "\f4a7"; --fa--fa: "\f4a7\f4a7"; }
.fa-message-pen { --fa: "\f4a4"; --fa--fa: "\f4a4\f4a4"; }
.fa-message-plus { --fa: "\f4a8"; --fa--fa: "\f4a8\f4a8"; }
.fa-message-question { --fa: "\e1e3"; --fa--fa: "\e1e3\e1e3"; }
.fa-message-quote { --fa: "\e1e4"; --fa--fa: "\e1e4\e1e4"; }
.fa-message-slash { --fa: "\f4a9"; --fa--fa: "\f4a9\f4a9"; }
.fa-message-smile { --fa: "\f4aa"; --fa--fa: "\f4aa\f4aa"; }
.fa-message-sms { --fa: "\e1e5"; --fa--fa: "\e1e5\e1e5"; }
.fa-message-times { --fa: "\f4ab"; --fa--fa: "\f4ab\f4ab"; }
.fa-message-xmark { --fa: "\f4ab"; --fa--fa: "\f4ab\f4ab"; }
.fa-messages { --fa: "\f4b6"; --fa--fa: "\f4b6\f4b6"; }
.fa-messages-question { --fa: "\e1e7"; --fa--fa: "\e1e7\e1e7"; }
.fa-messaging { --fa: "\f4a3"; --fa--fa: "\f4a3\f4a3"; }
.fa-meteor { --fa: "\f753"; --fa--fa: "\f753\f753"; }
.fa-microphone-alt { --fa: "\f3c9"; --fa--fa: "\f3c9\f3c9"; }
.fa-microphone-alt-slash { --fa: "\f539"; --fa--fa: "\f539\f539"; }
.fa-microphone-lines { --fa: "\f3c9"; --fa--fa: "\f3c9\f3c9"; }
.fa-microphone-lines-slash { --fa: "\f539"; --fa--fa: "\f539\f539"; }
.fa-minus { --fa: "\f068"; --fa--fa: "\f068\f068"; }
.fa-minus-circle { --fa: "\f056"; --fa--fa: "\f056\f056"; }
.fa-mobile { --fa: "\f3ce"; --fa--fa: "\f3ce\f3ce"; }
.fa-mobile-android { --fa: "\f3ce"; --fa--fa: "\f3ce\f3ce"; }
.fa-mobile-phone { --fa: "\f3ce"; --fa--fa: "\f3ce\f3ce"; }
.fa-mobile-signal-out { --fa: "\e1f0"; --fa--fa: "\e1f0\e1f0"; }
.fa-moon { --fa: "\f186"; --fa--fa: "\f186\f186"; }
.fa-moon-cloud { --fa: "\f754"; --fa--fa: "\f754\f754"; }
.fa-moon-stars { --fa: "\f755"; --fa--fa: "\f755\f755"; }
.fa-moped { --fa: "\e3b9"; --fa--fa: "\e3b9\e3b9"; }
.fa-mortar-board { --fa: "\f19d"; --fa--fa: "\f19d\f19d"; }
.fa-motorcycle { --fa: "\f21c"; --fa--fa: "\f21c\f21c"; }
.fa-mountain { --fa: "\f6fc"; --fa--fa: "\f6fc\f6fc"; }
.fa-mountain-city { --fa: "\e52e"; --fa--fa: "\e52e\e52e"; }
.fa-mountain-sun { --fa: "\e52f"; --fa--fa: "\e52f\e52f"; }
.fa-mouse-alt { --fa: "\f8cd"; --fa--fa: "\f8cd\f8cd"; }
.fa-mouse-pointer { --fa: "\f245"; --fa--fa: "\f245\f245"; }
.fa-mug-tea { --fa: "\f875"; --fa--fa: "\f875\f875"; }
.fa-multiply { --fa: "\f00d"; --fa--fa: "\f00d\f00d"; }
.fa-museum { --fa: "\f19c"; --fa--fa: "\f19c\f19c"; }
.fa-music { --fa: "\f001"; --fa--fa: "\f001\f001"; }
.fa-music-slash { --fa: "\f8d1"; --fa--fa: "\f8d1\f8d1"; }
.fa-navicon { --fa: "\f0c9"; --fa--fa: "\f0c9\f0c9"; }
.fa-newspaper { --fa: "\f1ea"; --fa--fa: "\f1ea\f1ea"; }
.fa-note-sticky { --fa: "\f249"; --fa--fa: "\f249\f249"; }
.fa-notebook { --fa: "\e201"; --fa--fa: "\e201\e201"; }
.fa-notes-medical { --fa: "\f481"; --fa--fa: "\f481\f481"; }
.fa-octagon-xmark { --fa: "\f2f0"; --fa--fa: "\f2f0\f2f0"; }
.fa-page { --fa: "\e428"; --fa--fa: "\e428\e428"; }
.fa-paint-roller { --fa: "\f5aa"; --fa--fa: "\f5aa\f5aa"; }
.fa-palette { --fa: "\f53f"; --fa--fa: "\f53f\f53f"; }
.fa-paper-plane { --fa: "\f1d8"; --fa--fa: "\f1d8\f1d8"; }
.fa-paper-plane-alt { --fa: "\e20a"; --fa--fa: "\e20a\e20a"; }
.fa-paper-plane-top { --fa: "\e20a"; --fa--fa: "\e20a\e20a"; }
.fa-paperclip { --fa: "\f0c6"; --fa--fa: "\f0c6\f0c6"; }
.fa-paperclip-vertical { --fa: "\e3c2"; --fa--fa: "\e3c2\e3c2"; }
.fa-parking { --fa: "\f540"; --fa--fa: "\f540\f540"; }
.fa-parking-circle-slash { --fa: "\f616"; --fa--fa: "\f616\f616"; }
.fa-party-back { --fa: "\e45c"; --fa--fa: "\e45c\e45c"; }
.fa-party-horn { --fa: "\e31b"; --fa--fa: "\e31b\e31b"; }
.fa-paste { --fa: "\f0ea"; --fa--fa: "\f0ea\f0ea"; }
.fa-pause { --fa: "\f04c"; --fa--fa: "\f04c\f04c"; }
.fa-pen { --fa: "\f304"; --fa--fa: "\f304\f304"; }
.fa-pen-line { --fa: "\e212"; --fa--fa: "\e212\e212"; }
.fa-pen-ruler { --fa: "\f5ae"; --fa--fa: "\f5ae\f5ae"; }
.fa-pen-slash { --fa: "\e213"; --fa--fa: "\e213\e213"; }
.fa-pen-to-square { --fa: "\f044"; --fa--fa: "\f044\f044"; }
.fa-pencil { --fa: "\f303"; --fa--fa: "\f303\f303"; }
.fa-pencil-alt { --fa: "\f303"; --fa--fa: "\f303\f303"; }
.fa-pencil-ruler { --fa: "\f5ae"; --fa--fa: "\f5ae\f5ae"; }
.fa-people { --fa: "\e216"; --fa--fa: "\e216\e216"; }
.fa-people-dress { --fa: "\e217"; --fa--fa: "\e217\e217"; }
.fa-people-dress-simple { --fa: "\e218"; --fa--fa: "\e218\e218"; }
.fa-people-group { --fa: "\e533"; --fa--fa: "\e533\e533"; }
.fa-people-pants { --fa: "\e219"; --fa--fa: "\e219\e219"; }
.fa-people-pants-simple { --fa: "\e21a"; --fa--fa: "\e21a\e21a"; }
.fa-people-simple { --fa: "\e21b"; --fa--fa: "\e21b\e21b"; }
.fa-pepper-hot { --fa: "\f816"; --fa--fa: "\f816\f816"; }
.fa-percent { --fa: "\25"; --fa--fa: "\25\25"; }
.fa-percentage { --fa: "\25"; --fa--fa: "\25\25"; }
.fa-period { --fa: "\2e"; --fa--fa: "\2e\2e"; }
.fa-person { --fa: "\f183"; --fa--fa: "\f183\f183"; }
.fa-person-biking { --fa: "\f84a"; --fa--fa: "\f84a\f84a"; }
.fa-person-booth { --fa: "\f756"; --fa--fa: "\f756\f756"; }
.fa-person-cane { --fa: "\e53c"; --fa--fa: "\e53c\e53c"; }
.fa-person-chalkboard { --fa: "\e53d"; --fa--fa: "\e53d\e53d"; }
.fa-person-digging { --fa: "\f85e"; --fa--fa: "\f85e\f85e"; }
.fa-person-dress { --fa: "\f182"; --fa--fa: "\f182\f182"; }
.fa-person-dress-fairy { --fa: "\e607"; --fa--fa: "\e607\e607"; }
.fa-person-dress-simple { --fa: "\e21c"; --fa--fa: "\e21c\e21c"; }
.fa-person-drowning { --fa: "\e545"; --fa--fa: "\e545\e545"; }
.fa-person-falling { --fa: "\e546"; --fa--fa: "\e546\e546"; }
.fa-person-half-dress { --fa: "\e548"; --fa--fa: "\e548\e548"; }
.fa-person-harassing { --fa: "\e549"; --fa--fa: "\e549\e549"; }
.fa-person-hiking { --fa: "\f6ec"; --fa--fa: "\f6ec\f6ec"; }
.fa-person-military-pointing { --fa: "\e54a"; --fa--fa: "\e54a\e54a"; }
.fa-person-praying { --fa: "\f683"; --fa--fa: "\f683\f683"; }
.fa-person-pregnant { --fa: "\e31e"; --fa--fa: "\e31e\e31e"; }
.fa-person-rifle { --fa: "\e54e"; --fa--fa: "\e54e\e54e"; }
.fa-person-running { --fa: "\f70c"; --fa--fa: "\f70c\f70c"; }
.fa-person-sign { --fa: "\f757"; --fa--fa: "\f757\f757"; }
.fa-person-simple { --fa: "\e220"; --fa--fa: "\e220\e220"; }
.fa-person-skating { --fa: "\f7c5"; --fa--fa: "\f7c5\f7c5"; }
.fa-person-swimming { --fa: "\f5c4"; --fa--fa: "\f5c4\f5c4"; }
.fa-person-to-door { --fa: "\e433"; --fa--fa: "\e433\e433"; }
.fa-person-walking { --fa: "\f554"; --fa--fa: "\f554\f554"; }
.fa-person-walking-arrow-loop-left { --fa: "\e551"; --fa--fa: "\e551\e551"; }
.fa-person-walking-arrow-right { --fa: "\e552"; --fa--fa: "\e552\e552"; }
.fa-person-walking-luggage { --fa: "\e554"; --fa--fa: "\e554\e554"; }
.fa-person-walking-with-cane { --fa: "\f29d"; --fa--fa: "\f29d\f29d"; }
.fa-phone { --fa: "\f095"; --fa--fa: "\f095\f095"; }
.fa-phone-laptop { --fa: "\f87a"; --fa--fa: "\f87a\f87a"; }
.fa-phone-missed { --fa: "\e226"; --fa--fa: "\e226\e226"; }
.fa-phone-office { --fa: "\f67d"; --fa--fa: "\f67d\f67d"; }
.fa-phone-plus { --fa: "\f4d2"; --fa--fa: "\f4d2\f4d2"; }
.fa-phone-slash { --fa: "\f3dd"; --fa--fa: "\f3dd\f3dd"; }
.fa-phone-volume { --fa: "\f2a0"; --fa--fa: "\f2a0\f2a0"; }
.fa-photo-film { --fa: "\f87c"; --fa--fa: "\f87c\f87c"; }
.fa-photo-film-music { --fa: "\e228"; --fa--fa: "\e228\e228"; }
.fa-photo-video { --fa: "\f87c"; --fa--fa: "\f87c\f87c"; }
.fa-pie-chart { --fa: "\f200"; --fa--fa: "\f200\f200"; }
.fa-pizza-slice { --fa: "\f818"; --fa--fa: "\f818\f818"; }
.fa-plane { --fa: "\f072"; --fa--fa: "\f072\f072"; }
.fa-plane-departure { --fa: "\f5b0"; --fa--fa: "\f5b0\f5b0"; }
.fa-plane-up { --fa: "\e22d"; --fa--fa: "\e22d\e22d"; }
.fa-plane-up-slash { --fa: "\e22e"; --fa--fa: "\e22e\e22e"; }
.fa-plate-utensils { --fa: "\e43b"; --fa--fa: "\e43b\e43b"; }
.fa-play { --fa: "\f04b"; --fa--fa: "\f04b\f04b"; }
.fa-play-circle { --fa: "\f144"; --fa--fa: "\f144\f144"; }
.fa-play-pause { --fa: "\e22f"; --fa--fa: "\e22f\e22f"; }
.fa-plug { --fa: "\f1e6"; --fa--fa: "\f1e6\f1e6"; }
.fa-plus { --fa: "\2b"; --fa--fa: "\2b\2b"; }
.fa-plus-circle { --fa: "\f055"; --fa--fa: "\f055\f055"; }
.fa-plus-large { --fa: "\e59e"; --fa--fa: "\e59e\e59e"; }
.fa-podcast { --fa: "\f2ce"; --fa--fa: "\f2ce\f2ce"; }
.fa-podium { --fa: "\f680"; --fa--fa: "\f680\f680"; }
.fa-portrait { --fa: "\f3e0"; --fa--fa: "\f3e0\f3e0"; }
.fa-pot-food { --fa: "\e43f"; --fa--fa: "\e43f\e43f"; }
.fa-power-off { --fa: "\f011"; --fa--fa: "\f011\f011"; }
.fa-pray { --fa: "\f683"; --fa--fa: "\f683\f683"; }
.fa-print { --fa: "\f02f"; --fa--fa: "\f02f\f02f"; }
.fa-procedures { --fa: "\f487"; --fa--fa: "\f487\f487"; }
.fa-puzzle { --fa: "\e443"; --fa--fa: "\e443\e443"; }
.fa-puzzle-piece { --fa: "\f12e"; --fa--fa: "\f12e\f12e"; }
.fa-question { --fa: "\3f"; --fa--fa: "\3f\3f"; }
.fa-question-circle { --fa: "\f059"; --fa--fa: "\f059\f059"; }
.fa-question-square { --fa: "\f2fd"; --fa--fa: "\f2fd\f2fd"; }
.fa-quote-left { --fa: "\f10d"; --fa--fa: "\f10d\f10d"; }
.fa-quote-left-alt { --fa: "\f10d"; --fa--fa: "\f10d\f10d"; }
.fa-quote-right { --fa: "\f10e"; --fa--fa: "\f10e\f10e"; }
.fa-quote-right-alt { --fa: "\f10e"; --fa--fa: "\f10e\f10e"; }
.fa-radio { --fa: "\f8d7"; --fa--fa: "\f8d7\f8d7"; }
.fa-raindrops { --fa: "\f75c"; --fa--fa: "\f75c\f75c"; }
.fa-receipt { --fa: "\f543"; --fa--fa: "\f543\f543"; }
.fa-rectangle-hd { --fa: "\e1ae"; --fa--fa: "\e1ae\e1ae"; }
.fa-rectangle-history { --fa: "\e4a2"; --fa--fa: "\e4a2\e4a2"; }
.fa-recycle { --fa: "\f1b8"; --fa--fa: "\f1b8\f1b8"; }
.fa-refresh { --fa: "\f021"; --fa--fa: "\f021\f021"; }
.fa-remove { --fa: "\f00d"; --fa--fa: "\f00d\f00d"; }
.fa-reorder { --fa: "\f550"; --fa--fa: "\f550\f550"; }
.fa-repeat-1-alt { --fa: "\f366"; --fa--fa: "\f366\f366"; }
.fa-repeat-alt { --fa: "\f364"; --fa--fa: "\f364\f364"; }
.fa-reply { --fa: "\f3e5"; --fa--fa: "\f3e5\f3e5"; }
.fa-reply-all { --fa: "\f122"; --fa--fa: "\f122\f122"; }
.fa-restroom { --fa: "\f7bd"; --fa--fa: "\f7bd\f7bd"; }
.fa-restroom-simple { --fa: "\e23a"; --fa--fa: "\e23a\e23a"; }
.fa-retweet-alt { --fa: "\f361"; --fa--fa: "\f361\f361"; }
.fa-right { --fa: "\f356"; --fa--fa: "\f356\f356"; }
.fa-right-left-large { --fa: "\e5e1"; --fa--fa: "\e5e1\e5e1"; }
.fa-road { --fa: "\f018"; --fa--fa: "\f018\f018"; }
.fa-road-barrier { --fa: "\e562"; --fa--fa: "\e562\e562"; }
.fa-robot { --fa: "\f544"; --fa--fa: "\f544\f544"; }
.fa-rocket { --fa: "\f135"; --fa--fa: "\f135\f135"; }
.fa-rocket-launch { --fa: "\e027"; --fa--fa: "\e027\e027"; }
.fa-rod-asclepius { --fa: "\e579"; --fa--fa: "\e579\e579"; }
.fa-rod-snake { --fa: "\e579"; --fa--fa: "\e579\e579"; }
.fa-route { --fa: "\f4d7"; --fa--fa: "\f4d7\f4d7"; }
.fa-rss { --fa: "\f09e"; --fa--fa: "\f09e\f09e"; }
.fa-ruler { --fa: "\f545"; --fa--fa: "\f545\f545"; }
.fa-ruler-combined { --fa: "\f546"; --fa--fa: "\f546\f546"; }
.fa-ruler-horizontal { --fa: "\f547"; --fa--fa: "\f547\f547"; }
.fa-ruler-triangle { --fa: "\f61c"; --fa--fa: "\f61c\f61c"; }
.fa-ruler-vertical { --fa: "\f548"; --fa--fa: "\f548\f548"; }
.fa-running { --fa: "\f70c"; --fa--fa: "\f70c\f70c"; }
.fa-sailboat { --fa: "\e445"; --fa--fa: "\e445\e445"; }
.fa-salad { --fa: "\f81e"; --fa--fa: "\f81e\f81e"; }
.fa-sausage { --fa: "\f820"; --fa--fa: "\f820\f820"; }
.fa-save { --fa: "\f0c7"; --fa--fa: "\f0c7\f0c7"; }
.fa-scale-balanced { --fa: "\f24e"; --fa--fa: "\f24e\f24e"; }
.fa-school { --fa: "\f549"; --fa--fa: "\f549\f549"; }
.fa-scissors { --fa: "\f0c4"; --fa--fa: "\f0c4\f0c4"; }
.fa-screen-users { --fa: "\f63d"; --fa--fa: "\f63d\f63d"; }
.fa-screencast { --fa: "\e23e"; --fa--fa: "\e23e\e23e"; }
.fa-screwdriver-wrench { --fa: "\f7d9"; --fa--fa: "\f7d9\f7d9"; }
.fa-seal { --fa: "\e241"; --fa--fa: "\e241\e241"; }
.fa-seal-exclamation { --fa: "\e242"; --fa--fa: "\e242\e242"; }
.fa-seal-question { --fa: "\e243"; --fa--fa: "\e243\e243"; }
.fa-search { --fa: "\f002"; --fa--fa: "\f002\f002"; }
.fa-search-location { --fa: "\f689"; --fa--fa: "\f689\f689"; }
.fa-search-minus { --fa: "\f010"; --fa--fa: "\f010\f010"; }
.fa-search-plus { --fa: "\f00e"; --fa--fa: "\f00e\f00e"; }
.fa-seedling { --fa: "\f4d8"; --fa--fa: "\f4d8\f4d8"; }
.fa-send { --fa: "\e20a"; --fa--fa: "\e20a\e20a"; }
.fa-shapes { --fa: "\f61f"; --fa--fa: "\f61f\f61f"; }
.fa-share { --fa: "\f064"; --fa--fa: "\f064\f064"; }
.fa-share-all { --fa: "\f367"; --fa--fa: "\f367\f367"; }
.fa-share-alt { --fa: "\f1e0"; --fa--fa: "\f1e0\f1e0"; }
.fa-share-nodes { --fa: "\f1e0"; --fa--fa: "\f1e0\f1e0"; }
.fa-shield { --fa: "\f132"; --fa--fa: "\f132\f132"; }
.fa-shield-blank { --fa: "\f132"; --fa--fa: "\f132\f132"; }
.fa-shield-check { --fa: "\f2f7"; --fa--fa: "\f2f7\f2f7"; }
.fa-shield-slash { --fa: "\e24b"; --fa--fa: "\e24b\e24b"; }
.fa-ship { --fa: "\f21a"; --fa--fa: "\f21a\f21a"; }
.fa-shipping-timed { --fa: "\f48c"; --fa--fa: "\f48c\f48c"; }
.fa-shop { --fa: "\f54f"; --fa--fa: "\f54f\f54f"; }
.fa-shopping-bag { --fa: "\f290"; --fa--fa: "\f290\f290"; }
.fa-shopping-basket { --fa: "\f291"; --fa--fa: "\f291\f291"; }
.fa-shopping-basket-alt { --fa: "\e0af"; --fa--fa: "\e0af\e0af"; }
.fa-shopping-cart { --fa: "\f07a"; --fa--fa: "\f07a\f07a"; }
.fa-shower { --fa: "\f2cc"; --fa--fa: "\f2cc\f2cc"; }
.fa-shuttle-van { --fa: "\f5b6"; --fa--fa: "\f5b6\f5b6"; }
.fa-sign-in { --fa: "\f090"; --fa--fa: "\f090\f090"; }
.fa-sign-language { --fa: "\f2a7"; --fa--fa: "\f2a7\f2a7"; }
.fa-sign-out { --fa: "\f08b"; --fa--fa: "\f08b\f08b"; }
.fa-signing { --fa: "\f2a7"; --fa--fa: "\f2a7\f2a7"; }
.fa-siren { --fa: "\e02d"; --fa--fa: "\e02d\e02d"; }
.fa-siren-on { --fa: "\e02e"; --fa--fa: "\e02e\e02e"; }
.fa-sitemap { --fa: "\f0e8"; --fa--fa: "\f0e8\f0e8"; }
.fa-skating { --fa: "\f7c5"; --fa--fa: "\f7c5\f7c5"; }
.fa-sliders { --fa: "\f1de"; --fa--fa: "\f1de\f1de"; }
.fa-sliders-h { --fa: "\f1de"; --fa--fa: "\f1de\f1de"; }
.fa-smile { --fa: "\f118"; --fa--fa: "\f118\f118"; }
.fa-smile-beam { --fa: "\f5b8"; --fa--fa: "\f5b8\f5b8"; }
.fa-smile-wink { --fa: "\f4da"; --fa--fa: "\f4da\f4da"; }
.fa-snowflake { --fa: "\f2dc"; --fa--fa: "\f2dc\f2dc"; }
.fa-snowplow { --fa: "\f7d2"; --fa--fa: "\f7d2\f7d2"; }
.fa-soccer-ball { --fa: "\f1e3"; --fa--fa: "\f1e3\f1e3"; }
.fa-solar-panel { --fa: "\f5ba"; --fa--fa: "\f5ba\f5ba"; }
.fa-sort { --fa: "\f0dc"; --fa--fa: "\f0dc\f0dc"; }
.fa-sort-alpha-asc { --fa: "\f15d"; --fa--fa: "\f15d\f15d"; }
.fa-sort-alpha-desc { --fa: "\f881"; --fa--fa: "\f881\f881"; }
.fa-sort-alpha-down { --fa: "\f15d"; --fa--fa: "\f15d\f15d"; }
.fa-sort-alpha-down-alt { --fa: "\f881"; --fa--fa: "\f881\f881"; }
.fa-sort-alpha-up { --fa: "\f15e"; --fa--fa: "\f15e\f15e"; }
.fa-sort-alt { --fa: "\f883"; --fa--fa: "\f883\f883"; }
.fa-sort-amount-asc { --fa: "\f160"; --fa--fa: "\f160\f160"; }
.fa-sort-amount-desc { --fa: "\f884"; --fa--fa: "\f884\f884"; }
.fa-sort-amount-down { --fa: "\f160"; --fa--fa: "\f160\f160"; }
.fa-sort-amount-down-alt { --fa: "\f884"; --fa--fa: "\f884\f884"; }
.fa-sort-amount-up { --fa: "\f161"; --fa--fa: "\f161\f161"; }
.fa-sort-amount-up-alt { --fa: "\f885"; --fa--fa: "\f885\f885"; }
.fa-sort-numeric-asc { --fa: "\f162"; --fa--fa: "\f162\f162"; }
.fa-sort-numeric-desc { --fa: "\f886"; --fa--fa: "\f886\f886"; }
.fa-sort-numeric-down { --fa: "\f162"; --fa--fa: "\f162\f162"; }
.fa-sort-numeric-down-alt { --fa: "\f886"; --fa--fa: "\f886\f886"; }
.fa-sort-numeric-up { --fa: "\f163"; --fa--fa: "\f163\f163"; }
.fa-sort-up-down { --fa: "\e099"; --fa--fa: "\e099\e099"; }
.fa-sparkles { --fa: "\f890"; --fa--fa: "\f890\f890"; }
.fa-spinner { --fa: "\f110"; --fa--fa: "\f110\f110"; }
.fa-spinner-third { --fa: "\f3f4"; --fa--fa: "\f3f4\f3f4"; }
.fa-sprout { --fa: "\f4d8"; --fa--fa: "\f4d8\f4d8"; }
.fa-square { --fa: "\f0c8"; --fa--fa: "\f0c8\f0c8"; }
.fa-square-caret-down { --fa: "\f150"; --fa--fa: "\f150\f150"; }
.fa-square-caret-left { --fa: "\f191"; --fa--fa: "\f191\f191"; }
.fa-square-caret-right { --fa: "\f152"; --fa--fa: "\f152\f152"; }
.fa-square-caret-up { --fa: "\f151"; --fa--fa: "\f151\f151"; }
.fa-square-check { --fa: "\f14a"; --fa--fa: "\f14a\f14a"; }
.fa-square-full { --fa: "\f45c"; --fa--fa: "\f45c\f45c"; }
.fa-square-info { --fa: "\f30f"; --fa--fa: "\f30f\f30f"; }
.fa-square-parking { --fa: "\f540"; --fa--fa: "\f540\f540"; }
.fa-square-question { --fa: "\f2fd"; --fa--fa: "\f2fd\f2fd"; }
.fa-square-small { --fa: "\e27e"; --fa--fa: "\e27e\e27e"; }
.fa-square-xmark { --fa: "\f2d3"; --fa--fa: "\f2d3\f2d3"; }
.fa-squirrel { --fa: "\f71a"; --fa--fa: "\f71a\f71a"; }
.fa-staff-aesculapius { --fa: "\e579"; --fa--fa: "\e579\e579"; }
.fa-staff-snake { --fa: "\e579"; --fa--fa: "\e579\e579"; }
.fa-stairs { --fa: "\e289"; --fa--fa: "\e289\e289"; }
.fa-star { --fa: "\f005"; --fa--fa: "\f005\f005"; }
.fa-star-half { --fa: "\f089"; --fa--fa: "\f089\f089"; }
.fa-star-half-alt { --fa: "\f5c0"; --fa--fa: "\f5c0\f5c0"; }
.fa-star-half-stroke { --fa: "\f5c0"; --fa--fa: "\f5c0\f5c0"; }
.fa-star-of-life { --fa: "\f621"; --fa--fa: "\f621\f621"; }
.fa-step-backward { --fa: "\f048"; --fa--fa: "\f048\f048"; }
.fa-step-forward { --fa: "\f051"; --fa--fa: "\f051\f051"; }
.fa-stethoscope { --fa: "\f0f1"; --fa--fa: "\f0f1\f0f1"; }
.fa-sticky-note { --fa: "\f249"; --fa--fa: "\f249\f249"; }
.fa-stop { --fa: "\f04d"; --fa--fa: "\f04d\f04d"; }
.fa-stopwatch { --fa: "\f2f2"; --fa--fa: "\f2f2\f2f2"; }
.fa-store { --fa: "\f54e"; --fa--fa: "\f54e\f54e"; }
.fa-store-alt { --fa: "\f54f"; --fa--fa: "\f54f\f54f"; }
.fa-stream { --fa: "\f550"; --fa--fa: "\f550\f550"; }
.fa-street-view { --fa: "\f21d"; --fa--fa: "\f21d\f21d"; }
.fa-subtitles { --fa: "\e60f"; --fa--fa: "\e60f\e60f"; }
.fa-subtract { --fa: "\f068"; --fa--fa: "\f068\f068"; }
.fa-subway { --fa: "\f239"; --fa--fa: "\f239\f239"; }
.fa-subway-tunnel { --fa: "\e2a3"; --fa--fa: "\e2a3\e2a3"; }
.fa-suitcase-rolling { --fa: "\f5c1"; --fa--fa: "\f5c1\f5c1"; }
.fa-sun { --fa: "\f185"; --fa--fa: "\f185\f185"; }
.fa-sun-cloud { --fa: "\f763"; --fa--fa: "\f763\f763"; }
.fa-sun-dust { --fa: "\f764"; --fa--fa: "\f764\f764"; }
.fa-sun-haze { --fa: "\f765"; --fa--fa: "\f765\f765"; }
.fa-sunglasses { --fa: "\f892"; --fa--fa: "\f892\f892"; }
.fa-sunrise { --fa: "\f766"; --fa--fa: "\f766\f766"; }
.fa-sunset { --fa: "\f767"; --fa--fa: "\f767\f767"; }
.fa-swap-arrows { --fa: "\e60a"; --fa--fa: "\e60a\e60a"; }
.fa-swimmer { --fa: "\f5c4"; --fa--fa: "\f5c4\f5c4"; }
.fa-swimming-pool { --fa: "\f5c5"; --fa--fa: "\f5c5\f5c5"; }
.fa-sync { --fa: "\f021"; --fa--fa: "\f021\f021"; }
.fa-table-cells { --fa: "\f00a"; --fa--fa: "\f00a\f00a"; }
.fa-tablet { --fa: "\f3fb"; --fa--fa: "\f3fb\f3fb"; }
.fa-tablet-android { --fa: "\f3fb"; --fa--fa: "\f3fb\f3fb"; }
.fa-tachometer-alt-average { --fa: "\f624"; --fa--fa: "\f624\f624"; }
.fa-tag { --fa: "\f02b"; --fa--fa: "\f02b\f02b"; }
.fa-tags { --fa: "\f02c"; --fa--fa: "\f02c\f02c"; }
.fa-tasks { --fa: "\f0ae"; --fa--fa: "\f0ae\f0ae"; }
.fa-taxi { --fa: "\f1ba"; --fa--fa: "\f1ba\f1ba"; }
.fa-television { --fa: "\f26c"; --fa--fa: "\f26c\f26c"; }
.fa-temperature-3 { --fa: "\f2c8"; --fa--fa: "\f2c8\f2c8"; }
.fa-temperature-frigid { --fa: "\f768"; --fa--fa: "\f768\f768"; }
.fa-temperature-hot { --fa: "\f76a"; --fa--fa: "\f76a\f76a"; }
.fa-temperature-snow { --fa: "\f768"; --fa--fa: "\f768\f768"; }
.fa-temperature-sun { --fa: "\f76a"; --fa--fa: "\f76a\f76a"; }
.fa-temperature-three-quarters { --fa: "\f2c8"; --fa--fa: "\f2c8\f2c8"; }
.fa-th { --fa: "\f00a"; --fa--fa: "\f00a\f00a"; }
.fa-theater-masks { --fa: "\f630"; --fa--fa: "\f630\f630"; }
.fa-thermometer-3 { --fa: "\f2c8"; --fa--fa: "\f2c8\f2c8"; }
.fa-thermometer-three-quarters { --fa: "\f2c8"; --fa--fa: "\f2c8\f2c8"; }
.fa-thought-bubble { --fa: "\e32e"; --fa--fa: "\e32e\e32e"; }
.fa-thumb-tack { --fa: "\f08d"; --fa--fa: "\f08d\f08d"; }
.fa-thumbs-down { --fa: "\f165"; --fa--fa: "\f165\f165"; }
.fa-thumbs-up { --fa: "\f164"; --fa--fa: "\f164\f164"; }
.fa-thumbtack { --fa: "\f08d"; --fa--fa: "\f08d\f08d"; }
.fa-thunderstorm { --fa: "\f76c"; --fa--fa: "\f76c\f76c"; }
.fa-thunderstorm-moon { --fa: "\f76d"; --fa--fa: "\f76d\f76d"; }
.fa-thunderstorm-sun { --fa: "\f76e"; --fa--fa: "\f76e\f76e"; }
.fa-ticket-perforated { --fa: "\e63e"; --fa--fa: "\e63e\e63e"; }
.fa-timeline-arrow { --fa: "\e29d"; --fa--fa: "\e29d\e29d"; }
.fa-times { --fa: "\f00d"; --fa--fa: "\f00d\f00d"; }
.fa-times-circle { --fa: "\f057"; --fa--fa: "\f057\f057"; }
.fa-times-octagon { --fa: "\f2f0"; --fa--fa: "\f2f0\f2f0"; }
.fa-times-square { --fa: "\f2d3"; --fa--fa: "\f2d3\f2d3"; }
.fa-times-to-slot { --fa: "\f771"; --fa--fa: "\f771\f771"; }
.fa-tint { --fa: "\f043"; --fa--fa: "\f043\f043"; }
.fa-toggle-large-off { --fa: "\e5b0"; --fa--fa: "\e5b0\e5b0"; }
.fa-toggle-large-on { --fa: "\e5b1"; --fa--fa: "\e5b1\e5b1"; }
.fa-toggle-off { --fa: "\f204"; --fa--fa: "\f204\f204"; }
.fa-toggle-on { --fa: "\f205"; --fa--fa: "\f205\f205"; }
.fa-tools { --fa: "\f7d9"; --fa--fa: "\f7d9\f7d9"; }
.fa-tractor { --fa: "\f722"; --fa--fa: "\f722\f722"; }
.fa-traffic-cone { --fa: "\f636"; --fa--fa: "\f636\f636"; }
.fa-train { --fa: "\f238"; --fa--fa: "\f238\f238"; }
.fa-train-subway { --fa: "\f239"; --fa--fa: "\f239\f239"; }
.fa-train-subway-tunnel { --fa: "\e2a3"; --fa--fa: "\e2a3\e2a3"; }
.fa-train-track { --fa: "\e453"; --fa--fa: "\e453\e453"; }
.fa-train-tram { --fa: "\e5b4"; --fa--fa: "\e5b4\e5b4"; }
.fa-train-tunnel { --fa: "\e454"; --fa--fa: "\e454\e454"; }
.fa-tram { --fa: "\f7da"; --fa--fa: "\f7da\f7da"; }
.fa-transgender { --fa: "\f225"; --fa--fa: "\f225\f225"; }
.fa-transgender-alt { --fa: "\f225"; --fa--fa: "\f225\f225"; }
.fa-trash { --fa: "\f1f8"; --fa--fa: "\f1f8\f1f8"; }
.fa-trash-can-xmark { --fa: "\e2ae"; --fa--fa: "\e2ae\e2ae"; }
.fa-trash-check { --fa: "\e2af"; --fa--fa: "\e2af\e2af"; }
.fa-trash-plus { --fa: "\e2b2"; --fa--fa: "\e2b2\e2b2"; }
.fa-trash-slash { --fa: "\e2b3"; --fa--fa: "\e2b3\e2b3"; }
.fa-trash-xmark { --fa: "\e2b4"; --fa--fa: "\e2b4\e2b4"; }
.fa-tree { --fa: "\f1bb"; --fa--fa: "\f1bb\f1bb"; }
.fa-tree-alt { --fa: "\f400"; --fa--fa: "\f400\f400"; }
.fa-tree-christmas { --fa: "\f7db"; --fa--fa: "\f7db\f7db"; }
.fa-tree-city { --fa: "\e587"; --fa--fa: "\e587\e587"; }
.fa-tree-deciduous { --fa: "\f400"; --fa--fa: "\f400\f400"; }
.fa-tree-decorated { --fa: "\f7dc"; --fa--fa: "\f7dc\f7dc"; }
.fa-tree-large { --fa: "\f7dd"; --fa--fa: "\f7dd\f7dd"; }
.fa-tree-palm { --fa: "\f82b"; --fa--fa: "\f82b\f82b"; }
.fa-trees { --fa: "\f724"; --fa--fa: "\f724\f724"; }
.fa-trian-balbot { --fa: "\e45c"; --fa--fa: "\e45c\e45c"; }
.fa-triangle-circle-square { --fa: "\f61f"; --fa--fa: "\f61f\f61f"; }
.fa-triangle-exclamation { --fa: "\f071"; --fa--fa: "\f071\f071"; }
.fa-triangle-person-digging { --fa: "\f85d"; --fa--fa: "\f85d\f85d"; }
.fa-tricycle { --fa: "\e5c3"; --fa--fa: "\e5c3\e5c3"; }
.fa-trophy-alt { --fa: "\f2eb"; --fa--fa: "\f2eb\f2eb"; }
.fa-trophy-star { --fa: "\f2eb"; --fa--fa: "\f2eb\f2eb"; }
.fa-trowel-bricks { --fa: "\e58a"; --fa--fa: "\e58a\e58a"; }
.fa-truck { --fa: "\f0d1"; --fa--fa: "\f0d1\f0d1"; }
.fa-truck-arrow-right { --fa: "\e58b"; --fa--fa: "\e58b\e58b"; }
.fa-truck-bolt { --fa: "\e3d0"; --fa--fa: "\e3d0\e3d0"; }
.fa-truck-clock { --fa: "\f48c"; --fa--fa: "\f48c\f48c"; }
.fa-truck-container { --fa: "\f4dc"; --fa--fa: "\f4dc\f4dc"; }
.fa-truck-container-empty { --fa: "\e2b5"; --fa--fa: "\e2b5\e2b5"; }
.fa-truck-droplet { --fa: "\e58c"; --fa--fa: "\e58c\e58c"; }
.fa-truck-field { --fa: "\e58d"; --fa--fa: "\e58d\e58d"; }
.fa-truck-medical { --fa: "\f0f9"; --fa--fa: "\f0f9\f0f9"; }
.fa-truck-moving { --fa: "\f4df"; --fa--fa: "\f4df\f4df"; }
.fa-truck-pickup { --fa: "\f63c"; --fa--fa: "\f63c\f63c"; }
.fa-truck-tow { --fa: "\e2b8"; --fa--fa: "\e2b8\e2b8"; }
.fa-trumpet { --fa: "\f8e3"; --fa--fa: "\f8e3\f8e3"; }
.fa-tv { --fa: "\f26c"; --fa--fa: "\f26c\f26c"; }
.fa-tv-alt { --fa: "\f26c"; --fa--fa: "\f26c\f26c"; }
.fa-umbrella { --fa: "\f0e9"; --fa--fa: "\f0e9\f0e9"; }
.fa-umbrella-alt { --fa: "\e2bc"; --fa--fa: "\e2bc\e2bc"; }
.fa-umbrella-beach { --fa: "\f5ca"; --fa--fa: "\f5ca\f5ca"; }
.fa-umbrella-simple { --fa: "\e2bc"; --fa--fa: "\e2bc\e2bc"; }
.fa-undo { --fa: "\f0e2"; --fa--fa: "\f0e2\f0e2"; }
.fa-universal-access { --fa: "\f29a"; --fa--fa: "\f29a\f29a"; }
.fa-university { --fa: "\f19c"; --fa--fa: "\f19c\f19c"; }
.fa-unlock { --fa: "\f09c"; --fa--fa: "\f09c\f09c"; }
.fa-unlock-alt { --fa: "\f13e"; --fa--fa: "\f13e\f13e"; }
.fa-unlock-keyhole { --fa: "\f13e"; --fa--fa: "\f13e\f13e"; }
.fa-unsorted { --fa: "\f0dc"; --fa--fa: "\f0dc\f0dc"; }
.fa-up { --fa: "\f357"; --fa--fa: "\f357\f357"; }
.fa-up-right-from-square { --fa: "\f35d"; --fa--fa: "\f35d\f35d"; }
.fa-upload { --fa: "\f093"; --fa--fa: "\f093\f093"; }
.fa-usb-drive { --fa: "\f8e9"; --fa--fa: "\f8e9\f8e9"; }
.fa-user { --fa: "\f007"; --fa--fa: "\f007\f007"; }
.fa-user-alt { --fa: "\f406"; --fa--fa: "\f406\f406"; }
.fa-user-alt-slash { --fa: "\f4fa"; --fa--fa: "\f4fa\f4fa"; }
.fa-user-astronaut { --fa: "\f4fb"; --fa--fa: "\f4fb\f4fb"; }
.fa-user-check { --fa: "\f4fc"; --fa--fa: "\f4fc\f4fc"; }
.fa-user-chef { --fa: "\e3d2"; --fa--fa: "\e3d2\e3d2"; }
.fa-user-circle { --fa: "\f2bd"; --fa--fa: "\f2bd\f2bd"; }
.fa-user-clock { --fa: "\f4fd"; --fa--fa: "\f4fd\f4fd"; }
.fa-user-cog { --fa: "\f4fe"; --fa--fa: "\f4fe\f4fe"; }
.fa-user-construction { --fa: "\f82c"; --fa--fa: "\f82c\f82c"; }
.fa-user-cowboy { --fa: "\f8ea"; --fa--fa: "\f8ea\f8ea"; }
.fa-user-doctor { --fa: "\f0f0"; --fa--fa: "\f0f0\f0f0"; }
.fa-user-doctor-hair { --fa: "\e458"; --fa--fa: "\e458\e458"; }
.fa-user-doctor-hair-long { --fa: "\e459"; --fa--fa: "\e459\e459"; }
.fa-user-doctor-message { --fa: "\f82e"; --fa--fa: "\f82e\f82e"; }
.fa-user-edit { --fa: "\f4ff"; --fa--fa: "\f4ff\f4ff"; }
.fa-user-friends { --fa: "\f500"; --fa--fa: "\f500\f500"; }
.fa-user-gear { --fa: "\f4fe"; --fa--fa: "\f4fe\f4fe"; }
.fa-user-graduate { --fa: "\f501"; --fa--fa: "\f501\f501"; }
.fa-user-group { --fa: "\f500"; --fa--fa: "\f500\f500"; }
.fa-user-group-simple { --fa: "\e603"; --fa--fa: "\e603\e603"; }
.fa-user-hair { --fa: "\e45a"; --fa--fa: "\e45a\e45a"; }
.fa-user-hair-long { --fa: "\e45b"; --fa--fa: "\e45b\e45b"; }
.fa-user-hair-mullet { --fa: "\e45c"; --fa--fa: "\e45c\e45c"; }
.fa-user-hard-hat { --fa: "\f82c"; --fa--fa: "\f82c\f82c"; }
.fa-user-headset { --fa: "\f82d"; --fa--fa: "\f82d\f82d"; }
.fa-user-helmet-safety { --fa: "\f82c"; --fa--fa: "\f82c\f82c"; }
.fa-user-injured { --fa: "\f728"; --fa--fa: "\f728\f728"; }
.fa-user-large { --fa: "\f406"; --fa--fa: "\f406\f406"; }
.fa-user-large-slash { --fa: "\f4fa"; --fa--fa: "\f4fa\f4fa"; }
.fa-user-lock { --fa: "\f502"; --fa--fa: "\f502\f502"; }
.fa-user-magnifying-glass { --fa: "\e5c5"; --fa--fa: "\e5c5\e5c5"; }
.fa-user-md { --fa: "\f0f0"; --fa--fa: "\f0f0\f0f0"; }
.fa-user-md-chat { --fa: "\f82e"; --fa--fa: "\f82e\f82e"; }
.fa-user-minus { --fa: "\f503"; --fa--fa: "\f503\f503"; }
.fa-user-music { --fa: "\f8eb"; --fa--fa: "\f8eb\f8eb"; }
.fa-user-nurse { --fa: "\f82f"; --fa--fa: "\f82f\f82f"; }
.fa-user-pen { --fa: "\f4ff"; --fa--fa: "\f4ff\f4ff"; }
.fa-user-pilot { --fa: "\e2c0"; --fa--fa: "\e2c0\e2c0"; }
.fa-user-plus { --fa: "\f234"; --fa--fa: "\f234\f234"; }
.fa-user-police { --fa: "\e333"; --fa--fa: "\e333\e333"; }
.fa-user-robot { --fa: "\e04b"; --fa--fa: "\e04b\e04b"; }
.fa-user-secret { --fa: "\f21b"; --fa--fa: "\f21b\f21b"; }
.fa-user-shield { --fa: "\f505"; --fa--fa: "\f505\f505"; }
.fa-user-slash { --fa: "\f506"; --fa--fa: "\f506\f506"; }
.fa-user-tag { --fa: "\f507"; --fa--fa: "\f507\f507"; }
.fa-user-tie { --fa: "\f508"; --fa--fa: "\f508\f508"; }
.fa-user-tie-hair { --fa: "\e45f"; --fa--fa: "\e45f\e45f"; }
.fa-user-tie-hair-long { --fa: "\e460"; --fa--fa: "\e460\e460"; }
.fa-user-times { --fa: "\f235"; --fa--fa: "\f235\f235"; }
.fa-user-unlock { --fa: "\e058"; --fa--fa: "\e058\e058"; }
.fa-user-vneck { --fa: "\e461"; --fa--fa: "\e461\e461"; }
.fa-user-vneck-hair { --fa: "\e462"; --fa--fa: "\e462\e462"; }
.fa-user-vneck-hair-long { --fa: "\e463"; --fa--fa: "\e463\e463"; }
.fa-user-xmark { --fa: "\f235"; --fa--fa: "\f235\f235"; }
.fa-users { --fa: "\f0c0"; --fa--fa: "\f0c0\f0c0"; }
.fa-users-class { --fa: "\f63d"; --fa--fa: "\f63d\f63d"; }
.fa-users-cog { --fa: "\f509"; --fa--fa: "\f509\f509"; }
.fa-users-gear { --fa: "\f509"; --fa--fa: "\f509\f509"; }
.fa-users-medical { --fa: "\f830"; --fa--fa: "\f830\f830"; }
.fa-users-slash { --fa: "\e073"; --fa--fa: "\e073\e073"; }
.fa-utensil-fork { --fa: "\f2e3"; --fa--fa: "\f2e3\f2e3"; }
.fa-utensils { --fa: "\f2e7"; --fa--fa: "\f2e7\f2e7"; }
.fa-utensils-alt { --fa: "\f2e6"; --fa--fa: "\f2e6\f2e6"; }
.fa-utensils-slash { --fa: "\e464"; --fa--fa: "\e464\e464"; }
.fa-van-shuttle { --fa: "\f5b6"; --fa--fa: "\f5b6\f5b6"; }
.fa-vcard { --fa: "\f2bb"; --fa--fa: "\f2bb\f2bb"; }
.fa-venus { --fa: "\f221"; --fa--fa: "\f221\f221"; }
.fa-venus-double { --fa: "\f226"; --fa--fa: "\f226\f226"; }
.fa-venus-mars { --fa: "\f228"; --fa--fa: "\f228\f228"; }
.fa-vial { --fa: "\f492"; --fa--fa: "\f492\f492"; }
.fa-video { --fa: "\f03d"; --fa--fa: "\f03d\f03d"; }
.fa-video-camera { --fa: "\f03d"; --fa--fa: "\f03d\f03d"; }
.fa-virus { --fa: "\e074"; --fa--fa: "\e074\e074"; }
.fa-virus-covid { --fa: "\e4a8"; --fa--fa: "\e4a8\e4a8"; }
.fa-virus-covid-slash { --fa: "\e4a9"; --fa--fa: "\e4a9\e4a9"; }
.fa-virus-slash { --fa: "\e075"; --fa--fa: "\e075\e075"; }
.fa-viruses { --fa: "\e076"; --fa--fa: "\e076\e076"; }
.fa-voicemail { --fa: "\f897"; --fa--fa: "\f897\f897"; }
.fa-volume { --fa: "\f6a8"; --fa--fa: "\f6a8\f6a8"; }
.fa-volume-control-phone { --fa: "\f2a0"; --fa--fa: "\f2a0\f2a0"; }
.fa-volume-down { --fa: "\f027"; --fa--fa: "\f027\f027"; }
.fa-volume-high { --fa: "\f028"; --fa--fa: "\f028\f028"; }
.fa-volume-low { --fa: "\f027"; --fa--fa: "\f027\f027"; }
.fa-volume-medium { --fa: "\f6a8"; --fa--fa: "\f6a8\f6a8"; }
.fa-volume-mute { --fa: "\f6a9"; --fa--fa: "\f6a9\f6a9"; }
.fa-volume-off { --fa: "\f026"; --fa--fa: "\f026\f026"; }
.fa-volume-slash { --fa: "\f2e2"; --fa--fa: "\f2e2\f2e2"; }
.fa-volume-times { --fa: "\f6a9"; --fa--fa: "\f6a9\f6a9"; }
.fa-volume-up { --fa: "\f028"; --fa--fa: "\f028\f028"; }
.fa-volume-xmark { --fa: "\f6a9"; --fa--fa: "\f6a9\f6a9"; }
.fa-vote-nay { --fa: "\f771"; --fa--fa: "\f771\f771"; }
.fa-vote-yea { --fa: "\f772"; --fa--fa: "\f772\f772"; }
.fa-walking { --fa: "\f554"; --fa--fa: "\f554\f554"; }
.fa-warning { --fa: "\f071"; --fa--fa: "\f071\f071"; }
.fa-water { --fa: "\f773"; --fa--fa: "\f773\f773"; }
.fa-water-arrow-down { --fa: "\f774"; --fa--fa: "\f774\f774"; }
.fa-water-arrow-up { --fa: "\f775"; --fa--fa: "\f775\f775"; }
.fa-water-ladder { --fa: "\f5c5"; --fa--fa: "\f5c5\f5c5"; }
.fa-water-lower { --fa: "\f774"; --fa--fa: "\f774\f774"; }
.fa-water-rise { --fa: "\f775"; --fa--fa: "\f775\f775"; }
.fa-webcam { --fa: "\f832"; --fa--fa: "\f832\f832"; }
.fa-webcam-slash { --fa: "\f833"; --fa--fa: "\f833\f833"; }
.fa-wheat { --fa: "\f72d"; --fa--fa: "\f72d\f72d"; }
.fa-wheat-alt { --fa: "\e2cd"; --fa--fa: "\e2cd\e2cd"; }
.fa-wheat-awn { --fa: "\e2cd"; --fa--fa: "\e2cd\e2cd"; }
.fa-wheat-awn-slash { --fa: "\e338"; --fa--fa: "\e338\e338"; }
.fa-wheat-slash { --fa: "\e339"; --fa--fa: "\e339\e339"; }
.fa-wheelchair { --fa: "\f193"; --fa--fa: "\f193\f193"; }
.fa-wheelchair-alt { --fa: "\e2ce"; --fa--fa: "\e2ce\e2ce"; }
.fa-wheelchair-move { --fa: "\e2ce"; --fa--fa: "\e2ce\e2ce"; }
.fa-wifi { --fa: "\f1eb"; --fa--fa: "\f1eb\f1eb"; }
.fa-wifi-3 { --fa: "\f1eb"; --fa--fa: "\f1eb\f1eb"; }
.fa-wifi-slash { --fa: "\f6ac"; --fa--fa: "\f6ac\f6ac"; }
.fa-wifi-strong { --fa: "\f1eb"; --fa--fa: "\f1eb\f1eb"; }
.fa-wine-glass { --fa: "\f4e3"; --fa--fa: "\f4e3\f4e3"; }
.fa-wine-glass-alt { --fa: "\f5ce"; --fa--fa: "\f5ce\f5ce"; }
.fa-wine-glass-crack { --fa: "\f4bb"; --fa--fa: "\f4bb\f4bb"; }
.fa-wine-glass-empty { --fa: "\f5ce"; --fa--fa: "\f5ce\f5ce"; }
.fa-wreath { --fa: "\f7e2"; --fa--fa: "\f7e2\f7e2"; }
.fa-wreath-laurel { --fa: "\e5d2"; --fa--fa: "\e5d2\e5d2"; }
.fa-xmark { --fa: "\f00d"; --fa--fa: "\f00d\f00d"; }
.fa-xmark-circle { --fa: "\f057"; --fa--fa: "\f057\f057"; }
.fa-xmark-large { --fa: "\e59b"; --fa--fa: "\e59b\e59b"; }
.fa-xmark-octagon { --fa: "\f2f0"; --fa--fa: "\f2f0\f2f0"; }
.fa-xmark-square { --fa: "\f2d3"; --fa--fa: "\f2d3\f2d3"; }
.fa-xmark-to-slot { --fa: "\f771"; --fa--fa: "\f771\f771"; }
.fa-zap { --fa: "\f0e7"; --fa--fa: "\f0e7\f0e7"; }

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0; }

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0; }

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!../node_modules/.pnpm/@citeopolis-fontawesome+optimized@1.0.5/node_modules/@citeopolis-fontawesome/optimized/css/regular.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*!
 * Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Commercial License)
 * Copyright 2024 Fonticons, Inc.
 */
:root, :host {
  --fa-style-family-classic: 'Font Awesome 6 Pro';
  --fa-font-regular: normal 400 1em/1 'Font Awesome 6 Pro'; }

@font-face {
  font-family: 'Font Awesome 6 Pro';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url(/_next/static/media/fa-regular-400.5eac7839.woff2) format("woff2"), url(/_next/static/media/fa-regular-400.bd5e3a8e.ttf) format("truetype"); }

.far,
.fa-regular {
  font-weight: 400; }

/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/skip-links/SkipLinks.module.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.SkipLinks_skipLink__YIUbW {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1400;
  padding: 24px;
  font-size: 1.6rem;
  line-height: 110%;
  color: #214fab;
  white-space: nowrap;
  text-decoration: underline;
  text-decoration-thickness: 8%;
  text-underline-position: from-font;
  text-underline-offset: 17.5%;
  background: white;
  border: 2px solid #214fab;
  border-radius: 4px;
}
@media screen and (min-width: 1302px) {
  .SkipLinks_skipLink__YIUbW {
    font-size: 1.8rem;
  }
}
.SkipLinks_skipLink__YIUbW:not(:focus) {
  position: absolute !important;
  top: auto !important;
  left: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  border: 0 !important;
  clip: rect(1px, 1px, 1px, 1px);
}
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/toast/ToastViewportWrapper.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.ToastViewportWrapper_viewport__OAR9V {
  position: fixed;
  bottom: 30px;
  left: 50%;
  z-index: 1200;
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 500px;
  max-width: 100%;
  transform: translateX(-50%);
}
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[11].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[11].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[11].use[5]!./styles/global.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
::-webkit-backdrop {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: 0 solid;
}
::-webkit-file-upload-button {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: 0 solid;
}
*,
::after,
::before,
::backdrop,
::file-selector-button {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  border: 0 solid;
}

html,
:host {
  font-family: ui-sans-serif, system-ui, sans-serif;
  font-variation-settings: normal;
  font-feature-settings: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
       tab-size: 4;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  text-decoration: inherit;
}

b,
strong {
  font-weight: bolder;
}

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
  font-variation-settings: normal;
  font-feature-settings: normal;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

table {
  text-indent: 0;
  border-collapse: collapse;
  border-color: inherit;
}

:-moz-focusring {
  outline: auto;
}

progress {
  vertical-align: baseline;
}

summary {
  display: list-item;
}

ol,
ul,
menu {
  list-style-type: "";
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}

::-webkit-file-upload-button {
  font: inherit;
  font-variation-settings: inherit;
  font-feature-settings: inherit;
  color: inherit;
  letter-spacing: inherit;
  background-color: transparent;
  border-radius: 0;
  opacity: 1;
}

button,
input,
select,
optgroup,
textarea,
::file-selector-button {
  font: inherit;
  font-variation-settings: inherit;
  font-feature-settings: inherit;
  color: inherit;
  letter-spacing: inherit;
  background-color: transparent;
  border-radius: 0;
  opacity: 1;
}

:where(select:is([multiple], [size])) optgroup {
  font-weight: bolder;
}

:where(select:is([multiple], [size])) optgroup option {
  -webkit-padding-start: 20px;
          padding-inline-start: 20px;
}

::-webkit-file-upload-button {
  -webkit-margin-end: 4px;
          margin-inline-end: 4px;
}

::file-selector-button {
  -webkit-margin-end: 4px;
          margin-inline-end: 4px;
}

::placeholder {
  opacity: 1;
}

@supports (not (-webkit-appearance: -apple-pay-button)) or (contain-intrinsic-size: 1px) {
  ::placeholder {
    color: color-mix(in oklab, currentcolor 50%, transparent);
  }
}
textarea {
  resize: vertical;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
          appearance: none;
}

::-webkit-search-cancel-button {
  -webkit-appearance: none;
          appearance: none;
}

::-webkit-date-and-time-value {
  min-height: 1lh;
  text-align: inherit;
}

::-webkit-datetime-edit {
  display: inline-flex;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-datetime-edit,
::-webkit-datetime-edit-year-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-meridiem-field {
  padding-block: 0;
}

:-moz-ui-invalid {
  box-shadow: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
          appearance: button;
}

button,
input:where([type=button], [type=reset], [type=submit]),
::file-selector-button {
  -webkit-appearance: button;
     -moz-appearance: button;
          appearance: button;
}

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

[hidden]:where(:not([hidden=until-found])) {
  display: none !important;
}

/* Remove all animations, transitions and smooth scroll for people that prefer not to see them */
@media (prefers-reduced-motion: reduce) {
  html:focus-within {
    scroll-behavior: auto;
  }
  *,
  *::before,
  *::after {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    scroll-behavior: auto !important;
  }
}
#root,
#__next {
  isolation: isolate;
}

html {
  font-size: 62.5%;
  scroll-behavior: smooth;
  scroll-padding: var(--scroll-padding);
}

body {
  font-family: var(--typo-1), Arial, Tahoma, sans-serif;
  font-size: 1.6rem;
  line-height: 150%;
  color: #000;
}
@media screen and (min-width: 768px) {
  body {
    font-size: 1.8rem;
  }
}

*:focus-visible {
  outline: 2px solid #000;
  outline-offset: 2px;
}

.sr-only {
  position: absolute !important;
  top: auto !important;
  left: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  border: 0 !important;
  clip: rect(1px, 1px, 1px, 1px);
}

.container {
  max-width: 1216px;
  margin-inline: auto;
}
@media screen and (max-width: 767px) {
  .container {
    padding-inline: 16px;
  }
}
@media screen and (min-width: 768px) and (max-width: 1301px) {
  .container {
    padding-inline: 24px;
  }
}

.contained {
  width: 100%;
  max-width: 1216px;
  margin-inline: auto;
}
@media screen and (max-width: 767px) {
  .layout-1column-fullwidth .column.main > .contained {
    padding-inline: 16px;
  }
}
@media screen and (min-width: 768px) and (max-width: 1301px) {
  .layout-1column-fullwidth .column.main > .contained {
    padding-inline: 24px;
  }
}

.site-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
}

.site-content {
  flex: 1 1;
  outline-offset: -2px;
}

:root {
  --bprogress-height: 4px;
  --bprogress-color: #214fab;
  --header-height: 108px;
}
@media screen and (min-width: 768px) {
  :root {
    --header-height: 112px;
  }
}
@media screen and (min-width: 1302px) {
  :root {
    --header-height: 168px;
  }
}

.layout-1column,
.layout-2columns-left,
.layout-2columns-right,
.layout-3columns {
  width: 100%;
  max-width: 1216px;
  padding-inline: 16px;
  margin-block: 64px;
  margin-inline: auto;
}
.layout-1column .column,
.layout-2columns-left .column,
.layout-2columns-right .column,
.layout-3columns .column {
  margin-bottom: 64px;
}
.layout-1column .column:last-child,
.layout-2columns-left .column:last-child,
.layout-2columns-right .column:last-child,
.layout-3columns .column:last-child {
  margin-bottom: 0;
}
@media screen and (min-width: 768px) {
  .layout-1column,
  .layout-2columns-left,
  .layout-2columns-right,
  .layout-3columns {
    padding-inline: 24px;
    margin-block: 72px;
  }
  .layout-1column .column,
  .layout-2columns-left .column,
  .layout-2columns-right .column,
  .layout-3columns .column {
    margin-bottom: 72px;
  }
}
@media screen and (min-width: 1302px) {
  .layout-1column,
  .layout-2columns-left,
  .layout-2columns-right,
  .layout-3columns {
    display: flex;
    gap: 32px;
    padding-inline: 0;
    margin-block: 96px;
  }
  .layout-1column .column,
  .layout-2columns-left .column,
  .layout-2columns-right .column,
  .layout-3columns .column {
    margin-bottom: 0;
  }
  .layout-1column .column.main,
  .layout-2columns-left .column.main,
  .layout-2columns-right .column.main,
  .layout-3columns .column.main {
    flex: 1 1;
  }
}

.layout-1column-fullwidth {
  display: flex;
  width: 100%;
  margin-block: 64px;
}
.layout-1column-fullwidth .column.main {
  width: 100%;
}
@media screen and (min-width: 768px) {
  .layout-1column-fullwidth {
    margin-block: 72px;
  }
}
@media screen and (min-width: 1302px) {
  .layout-1column-fullwidth {
    margin-block: 96px;
  }
}
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[3].use[1]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[13].oneOf[3].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/font/local/target.css?{"path":"utils\\typography.ts","import":"","arguments":[{"src":[{"path":"../fonts/RethinkSans-Regular.woff2","weight":"400","style":"normal"},{"path":"../fonts/RethinkSans-Medium.woff2","weight":"500","style":"normal"},{"path":"../fonts/RethinkSans-SemiBold.woff2","weight":"600","style":"normal"},{"path":"../fonts/RethinkSans-Bold.woff2","weight":"700","style":"normal"},{"path":"../fonts/RethinkSans-ExtraBold.woff2","weight":"800","style":"normal"}],"variable":"--typo-1"}],"variableName":"rethinkSans"} ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@font-face {
font-family: 'rethinkSans';
src: url(/_next/static/media/1ff5a367c3da0895-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 400;
font-style: normal;
}

@font-face {
font-family: 'rethinkSans';
src: url(/_next/static/media/c0dd19aa19c6be25-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 500;
font-style: normal;
}

@font-face {
font-family: 'rethinkSans';
src: url(/_next/static/media/840afd9bb65156fd-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 600;
font-style: normal;
}

@font-face {
font-family: 'rethinkSans';
src: url(/_next/static/media/500607f5f0d3fc98-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 700;
font-style: normal;
}

@font-face {
font-family: 'rethinkSans';
src: url(/_next/static/media/33460492d7898b40-s.p.woff2) format('woff2');
font-display: swap;
font-weight: 800;
font-style: normal;
}@font-face {font-family: 'rethinkSans Fallback';src: local("Arial");ascent-override: 94.29%;descent-override: 29.47%;line-gap-override: 0.00%;size-adjust: 105.21%
}.__className_fa0bc1 {font-family: 'rethinkSans', 'rethinkSans Fallback'
}.__variable_fa0bc1 {--typo-1: 'rethinkSans', 'rethinkSans Fallback'
}

