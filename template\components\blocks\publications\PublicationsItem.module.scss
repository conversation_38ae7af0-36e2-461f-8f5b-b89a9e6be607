@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.publicationsItem {
  position: relative;
  display: grid;
  grid-template-areas:
    "image"
    "title"
    "teaser"
    "actions";
  grid-template-columns: 1fr;
  grid-gap: 12px;
  width: 100%;
  padding-block: 24px;

  @include breakpoint(medium only) {
    grid-template-areas:
      "image title"
      "teaser teaser"
      "actions actions";
    grid-template-columns: 152px 2fr;
    grid-gap: 24px 32px;
  }

  @include breakpoint(large up) {
    @container (min-width: 700px) {
      grid-template-areas:
        "image title actions"
        "image teaser actions";
      grid-template-columns: 210px 2fr 176px;
      grid-gap: 32px;
      padding-block: 32px;
    }
  }
}

.imageWrapper {
  position: relative;
  grid-area: image;

  img {
    @include object-fit;
    @include size(152px, 215px);

    @include breakpoint(large up) {
      @include size(210px, 298px);
    }
  }
}

.titleWrapper {
  grid-area: title;
  align-self: center;

  @include breakpoint(large up) {
    @container (min-width: 700px) {
      align-self: end;
    }
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 2.4rem;
  }
}

.title {
  font-size: 1.8rem;
  font-weight: $fw-bold;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 3.2rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.teaser {
  grid-area: teaser;
  font-size: 1.6rem;
  line-height: 150%;

  @include breakpoint(large up) {
    align-self: start;
    font-size: 1.8rem;
  }
}

.actions {
  z-index: $layer-link-block + 1;
  display: flex;
  flex-direction: column;
  grid-area: actions;
  gap: 12px 0;
  justify-content: center;

  @include breakpoint(large up) {
    gap: 16px 0;
  }
}

.documentsInfo {
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;
}

.buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;

  a {
    flex: 1;
  }

  @include breakpoint(medium up) {
    flex-direction: row;
  }

  @include breakpoint(large up) {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    a {
      min-width: 176px;
    }
  }
}
