import type { <PERSON>a, StoryObj } from "@storybook/nextjs";

function Page({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}

function Indicator({ text, color }: { text: string; color?: string }) {
  return (
    <div
      style={{
        background: color ?? "#aaa",
        width: "100%",
        textAlign: "center",
        padding: "1rem",
        boxSizing: "border-box",
      }}
    >
      {text}
    </div>
  );
}

const meta: Meta<typeof Page> = {
  title: "Layout",
  component: Page,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;

type Story = StoryObj<typeof Page>;

export const OneColumn: Story = {
  render: () => (
    <div className="layout-1column">
      <main className="column main">
        <Indicator text="Main" />
      </main>
    </div>
  ),
};

export const TwoColumnsLeft: Story = {
  render: () => (
    <div className="layout-2columns-left">
      <aside className="column sidebar">
        <Indicator text="Sidebar" color="lightblue" />
      </aside>
      <main className="column main">
        <Indicator text="Main" />
      </main>
    </div>
  ),
};

export const TwoColumnsRight: Story = {
  render: () => (
    <div className="layout-2columns-right">
      <main className="column main">
        <Indicator text="Main" />
      </main>
      <aside className="column sidebar">
        <Indicator text="Sidebar" color="lightblue" />
      </aside>
    </div>
  ),
};

export const ThreeColumns: Story = {
  render: () => (
    <div className="layout-3columns">
      <aside className="column sidebar">
        <Indicator text="Sidebar" color="lightblue" />
      </aside>
      <main className="column main">
        <Indicator text="Main" />
      </main>
      <aside className="column sidebar">
        <Indicator text="Sidebar" color="lightblue" />
      </aside>
    </div>
  ),
};

export const OneColumnFullWidth: Story = {
  render: () => (
    <div className="layout-1column-fullwidth">
      <main className="column main">
        <Indicator text="Main" />
      </main>
    </div>
  ),
};
