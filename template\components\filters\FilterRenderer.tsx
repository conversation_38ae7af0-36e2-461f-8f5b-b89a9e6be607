import { Filter, FilterValues } from "@/lib/filters";
import dynamic from "next/dynamic";

// Lazy load components dynamically
// const Select = dynamic(() => import("@/components/filters/select/Select"));
const Tree = dynamic(() => import("@/components/filters/tree/Tree"));
const Text = dynamic(() => import("@/components/filters/text/Text"));
const DateRange = dynamic(() => import("@/components/filters/date-range/DateRange"));

interface FilterRendererProps {
  url: string;
  filters: Filter[];
  filterValues: FilterValues;
}

export default function FilterRenderer({ filters, filterValues, url }: FilterRendererProps) {
  return (
    <>
      {filters.map((filter) => {
        if (filter.__typename === "TextFilter") {
          return (
            <Text
              url={url}
              filter={filter}
              key={filter.attribute}
              filterValues={filterValues}
              defaultValue={filterValues.get(filter.attribute)?.[0] ?? ""}
            />
          );
        }

        if (filter.__typename === "SelectFilter") {
          return (
            <Tree filter={filter} key={filter.attribute} selectedOptions={filterValues.get(filter.attribute) ?? []} />
          );
        }

        if (filter.__typename === "DateRangeFilter") {
          return (
            <DateRange
              url={url}
              filter={filter}
              key={filter.attribute}
              filterValues={filterValues}
              defaultValue={filterValues.get(filter.attribute) ?? []}
            />
          );
        }
      })}
    </>
  );
}
