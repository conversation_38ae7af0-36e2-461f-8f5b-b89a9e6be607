"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-state-with-deps@1.1.3_react@19.1.1";
exports.ids = ["vendor-chunks/use-state-with-deps@1.1.3_react@19.1.1"];
exports.modules = {

/***/ "(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/deps-are-equal.js":
/*!***********************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/deps-are-equal.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.depsAreEqual = depsAreEqual;\nfunction depsAreEqual(prevDeps, deps) {\n  return prevDeps.length === deps.length && deps.every(function (dep, index) {\n    return Object.is(dep, prevDeps[index]);\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3VzZS1zdGF0ZS13aXRoLWRlcHNAMS4xLjNfcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy91c2Utc3RhdGUtd2l0aC1kZXBzL2RlcHMtYXJlLWVxdWFsLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0giLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcdXNlLXN0YXRlLXdpdGgtZGVwc0AxLjEuM19yZWFjdEAxOS4xLjFcXG5vZGVfbW9kdWxlc1xcdXNlLXN0YXRlLXdpdGgtZGVwc1xcZGVwcy1hcmUtZXF1YWwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlcHNBcmVFcXVhbCA9IGRlcHNBcmVFcXVhbDtcbmZ1bmN0aW9uIGRlcHNBcmVFcXVhbChwcmV2RGVwcywgZGVwcykge1xuICByZXR1cm4gcHJldkRlcHMubGVuZ3RoID09PSBkZXBzLmxlbmd0aCAmJiBkZXBzLmV2ZXJ5KGZ1bmN0aW9uIChkZXAsIGluZGV4KSB7XG4gICAgcmV0dXJuIE9iamVjdC5pcyhkZXAsIHByZXZEZXBzW2luZGV4XSk7XG4gIH0pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/deps-are-equal.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/index.js":
/*!**************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/index.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.useStateWithDeps = useStateWithDeps;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar _useForceUpdate = __webpack_require__(/*! ./use-force-update */ \"(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/use-force-update.js\");\nvar _depsAreEqual = __webpack_require__(/*! ./deps-are-equal */ \"(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/deps-are-equal.js\");\nvar _isFunction = __webpack_require__(/*! ./is-function */ \"(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/is-function.js\");\n/**\n * `useState` hook with an additional dependency array that resets\n * the state to the `initialState` param when the dependencies passed\n * in the `deps` array change.\n *\n * @param initialState\n * The state that will be set when the component mounts or the\n * dependencies change.\n *\n * It can also be a function which resolves to the state. If the state\n * is reset due to a change of dependencies, this function will be called with the previous\n * state (`undefined` for the first call upon mount).\n * @param deps Dependencies for this hook that resets the state to `initialState`\n */\nfunction useStateWithDeps(initialState, deps) {\n  var isMounted = (0, _react.useRef)(false);\n\n  // Determine initial state\n  var usableInitialState = null;\n  if (!isMounted.current) {\n    isMounted.current = true;\n    if ((0, _isFunction.isFunction)(initialState)) {\n      usableInitialState = initialState();\n    } else {\n      usableInitialState = initialState;\n    }\n  }\n\n  // It would be possible to use useState instead of\n  // useRef to store the state, however this would\n  // trigger re-renders whenever the state is reset due\n  // to a change in dependencies. In order to avoid these\n  // re-renders, the state is stored in a ref and an\n  // update is triggered via forceUpdate below when necessary\n  var state = (0, _react.useRef)(usableInitialState);\n\n  // Check if dependencies have changed\n  var prevDeps = (0, _react.useRef)(deps);\n  if (!(0, _depsAreEqual.depsAreEqual)(prevDeps.current, deps)) {\n    // Update state and deps\n    var nextState;\n    if ((0, _isFunction.isFunction)(initialState)) {\n      nextState = initialState(state.current);\n    } else {\n      nextState = initialState;\n    }\n    state.current = nextState;\n    prevDeps.current = deps;\n  }\n  var forceUpdate = (0, _useForceUpdate.useForceUpdate)();\n  var updateState = (0, _react.useCallback)(function updateState(newState) {\n    var nextState;\n    if ((0, _isFunction.isFunction)(newState)) {\n      nextState = newState(state.current);\n    } else {\n      nextState = newState;\n    }\n    if (!Object.is(state.current, nextState)) {\n      state.current = nextState;\n      forceUpdate();\n    }\n  }, []);\n  return [state.current, updateState];\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/is-function.js":
/*!********************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/is-function.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.isFunction = isFunction;\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nfunction isFunction(input) {\n  return typeof input === \"function\";\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3VzZS1zdGF0ZS13aXRoLWRlcHNAMS4xLjNfcmVhY3RAMTkuMS4xL25vZGVfbW9kdWxlcy91c2Utc3RhdGUtd2l0aC1kZXBzL2lzLWZ1bmN0aW9uLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFx1c2Utc3RhdGUtd2l0aC1kZXBzQDEuMS4zX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFx1c2Utc3RhdGUtd2l0aC1kZXBzXFxpcy1mdW5jdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuaXNGdW5jdGlvbiA9IGlzRnVuY3Rpb247XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLXVuc2FmZS1mdW5jdGlvbi10eXBlXG5mdW5jdGlvbiBpc0Z1bmN0aW9uKGlucHV0KSB7XG4gIHJldHVybiB0eXBlb2YgaW5wdXQgPT09IFwiZnVuY3Rpb25cIjtcbn0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/is-function.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/use-force-update.js":
/*!*************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/use-force-update.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.useForceUpdate = useForceUpdate;\nvar _react = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction useForceUpdate() {\n  var _useReducer = (0, _react.useReducer)(function () {\n      return Symbol();\n    }, undefined),\n    _useReducer2 = _slicedToArray(_useReducer, 2),\n    forceUpdate = _useReducer2[1];\n  return function () {\n    return forceUpdate();\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/use-state-with-deps@1.1.3_react@19.1.1/node_modules/use-state-with-deps/use-force-update.js\n");

/***/ })

};
;