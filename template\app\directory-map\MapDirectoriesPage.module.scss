@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.main {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100dvh;
}

.fixedNavWrapper {
  display: flex;
  background-color: $color-neutral-100;
  border-top: 1px solid $color-white;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);

  @include breakpoint(large up) {
    display: none;
  }
}

.header {
  position: relative;
  z-index: $layer-floating-header;
  display: flex;
  flex-wrap: wrap;
  gap: 0 8px;
  align-items: center;
  justify-content: space-between;
  background-color: $color-neutral-100;
  border-bottom: 1px solid $color-white;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);

  @include breakpoint(large up) {
    padding: 0 6px;
  }
}

.navWrapper {
  display: none;

  @include breakpoint(large up) {
    display: flex;
    height: 100%;
  }
}

.actions {
  display: flex;
  order: 3;
  width: 100%;

  @include breakpoint(large up) {
    order: 2;
    width: auto;
    margin-left: auto;
  }
}

.wrapper {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
  width: calc(100% - 50px);
  min-height: 70px;

  @include breakpoint(large up) {
    width: auto;
  }
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-primary-500;
}

.filter {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: space-between;
  order: 3;
  width: 100%;
  height: 70px;
  padding: 0 12px;

  @include breakpoint(medium up) {
    padding: 0 56px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    align-items: center;
    justify-content: center;
    width: fit-content;
    max-width: 700px;
    height: 70px;
    padding: 0 32px;
  }
}

.search {
  width: 100%;

  @include breakpoint(large up) {
    width: 347px;
  }
}

.filterButton {
  span {
    display: none;

    @include breakpoint(medium up) {
      display: block;
    }
  }
}

.trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  order: 2;
  height: 70px;

  @include breakpoint(large up) {
    order: 3;
  }
}
