"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { TextField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type TextProps = Omit<TextField, "__typename">;

export default function Text({
  name,
  autocomplete,
  defaultValue,
  label,
  description,
  pattern,
  placeholder,
  required,
  condition,
  validationMessage,
  columnSpan,
}: TextProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const inputId = useId();
  const errorId = useId();

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <Textfield
          id={inputId}
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          pattern={pattern ?? undefined}
          placeholder={placeholder ?? undefined}
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
          {...register(name, { required })}
        />

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
