import clsx from "clsx";
import { findAll } from "highlight-words-core";
import { Fragment } from "react";

interface HighlighterProps {
  caseSensitive?: boolean;
  highlightClassName?: string;
  highlightTag?: string | React.ComponentType<{ children: string; className: string }>;
  searchWords: string[];
  textToHighlight: string;
}

/**
 * A simpler implemenation of https://github.com/bvaughn/react-highlight-words
 * Wraps the content into a `Fragment` instead of an unnecessary `span`.
 * Type safe by default.
 */
export default function Highlighter({
  caseSensitive,
  highlightClassName,
  highlightTag: HighlightTag = "mark",
  searchWords,
  textToHighlight,
}: HighlighterProps) {
  const chunks = findAll({
    caseSensitive,
    searchWords,
    textToHighlight,
  });

  return (
    <>
      {chunks.map((chunk, index) => {
        const text = textToHighlight.slice(chunk.start, chunk.end);

        return chunk.highlight ? (
          <HighlightTag key={index} className={clsx(highlightClassName)}>
            {text}
          </HighlightTag>
        ) : (
          <Fragment key={index}>{text}</Fragment>
        );
      })}
    </>
  );
}
