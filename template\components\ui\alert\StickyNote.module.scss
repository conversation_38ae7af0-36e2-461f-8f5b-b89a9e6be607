@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.stickyNote {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: $layer-sticky-note;
  padding: 0 16px;

  @include breakpoint(large up) {
    flex-direction: row;
    gap: 32px;
  }
}

.container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  max-width: 592px;
  padding: 24px 12px;
  margin: auto;
  border-radius: 4px 4px 0 0;

  @include breakpoint(medium up) {
    padding: 24px 16px;
  }

  @include breakpoint(large up) {
    gap: 16px;
    padding: 32px 16px;
  }

  &.color-primary {
    color: $color-white;
    background-color: $color-primary-500;
  }

  &.color-secondary {
    background-color: $color-secondary-500;
  }

  &.color-tertiary {
    background-color: $color-tertiary-500;
  }

  &.color-danger {
    color: $color-white;
    background-color: $color-danger;
  }

  .topIcon {
    font-size: 2.4rem;
  }
}

.wrapper {
  display: flex;
  flex-wrap: nowrap;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
  min-width: 0;

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @include breakpoint(large up) {
    gap: 16px;
  }

  .action {
    display: flex;
    justify-content: center;
    width: 100%;
  }
}

.header {
  display: flex;
  flex-direction: column;
  gap: 6px;

  @include breakpoint(large up) {
    gap: 8px;
  }

  .title {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 110%;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }
  }

  .description {
    font-size: 1.2rem;
    line-height: 110%;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }
  }
}

.close {
  padding: 0 8px;
  margin-left: auto;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 4px;
  transition:
    background 0.5s ease-in-out,
    color 0.5s ease-in-out;

  i {
    font-size: 1.4rem;
  }

  &.color-primary {
    &:hover,
    &:focus {
      color: currentcolor;
      background-color: white;

      > * {
        color: $color-primary-500;
      }
    }
  }

  &.color-danger {
    &:hover,
    &:focus {
      color: currentcolor;
      background-color: white;

      > * {
        color: $color-danger;
      }
    }
  }

  &.color-secondary,
  &.color-tertiary {
    &:hover,
    &:focus {
      color: currentcolor;
      background-color: white;

      > * {
        color: $color-black;
      }
    }
  }
}
