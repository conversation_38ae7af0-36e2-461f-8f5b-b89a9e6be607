import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Text from "./Text";

const meta: Meta<typeof Text> = {
  title: "Filters/Text",
  component: Text,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Text>;

export const Default: Story = {
  args: {
    filter: {
      label: "Rechercher",
      attribute: "s",
    },
    placeholder: "Que recherchez vous ?",
  },
};
