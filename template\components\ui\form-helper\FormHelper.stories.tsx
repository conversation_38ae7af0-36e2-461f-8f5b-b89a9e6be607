import type { Meta, StoryObj } from "@storybook/nextjs";
import FormHelper from "./FormHelper";

const meta: Meta<typeof FormHelper> = {
  title: "Components/FormHelper",
  component: FormHelper,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof FormHelper>;

export const Info: Story = {
  args: {
    variant: "info",
    children: "Texte du message",
  },
};

export const Error: Story = {
  args: {
    variant: "error",
    children: "Texte du message",
  },
};

export const Warn: Story = {
  args: {
    variant: "warn",
    children: "Texte du message",
  },
};

export const Success: Story = {
  args: {
    variant: "success",
    children: "Texte du message",
  },
};
