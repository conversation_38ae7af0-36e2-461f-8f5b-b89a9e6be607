const path = require("path");

// https://nextjs.org/docs/app/api-reference/config/eslint#lint-staged
const buildEslintCommand = (filenames) =>
  `next lint --quiet --fix --file ${filenames.map((f) => path.relative(process.cwd(), f)).join(" --file ")}`;

module.exports = {
  "*.{js,jsx,ts,tsx}": ["prettier -w", buildEslintCommand],
  "*.{css,scss}": ["prettier -w", "stylelint --fix"],
  "*.{json,md}": ["prettier -w"],
  "*.{ts,tsx}": ["vitest related --run"],
};
