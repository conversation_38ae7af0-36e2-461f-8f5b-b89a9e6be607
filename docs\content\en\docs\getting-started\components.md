---
title: "Components"
weight: 1
---

# Components

Components are located in the `components` directory of the Citéopolis project.
They also grouped by categories:

## UI

The **ui** components contains presentational and reusable components through the entire application.

These components are considered **stateless** by design, so they can be integrated nicely in storybook.

### Container pattern

If you need to add some logic (data fetching), you can do so in a `Container`.

See this [documentation](https://www.patterns.dev/react/presentational-container-pattern/) for more info.

## Blocks

The **blocks** directory contains components related to the `BlockInterface` GraphQL type.

They are dynamically rendered from the `StructuredContent` GraphQL scalar of the page. For more information check the [GraphQL specification](https://code.stratis.fr/citeopolis-5/citeopolis-spec).

{{% hint danger %}}
A block should never be called from another component except the `BlockRenderer`.
{{% /hint %}}

## Form

The **form** directory contains the components related to the `Form` GraphQL type.

Like `StructuredContent` the form has an unknown depth and set of properties. For more information check the [GraphQL specification](https://code.stratis.fr/citeopolis-5/citeopolis-spec).

{{% hint danger %}}
A form field should never be called from another component except the `FormRenderer`.
{{% /hint %}}

## Naming

The naming convention for components is **PascalCase**.
The default export must be a function which return type is infered.

Example:

```typescript
// components/ui/atoms/button/Button.tsx
export default function Button() {}
```
