#!/usr/bin/env node
import { Command } from "commander";
import process from "node:process";
import packageJson from "../package.json";
import { createProject } from "./commands/create";
import { logger } from "./utils/logger";

process.on("SIGINT", () => process.exit(0));
process.on("SIGTERM", () => process.exit(0));

const program = new Command()
  .name("create-app")
  .description("create a new citéopolis project")
  .argument("[path]", "where the project will be created")
  .option("-f, --features <features>", "comma separated list of features to build", (value) =>
    value.split(",").map((v) => v.trim())
  )
  .version(packageJson.version || "0.0.0", "-v, --version", "display the version number")
  .action(async (projectPath, options) => {
    try {
      await createProject(projectPath, { features: options.features ?? [] });
    } catch (error) {
      logger.error(error);
      process.exit(1);
    }
  });

program.parse();
