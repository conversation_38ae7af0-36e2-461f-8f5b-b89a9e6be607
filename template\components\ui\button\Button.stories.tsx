import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Button from "./Button";

const meta: Meta<typeof Button> = {
  title: "Components/Button",
  component: Button,
  argTypes: {
    variant: {
      control: {
        type: "select",
        options: ["contained", "outlined", "text"],
      },
    },
    color: {
      control: {
        type: "select",
        options: ["primary", "secondary", "tertiary"],
      },
    },
    size: {
      control: {
        type: "radio",
      },
      options: ["xs", "sm", "md", "lg"],
    },
    startIcon: {
      control: {
        type: "boolean",
      },
      mapping: {
        false: "",
        true: "far fa-circle-user",
      },
    },
    endIcon: {
      control: {
        type: "boolean",
      },
      mapping: {
        false: "",
        true: "far fa-circle-user",
      },
    },
    disabled: {
      description: "Toggle to disable the button",
      control: { type: "boolean" },
    },
  },
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    children: "Bouton",
  },
};

export const Large: Story = {
  args: {
    children: "Bouton",
    size: "lg",
  },
};

export const Medium: Story = {
  args: {
    children: "Bouton",
    size: "md",
  },
};

export const Small: Story = {
  args: {
    children: "Bouton",
    size: "sm",
  },
};

export const ExtraSmall: Story = {
  args: {
    children: "Bouton",
    size: "xs",
  },
};

export const Decorated: Story = {
  args: {
    children: "Bouton",
    startIcon: "far fa-circle-user",
    endIcon: "far fa-circle-user",
  },
};

export const Secondary: Story = {
  args: {
    children: "Bouton",
    color: "secondary",
  },
};

export const Tertiary: Story = {
  args: {
    children: "Bouton",
    color: "tertiary",
  },
};

export const Disabled: Story = {
  args: {
    children: "Bouton",
    disabled: true,
  },
};

export const Outlined: Story = {
  args: {
    children: "Bouton",
    variant: "outlined",
  },
};

export const Text: Story = {
  args: {
    children: "Bouton",
    variant: "text",
  },
};

export const AsLink: Story = {
  args: {
    // eslint-disable-next-line jsx-a11y/anchor-is-valid -- Example link
    children: <a href="#">Lien</a>,
    asChild: true,
  },
};

export const Submit: Story = {
  args: {
    type: "submit",
    children: "Envoyer",
  },
};
