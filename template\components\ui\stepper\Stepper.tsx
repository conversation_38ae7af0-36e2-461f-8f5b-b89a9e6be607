import clsx from "clsx";
import { Fragment } from "react";
import styles from "./Stepper.module.scss";

interface StepperProps {
  current?: number;
  total: number;
}

export default function Stepper({ current = 0, total }: StepperProps) {
  return (
    <div className={styles.stepper}>
      {Array.from({ length: total }).map((_, index) => (
        <Fragment key={index}>
          {index > 0 && <div className={clsx(styles.separator, current >= index && styles.active)}></div>}
          <div className={clsx(styles.step, current >= index && styles.active)}></div>
        </Fragment>
      ))}
    </div>
  );
}
