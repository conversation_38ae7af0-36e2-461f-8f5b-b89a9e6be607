@use "sass:color";
@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.button {
  display: inline-flex;
  gap: 2px;
  align-items: center;
  justify-content: center;
  width: fit-content;
  line-height: normal;
  color: $color-white;
  cursor: pointer;
  background-color: $color-primary-500;
  border-radius: 4px;
  transition:
    background-color 200ms ease-in-out,
    color 200ms ease-in-out,
    border-color 200ms ease-in-out;

  &:hover {
    background-color: $color-primary-400;
  }

  &.size-sm {
    padding: 6px 12px;
    font-size: 1.2rem;

    i {
      font-size: 1.4rem;
    }
  }

  &.size-md {
    gap: 8px;
    padding: 11px 24px;
    font-size: 1.6rem;
    font-weight: 700;

    i {
      font-size: 2rem;
    }
  }

  i {
    line-height: 1;
    vertical-align: middle;
  }

  &[data-state="open"] {
    color: $color-primary-500;
    background: linear-gradient(0deg, $color-primary-500 4px, $color-primary-50 4px);
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;

    .arrowIcon {
      transform: rotate(-180deg);
    }
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}

.content {
  z-index: $layer-dropdown;
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: flex-start;
  min-width: 220px;
  padding: 16px;
  background-color: $color-white;
  border-radius: 0 4px 4px;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);
}

.labelledRadio {
  display: flex;
  gap: 6px;
  align-items: center;
  cursor: pointer;

  span {
    font-size: 1.6rem;
    line-height: 120%;
    color: $color-neutral-700;
  }

  &:hover {
    outline: none;
  }
}
