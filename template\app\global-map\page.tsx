import Button from "@/components/ui/button/Button";
import MapMenuButton from "@/components/ui/cartography/MapMenuButton";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import assert from "node:assert";
import styles from "./page.module.scss";

const GET_SITE_AND_MAP_DETAILS_QUERY = graphql(`
  query GetSiteAndMapDetails($url: URL) {
    menu(position: "header") {
      items {
        ...MenuItemFragment
        children {
          ...MenuItemFragment
          children {
            ...MenuItemFragment
          }
        }
      }
    }
    siteConfig {
      siteName
      header {
        logoDark {
          alt
          height
          url
          width
        }
      }
      footer {
        quickAccess2 {
          rel
          target
          text
          url
        }
      }
    }
    route(url: $url) {
      ... on GlobalMap {
        __typename
        title
        leadText
        metadata {
          title
          description
        }
        mapLinks {
          url
          text
          icon {
            src
          }
        }
      }
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: GET_SITE_AND_MAP_DETAILS_QUERY,
    variables: { url: await getCurrentServerUrl() },
  });

  assert.ok(data.route?.__typename === "GlobalMap");

  const { metadata } = data.route;

  const metaTitle = metadata?.title;
  const metaDescription = metadata?.description;

  return {
    title: metaTitle,
    description: metaDescription,
  };
}

export default async function MapSelectPage() {
  const { data } = await query({
    query: GET_SITE_AND_MAP_DETAILS_QUERY,
    variables: { url: await getCurrentServerUrl() },
  });

  const { siteConfig, menu, route } = data;

  assert.ok(route?.__typename === "GlobalMap");

  const logoDark = siteConfig?.header?.logoDark ?? null;
  const quickAccess2 = siteConfig?.footer?.quickAccess2 ?? [];
  const items = menu?.items ?? [];

  const { title, mapLinks, leadText } = route;

  return (
    <>
      <header className={styles.header}>
        <nav role="navigation" aria-label="Navigation principale" className={styles.nav}>
          <Button size="sm" color="primary-inverted" startIcon="far fa-arrow-left" asChild>
            <Link href="/">Retour au site</Link>
          </Button>

          {items.length > 0 && (
            <MapMenuButton logoDark={logoDark} quickAccess2={quickAccess2} mapLinks={mapLinks} menuItems={items}>
              <Button size="sm" color="primary-inverted" endIcon="far fa-ellipsis-vertical">
                Menu
              </Button>
            </MapMenuButton>
          )}
        </nav>
      </header>
      <main id="main" className={clsx("site-content", styles.main)} tabIndex={-1}>
        <div className={styles.logo}>
          {logoDark?.url && (
            <Image
              className={styles.logoImage}
              src={logoDark.url}
              width={logoDark.width || 229}
              height={logoDark.height || 90}
              alt={logoDark.alt ?? ""}
              priority
            />
          )}
        </div>

        <div className={styles.details}>
          <h1 className={styles.title}>{title && `${title} de ${siteConfig?.siteName ?? ""}`}</h1>
          {leadText && <p className={styles.leadText}>{leadText}</p>}
        </div>

        <nav role="navigation" aria-label="Liste des cartographies">
          {mapLinks.length === 0 ? (
            <p>Aucune carte disponible pour le moment.</p>
          ) : (
            <ul className={styles.mapTypeList}>
              {mapLinks.map(({ icon, text, url }, index) => (
                <li className={styles.mapTypeItem} key={index}>
                  <Link className={styles.mapTypeLink} href={url}>
                    {icon?.src && <i className={clsx(icon.src, styles.mapTypeIcon)} aria-hidden />}
                    {text}
                  </Link>
                </li>
              ))}
            </ul>
          )}
        </nav>

        <Button
          className={styles.tutorialButton}
          color="primary-inverted"
          variant="outlined"
          startIcon="far fa-circle-question"
          size="sm"
          type="button"
        >
          Voir le tutoriel
        </Button>
      </main>
    </>
  );
}
