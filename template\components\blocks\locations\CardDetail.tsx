import Accessibility from "@/components/ui/accessibility/Accessibility";
import Address from "@/components/ui/address/Address";
import Button from "@/components/ui/button/Button";
import Hx from "@/components/ui/title/Hx";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import styles from "./CardDetail.module.scss";

interface CardDetailProps {
  onClose: () => void;
  data?: {
    title?: string;
    address?: string;
    directory?: string;
  };
}

export default function CardDetail({ data, onClose }: CardDetailProps) {
  if (!data) return null;

  const { title, address, directory } = data;

  if (!address || typeof address !== "string" || !directory || typeof directory !== "string") {
    onClose();
    return;
  }

  const { accessibility, phones, url, website, email } = JSON.parse(directory);

  return (
    <div className={styles.root}>
      <div className={clsx(styles.row, styles.gap12)}>
        <p className={styles.subtitle}>Thématique</p>

        {title && (
          <Hx level={2} className={clsx(styles.title, styles["size-lg"])}>
            {title}
          </Hx>
        )}

        <Image
          fill
          sizes="200px"
          className={styles.poster}
          alt="An example image"
          src="/assets/placeholder-720x480.png"
        />
      </div>

      <div className={styles.row}>
        <Hx level={2} className={clsx(styles.title, styles["size-sm"], styles["color-primary"])}>
          Nom du Lieu
        </Hx>

        {address && <Address className={styles.address} address={JSON.parse(address)} />}

        <Hx level={2} className={clsx(styles.title, styles["size-sm"], styles["color-primary"])}>
          Accessibilité
        </Hx>

        {accessibility && <Accessibility accessibility={accessibility} />}
      </div>

      <div className={clsx(styles.row, styles.gap16)}>
        <Hx level={2} className={clsx(styles.title, styles["size-md"], styles["color-primary"])}>
          <i className={clsx("far", "fa-paperclip", styles["color-secondary"])} aria-hidden={true} /> Plus d'infos
        </Hx>

        <ul className={styles.list}>
          {phones && (
            <li className={styles.item}>
              <Link href="tel:0123456789">
                <i className="far fa-phone" aria-hidden={true} />
                <span className={styles.text}>{phones[0]?.number}</span>
              </Link>
            </li>
          )}
          {email && (
            <li className={styles.item}>
              <Link href="mailto:<EMAIL>">
                <i className="far fa-envelope" aria-hidden={true} />
                <span className={styles.text}>{email}</span>
              </Link>
            </li>
          )}
          {website && (
            <li className={styles.item}>
              <Link href="https://stratis.com">
                <i className="fa-regular fa-arrow-up-right-from-square" aria-hidden={true} />
                <span className={styles.text}>{website}</span>
              </Link>
            </li>
          )}
        </ul>
      </div>

      {url && (
        <div className={styles.row}>
          <Button asChild color="primary" variant="text" startIcon="far fa-arrow-right">
            <Link href={url}>Voir la fiche détaillée</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
