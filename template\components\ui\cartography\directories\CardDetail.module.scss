@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.root {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 1.4rem;
  font-style: normal;
  line-height: 110%;
  color: $color-black;

  &:not(:first-child) {
    padding-top: 24px;
    margin-top: 24px;
    border-top: 1px solid $color-neutral-300;
  }

  &:last-child {
    padding-bottom: 24px;
  }
}

.title {
  font-weight: 700;
  line-height: 110%;

  &:not(:first-of-type) {
    margin-top: 16px;
  }
}

.subtitle {
  font-size: 1.4rem;
  line-height: 130%;
  color: $color-primary-500;
}

.imageWrapper {
  display: flex;
  align-items: flex-start;
  max-width: 200px;
  max-height: 134px;
}

.poster {
  @include object-fit;

  width: 200px;
  height: 134px;
}

.location {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 32px;
}

.city {
  font-size: 1.6rem;
  font-weight: 700;
  color: $color-primary-500;
}

.place {
  font-size: 1.4rem;
  line-height: 110%;
}

.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 27px;
}

.color-primary {
  color: $color-primary-500;
}

.color-secondary {
  color: $color-secondary-500;
}

.size-lg {
  font-size: 2.4rem;
}

.size-md {
  font-size: 1.8rem;
}

.size-sm {
  font-size: 1.6rem;
}

.list {
  display: flex;
  flex-direction: column;
}

.item a {
  display: flex;
  gap: 6px;
  align-items: center;
  line-height: 150%;
  color: $color-primary-500;
}

.item:hover .text {
  text-decoration: underline;
}

.gap12 {
  gap: 12px;
}

.gap16 {
  gap: 16px;
}

.button {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;
  font-weight: 700;
  color: $color-primary-500;
}
