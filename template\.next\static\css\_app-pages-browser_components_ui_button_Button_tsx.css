/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/button/Button.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.Button_button__qOu8O {
  display: inline-flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 200ms ease-in-out, color 200ms ease-in-out, border-color 200ms ease-in-out;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O {
  border: 2px solid transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary__s9PDR {
  color: #fff;
  background-color: #214fab;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary__s9PDR:hover {
  background-color: #3269d7;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary__PUGgD {
  color: #000;
  background-color: #3ec8ad;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary__PUGgD:hover {
  background-color: #9ce3d5;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary__UiubZ {
  color: #000;
  background-color: #eec478;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary__UiubZ:hover {
  background-color: #f5dcad;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary-inverted__wtAtY {
  color: #fff;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary-inverted__wtAtY:hover {
  color: #214fab;
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary-inverted__6dhHp, .Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary-inverted__HBWl5 {
  color: #000;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary-inverted__6dhHp:hover, .Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary-inverted__HBWl5:hover {
  background-color: #fff;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-danger-inverted__p6aqN {
  color: #fff;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-danger-inverted__p6aqN:hover {
  color: #d61200;
  background-color: #fff;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary__s9PDR {
  color: #214fab;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary__s9PDR:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary-inverted__wtAtY {
  color: #fff;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary-inverted__wtAtY:hover {
  color: #214fab;
  background-color: #e5ecfa;
  border-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-secondary-inverted__6dhHp, .Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-tertiary-inverted__HBWl5 {
  color: #000;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-secondary-inverted__6dhHp:hover, .Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-tertiary-inverted__HBWl5:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-danger-inverted__p6aqN {
  color: #fff;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-danger-inverted__p6aqN:hover {
  color: #d61200;
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-text__zAK9M {
  border: 2px solid transparent;
}
.Button_button__qOu8O.Button_variant-text__zAK9M.Button_color-primary__s9PDR {
  color: #214fab;
}
.Button_button__qOu8O.Button_variant-text__zAK9M.Button_color-primary__s9PDR:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_size-xs__riZtg {
  padding: 6px 12px;
  font-size: 1.2rem;
}
.Button_button__qOu8O.Button_size-xs__riZtg i {
  font-size: 1.4rem;
}
.Button_button__qOu8O.Button_size-sm__wJOqU {
  padding: 8px 16px;
  font-size: 1.4rem;
}
.Button_button__qOu8O.Button_size-sm__wJOqU i {
  font-size: 1.6rem;
}
.Button_button__qOu8O.Button_size-md__k78ss, .Button_button__qOu8O.Button_size-lg__jW51I {
  padding: 11px 24px;
  font-size: 1.6rem;
  font-weight: 700;
}
.Button_button__qOu8O.Button_size-md__k78ss i, .Button_button__qOu8O.Button_size-lg__jW51I i {
  font-size: 2rem;
  font-weight: 400;
}
@media screen and (min-width: 1302px) {
  .Button_button__qOu8O.Button_size-lg__jW51I {
    gap: 16px;
    padding: 18px 32px;
    font-size: 1.8rem;
  }
  .Button_button__qOu8O.Button_size-lg__jW51I i {
    font-size: 2.4rem;
  }
}
.Button_button__qOu8O .Button_startIcon__gXO6b,
.Button_button__qOu8O .Button_endIcon__HyjGg {
  line-height: 1;
}
.Button_button__qOu8O i {
  vertical-align: middle;
}
.Button_button__qOu8O[aria-disabled=true] {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.3;
}
