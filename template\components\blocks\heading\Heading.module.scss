@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heading {
  margin-top: 32px;
  margin-bottom: 12px;

  @include breakpoint(medium up) {
    margin-top: 32px;
  }

  @include breakpoint(large up) {
    margin-bottom: 16px;
  }

  :global(.block-accordion) &,
  :global(.block-columns) & {
    margin-top: 40px;

    @include breakpoint(medium up) {
      margin-top: 48px;
    }
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

h2.heading {
  margin-top: 64px;

  @include breakpoint(medium up) {
    margin-top: 72px;
  }

  @include breakpoint(large up) {
    margin-top: 96px;
  }
}
