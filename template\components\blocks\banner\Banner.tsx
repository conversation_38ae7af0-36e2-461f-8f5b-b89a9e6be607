"use client";

import { BannerBlock, BlockLayout } from "@/generated/graphql/graphql";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import styles from "./Banner.module.scss";

const Hx = dynamic(() => import("@/components/ui/title/Hx"));

// TODO: Get these values from BE
export enum BannerBlockVariant {
  IMAGE_LEFT = "IMAGE_LEFT",
  IMAGE_RIGHT = "IMAGE_RIGHT",
  IMAGE_ONLY = "IMAGE_ONLY",
  TEXT_ONLY = "TEXT_ONLY",
}

type BannerProps = Partial<Omit<BannerBlock, "__typename" | "innerBlocks">>;

export default function Banner({
  anchor,
  layout = BlockLayout.CONTAINED,
  variant = BannerBlockVariant.IMAGE_LEFT,
  backgroundColor,
  textColor,
  image,
  title,
  description,
  action,
}: BannerProps) {
  const { url: actionUrl, text } = action ?? {};
  const { url: imageUrl, height, width, alt } = image ?? {};
  const showImage = variant !== BannerBlockVariant.TEXT_ONLY && image;
  const showContent = variant !== BannerBlockVariant.IMAGE_ONLY;
  const contentStyle = { color: textColor ?? undefined, backgroundColor: backgroundColor ?? undefined };

  return (
    <section
      id={anchor ?? undefined}
      className={clsx("block-banner", layout === BlockLayout.CONTAINED && "contained", styles.bannerContainer)}
    >
      <div className={clsx(styles.banner, styles[`variant-${variant?.toLowerCase()}`])}>
        {showContent && (title || description) && (
          <div className={styles.content} style={contentStyle}>
            {title && (
              <Hx level={2} className={styles.title}>
                {title}
              </Hx>
            )}
            {description && <p className={styles.description}>{description}</p>}
            {actionUrl && (
              <Link href={actionUrl} className={styles.actionLink}>
                {text}
                <i className="fal fa-arrow-right" aria-hidden="true"></i>
              </Link>
            )}
          </div>
        )}

        {showImage && imageUrl && (
          <div className={styles.imageWrapper}>
            {actionUrl && variant === BannerBlockVariant.IMAGE_ONLY ? (
              <Link href={actionUrl}>
                <Image src={imageUrl} alt={alt ?? ""} width={width} height={height} />
              </Link>
            ) : (
              <Image src={imageUrl} alt={alt ?? ""} width={width} height={height} />
            )}
          </div>
        )}
      </div>
    </section>
  );
}
