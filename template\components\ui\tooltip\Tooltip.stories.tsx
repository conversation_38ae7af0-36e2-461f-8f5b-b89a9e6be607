import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Tooltip from "./Tooltip";

// Create a wrapped button component for demonstration
const meta: Meta<typeof Tooltip> = {
  title: "Components/Tooltip",
  component: Tooltip,
  tags: ["autodocs"],
  argTypes: {
    placement: {
      options: ["top", "right", "bottom", "left", "top-left", "top-right", "bottom-left", "bottom-right"],
      control: { type: "select" },
      defaultValue: "top",
    },
    variant: {
      options: ["primary", "secondary", "tertiary", "neutral"],
      control: { type: "select" },
      defaultValue: "primary",
    },
    size: {
      options: ["md", "lg"],
      control: { type: "radio" },
      defaultValue: "md",
    },
    isOpen: {
      options: [true, false],
      control: { type: "radio" },
      defaultValue: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
  parameters: {
    viewport: {
      viewports: {
        small: {
          name: "Small",
          styles: {
            width: "320px",
            height: "568px",
          },
        },
        large: {
          name: "Large",
          styles: {
            width: "1024px",
            height: "1968px",
          },
        },
      },
      defaultViewport: "large",
    },
  },
  args: {
    content: "This tooltip should adapt to viewport size",
    children: <span>Test me in different viewports</span>,
    isOpen: true,
  },
  decorators: [
    (Story) => (
      <div
        style={{
          minHeight: "50vh",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "3rem",
        }}
      >
        <Story />
      </div>
    ),
  ],
};

export const Placement: Story = {
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(3, 1fr)",
        gap: "4rem",
        placeItems: "center",
        justifyContent: "space-between",
        padding: "6rem",
      }}
    >
      <Tooltip isOpen content="Top Left" placement="top-left">
        <span>Top Left</span>
      </Tooltip>
      <Tooltip isOpen content="Top" placement="top">
        <span>Top</span>
      </Tooltip>
      <Tooltip isOpen content="Top Right" placement="top-right">
        <span>Top Right</span>
      </Tooltip>
      <Tooltip isOpen content="Left" placement="left">
        <span>Left</span>
      </Tooltip>
      <span></span>
      <Tooltip isOpen content="Right" placement="right">
        <span>Right</span>
      </Tooltip>
      <Tooltip isOpen content="Bottom Left" placement="bottom-left">
        <span>Bottom Left</span>
      </Tooltip>
      <Tooltip isOpen content="Bottom" placement="bottom">
        <span>Bottom</span>
      </Tooltip>
      <Tooltip isOpen content="Bottom Right" placement="bottom-right">
        <span>Bottom Right</span>
      </Tooltip>
    </div>
  ),
};
