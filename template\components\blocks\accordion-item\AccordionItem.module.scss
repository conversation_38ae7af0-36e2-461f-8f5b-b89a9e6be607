@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.accordionItem {
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }

  &:not([open]) {
    .closedIcon {
      display: none;
    }
  }

  &[open] {
    .openIcon {
      display: none;
    }
  }
}

.trigger {
  display: flex;
  gap: 12px;
  align-items: baseline;
  justify-content: space-between;
  width: 100%;
  padding: 4px 8px 4px 24px;
  font-weight: 700;
  line-height: 110%;
  text-align: left;
  cursor: pointer;
  background-color: $color-primary-50;
  transition:
    color 100ms linear,
    background-color 100ms linear;

  .accordionItem[open] & {
    color: $color-white;
    background-color: $color-primary-500;
  }

  .title {
    padding-block: 15px;
  }

  i {
    padding: 10px;
    line-height: 120%;
    vertical-align: middle;
  }

  &:hover {
    color: $color-white;
    background-color: $color-primary-400;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.content {
  padding-block: 16px;

  @include breakpoint(medium up) {
    padding: 24px 48px;
  }

  @include breakpoint(large up) {
    padding: 32px 56px;
  }
}
