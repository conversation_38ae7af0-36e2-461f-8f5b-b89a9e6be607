import type { Meta, StoryObj } from "@storybook/nextjs";
import Textfield from "./Textfield";

const meta: Meta<typeof Textfield> = {
  title: "Components/Textfield",
  component: Textfield,
  argTypes: {
    startIcon: {
      control: {
        type: "boolean",
      },
      mapping: {
        false: "",
        true: "fa-regular fa-circle-user",
      },
    },
    endIcon: {
      control: {
        type: "boolean",
      },
      mapping: {
        false: "",
        true: "fa-regular fa-circle-user",
      },
    },
    required: {
      control: {
        type: "boolean",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Textfield>;

export const Default: Story = {
  args: {},
};

export const Multiline: Story = {
  args: {
    placeholder: "Multiline",
    multiline: true,
  },
};

export const Labeled: Story = {
  args: {
    label: "Que peut-on faire pour vous ?",
  },
};

export const Decorated: Story = {
  args: {
    placeholder: "Icons on the left and the right side",
    startIcon: "far fa-envelope",
    endIcon: "far fa-envelope",
  },
};

export const Error: Story = {
  args: {
    placeholder: "Error",
    error: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Disabled",
    disabled: true,
  },
};

export const OutlinedSmall: Story = {
  args: {
    placeholder: "Outlined Small",
    variant: "outlined",
    size: "small",
  },
};

export const OutlinedMedium: Story = {
  args: {
    placeholder: "Outlined Medium",
    variant: "outlined",
    size: "small",
  },
};

export const OutlinedLarge: Story = {
  args: {
    placeholder: "Outlined Large",
    variant: "outlined",
    size: "large",
  },
};

export const UnderlineSmall: Story = {
  args: {
    placeholder: "Underline Small",
    variant: "underline",
    size: "small",
  },
};

export const UnderlineMedium: Story = {
  args: {
    placeholder: "Underline Medium",
    variant: "underline",
    size: "small",
  },
};

export const UnderlineLarge: Story = {
  args: {
    placeholder: "Underline Large",
    variant: "underline",
    size: "large",
  },
};

export const FilledSmall: Story = {
  args: {
    placeholder: "Filled Small",
    variant: "filled",
    size: "small",
  },
};

export const FilledMedium: Story = {
  args: {
    placeholder: "Filled Medium",
    variant: "filled",
    size: "small",
  },
};

export const FilledLarge: Story = {
  args: {
    placeholder: "Filled Large",
    variant: "filled",
    size: "large",
  },
};
