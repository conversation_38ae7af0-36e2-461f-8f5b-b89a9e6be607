"use client";

import Textfield from "@/components/ui/textfield/Textfield";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import styles from "@/styles/ErrorPage.module.scss";
import Image from "next/image";
import Link from "next/link";

// TODO: Get contact page url from siteConfig
// TODO: Form vers global search page
export default function NotFound() {
  return (
    <div className="container">
      <div className={styles.errorPage}>
        <div className={styles.content}>
          <h1>Erreur 404</h1>
          <h2>
            Nous sommes désolés, cette page est <em>introuvable !</em>
          </h2>
          <p>
            L'URL saisie est peut-être erronée ou bien la page n'existe pas ou n'est plus en ligne... Mais pas
            d'inquiétude, nous allons vous aider à retrouver votre chemin :)
          </p>
          <Link href="/" className={styles.returnLink}>
            <i className="far fa-undo" aria-hidden="true"></i>
            Retourner à l'accueil
          </Link>
          <form className={styles.searchForm} action="/recherche">
            <Textfield
              type="search"
              name="s"
              label="Rechercher sur le site ..."
              variant="underline"
              size="large"
              className={styles.searchInput}
              endIcon={
                <Tooltip content="Rechercher sur le site">
                  <button type="submit" className={styles.searchButton}>
                    <i className="far fa-search" aria-hidden="true"></i>
                    <span className="sr-only">Rechercher sur le site</span>
                  </button>
                </Tooltip>
              }
            />
          </form>
        </div>
        <Image className={styles.illustration} src="/images/404.svg" width={538} height={559} alt="" />
      </div>
      <p className={styles.contact}>
        Pour toute question nous vous remercions de bien vouloir{" "}
        <Link href="/contact">nous contacter par le formulaire du site</Link>
      </p>
    </div>
  );
}
