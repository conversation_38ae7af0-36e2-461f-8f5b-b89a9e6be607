"use client";

import clsx from "clsx";
import { useState } from "react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import type { ImageProps } from "./Image";
import { ImageWrapper, WithCaption } from "./Image";
import styles from "./Image.module.scss";

export default function ImageLightbox({ caption, src, alt, width, height, aspectRatio, scale }: ImageProps) {
  const [open, setOpen] = useState(false);

  if (!src) {
    return null;
  }

  const handleClick = (event: React.MouseEvent) => {
    event.preventDefault();

    setOpen(true);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === " " || event.key === "Enter") {
      event.preventDefault();

      setOpen(true);
    }
  };

  return (
    <div className={clsx("block-image", styles.imageContainer)}>
      <WithCaption caption={caption}>
        <a href={src} aria-haspopup="dialog" onClick={handleClick} onKeyDown={handleKeyDown}>
          <ImageWrapper aspectRatio={aspectRatio ?? undefined} width={width} height={height}>
            {/* eslint-disable-next-line @next/next/no-img-element -- Image dimensions are user defined */}
            <img
              src={src}
              className={styles.image}
              alt={alt ? `Agrandir l'image : ${alt}` : "Agrandir l'image décorative"}
              style={{ objectFit: (scale ?? "contain") as React.CSSProperties["objectFit"] }}
            />
          </ImageWrapper>
        </a>
      </WithCaption>

      <Lightbox
        open={open}
        close={() => setOpen(false)}
        slides={[
          {
            src: src,
            alt: alt ?? "",
          },
        ]}
        labels={{
          Close: "Fermer le dialogue",
          Previous: "Image précédente",
          Next: "Image suivante",
          Carousel: "Carrousel",
          "Photo gallery": "Galerie photos",
          "{index} of {total}": "{index} sur {total}",
          Slide: "Image",
          Lightbox: "Visionneuse d'images",
        }}
        carousel={{ finite: true }}
        render={{
          buttonPrev: () => null,
          buttonNext: () => null,
        }}
      />
    </div>
  );
}
