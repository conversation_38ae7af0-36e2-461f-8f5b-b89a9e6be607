"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8";
exports.ids = ["vendor-chunks/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.rsc.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.rsc.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApolloClient: () => (/* binding */ ApolloClient),\n/* harmony export */   DebounceMultipartResponsesLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.DebounceMultipartResponsesLink),\n/* harmony export */   InMemoryCache: () => (/* binding */ InMemoryCache),\n/* harmony export */   RemoveMultipartDirectivesLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.RemoveMultipartDirectivesLink),\n/* harmony export */   SSRMultipartLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.SSRMultipartLink),\n/* harmony export */   built_for_rsc: () => (/* binding */ built_for_rsc),\n/* harmony export */   registerApolloClient: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.registerApolloClient)\n/* harmony export */ });\n/* harmony import */ var _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client-react-streaming */ \"(rsc)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.rsc.js\");\n\n\n\n// src/index.shared.ts\n\n// src/bundleInfo.ts\nvar bundle = {\n  pkg: \"@apollo/experimental-nextjs-app-support\",\n  client: \"ApolloClient\",\n  cache: \"InMemoryCache\"\n};\nvar ApolloClient = class extends _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.ApolloClient {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n};\nvar InMemoryCache = class extends _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n};\nconst built_for_rsc = true;\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.rsc.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.rsc.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.ssr.js":
/*!*****************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.ssr.js ***!
  \*****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApolloClient: () => (/* binding */ ApolloClient),\n/* harmony export */   ApolloNextAppProvider: () => (/* binding */ ApolloNextAppProvider),\n/* harmony export */   DebounceMultipartResponsesLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.DebounceMultipartResponsesLink),\n/* harmony export */   InMemoryCache: () => (/* binding */ InMemoryCache),\n/* harmony export */   RemoveMultipartDirectivesLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.RemoveMultipartDirectivesLink),\n/* harmony export */   SSRMultipartLink: () => (/* reexport safe */ _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.SSRMultipartLink),\n/* harmony export */   built_for_ssr: () => (/* binding */ built_for_ssr),\n/* harmony export */   resetApolloClientSingletons: () => (/* binding */ resetApolloClientSingletons)\n/* harmony export */ });\n/* harmony import */ var _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client-react-streaming */ \"(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/index.ssr.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _apollo_client_react_streaming_manual_transport__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @apollo/client-react-streaming/manual-transport */ \"(ssr)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/manual-transport.ssr.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/api/navigation.js\");\n\n\n\n\n\n\n// src/index.shared.ts\n\n// src/bundleInfo.ts\nvar bundle = {\n  pkg: \"@apollo/experimental-nextjs-app-support\",\n  client: \"ApolloClient\",\n  cache: \"InMemoryCache\"\n};\nvar ApolloClient = class extends _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.ApolloClient {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n};\nvar InMemoryCache = class extends _apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.InMemoryCache {\n  /**\n   * Information about the current package and it's export names, for use in error messages.\n   *\n   * @internal\n   */\n  static info = bundle;\n};\nvar ApolloNextAppProvider = /* @__PURE__ */ (0,_apollo_client_react_streaming__WEBPACK_IMPORTED_MODULE_0__.WrapApolloProvider)(\n  (0,_apollo_client_react_streaming_manual_transport__WEBPACK_IMPORTED_MODULE_2__.buildManualDataTransport)({\n    useInsertHtml() {\n      const insertHtml = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(next_navigation_js__WEBPACK_IMPORTED_MODULE_3__.ServerInsertedHTMLContext);\n      if (!insertHtml) {\n        throw new Error(\n          'The SSR build of ApolloNextAppProvider cannot be used outside of the Next App Router!\\nIf you encounter this in a test, make sure that your tests are using the browser build by adding the \"browser\" import condition to your test setup.'\n        );\n      }\n      return insertHtml;\n    }\n  })\n);\nApolloNextAppProvider.info = bundle;\nvar resetApolloClientSingletons = _apollo_client_react_streaming_manual_transport__WEBPACK_IMPORTED_MODULE_2__.resetManualSSRApolloSingletons;\nconst built_for_ssr = true;\n\n\n//# sourceMappingURL=out.js.map\n//# sourceMappingURL=index.ssr.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhcG9sbG8rZXhwZXJpbWVudGFsLW5leHRqc185MzgzODk4MzcwYWQwMGY3YmE3MGI0MTMzZTZhNzZmOC9ub2RlX21vZHVsZXMvQGFwb2xsby9leHBlcmltZW50YWwtbmV4dGpzLWFwcC1zdXBwb3J0L2Rpc3QvaW5kZXguc3NyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzSTtBQUNMO0FBQzlGO0FBQ3dGO0FBQzVEOztBQUUvRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMsd0VBQWM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MseUVBQWU7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsa0ZBQWtCO0FBQzlELEVBQUUseUdBQXdCO0FBQzFCO0FBQ0EseUJBQXlCLGlEQUFVLENBQUMseUVBQXlCO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxrQ0FBa0MsMkdBQThCO0FBQ2hFOztBQUUwRztBQUMxRztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhcG9sbG8rZXhwZXJpbWVudGFsLW5leHRqc185MzgzODk4MzcwYWQwMGY3YmE3MGI0MTMzZTZhNzZmOFxcbm9kZV9tb2R1bGVzXFxAYXBvbGxvXFxleHBlcmltZW50YWwtbmV4dGpzLWFwcC1zdXBwb3J0XFxkaXN0XFxpbmRleC5zc3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgV3JhcEFwb2xsb1Byb3ZpZGVyLCBBcG9sbG9DbGllbnQgYXMgQXBvbGxvQ2xpZW50JDEsIEluTWVtb3J5Q2FjaGUgYXMgSW5NZW1vcnlDYWNoZSQxIH0gZnJvbSAnQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nJztcbmV4cG9ydCB7IERlYm91bmNlTXVsdGlwYXJ0UmVzcG9uc2VzTGluaywgUmVtb3ZlTXVsdGlwYXJ0RGlyZWN0aXZlc0xpbmssIFNTUk11bHRpcGFydExpbmsgfSBmcm9tICdAYXBvbGxvL2NsaWVudC1yZWFjdC1zdHJlYW1pbmcnO1xuaW1wb3J0IHsgdXNlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IGJ1aWxkTWFudWFsRGF0YVRyYW5zcG9ydCwgcmVzZXRNYW51YWxTU1JBcG9sbG9TaW5nbGV0b25zIH0gZnJvbSAnQGFwb2xsby9jbGllbnQtcmVhY3Qtc3RyZWFtaW5nL21hbnVhbC10cmFuc3BvcnQnO1xuaW1wb3J0IHsgU2VydmVySW5zZXJ0ZWRIVE1MQ29udGV4dCB9IGZyb20gJ25leHQvbmF2aWdhdGlvbi5qcyc7XG5cbi8vIHNyYy9pbmRleC5zaGFyZWQudHNcblxuLy8gc3JjL2J1bmRsZUluZm8udHNcbnZhciBidW5kbGUgPSB7XG4gIHBrZzogXCJAYXBvbGxvL2V4cGVyaW1lbnRhbC1uZXh0anMtYXBwLXN1cHBvcnRcIixcbiAgY2xpZW50OiBcIkFwb2xsb0NsaWVudFwiLFxuICBjYWNoZTogXCJJbk1lbW9yeUNhY2hlXCJcbn07XG52YXIgQXBvbGxvQ2xpZW50ID0gY2xhc3MgZXh0ZW5kcyBBcG9sbG9DbGllbnQkMSB7XG4gIC8qKlxuICAgKiBJbmZvcm1hdGlvbiBhYm91dCB0aGUgY3VycmVudCBwYWNrYWdlIGFuZCBpdCdzIGV4cG9ydCBuYW1lcywgZm9yIHVzZSBpbiBlcnJvciBtZXNzYWdlcy5cbiAgICpcbiAgICogQGludGVybmFsXG4gICAqL1xuICBzdGF0aWMgaW5mbyA9IGJ1bmRsZTtcbn07XG52YXIgSW5NZW1vcnlDYWNoZSA9IGNsYXNzIGV4dGVuZHMgSW5NZW1vcnlDYWNoZSQxIHtcbiAgLyoqXG4gICAqIEluZm9ybWF0aW9uIGFib3V0IHRoZSBjdXJyZW50IHBhY2thZ2UgYW5kIGl0J3MgZXhwb3J0IG5hbWVzLCBmb3IgdXNlIGluIGVycm9yIG1lc3NhZ2VzLlxuICAgKlxuICAgKiBAaW50ZXJuYWxcbiAgICovXG4gIHN0YXRpYyBpbmZvID0gYnVuZGxlO1xufTtcbnZhciBBcG9sbG9OZXh0QXBwUHJvdmlkZXIgPSAvKiBAX19QVVJFX18gKi8gV3JhcEFwb2xsb1Byb3ZpZGVyKFxuICBidWlsZE1hbnVhbERhdGFUcmFuc3BvcnQoe1xuICAgIHVzZUluc2VydEh0bWwoKSB7XG4gICAgICBjb25zdCBpbnNlcnRIdG1sID0gdXNlQ29udGV4dChTZXJ2ZXJJbnNlcnRlZEhUTUxDb250ZXh0KTtcbiAgICAgIGlmICghaW5zZXJ0SHRtbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgJ1RoZSBTU1IgYnVpbGQgb2YgQXBvbGxvTmV4dEFwcFByb3ZpZGVyIGNhbm5vdCBiZSB1c2VkIG91dHNpZGUgb2YgdGhlIE5leHQgQXBwIFJvdXRlciFcXG5JZiB5b3UgZW5jb3VudGVyIHRoaXMgaW4gYSB0ZXN0LCBtYWtlIHN1cmUgdGhhdCB5b3VyIHRlc3RzIGFyZSB1c2luZyB0aGUgYnJvd3NlciBidWlsZCBieSBhZGRpbmcgdGhlIFwiYnJvd3NlclwiIGltcG9ydCBjb25kaXRpb24gdG8geW91ciB0ZXN0IHNldHVwLidcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBpbnNlcnRIdG1sO1xuICAgIH1cbiAgfSlcbik7XG5BcG9sbG9OZXh0QXBwUHJvdmlkZXIuaW5mbyA9IGJ1bmRsZTtcbnZhciByZXNldEFwb2xsb0NsaWVudFNpbmdsZXRvbnMgPSByZXNldE1hbnVhbFNTUkFwb2xsb1NpbmdsZXRvbnM7XG5jb25zdCBidWlsdF9mb3Jfc3NyID0gdHJ1ZTtcblxuZXhwb3J0IHsgQXBvbGxvQ2xpZW50LCBBcG9sbG9OZXh0QXBwUHJvdmlkZXIsIEluTWVtb3J5Q2FjaGUsIGJ1aWx0X2Zvcl9zc3IsIHJlc2V0QXBvbGxvQ2xpZW50U2luZ2xldG9ucyB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b3V0LmpzLm1hcFxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguc3NyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@apollo+experimental-nextjs_9383898370ad00f7ba70b4133e6a76f8/node_modules/@apollo/experimental-nextjs-app-support/dist/index.ssr.js\n");

/***/ })

};
;