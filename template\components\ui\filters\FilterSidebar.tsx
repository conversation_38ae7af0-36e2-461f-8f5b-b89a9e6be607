"use client";

import FilterRenderer from "@/components/filters/FilterRenderer";
import { FilterInterface } from "@/generated/graphql/graphql";
import { FilterValues } from "@/lib/filters";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";
import styles from "./FilterSidebar.module.scss";

interface FilterSidebarProps {
  filters: FilterInterface[];
  title: string;
  url: string;
}

export default function FilterSidebar({ filters, title, url }: FilterSidebarProps) {
  const searchParams = useSearchParams();

  const filterValues = useMemo(() => {
    const map: FilterValues = new Map();

    for (const filter of filters) {
      if (searchParams.has(filter.attribute) && searchParams.getAll(filter.attribute).some(Boolean)) {
        map.set(filter.attribute, searchParams.getAll(filter.attribute));
      }
    }

    return map;
  }, [filters, searchParams]);

  return (
    <div className="widget-search-filters">
      {title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.content}>
        <FilterRenderer filters={filters} filterValues={filterValues} url={url} />
      </div>
    </div>
  );
}
