import { render, screen } from "@testing-library/react";
import { describe, expect, test } from "vitest";
import useTitleLevel, { SubtitleLevelProvider, TitleLevelProvider } from "./useTitleLevel";

// A block that can contains other blocks.
// The titleLevel is either defined as props or inferred by the depth of hooks.
function Block({ title, titleLevel, children }: React.PropsWithChildren<{ title: string; titleLevel?: number }>) {
  const currentLevel = useTitleLevel();
  const level = titleLevel ?? currentLevel;
  const Title = `h${level}` as React.ElementType;
  return (
    <div>
      <Title>{title}</Title>
      <SubtitleLevelProvider>{children}</SubtitleLevelProvider>
    </div>
  );
}

describe("useTitleLevel", () => {
  test("is level 1 by default", () => {
    render(<Block title="Heading" />);
    expect(screen.getByText("Heading").tagName).toBe("H1");
  });

  test("increases title level automatically", () => {
    render(
      <Block title="NewsBlock">
        <Block title="NewsFocus" />
        <Block title="NewsList">
          <Block title="News 1" />
          <Block title="News 2" />
          <Block title="News 3" />
        </Block>
        <Block title="BriefList">
          <Block title="BriefNews 1" />
          <Block title="BriefNews 2" />
          <Block title="BriefNews 3" />
        </Block>
      </Block>
    );

    expect(screen.getByText("NewsBlock").tagName).toBe("H1");
    expect(screen.getByText("NewsFocus").tagName).toBe("H2");
    expect(screen.getByText("NewsList").tagName).toBe("H2");
    expect(screen.getByText("News 1").tagName).toBe("H3");
    expect(screen.getByText("News 2").tagName).toBe("H3");
    expect(screen.getByText("News 3").tagName).toBe("H3");
    expect(screen.getByText("BriefList").tagName).toBe("H2");
    expect(screen.getByText("BriefNews 1").tagName).toBe("H3");
    expect(screen.getByText("BriefNews 2").tagName).toBe("H3");
    expect(screen.getByText("BriefNews 3").tagName).toBe("H3");
  });

  test("cannot go past level 6", () => {
    render(
      <Block title="Level 1">
        <Block title="Level 2">
          <Block title="Level 3">
            <Block title="Level 4">
              <Block title="Level 5">
                <Block title="Level 6">
                  <Block title="Level 7" />
                </Block>
              </Block>
            </Block>
          </Block>
        </Block>
      </Block>
    );

    expect(screen.getByText("Level 1").tagName).toBe("H1");
    expect(screen.getByText("Level 2").tagName).toBe("H2");
    expect(screen.getByText("Level 3").tagName).toBe("H3");
    expect(screen.getByText("Level 4").tagName).toBe("H4");
    expect(screen.getByText("Level 5").tagName).toBe("H5");
    expect(screen.getByText("Level 6").tagName).toBe("H6");
    expect(screen.getByText("Level 7").tagName).toBe("H6");
  });

  test("can start at a lower level", () => {
    render(
      <TitleLevelProvider level={2}>
        <Block title="Level 2">
          <Block title="Level 3" />
        </Block>
      </TitleLevelProvider>
    );

    expect(screen.getByText("Level 2").tagName).toBe("H2");
    expect(screen.getByText("Level 3").tagName).toBe("H3");
  });
});
