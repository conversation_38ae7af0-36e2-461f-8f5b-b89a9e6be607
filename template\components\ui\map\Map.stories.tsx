import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Map from "./Map";
import { markers } from "./markers.mock";

const meta: Meta<typeof Map> = {
  title: "Components/Map",
  component: Map,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Map>;

export const Default: Story = {
  args: {
    markers: markers,
    style: {
      height: "500px",
    },
  },
};
