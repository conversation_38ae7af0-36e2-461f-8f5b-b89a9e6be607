import { SitemapBlock as SitemapBlockTypes, SitemapEntry } from "@/generated/graphql/graphql";
import { AccordionContent, Content, Header, Item, Root, Trigger } from "@radix-ui/react-accordion";
import clsx from "clsx";
import Link from "next/link";
import React from "react";
import styles from "./Sitemap.module.scss";

type SitemapBlockProps = Partial<Omit<SitemapBlockTypes, "__typename" | "innerBlocks">>;

export default function Sitemap({ sitemap }: SitemapBlockProps) {
  if (!sitemap?.length) return null;

  const renderTitle = (url: string, className: string, title: string, extraProps = {}) =>
    url ? (
      <Link href={url} className={className} {...extraProps}>
        {title}
      </Link>
    ) : (
      <span className={className} {...extraProps}>
        {title}
      </span>
    );

  const renderEntry = (entry: SitemapEntry, index: number): React.ReactNode => {
    const children = entry.children?.filter((child) => child?.title) || [];
    const levelClass = styles[`level-${entry.level}`];
    const hasChildren = children.length > 0;

    if (entry.level === 0) {
      const value = `accordion-${index}`;
      return (
        <li key={index} className={clsx(styles.listItem, levelClass)}>
          <Item id={value} value={value} className={styles.accordion}>
            <Header asChild className={styles.accordionHeader}>
              <div>
                {renderTitle(entry.url, clsx(styles.title, levelClass), entry.title, {
                  "aria-label": entry.title,
                })}

                {hasChildren && (
                  <Trigger aria-labelledby={value} className={styles.accordionTrigger}>
                    <i className={clsx("far fa-plus", styles.openIcon)} aria-hidden="true" />

                    <i className={clsx("far fa-minus", styles.closedIcon)} aria-hidden="true" />
                  </Trigger>
                )}
              </div>
            </Header>

            <Content asChild className={styles.accordionContent} aria-labelledby={value}>
              <AccordionContent asChild>
                <ul className={clsx(styles.list, levelClass)}>
                  {children.map((entry, index) => renderEntry(entry, index))}
                </ul>
              </AccordionContent>
            </Content>
          </Item>
        </li>
      );
    }

    return (
      <li key={index} className={clsx(styles.listItem, levelClass)}>
        {renderTitle(entry.url, clsx(styles.title, levelClass), entry.title)}

        {hasChildren && entry.level === 1 && (
          <ul className={clsx(styles.list, levelClass)}>{children.map((entry, index) => renderEntry(entry, index))}</ul>
        )}
      </li>
    );
  };

  return (
    <section className={clsx("block-sitemap contained", styles.sitemap)}>
      <Root type="multiple" asChild>
        <ul className={styles.list}>
          {sitemap.filter((entry) => entry?.title).map((entry, index) => renderEntry(entry, index))}
        </ul>
      </Root>
    </section>
  );
}
