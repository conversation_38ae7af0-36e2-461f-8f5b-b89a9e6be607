import clsx from "clsx";
import styles from "./Radio.module.scss";

interface RadioProps {
  error?: boolean;
  size?: "small" | "medium";
}

export default function Radio({
  error,
  size,
  className,
  ...restProps
}: RadioProps & Omit<React.InputHTMLAttributes<HTMLInputElement>, "size" | "type">) {
  return (
    <input
      type="radio"
      className={clsx(styles.input, error && styles.error, size && styles[`size-${size}`], className)}
      {...restProps}
    />
  );
}
