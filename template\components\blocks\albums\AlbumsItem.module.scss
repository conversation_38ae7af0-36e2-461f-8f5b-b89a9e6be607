@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.albumsItem {
  position: relative;
  display: flex;
  flex-direction: column-reverse;
}

.details {
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image {
  width: 100%;
}

.top {
  display: flex;
  flex-direction: row-reverse;
  gap: 16px;
  justify-content: left;

  @container (min-width: 800px) {
    flex-direction: column-reverse;
  }

  @include breakpoint(medium up) {
    flex-direction: column-reverse;
    gap: 12px;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 200ms ease-in-out;

  @include size(64px);

  &.color-isVideo {
    background-color: $color-secondary-500;

    &:hover {
      background-color: $color-secondary-300;
    }
  }

  &.color-isImage {
    color: $color-white;
    background-color: $color-primary-500;

    &:hover {
      background-color: $color-primary-300;
    }
  }

  > i {
    font-size: 2.4rem;
    text-align: center;
  }

  @include breakpoint(large up) {
    @include size(96px);

    > i {
      font-size: 3.2rem;
    }
  }
}

.titleWrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  width: 83%;
  margin-top: -50px;

  @include breakpoint(large up) {
    margin-top: -48px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    width: 65%;
    font-size: 2.4rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.titleLink {
  @extend %underline;
  @extend %link-block;
}
