@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.stepper {
  display: flex;
  gap: 6px;
  align-items: center;
  min-height: 15px;
}

.separator {
  flex: 1;
  height: 4px;
  background-color: $color-primary-50;
  border-radius: 4px;
  transition: background-color 200ms ease-in;

  &.active {
    background-color: $color-primary-500;
  }
}

.step {
  @include size(9px);

  margin-inline: 3px;
  background-color: $color-primary-50;
  border-radius: 50%;
  transition:
    margin 200ms ease,
    width 200ms ease,
    height 200ms ease,
    background-color 200ms ease;

  &.active {
    @include size(15px);

    margin-inline: 0;
    background-color: $color-primary-500;
  }
}
