import { getDocument } from "ssr-window";
import { AutoplayOptions, PaginationOptions, SwiperEvents, SwiperModule } from "swiper/types";
import { classesToSelector, elementIndex, getRandomNumber, makeElementsArray } from "./utils";

/**
 * Swiper module to handle accessibility.
 * Fork of https://github.com/nolimits4web/swiper/blob/master/src/modules/a11y/a11y.mjs
 */
const A11y: SwiperModule = ({ swiper, extendParams, on }) => {
  extendParams({
    a11y: {
      enabled: true,
      notificationClass: "swiper-notification",
      prevSlideMessage: "Previous slide",
      nextSlideMessage: "Next slide",
      firstSlideMessage: "This is the first slide",
      lastSlideMessage: "This is the last slide",
      prevButtonMessage: null,
      nextButtonMessage: null,
      paginationBulletMessage: "Go to slide {{index}}",
      slideLabelMessage: "{{index}} / {{slidesLength}}",
      containerMessage: null,
      containerRoleDescriptionMessage: null,
      containerRole: null,
      itemRoleDescriptionMessage: null,
      slideRole: "group",
      id: null,
      scrollOnFocus: true,
      inertInvisibleSlides: false,
    },
  });

  swiper.a11y = {
    clicked: false,
  };

  let liveRegion: HTMLElement | undefined;
  let preventFocusHandler: boolean | undefined;
  let focusTargetSlideElement: HTMLElement | undefined;
  let visibilityChangedTimestamp = Date.now();

  function notify(message?: string) {
    if (liveRegion) {
      liveRegion.innerHTML = message ?? "";
    }
  }

  function onEnterOrSpaceKey(e: KeyboardEvent) {
    if (e.key !== "Enter" && e.key !== " ") {
      return;
    }

    e.preventDefault();

    const params = swiper.params.a11y!;
    const targetElement = e.target;

    if (
      swiper.pagination &&
      swiper.pagination.el &&
      (targetElement === swiper.pagination.el || swiper.pagination.el.contains(e.target as HTMLElement)) &&
      !(e.target as HTMLElement).matches(classesToSelector((swiper.params.pagination as PaginationOptions).bulletClass))
    ) {
      return;
    }

    if (swiper.navigation && swiper.navigation.prevEl && swiper.navigation.nextEl) {
      const previousEls = makeElementsArray(swiper.navigation.prevEl);
      const nextEls = makeElementsArray(swiper.navigation.nextEl);

      if (nextEls.includes(targetElement as HTMLElement)) {
        if (!(swiper.isEnd && !swiper.params.loop)) {
          swiper.slideNext();
        }

        if (swiper.isEnd) {
          notify(params.lastSlideMessage);
        } else {
          notify(params.nextSlideMessage);
        }
      }

      if (previousEls.includes(targetElement as HTMLElement)) {
        if (!(swiper.isBeginning && !swiper.params.loop)) {
          swiper.slidePrev();
        }

        if (swiper.isBeginning) {
          notify(params.firstSlideMessage);
        } else {
          notify(params.prevSlideMessage);
        }
      }
    }

    if (
      swiper.pagination &&
      (targetElement as HTMLElement).matches(
        classesToSelector((swiper.params.pagination as PaginationOptions).bulletClass)
      )
    ) {
      (targetElement as HTMLElement).click();
    }
  }

  function updateNavigation() {
    if (swiper.params.loop || swiper.params.rewind || !swiper.navigation) return;
    const { nextEl, prevEl } = swiper.navigation;

    if (prevEl) {
      if (swiper.isBeginning) {
        for (const element of makeElementsArray(prevEl)) {
          element.setAttribute("aria-disabled", "true");
          element.setAttribute("tabindex", "-1");
        }
      } else {
        for (const element of makeElementsArray(prevEl)) {
          element.setAttribute("aria-disabled", "false");
          element.setAttribute("tabindex", "0");
        }
      }
    }

    if (nextEl) {
      if (swiper.isEnd) {
        for (const element of makeElementsArray(nextEl)) {
          element.setAttribute("aria-disabled", "true");
          element.setAttribute("tabindex", "-1");
        }
      } else {
        for (const element of makeElementsArray(nextEl)) {
          element.setAttribute("aria-disabled", "false");
          element.setAttribute("tabindex", "0");
        }
      }
    }
  }

  function hasPagination(): boolean {
    return swiper.pagination && swiper.pagination.bullets && swiper.pagination.bullets.length > 0;
  }

  function hasClickablePagination(): boolean {
    return hasPagination() && ((swiper.params.pagination as PaginationOptions | undefined)?.clickable ?? false);
  }

  function updatePagination() {
    if (!hasPagination()) {
      return;
    }

    const params = swiper.params.a11y!;
    const paginationParams = swiper.params.pagination as PaginationOptions;

    for (const bulletElement of swiper.pagination.bullets) {
      if (paginationParams.clickable && !paginationParams.renderBullet) {
        bulletElement.setAttribute("tabindex", "0");
        bulletElement.setAttribute("role", "button");

        if (params.paginationBulletMessage) {
          bulletElement.setAttribute(
            "aria-label",
            params.paginationBulletMessage.replace("{{index}}", String(elementIndex(bulletElement) ?? 0 + 1))
          );
        }
      }

      const isActive = bulletElement.matches(classesToSelector(paginationParams.bulletActiveClass));

      if (bulletElement.getAttribute("role") === "tab") {
        bulletElement.setAttribute("aria-selected", String(isActive));
      } else {
        if (isActive) {
          bulletElement.setAttribute("aria-current", "true");
        } else {
          bulletElement.removeAttribute("aria-current");
        }
      }
    }
  }

  function initNavElement(element: HTMLElement, wrapperId?: string, message?: string | null) {
    element.setAttribute("tabindex", "0");

    if (element.tagName !== "BUTTON") {
      element.setAttribute("role", "button");
      element.addEventListener("keydown", onEnterOrSpaceKey);
    }

    if (wrapperId) {
      element.setAttribute("aria-controls", wrapperId);
    }

    if (message) {
      element.setAttribute("aria-label", message);
    }
  }

  function handlePointerDown(e: PointerEvent) {
    if (
      focusTargetSlideElement &&
      focusTargetSlideElement !== e.target &&
      !focusTargetSlideElement.contains(e.target as HTMLElement)
    ) {
      preventFocusHandler = true;
    }

    swiper.a11y.clicked = true;
  }

  function handlePointerUp() {
    preventFocusHandler = false;
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        if (!swiper.destroyed) {
          swiper.a11y.clicked = false;
        }
      });
    });
  }

  function onVisibilityChange() {
    visibilityChangedTimestamp = Date.now();
  }

  function handleFocus(e: FocusEvent) {
    if (
      swiper.a11y!.clicked ||
      !swiper.params.a11y!.scrollOnFocus ||
      !e.target ||
      Date.now() - visibilityChangedTimestamp < 100
    ) {
      return;
    }

    const slideElement = (e.target as HTMLElement).closest<HTMLElement>(`.${swiper.params.slideClass}, swiper-slide`);

    if (!slideElement || !swiper.slides.includes(slideElement)) {
      return;
    }

    focusTargetSlideElement = slideElement;
    const isActive = swiper.slides.indexOf(slideElement) === swiper.activeIndex;
    const isVisible =
      swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideElement);

    if (isActive || isVisible) {
      return;
    }

    // TODO: This is not supported in FF https://developer.mozilla.org/en-US/docs/Web/API/UIEvent/sourceCapabilities
    // if (e.sourceCapabilities && e.sourceCapabilities.firesTouchEvents) return;

    if (swiper.isHorizontal()) {
      swiper.el.scrollLeft = 0;
    } else {
      swiper.el.scrollTop = 0;
    }

    requestAnimationFrame(() => {
      if (preventFocusHandler) {
        return;
      }

      if (swiper.params.loop) {
        swiper.slideToLoop(Number.parseInt(slideElement.dataset.swiperSlideIndex!), 0);
      } else {
        swiper.slideTo(swiper.slides.indexOf(slideElement), 0);
      }

      preventFocusHandler = false;
    });
  }

  function initSlides() {
    const params = swiper.params.a11y!;

    if (params.itemRoleDescriptionMessage) {
      for (const slide of swiper.slides) slide.setAttribute("aria-roledescription", params.itemRoleDescriptionMessage!);
    }

    if (params.slideRole) {
      for (const slide of swiper.slides) slide.setAttribute("role", params.slideRole!);
    }

    if (params.slideLabelMessage) {
      const slidesLength = swiper.slides.length;

      for (const [index, slideElement] of swiper.slides.entries()) {
        const slideIndex = swiper.params.loop ? Number.parseInt(slideElement.dataset.swiperSlideIndex!, 10) : index;

        const ariaLabelMessage = params.slideLabelMessage
          ?.replace(/{{index}}/, String(slideIndex + 1))
          .replace(/{{slidesLength}}/, String(slidesLength));

        // `aria-label` is not understood by AT if the tag is an `<li>`
        if (ariaLabelMessage) {
          slideElement.setAttribute(
            slideElement.tagName === "LI" ? "aria-description" : "aria-label",
            ariaLabelMessage
          );
        }
      }
    }
  }

  function init() {
    const params = swiper.params.a11y!;

    swiper.el.append(liveRegion!);

    // Container
    const { el: containerElement } = swiper;

    if (params.containerRoleDescriptionMessage) {
      containerElement.setAttribute("aria-roledescription", params.containerRoleDescriptionMessage);
    }

    if (params.containerMessage) {
      containerElement.setAttribute("aria-label", params.containerMessage);
    }

    if (params.containerRole) {
      containerElement.setAttribute("role", params.containerRole);
    }

    // Wrapper
    const { wrapperEl } = swiper;
    const wrapperId = String(params.id || wrapperEl.getAttribute("id") || `swiper-wrapper-${getRandomNumber(16)}`);
    const live = swiper.params.autoplay && (swiper.params.autoplay as AutoplayOptions).enabled ? "off" : "polite";

    wrapperEl.setAttribute("id", wrapperId);
    wrapperEl.setAttribute("aria-live", live);

    // Slide
    initSlides();

    // Navigation
    const { nextEl, prevEl } = swiper.navigation ?? {};
    const nextElements = makeElementsArray(nextEl);
    const previousElements = makeElementsArray(prevEl);

    if (nextEl) {
      for (const element of nextElements) initNavElement(element, wrapperId, params.nextButtonMessage);
    }

    if (prevEl) {
      for (const element of previousElements) initNavElement(element, wrapperId, params.prevButtonMessage);
    }

    // Pagination
    if (hasClickablePagination()) {
      const paginationElement = makeElementsArray(swiper.pagination.el);

      for (const element of paginationElement) {
        element.addEventListener("keydown", onEnterOrSpaceKey);
      }
    }

    // Tab focus
    const document = getDocument();

    document.addEventListener("visibilitychange", onVisibilityChange);
    swiper.el.addEventListener("focus", handleFocus, true);
    swiper.el.addEventListener("focus", handleFocus, true);
    swiper.el.addEventListener("pointerdown", handlePointerDown, true);
    swiper.el.addEventListener("pointerup", handlePointerUp, true);
  }

  function destroy() {
    if (liveRegion) {
      liveRegion.remove();
    }

    const { nextEl, prevEl } = swiper.navigation ?? {};

    const nextElements = makeElementsArray(nextEl);
    const previousElements = makeElementsArray(prevEl);

    if (nextEl) {
      for (const element of nextElements) element.removeEventListener("keydown", onEnterOrSpaceKey);
    }

    if (prevEl) {
      for (const element of previousElements) element.removeEventListener("keydown", onEnterOrSpaceKey);
    }

    // Pagination
    if (hasClickablePagination()) {
      const paginationElement = makeElementsArray(swiper.pagination.el);

      for (const element of paginationElement) {
        element.removeEventListener("keydown", onEnterOrSpaceKey);
      }
    }

    const document = getDocument();

    document.removeEventListener("visibilitychange", onVisibilityChange);

    // Tab focus
    if (swiper.el && typeof swiper.el !== "string") {
      swiper.el.removeEventListener("focus", handleFocus, true);
      swiper.el.removeEventListener("pointerdown", handlePointerDown, true);
      swiper.el.removeEventListener("pointerup", handlePointerUp, true);
    }
  }

  function inertInvisibleSlides() {
    const params = swiper.params.a11y!;

    if (!params.inertInvisibleSlides) return;

    for (const slideElement of swiper.slides) {
      const isActive = swiper.slides.indexOf(slideElement) === swiper.activeIndex;
      const isVisible =
        swiper.params.watchSlidesProgress && swiper.visibleSlides && swiper.visibleSlides.includes(slideElement);

      if (isActive || isVisible) {
        slideElement.removeAttribute("inert");
      } else {
        slideElement.setAttribute("inert", "");
      }
    }
  }

  on("beforeInit", () => {
    const document = getDocument();

    liveRegion = document.createElement("span");
    liveRegion.classList.add(...(swiper.params.a11y!.notificationClass?.split(" ") ?? []));
    liveRegion.setAttribute("role", "status");
  });

  on("afterInit", () => {
    if (!swiper.params.a11y!.enabled) return;
    init();
  });

  on("slidesLengthChange snapGridLengthChange slidesGridLengthChange" as keyof SwiperEvents, () => {
    if (!swiper.params.a11y!.enabled) return;
    initSlides();
  });

  on("fromEdge toEdge afterInit lock unlock" as keyof SwiperEvents, () => {
    if (!swiper.params.a11y!.enabled) return;
    updateNavigation();
  });

  on("paginationUpdate", () => {
    if (!swiper.params.a11y!.enabled) return;
    updatePagination();
  });

  on("destroy", () => {
    if (!swiper.params.a11y!.enabled) return;
    destroy();
  });

  on("afterInit slideChange slidesLengthChange" as keyof SwiperEvents, () => {
    if (!swiper.params.a11y!.enabled) return;
    inertInvisibleSlides();
  });
};

export default A11y;
