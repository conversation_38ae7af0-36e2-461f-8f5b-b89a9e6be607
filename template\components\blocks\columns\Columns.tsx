import { ColumnsBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import React from "react";
import styles from "./Columns.module.scss";

type ColumnsProps = Partial<Omit<ColumnsBlock, "__typename" | "innerBlocks">>;

export default function Columns({ anchor, children, isStackedOnMobile = true }: React.PropsWithChildren<ColumnsProps>) {
  return (
    <div
      id={anchor ?? undefined}
      className={clsx("block-columns contained", styles.columns, isStackedOnMobile && styles.stacked)}
    >
      {children}
    </div>
  );
}
