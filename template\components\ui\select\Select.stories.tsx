import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Select from "./Select";

const meta: Meta<typeof Select> = {
  title: "Components/Select",
  component: Select,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Select>;

export const Default: Story = {
  args: {
    children: (
      <>
        <option value="1">Lorem ipsum dolor sit amet</option>
        <option value="2">Lorem ipsum dolor sit amet</option>
        <option value="3">Lorem ipsum dolor sit amet</option>
      </>
    ),
  },
};

export const Error: Story = {
  args: {
    placeholder: "Error",
    error: true,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "Disabled",
    disabled: true,
  },
};

export const Outlined: Story = {
  args: {
    placeholder: "Outlined",
    variant: "outlined",
  },
};

export const Underline: Story = {
  args: {
    placeholder: "Underline",
    variant: "underline",
  },
};

export const Filled: Story = {
  args: {
    placeholder: "Filled",
    variant: "filled",
  },
};

export const WithOptGroup: Story = {
  args: {
    children: (
      <>
        <optgroup label="Groupe 1">
          <option value="1">Option 1</option>
          <option value="2">Option 2</option>
        </optgroup>
        <optgroup label="Groupe 2">
          <option value="3">Option 3</option>
          <option value="4">Option 4</option>
        </optgroup>
      </>
    ),
    placeholder: "Toutes les options",
  },
};
