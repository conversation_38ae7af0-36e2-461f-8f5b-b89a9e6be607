import Button from "@/components/ui/button/Button";
import Textfield from "@/components/ui/textfield/Textfield";
import type { TextFilter } from "@/generated/graphql/graphql";
import { FilterValues } from "@/lib/filters";
import Form from "next/form";
import { useId } from "react";
import HiddenInputs from "../HiddenInputs";
import styles from "./Text.module.scss";

interface TextProps {
  defaultValue?: string;
  filter: Omit<TextFilter, "__typename">;
  filterValues?: FilterValues;
  placeholder?: string;
  url: string;
}

export default function Text({
  defaultValue,
  filter: { attribute, label },
  filterValues,
  placeholder,
  url,
}: TextProps) {
  const inputId = useId();

  return (
    <div className={styles.root}>
      <Form
        action={url}
        className={styles.form}
        scroll={false}
        aria-label="Recherche par mot-clé"
        data-filter={attribute}
      >
        <label className={styles.label} htmlFor={inputId}>
          {label}
        </label>

        <div className={styles.field}>
          <Textfield
            id={inputId}
            type="text"
            name={attribute}
            defaultValue={defaultValue}
            placeholder={placeholder ?? "Que recherchez vous ?"}
          />

          <Button type="submit" className={styles.submit} startIcon="far fa-search">
            <span className="sr-only">Appliquer la recherche</span>
          </Button>
        </div>

        {filterValues && <HiddenInputs filterValues={filterValues} omit={[attribute]} />}
      </Form>
    </div>
  );
}
