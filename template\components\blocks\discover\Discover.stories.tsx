import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Discover from "./Discover";

const meta: Meta<typeof Discover> = {
  title: "Blocks/Discover",
  component: Discover,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Discover>;

export const Default: Story = {
  args: {
    items: [
      {
        image: {
          url: "/assets/placeholder-720x480.png",
          width: 720,
          height: 480,
          alt: "Image d'exemple",
        },
        title: "Maecenas mollis vitae sagittis lorem ipsum dolor sit amet",
        leadText: "Surtitre",
        imagePosition: "left",
        action: {
          text: "Learn More",
          url: "#",
          class: null,
          icon: null,
          rel: null,
          target: null,
        },
      },
      {
        image: {
          url: "/assets/placeholder-720x480.png",
          width: 720,
          height: 480,
          alt: "Image d'exemple",
        },
        title: "Maecenas mollis vitae sagittis lorem ipsum dolor sit amet",
        leadText: "Surti<PERSON>",
        imagePosition: "right",
        action: {
          text: "Learn More",
          url: "#",
          class: null,
          icon: null,
          rel: null,
          target: null,
        },
      },
      {
        image: {
          url: "/assets/placeholder-720x480.png",
          width: 720,
          height: 480,
          alt: "Image d'exemple",
        },
        title: "Maecenas mollis vitae sagittis lorem ipsum dolor sit amet",
        leadText: "Surtitre",
        imagePosition: "left",
        action: {
          text: "Learn More",
          url: "#",
          class: null,
          icon: null,
          rel: null,
          target: null,
        },
      },
    ],
  },
};
