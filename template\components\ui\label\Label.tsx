import { Slot, Slottable } from "@radix-ui/react-slot";
import clsx from "clsx";
import styles from "./Label.module.scss";

interface LabelProps {
  description?: string;
  asChild?: boolean;
  required?: boolean;
}

export default function Label({
  children,
  description,
  className,
  required,
  asChild = false,
  ...restProps
}: React.PropsWithChildren<LabelProps & React.LabelHTMLAttributes<HTMLLabelElement>>) {
  const Comp = asChild ? Slot : "label";

  return (
    <Comp className={clsx(styles.label, className)} {...restProps}>
      <Slottable>{children}</Slottable>
      {required && (
        <span className={styles.required} aria-hidden="true">
          {" "}
          (Obligatoire)
        </span>
      )}
      {description && <span className={styles.description}>{description}</span>}
    </Comp>
  );
}
