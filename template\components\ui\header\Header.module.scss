@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.header {
  display: block;
  width: 100%;
  background-color: $color-white;
}

.top {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 8px 16px;
  background-color: $color-neutral-100;

  @include breakpoint(medium up) {
    padding: 8px 24px;
  }

  @include breakpoint(large up) {
    padding: 8px 32px;
  }
}

.socialLinks {
  display: flex;
  gap: 2px;
  margin-left: auto;
}

.inner {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-end;
  height: 60px;
  padding: 8px 16px;

  @include breakpoint(medium up) {
    height: 64px;
    padding: 8px 24px;
  }

  @include breakpoint(large up) {
    height: 120px;
    padding: 8px 40px;
  }
}

.wrapper {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-block: 14px;
  margin-right: auto;

  @include breakpoint(medium up) {
    gap: 32px;
    margin-block: 10px;
  }
}

.actions {
  display: flex;
  gap: 8px;
}

.searchButton,
.menuButton {
  width: 48px;
  height: 48px;
}
