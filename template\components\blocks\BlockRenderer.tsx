import "server-only";

import { pascalCase } from "change-case";
import dynamic from "next/dynamic";
import path from "node:path";
import { ComponentType } from "react";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- Unknown component type
const components = new Map<string, ComponentType<any>>();

/**
 * Autoload the component using the `block.ts` file default export as entrypoint.
 * Infer the block typename from the dirname of the component like so: `ParagraphBlock`.
 * As a side effect, this enforces proper code splitting per block.
 *
 * @example `paragraph/block.ts` -> `ParagraphBlock`
 * @example `quick-access/block.ts` -> `QuickAccessBlock`
 */
const entries = require.context("./", true, /.*\/block\.ts$/, "lazy");

for (const filepath of entries.keys()) {
  const typename = pascalCase(path.dirname(filepath)) + "Block";
  const comp = dynamic(async () => {
    const inst = await entries(filepath);
    return inst.default;
  });

  components.set(typename, comp);
}

type Block = Record<string, unknown> & {
  __typename: string;
  id: string;
  innerBlocks?: Block[];
};

interface BlockRendererProps {
  structuredContent: Block[];
}

/**
 * Recursively render the structured content of a page.
 */
export default function BlockRenderer({ structuredContent }: BlockRendererProps) {
  return (
    <>
      {structuredContent.map((block, index) => {
        const { __typename: typename, innerBlocks, ...data } = block;
        const Component = components.get(typename);

        if (!Component) {
          console.warn(`BlockRenderer: No component found for "${typename}"`);
          return;
        }

        // NOTE: Do not pass innerBlocks to the component. Rendering of the children will happen if necessary.
        return (
          <Component key={data.id ?? index} {...data}>
            {innerBlocks && <BlockRenderer structuredContent={innerBlocks} />}
          </Component>
        );
      })}
    </>
  );
}
