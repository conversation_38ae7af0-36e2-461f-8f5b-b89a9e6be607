import Card from "./Card";
import styles from "./CardList.module.scss";
import { LocationMarker } from "./DirectoriesMap";

interface CardListProps {
  selectedMarkers: LocationMarker[];
  onSelectCard: (id: number) => void;
}

export default function CardList({ selectedMarkers, onSelectCard }: CardListProps) {
  return (
    <ul className={styles.list}>
      {selectedMarkers.map((marker: LocationMarker, index) => {
        const { data } = marker;
        const parsedData = data ? JSON.parse(data.directory) : {};

        console.log("parsedData", parsedData);
        const { id, title, categories, images } = parsedData;
        const image = images?.ratio_3x2 || "";
        const category = categories && categories.length > 0 ? categories[0].title : "No Category";
        const safeTitle = title || "No Title";

        return (
          <li key={index} className={styles.item}>
            <Card
              id={Number(id)}
              title={safeTitle}
              category={category}
              image={image}
              handleClick={() => onSelectCard(Number(id))}
            />
          </li>
        );
      })}
    </ul>
  );
}
