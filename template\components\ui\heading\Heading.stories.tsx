import type { Meta, StoryObj } from "@storybook/nextjs";
import Heading from "./Heading";
import HeadingAgenda from "./HeadingAgenda";
import HeadingContact from "./HeadingContact";
import HeadingImageBottom from "./HeadingImageBottom";
import HeadingImageRight from "./HeadingImageRight";
import HeadingPublication from "./HeadingPublication";

const meta: Meta<typeof Heading> = {
  title: "Components/Heading",
  component: Heading,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Heading>;

export const Default: Story = {
  args: {
    title: "On en parle",
    surtitle: "Actualités",
    leadText:
      "Retrouvez ici l’intégralité de nos actualités, initiatives et nouveautés. Restez informé(e) en temps réel et revenez régulièrement pour ne rien manquer. Bonne lecture !",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    rssUrl: "#",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
  },
};

export const ImageRight: StoryObj<typeof HeadingImageRight> = {
  render: (args) => <HeadingImageRight {...args} />,
  args: {
    title: "On en parle",
    surtitle: "Actualités",
    leadText:
      "Retrouvez ici l’intégralité de nos actualités, initiatives et nouveautés. Restez informé(e) en temps réel et revenez régulièrement pour ne rien manquer. Bonne lecture !",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
    imageSrc: "/assets/placeholder-720x480.png",
  },
};

export const ImageBottom: StoryObj<typeof HeadingImageBottom> = {
  render: (args) => <HeadingImageBottom {...args} />,
  args: {
    title: "On en parle",
    surtitle: "Actualités",
    leadText:
      "Retrouvez ici l’intégralité de nos actualités, initiatives et nouveautés. Restez informé(e) en temps réel et revenez régulièrement pour ne rien manquer. Bonne lecture !",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
    imageSrc: "/assets/placeholder-1216x512.png",
  },
};

export const Contact: StoryObj<typeof HeadingContact> = {
  render: (args) => <HeadingContact {...args} />,
  args: {
    title: "Marie Dupont",
    surtitle: "Équipe Direction Générale",
    roles: [
      "Directrice de la communication institutionnelle",
      "Responsable des relations médias",
      "Porte-parole officielle",
    ],
    rolesRoleDescription: "Fonctions",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
    imageSrc: "/assets/placeholder-280x280.png",
  },
};

export const Publication: StoryObj<typeof HeadingPublication> = {
  render: (args) => <HeadingPublication {...args} />,
  args: {
    title: "Bilan annuel des actions sociales et solidaires menées dans la commune",
    surtitle: "Solidarité et inclusion",
    subtitle: "Septembre 2025 – N°123",
    leadText:
      "Ce rapport dresse le panorama des initiatives locales en matière d’aide sociale, d’accès aux soins et d’accompagnement des publics fragiles.",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
    imageSrc: "/assets/placeholder-268x380.png",
  },
};

export const Agenda: StoryObj<typeof HeadingAgenda> = {
  render: (args) => <HeadingAgenda {...args} />,
  args: {
    title: "Fête de la musique au parc municipal",
    surtitle: "Culture & loisirs",
    leadText: "Concerts gratuits, food trucks et animations pour toute la famille de 18h à minuit.",
    tags: Array.from({ length: 6 }, () => ({ text: "Lorem Ipsum", url: "#" })),
    tagsRoleDescription: "Thématiques",
    publicationDate: "2025-01-25",
    modifiedDate: "2030-02-26",
    imageSrc: "/assets/placeholder-720x480.png",
    startDate: "2025-06-21",
    endDate: "2025-06-22",
  },
};
