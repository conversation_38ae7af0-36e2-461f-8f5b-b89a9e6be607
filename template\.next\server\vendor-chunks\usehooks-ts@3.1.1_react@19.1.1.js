"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/usehooks-ts@3.1.1_react@19.1.1";
exports.ids = ["vendor-chunks/usehooks-ts@3.1.1_react@19.1.1"];
exports.modules = {

/***/ "(ssr)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js":
/*!***************************************************************************************************!*\
  !*** ../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBoolean: () => (/* binding */ useBoolean),\n/* harmony export */   useClickAnyWhere: () => (/* binding */ useClickAnyWhere),\n/* harmony export */   useCopyToClipboard: () => (/* binding */ useCopyToClipboard),\n/* harmony export */   useCountdown: () => (/* binding */ useCountdown),\n/* harmony export */   useCounter: () => (/* binding */ useCounter),\n/* harmony export */   useDarkMode: () => (/* binding */ useDarkMode),\n/* harmony export */   useDebounceCallback: () => (/* binding */ useDebounceCallback),\n/* harmony export */   useDebounceValue: () => (/* binding */ useDebounceValue),\n/* harmony export */   useDocumentTitle: () => (/* binding */ useDocumentTitle),\n/* harmony export */   useEventCallback: () => (/* binding */ useEventCallback),\n/* harmony export */   useEventListener: () => (/* binding */ useEventListener),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsClient: () => (/* binding */ useIsClient),\n/* harmony export */   useIsMounted: () => (/* binding */ useIsMounted),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery),\n/* harmony export */   useOnClickOutside: () => (/* binding */ useOnClickOutside),\n/* harmony export */   useReadLocalStorage: () => (/* binding */ useReadLocalStorage),\n/* harmony export */   useResizeObserver: () => (/* binding */ useResizeObserver),\n/* harmony export */   useScreen: () => (/* binding */ useScreen),\n/* harmony export */   useScript: () => (/* binding */ useScript),\n/* harmony export */   useScrollLock: () => (/* binding */ useScrollLock),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useStep: () => (/* binding */ useStep),\n/* harmony export */   useTernaryDarkMode: () => (/* binding */ useTernaryDarkMode),\n/* harmony export */   useTimeout: () => (/* binding */ useTimeout),\n/* harmony export */   useToggle: () => (/* binding */ useToggle),\n/* harmony export */   useUnmount: () => (/* binding */ useUnmount),\n/* harmony export */   useWindowSize: () => (/* binding */ useWindowSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(ssr)/../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\");\n\n\n\n// src/useBoolean/useBoolean.ts\nfunction useBoolean(defaultValue = false) {\n  if (typeof defaultValue !== \"boolean\") {\n    throw new Error(\"defaultValue must be `true` or `false`\");\n  }\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue);\n  const setTrue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue(true);\n  }, []);\n  const setFalse = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue(false);\n  }, []);\n  const toggle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue((x) => !x);\n  }, []);\n  return { value, setValue, setTrue, setFalse, toggle };\n}\nvar useIsomorphicLayoutEffect = typeof window !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n// src/useEventListener/useEventListener.ts\nfunction useEventListener(eventName, handler, element, options) {\n  const savedHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    savedHandler.current = handler;\n  }, [handler]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const targetElement = (element == null ? void 0 : element.current) ?? window;\n    if (!(targetElement && targetElement.addEventListener))\n      return;\n    const listener = (event) => {\n      savedHandler.current(event);\n    };\n    targetElement.addEventListener(eventName, listener, options);\n    return () => {\n      targetElement.removeEventListener(eventName, listener, options);\n    };\n  }, [eventName, element, options]);\n}\n\n// src/useClickAnyWhere/useClickAnyWhere.ts\nfunction useClickAnyWhere(handler) {\n  useEventListener(\"click\", (event) => {\n    handler(event);\n  });\n}\nfunction useCopyToClipboard() {\n  const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const copy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (text) => {\n    if (!(navigator == null ? void 0 : navigator.clipboard)) {\n      console.warn(\"Clipboard not supported\");\n      return false;\n    }\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedText(text);\n      return true;\n    } catch (error) {\n      console.warn(\"Copy failed\", error);\n      setCopiedText(null);\n      return false;\n    }\n  }, []);\n  return [copiedText, copy];\n}\nfunction useCounter(initialValue) {\n  const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue ?? 0);\n  const increment = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount((x) => x + 1);\n  }, []);\n  const decrement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount((x) => x - 1);\n  }, []);\n  const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount(initialValue ?? 0);\n  }, [initialValue]);\n  return {\n    count,\n    increment,\n    decrement,\n    reset,\n    setCount\n  };\n}\nfunction useInterval(callback, delay) {\n  const savedCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (delay === null) {\n      return;\n    }\n    const id = setInterval(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearInterval(id);\n    };\n  }, [delay]);\n}\n\n// src/useCountdown/useCountdown.ts\nfunction useCountdown({\n  countStart,\n  countStop = 0,\n  intervalMs = 1e3,\n  isIncrement = false\n}) {\n  const {\n    count,\n    increment,\n    decrement,\n    reset: resetCounter\n  } = useCounter(countStart);\n  const {\n    value: isCountdownRunning,\n    setTrue: startCountdown,\n    setFalse: stopCountdown\n  } = useBoolean(false);\n  const resetCountdown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    stopCountdown();\n    resetCounter();\n  }, [stopCountdown, resetCounter]);\n  const countdownCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (count === countStop) {\n      stopCountdown();\n      return;\n    }\n    if (isIncrement) {\n      increment();\n    } else {\n      decrement();\n    }\n  }, [count, countStop, decrement, increment, isIncrement, stopCountdown]);\n  useInterval(countdownCallback, isCountdownRunning ? intervalMs : null);\n  return [count, { startCountdown, stopCountdown, resetCountdown }];\n}\nfunction useEventCallback(fn) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  useIsomorphicLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args) => {\n    var _a;\n    return (_a = ref.current) == null ? void 0 : _a.call(ref, ...args);\n  }, [ref]);\n}\n\n// src/useLocalStorage/useLocalStorage.ts\nvar IS_SERVER = typeof window === \"undefined\";\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried setting localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.localStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting localStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried removing localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.localStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nvar IS_SERVER2 = typeof window === \"undefined\";\nfunction useMediaQuery(query, {\n  defaultValue = false,\n  initializeWithValue = true\n} = {}) {\n  const getMatches = (query2) => {\n    if (IS_SERVER2) {\n      return defaultValue;\n    }\n    return window.matchMedia(query2).matches;\n  };\n  const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return getMatches(query);\n    }\n    return defaultValue;\n  });\n  function handleChange() {\n    setMatches(getMatches(query));\n  }\n  useIsomorphicLayoutEffect(() => {\n    const matchMedia = window.matchMedia(query);\n    handleChange();\n    if (matchMedia.addListener) {\n      matchMedia.addListener(handleChange);\n    } else {\n      matchMedia.addEventListener(\"change\", handleChange);\n    }\n    return () => {\n      if (matchMedia.removeListener) {\n        matchMedia.removeListener(handleChange);\n      } else {\n        matchMedia.removeEventListener(\"change\", handleChange);\n      }\n    };\n  }, [query]);\n  return matches;\n}\n\n// src/useDarkMode/useDarkMode.ts\nvar COLOR_SCHEME_QUERY = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY = \"usehooks-ts-dark-mode\";\nfunction useDarkMode(options = {}) {\n  const {\n    defaultValue,\n    localStorageKey = LOCAL_STORAGE_KEY,\n    initializeWithValue = true\n  } = options;\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY, {\n    initializeWithValue,\n    defaultValue\n  });\n  const [isDarkMode, setDarkMode] = useLocalStorage(\n    localStorageKey,\n    defaultValue ?? isDarkOS ?? false,\n    { initializeWithValue }\n  );\n  useIsomorphicLayoutEffect(() => {\n    if (isDarkOS !== isDarkMode) {\n      setDarkMode(isDarkOS);\n    }\n  }, [isDarkOS]);\n  return {\n    isDarkMode,\n    toggle: () => {\n      setDarkMode((prev) => !prev);\n    },\n    enable: () => {\n      setDarkMode(true);\n    },\n    disable: () => {\n      setDarkMode(false);\n    },\n    set: (value) => {\n      setDarkMode(value);\n    }\n  };\n}\nfunction useUnmount(func) {\n  const funcRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(func);\n  funcRef.current = func;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(\n    () => () => {\n      funcRef.current();\n    },\n    []\n  );\n}\n\n// src/useDebounceCallback/useDebounceCallback.ts\nfunction useDebounceCallback(func, delay = 500, options) {\n  const debouncedFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  useUnmount(() => {\n    if (debouncedFunc.current) {\n      debouncedFunc.current.cancel();\n    }\n  });\n  const debounced = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const debouncedFuncInstance = lodash_debounce__WEBPACK_IMPORTED_MODULE_1__(func, delay, options);\n    const wrappedFunc = (...args) => {\n      return debouncedFuncInstance(...args);\n    };\n    wrappedFunc.cancel = () => {\n      debouncedFuncInstance.cancel();\n    };\n    wrappedFunc.isPending = () => {\n      return !!debouncedFunc.current;\n    };\n    wrappedFunc.flush = () => {\n      return debouncedFuncInstance.flush();\n    };\n    return wrappedFunc;\n  }, [func, delay, options]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    debouncedFunc.current = lodash_debounce__WEBPACK_IMPORTED_MODULE_1__(func, delay, options);\n  }, [func, delay, options]);\n  return debounced;\n}\nfunction useDebounceValue(initialValue, delay, options) {\n  const eq = (options == null ? void 0 : options.equalityFn) ?? ((left, right) => left === right);\n  const unwrappedInitialValue = initialValue instanceof Function ? initialValue() : initialValue;\n  const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(unwrappedInitialValue);\n  const previousValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(unwrappedInitialValue);\n  const updateDebouncedValue = useDebounceCallback(\n    setDebouncedValue,\n    delay,\n    options\n  );\n  if (!eq(previousValueRef.current, unwrappedInitialValue)) {\n    updateDebouncedValue(unwrappedInitialValue);\n    previousValueRef.current = unwrappedInitialValue;\n  }\n  return [debouncedValue, updateDebouncedValue];\n}\nfunction useDocumentTitle(title, options = {}) {\n  const { preserveTitleOnUnmount = true } = options;\n  const defaultTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  useIsomorphicLayoutEffect(() => {\n    defaultTitle.current = window.document.title;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    window.document.title = title;\n  }, [title]);\n  useUnmount(() => {\n    if (!preserveTitleOnUnmount && defaultTitle.current) {\n      window.document.title = defaultTitle.current;\n    }\n  });\n}\nfunction useHover(elementRef) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const handleMouseEnter = () => {\n    setValue(true);\n  };\n  const handleMouseLeave = () => {\n    setValue(false);\n  };\n  useEventListener(\"mouseenter\", handleMouseEnter, elementRef);\n  useEventListener(\"mouseleave\", handleMouseLeave, elementRef);\n  return value;\n}\nfunction useIntersectionObserver({\n  threshold = 0,\n  root = null,\n  rootMargin = \"0%\",\n  freezeOnceVisible = false,\n  initialIsIntersecting = false,\n  onChange\n} = {}) {\n  var _a;\n  const [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ({\n    isIntersecting: initialIsIntersecting,\n    entry: void 0\n  }));\n  const callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  callbackRef.current = onChange;\n  const frozen = ((_a = state.entry) == null ? void 0 : _a.isIntersecting) && freezeOnceVisible;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!ref)\n      return;\n    if (!(\"IntersectionObserver\" in window))\n      return;\n    if (frozen)\n      return;\n    let unobserve;\n    const observer = new IntersectionObserver(\n      (entries) => {\n        const thresholds = Array.isArray(observer.thresholds) ? observer.thresholds : [observer.thresholds];\n        entries.forEach((entry) => {\n          const isIntersecting = entry.isIntersecting && thresholds.some((threshold2) => entry.intersectionRatio >= threshold2);\n          setState({ isIntersecting, entry });\n          if (callbackRef.current) {\n            callbackRef.current(isIntersecting, entry);\n          }\n          if (isIntersecting && freezeOnceVisible && unobserve) {\n            unobserve();\n            unobserve = void 0;\n          }\n        });\n      },\n      { threshold, root, rootMargin }\n    );\n    observer.observe(ref);\n    return () => {\n      observer.disconnect();\n    };\n  }, [\n    ref,\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    JSON.stringify(threshold),\n    root,\n    rootMargin,\n    frozen,\n    freezeOnceVisible\n  ]);\n  const prevRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    var _a2;\n    if (!ref && ((_a2 = state.entry) == null ? void 0 : _a2.target) && !freezeOnceVisible && !frozen && prevRef.current !== state.entry.target) {\n      prevRef.current = state.entry.target;\n      setState({ isIntersecting: initialIsIntersecting, entry: void 0 });\n    }\n  }, [ref, state.entry, freezeOnceVisible, frozen, initialIsIntersecting]);\n  const result = [\n    setRef,\n    !!state.isIntersecting,\n    state.entry\n  ];\n  result.ref = result[0];\n  result.isIntersecting = result[1];\n  result.entry = result[2];\n  return result;\n}\nfunction useIsClient() {\n  const [isClient, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setClient(true);\n  }, []);\n  return isClient;\n}\nfunction useIsMounted() {\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => isMounted.current, []);\n}\nfunction useMap(initialState = /* @__PURE__ */ new Map()) {\n  const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map(initialState));\n  const actions = {\n    set: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key, value) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.set(key, value);\n        return copy;\n      });\n    }, []),\n    setAll: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((entries) => {\n      setMap(() => new Map(entries));\n    }, []),\n    remove: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.delete(key);\n        return copy;\n      });\n    }, []),\n    reset: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n      setMap(() => /* @__PURE__ */ new Map());\n    }, [])\n  };\n  return [map, actions];\n}\n\n// src/useOnClickOutside/useOnClickOutside.ts\nfunction useOnClickOutside(ref, handler, eventType = \"mousedown\", eventListenerOptions = {}) {\n  useEventListener(\n    eventType,\n    (event) => {\n      const target = event.target;\n      if (!target || !target.isConnected) {\n        return;\n      }\n      const isOutside = Array.isArray(ref) ? ref.filter((r) => Boolean(r.current)).every((r) => r.current && !r.current.contains(target)) : ref.current && !ref.current.contains(target);\n      if (isOutside) {\n        handler(event);\n      }\n    },\n    void 0,\n    eventListenerOptions\n  );\n}\nvar IS_SERVER3 = typeof window === \"undefined\";\nfunction useReadLocalStorage(key, options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER3) {\n    initializeWithValue = false;\n  }\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return null;\n      }\n      return parsed;\n    },\n    [options]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (IS_SERVER3) {\n      return null;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : null;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return null;\n    }\n  }, [key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return void 0;\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return storedValue;\n}\nvar initialSize = {\n  width: void 0,\n  height: void 0\n};\nfunction useResizeObserver(options) {\n  const { ref, box = \"content-box\" } = options;\n  const [{ width, height }, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialSize);\n  const isMounted = useIsMounted();\n  const previousSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ ...initialSize });\n  const onResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n  onResize.current = options.onResize;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!ref.current)\n      return;\n    if (typeof window === \"undefined\" || !(\"ResizeObserver\" in window))\n      return;\n    const observer = new ResizeObserver(([entry]) => {\n      const boxProp = box === \"border-box\" ? \"borderBoxSize\" : box === \"device-pixel-content-box\" ? \"devicePixelContentBoxSize\" : \"contentBoxSize\";\n      const newWidth = extractSize(entry, boxProp, \"inlineSize\");\n      const newHeight = extractSize(entry, boxProp, \"blockSize\");\n      const hasChanged = previousSize.current.width !== newWidth || previousSize.current.height !== newHeight;\n      if (hasChanged) {\n        const newSize = { width: newWidth, height: newHeight };\n        previousSize.current.width = newWidth;\n        previousSize.current.height = newHeight;\n        if (onResize.current) {\n          onResize.current(newSize);\n        } else {\n          if (isMounted()) {\n            setSize(newSize);\n          }\n        }\n      }\n    });\n    observer.observe(ref.current, { box });\n    return () => {\n      observer.disconnect();\n    };\n  }, [box, ref, isMounted]);\n  return { width, height };\n}\nfunction extractSize(entry, box, sizeType) {\n  if (!entry[box]) {\n    if (box === \"contentBoxSize\") {\n      return entry.contentRect[sizeType === \"inlineSize\" ? \"width\" : \"height\"];\n    }\n    return void 0;\n  }\n  return Array.isArray(entry[box]) ? entry[box][0][sizeType] : (\n    // @ts-ignore Support Firefox's non-standard behavior\n    entry[box][sizeType]\n  );\n}\nvar IS_SERVER4 = typeof window === \"undefined\";\nfunction useScreen(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER4) {\n    initializeWithValue = false;\n  }\n  const readScreen = () => {\n    if (IS_SERVER4) {\n      return void 0;\n    }\n    return window.screen;\n  };\n  const [screen, setScreen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readScreen();\n    }\n    return void 0;\n  });\n  const debouncedSetScreen = useDebounceCallback(\n    setScreen,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const newScreen = readScreen();\n    const setSize = options.debounceDelay ? debouncedSetScreen : setScreen;\n    if (newScreen) {\n      const {\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      } = newScreen;\n      setSize({\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      });\n    }\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return screen;\n}\nvar cachedScriptStatuses = /* @__PURE__ */ new Map();\nfunction getScriptNode(src) {\n  const node = document.querySelector(\n    `script[src=\"${src}\"]`\n  );\n  const status = node == null ? void 0 : node.getAttribute(\"data-status\");\n  return {\n    node,\n    status\n  };\n}\nfunction useScript(src, options) {\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return \"idle\";\n    }\n    if (typeof window === \"undefined\") {\n      return \"loading\";\n    }\n    return cachedScriptStatuses.get(src) ?? \"loading\";\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return;\n    }\n    const cachedScriptStatus = cachedScriptStatuses.get(src);\n    if (cachedScriptStatus === \"ready\" || cachedScriptStatus === \"error\") {\n      setStatus(cachedScriptStatus);\n      return;\n    }\n    const script = getScriptNode(src);\n    let scriptNode = script.node;\n    if (!scriptNode) {\n      scriptNode = document.createElement(\"script\");\n      scriptNode.src = src;\n      scriptNode.async = true;\n      if (options == null ? void 0 : options.id) {\n        scriptNode.id = options.id;\n      }\n      scriptNode.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(scriptNode);\n      const setAttributeFromEvent = (event) => {\n        const scriptStatus = event.type === \"load\" ? \"ready\" : \"error\";\n        scriptNode == null ? void 0 : scriptNode.setAttribute(\"data-status\", scriptStatus);\n      };\n      scriptNode.addEventListener(\"load\", setAttributeFromEvent);\n      scriptNode.addEventListener(\"error\", setAttributeFromEvent);\n    } else {\n      setStatus(script.status ?? cachedScriptStatus ?? \"loading\");\n    }\n    const setStateFromEvent = (event) => {\n      const newStatus = event.type === \"load\" ? \"ready\" : \"error\";\n      setStatus(newStatus);\n      cachedScriptStatuses.set(src, newStatus);\n    };\n    scriptNode.addEventListener(\"load\", setStateFromEvent);\n    scriptNode.addEventListener(\"error\", setStateFromEvent);\n    return () => {\n      if (scriptNode) {\n        scriptNode.removeEventListener(\"load\", setStateFromEvent);\n        scriptNode.removeEventListener(\"error\", setStateFromEvent);\n      }\n      if (scriptNode && (options == null ? void 0 : options.removeOnUnmount)) {\n        scriptNode.remove();\n        cachedScriptStatuses.delete(src);\n      }\n    };\n  }, [src, options == null ? void 0 : options.shouldPreventLoad, options == null ? void 0 : options.removeOnUnmount, options == null ? void 0 : options.id]);\n  return status;\n}\nvar IS_SERVER5 = typeof window === \"undefined\";\nfunction useScrollLock(options = {}) {\n  const { autoLock = true, lockTarget, widthReflow = true } = options;\n  const [isLocked, setIsLocked] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const target = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const originalStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const lock = () => {\n    if (target.current) {\n      const { overflow, paddingRight } = target.current.style;\n      originalStyle.current = { overflow, paddingRight };\n      if (widthReflow) {\n        const offsetWidth = target.current === document.body ? window.innerWidth : target.current.offsetWidth;\n        const currentPaddingRight = parseInt(window.getComputedStyle(target.current).paddingRight, 10) || 0;\n        const scrollbarWidth = offsetWidth - target.current.scrollWidth;\n        target.current.style.paddingRight = `${scrollbarWidth + currentPaddingRight}px`;\n      }\n      target.current.style.overflow = \"hidden\";\n      setIsLocked(true);\n    }\n  };\n  const unlock = () => {\n    if (target.current && originalStyle.current) {\n      target.current.style.overflow = originalStyle.current.overflow;\n      if (widthReflow) {\n        target.current.style.paddingRight = originalStyle.current.paddingRight;\n      }\n    }\n    setIsLocked(false);\n  };\n  useIsomorphicLayoutEffect(() => {\n    if (IS_SERVER5)\n      return;\n    if (lockTarget) {\n      target.current = typeof lockTarget === \"string\" ? document.querySelector(lockTarget) : lockTarget;\n    }\n    if (!target.current) {\n      target.current = document.body;\n    }\n    if (autoLock) {\n      lock();\n    }\n    return () => {\n      unlock();\n    };\n  }, [autoLock, lockTarget, widthReflow]);\n  return { isLocked, lock, unlock };\n}\nvar IS_SERVER6 = typeof window === \"undefined\";\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER6) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.sessionStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading sessionStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried setting sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.sessionStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting sessionStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried removing sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.sessionStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"session-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nfunction useStep(maxStep) {\n  const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const canGoToNextStep = currentStep + 1 <= maxStep;\n  const canGoToPrevStep = currentStep - 1 > 0;\n  const setStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (step) => {\n      const newStep = step instanceof Function ? step(currentStep) : step;\n      if (newStep >= 1 && newStep <= maxStep) {\n        setCurrentStep(newStep);\n        return;\n      }\n      throw new Error(\"Step not valid\");\n    },\n    [maxStep, currentStep]\n  );\n  const goToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (canGoToNextStep) {\n      setCurrentStep((step) => step + 1);\n    }\n  }, [canGoToNextStep]);\n  const goToPrevStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (canGoToPrevStep) {\n      setCurrentStep((step) => step - 1);\n    }\n  }, [canGoToPrevStep]);\n  const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCurrentStep(1);\n  }, []);\n  return [\n    currentStep,\n    {\n      goToNextStep,\n      goToPrevStep,\n      canGoToNextStep,\n      canGoToPrevStep,\n      setStep,\n      reset\n    }\n  ];\n}\n\n// src/useTernaryDarkMode/useTernaryDarkMode.ts\nvar COLOR_SCHEME_QUERY2 = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY2 = \"usehooks-ts-ternary-dark-mode\";\nfunction useTernaryDarkMode({\n  defaultValue = \"system\",\n  localStorageKey = LOCAL_STORAGE_KEY2,\n  initializeWithValue = true\n} = {}) {\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY2, { initializeWithValue });\n  const [mode, setMode] = useLocalStorage(localStorageKey, defaultValue, {\n    initializeWithValue\n  });\n  const isDarkMode = mode === \"dark\" || mode === \"system\" && isDarkOS;\n  const toggleTernaryDarkMode = () => {\n    const modes = [\"light\", \"system\", \"dark\"];\n    setMode((prevMode) => {\n      const nextIndex = (modes.indexOf(prevMode) + 1) % modes.length;\n      return modes[nextIndex];\n    });\n  };\n  return {\n    isDarkMode,\n    ternaryDarkMode: mode,\n    setTernaryDarkMode: setMode,\n    toggleTernaryDarkMode\n  };\n}\nfunction useTimeout(callback, delay) {\n  const savedCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!delay && delay !== 0) {\n      return;\n    }\n    const id = setTimeout(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearTimeout(id);\n    };\n  }, [delay]);\n}\nfunction useToggle(defaultValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!defaultValue);\n  const toggle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue((x) => !x);\n  }, []);\n  return [value, toggle, setValue];\n}\nvar IS_SERVER7 = typeof window === \"undefined\";\nfunction useWindowSize(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER7) {\n    initializeWithValue = false;\n  }\n  const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return {\n        width: window.innerWidth,\n        height: window.innerHeight\n      };\n    }\n    return {\n      width: void 0,\n      height: void 0\n    };\n  });\n  const debouncedSetWindowSize = useDebounceCallback(\n    setWindowSize,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const setSize = options.debounceDelay ? debouncedSetWindowSize : setWindowSize;\n    setSize({\n      width: window.innerWidth,\n      height: window.innerHeight\n    });\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return windowSize;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3VzZWhvb2tzLXRzQDMuMS4xX3JlYWN0QDE5LjEuMS9ub2RlX21vZHVsZXMvdXNlaG9va3MtdHMvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUEyRjtBQUNwRDs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QiwrQ0FBUTtBQUNwQyxrQkFBa0Isa0RBQVc7QUFDN0I7QUFDQSxHQUFHO0FBQ0gsbUJBQW1CLGtEQUFXO0FBQzlCO0FBQ0EsR0FBRztBQUNILGlCQUFpQixrREFBVztBQUM1QjtBQUNBLEdBQUc7QUFDSCxXQUFXO0FBQ1g7QUFDQSxnRUFBZ0Usa0RBQWUsR0FBRyw0Q0FBUzs7QUFFM0Y7QUFDQTtBQUNBLHVCQUF1Qiw2Q0FBTTtBQUM3QjtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0Esc0NBQXNDLCtDQUFRO0FBQzlDLGVBQWUsa0RBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQVE7QUFDcEMsb0JBQW9CLGtEQUFXO0FBQy9CO0FBQ0EsR0FBRztBQUNILG9CQUFvQixrREFBVztBQUMvQjtBQUNBLEdBQUc7QUFDSCxnQkFBZ0Isa0RBQVc7QUFDM0I7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDZDQUFNO0FBQzlCO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLHlCQUF5QixrREFBVztBQUNwQztBQUNBO0FBQ0EsR0FBRztBQUNILDRCQUE0QixrREFBVztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLG1CQUFtQiwrQ0FBK0M7QUFDbEU7QUFDQTtBQUNBLGNBQWMsNkNBQU07QUFDcEI7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSCxTQUFTLGtEQUFXO0FBQ3BCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBLHdEQUF3RDtBQUN4RCxVQUFVLDZCQUE2QjtBQUN2QyxxQkFBcUIsa0RBQVc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsdUJBQXVCLGtEQUFXO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLG9CQUFvQixrREFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTiwyREFBMkQsSUFBSTtBQUMvRDtBQUNBO0FBQ0EsR0FBRztBQUNILHdDQUF3QywrQ0FBUTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsSUFBSTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsS0FBSztBQUNwRSxNQUFNO0FBQ04sMkRBQTJELElBQUk7QUFDL0Q7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsaURBQWlELElBQUk7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxLQUFLO0FBQ2xFLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQSxHQUFHO0FBQ0gsOEJBQThCLGtEQUFXO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFLElBQUk7QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsK0NBQVE7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiw2Q0FBTTtBQUN4QjtBQUNBLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esd0JBQXdCLDZDQUFNO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILG9CQUFvQiw4Q0FBTztBQUMzQixrQ0FBa0MsNENBQVE7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYLDRCQUE0Qiw0Q0FBUTtBQUNwQyxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QywrQ0FBUTtBQUN0RCwyQkFBMkIsNkNBQU07QUFDakM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZDQUE2QztBQUM3QyxVQUFVLGdDQUFnQztBQUMxQyx1QkFBdUIsNkNBQU07QUFDN0I7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQVE7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOO0FBQ0Esd0JBQXdCLCtDQUFRO0FBQ2hDLDRCQUE0QiwrQ0FBUTtBQUNwQztBQUNBO0FBQ0EsR0FBRztBQUNILHNCQUFzQiw2Q0FBTTtBQUM1QjtBQUNBO0FBQ0EsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix1QkFBdUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNkNBQU07QUFDeEIsRUFBRSxnREFBUztBQUNYO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixzREFBc0Q7QUFDdkU7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQywrQ0FBUTtBQUN4QyxFQUFFLGdEQUFTO0FBQ1g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFNO0FBQzFCLEVBQUUsZ0RBQVM7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxTQUFTLGtEQUFXO0FBQ3BCO0FBQ0E7QUFDQSx3QkFBd0IsK0NBQVE7QUFDaEM7QUFDQSxTQUFTLGtEQUFXO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTCxZQUFZLGtEQUFXO0FBQ3ZCO0FBQ0EsS0FBSztBQUNMLFlBQVksa0RBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsS0FBSztBQUNMLFdBQVcsa0RBQVc7QUFDdEI7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkZBQTJGO0FBQzNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE4QztBQUM5QyxRQUFRLDZCQUE2QjtBQUNyQztBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsa0RBQVc7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLG9CQUFvQixrREFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sMkRBQTJELElBQUk7QUFDL0Q7QUFDQTtBQUNBLEdBQUc7QUFDSCx3Q0FBd0MsK0NBQVE7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0EsR0FBRztBQUNILDhCQUE4QixrREFBVztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDJCQUEyQjtBQUNyQyxXQUFXLGVBQWUsYUFBYSwrQ0FBUTtBQUMvQztBQUNBLHVCQUF1Qiw2Q0FBTSxHQUFHLGdCQUFnQjtBQUNoRCxtQkFBbUIsNkNBQU07QUFDekI7QUFDQSxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxvQ0FBb0MsS0FBSztBQUN6QztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0IsUUFBUSw2QkFBNkI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLCtDQUFRO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLElBQUk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QiwrQ0FBUTtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DLFVBQVUsa0RBQWtEO0FBQzVELGtDQUFrQywrQ0FBUTtBQUMxQyxpQkFBaUIsNkNBQU07QUFDdkIsd0JBQXdCLDZDQUFNO0FBQzlCO0FBQ0E7QUFDQSxjQUFjLHlCQUF5QjtBQUN2QyxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MscUNBQXFDO0FBQ3BGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsV0FBVztBQUNYO0FBQ0E7QUFDQSwwREFBMEQ7QUFDMUQsVUFBVSw2QkFBNkI7QUFDdkMscUJBQXFCLGtEQUFXO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHVCQUF1QixrREFBVztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxvQkFBb0Isa0RBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ04sNkRBQTZELElBQUk7QUFDakU7QUFDQTtBQUNBLEdBQUc7QUFDSCx3Q0FBd0MsK0NBQVE7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELElBQUk7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLEtBQUs7QUFDdEUsTUFBTTtBQUNOLDZEQUE2RCxJQUFJO0FBQ2pFO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxJQUFJO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsS0FBSztBQUNwRSxHQUFHO0FBQ0gsRUFBRSxnREFBUztBQUNYO0FBQ0EsR0FBRztBQUNILDhCQUE4QixrREFBVztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLCtDQUFRO0FBQ2hEO0FBQ0E7QUFDQSxrQkFBa0Isa0RBQVc7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLHVCQUF1QixrREFBVztBQUNsQztBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsdUJBQXVCLGtEQUFXO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxnQkFBZ0Isa0RBQVc7QUFDM0I7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ04sd0RBQXdELHFCQUFxQjtBQUM3RTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsNkNBQU07QUFDOUI7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLDRCQUE0QiwrQ0FBUTtBQUNwQyxpQkFBaUIsa0RBQVc7QUFDNUI7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DO0FBQ25DLFFBQVEsNkJBQTZCO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQywrQ0FBUTtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFZ2hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHVzZWhvb2tzLXRzQDMuMS4xX3JlYWN0QDE5LjEuMVxcbm9kZV9tb2R1bGVzXFx1c2Vob29rcy10c1xcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VMYXlvdXRFZmZlY3QsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGRlYm91bmNlIGZyb20gJ2xvZGFzaC5kZWJvdW5jZSc7XG5cbi8vIHNyYy91c2VCb29sZWFuL3VzZUJvb2xlYW4udHNcbmZ1bmN0aW9uIHVzZUJvb2xlYW4oZGVmYXVsdFZhbHVlID0gZmFsc2UpIHtcbiAgaWYgKHR5cGVvZiBkZWZhdWx0VmFsdWUgIT09IFwiYm9vbGVhblwiKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiZGVmYXVsdFZhbHVlIG11c3QgYmUgYHRydWVgIG9yIGBmYWxzZWBcIik7XG4gIH1cbiAgY29uc3QgW3ZhbHVlLCBzZXRWYWx1ZV0gPSB1c2VTdGF0ZShkZWZhdWx0VmFsdWUpO1xuICBjb25zdCBzZXRUcnVlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFZhbHVlKHRydWUpO1xuICB9LCBbXSk7XG4gIGNvbnN0IHNldEZhbHNlID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFZhbHVlKGZhbHNlKTtcbiAgfSwgW10pO1xuICBjb25zdCB0b2dnbGUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc2V0VmFsdWUoKHgpID0+ICF4KTtcbiAgfSwgW10pO1xuICByZXR1cm4geyB2YWx1ZSwgc2V0VmFsdWUsIHNldFRydWUsIHNldEZhbHNlLCB0b2dnbGUgfTtcbn1cbnZhciB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIiA/IHVzZUxheW91dEVmZmVjdCA6IHVzZUVmZmVjdDtcblxuLy8gc3JjL3VzZUV2ZW50TGlzdGVuZXIvdXNlRXZlbnRMaXN0ZW5lci50c1xuZnVuY3Rpb24gdXNlRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGhhbmRsZXIsIGVsZW1lbnQsIG9wdGlvbnMpIHtcbiAgY29uc3Qgc2F2ZWRIYW5kbGVyID0gdXNlUmVmKGhhbmRsZXIpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBzYXZlZEhhbmRsZXIuY3VycmVudCA9IGhhbmRsZXI7XG4gIH0sIFtoYW5kbGVyXSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGFyZ2V0RWxlbWVudCA9IChlbGVtZW50ID09IG51bGwgPyB2b2lkIDAgOiBlbGVtZW50LmN1cnJlbnQpID8/IHdpbmRvdztcbiAgICBpZiAoISh0YXJnZXRFbGVtZW50ICYmIHRhcmdldEVsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcikpXG4gICAgICByZXR1cm47XG4gICAgY29uc3QgbGlzdGVuZXIgPSAoZXZlbnQpID0+IHtcbiAgICAgIHNhdmVkSGFuZGxlci5jdXJyZW50KGV2ZW50KTtcbiAgICB9O1xuICAgIHRhcmdldEVsZW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihldmVudE5hbWUsIGxpc3RlbmVyLCBvcHRpb25zKTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgdGFyZ2V0RWxlbWVudC5yZW1vdmVFdmVudExpc3RlbmVyKGV2ZW50TmFtZSwgbGlzdGVuZXIsIG9wdGlvbnMpO1xuICAgIH07XG4gIH0sIFtldmVudE5hbWUsIGVsZW1lbnQsIG9wdGlvbnNdKTtcbn1cblxuLy8gc3JjL3VzZUNsaWNrQW55V2hlcmUvdXNlQ2xpY2tBbnlXaGVyZS50c1xuZnVuY3Rpb24gdXNlQ2xpY2tBbnlXaGVyZShoYW5kbGVyKSB7XG4gIHVzZUV2ZW50TGlzdGVuZXIoXCJjbGlja1wiLCAoZXZlbnQpID0+IHtcbiAgICBoYW5kbGVyKGV2ZW50KTtcbiAgfSk7XG59XG5mdW5jdGlvbiB1c2VDb3B5VG9DbGlwYm9hcmQoKSB7XG4gIGNvbnN0IFtjb3BpZWRUZXh0LCBzZXRDb3BpZWRUZXh0XSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBjb3B5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKHRleHQpID0+IHtcbiAgICBpZiAoIShuYXZpZ2F0b3IgPT0gbnVsbCA/IHZvaWQgMCA6IG5hdmlnYXRvci5jbGlwYm9hcmQpKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCJDbGlwYm9hcmQgbm90IHN1cHBvcnRlZFwiKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRleHQpO1xuICAgICAgc2V0Q29waWVkVGV4dCh0ZXh0KTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCJDb3B5IGZhaWxlZFwiLCBlcnJvcik7XG4gICAgICBzZXRDb3BpZWRUZXh0KG51bGwpO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfSwgW10pO1xuICByZXR1cm4gW2NvcGllZFRleHQsIGNvcHldO1xufVxuZnVuY3Rpb24gdXNlQ291bnRlcihpbml0aWFsVmFsdWUpIHtcbiAgY29uc3QgW2NvdW50LCBzZXRDb3VudF0gPSB1c2VTdGF0ZShpbml0aWFsVmFsdWUgPz8gMCk7XG4gIGNvbnN0IGluY3JlbWVudCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRDb3VudCgoeCkgPT4geCArIDEpO1xuICB9LCBbXSk7XG4gIGNvbnN0IGRlY3JlbWVudCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRDb3VudCgoeCkgPT4geCAtIDEpO1xuICB9LCBbXSk7XG4gIGNvbnN0IHJlc2V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldENvdW50KGluaXRpYWxWYWx1ZSA/PyAwKTtcbiAgfSwgW2luaXRpYWxWYWx1ZV0pO1xuICByZXR1cm4ge1xuICAgIGNvdW50LFxuICAgIGluY3JlbWVudCxcbiAgICBkZWNyZW1lbnQsXG4gICAgcmVzZXQsXG4gICAgc2V0Q291bnRcbiAgfTtcbn1cbmZ1bmN0aW9uIHVzZUludGVydmFsKGNhbGxiYWNrLCBkZWxheSkge1xuICBjb25zdCBzYXZlZENhbGxiYWNrID0gdXNlUmVmKGNhbGxiYWNrKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgc2F2ZWRDYWxsYmFjay5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0sIFtjYWxsYmFja10pO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChkZWxheSA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBpZCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNhdmVkQ2FsbGJhY2suY3VycmVudCgpO1xuICAgIH0sIGRlbGF5KTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJJbnRlcnZhbChpZCk7XG4gICAgfTtcbiAgfSwgW2RlbGF5XSk7XG59XG5cbi8vIHNyYy91c2VDb3VudGRvd24vdXNlQ291bnRkb3duLnRzXG5mdW5jdGlvbiB1c2VDb3VudGRvd24oe1xuICBjb3VudFN0YXJ0LFxuICBjb3VudFN0b3AgPSAwLFxuICBpbnRlcnZhbE1zID0gMWUzLFxuICBpc0luY3JlbWVudCA9IGZhbHNlXG59KSB7XG4gIGNvbnN0IHtcbiAgICBjb3VudCxcbiAgICBpbmNyZW1lbnQsXG4gICAgZGVjcmVtZW50LFxuICAgIHJlc2V0OiByZXNldENvdW50ZXJcbiAgfSA9IHVzZUNvdW50ZXIoY291bnRTdGFydCk7XG4gIGNvbnN0IHtcbiAgICB2YWx1ZTogaXNDb3VudGRvd25SdW5uaW5nLFxuICAgIHNldFRydWU6IHN0YXJ0Q291bnRkb3duLFxuICAgIHNldEZhbHNlOiBzdG9wQ291bnRkb3duXG4gIH0gPSB1c2VCb29sZWFuKGZhbHNlKTtcbiAgY29uc3QgcmVzZXRDb3VudGRvd24gPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgc3RvcENvdW50ZG93bigpO1xuICAgIHJlc2V0Q291bnRlcigpO1xuICB9LCBbc3RvcENvdW50ZG93biwgcmVzZXRDb3VudGVyXSk7XG4gIGNvbnN0IGNvdW50ZG93bkNhbGxiYWNrID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChjb3VudCA9PT0gY291bnRTdG9wKSB7XG4gICAgICBzdG9wQ291bnRkb3duKCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGlmIChpc0luY3JlbWVudCkge1xuICAgICAgaW5jcmVtZW50KCk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRlY3JlbWVudCgpO1xuICAgIH1cbiAgfSwgW2NvdW50LCBjb3VudFN0b3AsIGRlY3JlbWVudCwgaW5jcmVtZW50LCBpc0luY3JlbWVudCwgc3RvcENvdW50ZG93bl0pO1xuICB1c2VJbnRlcnZhbChjb3VudGRvd25DYWxsYmFjaywgaXNDb3VudGRvd25SdW5uaW5nID8gaW50ZXJ2YWxNcyA6IG51bGwpO1xuICByZXR1cm4gW2NvdW50LCB7IHN0YXJ0Q291bnRkb3duLCBzdG9wQ291bnRkb3duLCByZXNldENvdW50ZG93biB9XTtcbn1cbmZ1bmN0aW9uIHVzZUV2ZW50Q2FsbGJhY2soZm4pIHtcbiAgY29uc3QgcmVmID0gdXNlUmVmKCgpID0+IHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDYW5ub3QgY2FsbCBhbiBldmVudCBoYW5kbGVyIHdoaWxlIHJlbmRlcmluZy5cIik7XG4gIH0pO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICByZWYuY3VycmVudCA9IGZuO1xuICB9LCBbZm5dKTtcbiAgcmV0dXJuIHVzZUNhbGxiYWNrKCguLi5hcmdzKSA9PiB7XG4gICAgdmFyIF9hO1xuICAgIHJldHVybiAoX2EgPSByZWYuY3VycmVudCkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmNhbGwocmVmLCAuLi5hcmdzKTtcbiAgfSwgW3JlZl0pO1xufVxuXG4vLyBzcmMvdXNlTG9jYWxTdG9yYWdlL3VzZUxvY2FsU3RvcmFnZS50c1xudmFyIElTX1NFUlZFUiA9IHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCI7XG5mdW5jdGlvbiB1c2VMb2NhbFN0b3JhZ2Uoa2V5LCBpbml0aWFsVmFsdWUsIG9wdGlvbnMgPSB7fSkge1xuICBjb25zdCB7IGluaXRpYWxpemVXaXRoVmFsdWUgPSB0cnVlIH0gPSBvcHRpb25zO1xuICBjb25zdCBzZXJpYWxpemVyID0gdXNlQ2FsbGJhY2soXG4gICAgKHZhbHVlKSA9PiB7XG4gICAgICBpZiAob3B0aW9ucy5zZXJpYWxpemVyKSB7XG4gICAgICAgIHJldHVybiBvcHRpb25zLnNlcmlhbGl6ZXIodmFsdWUpO1xuICAgICAgfVxuICAgICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHZhbHVlKTtcbiAgICB9LFxuICAgIFtvcHRpb25zXVxuICApO1xuICBjb25zdCBkZXNlcmlhbGl6ZXIgPSB1c2VDYWxsYmFjayhcbiAgICAodmFsdWUpID0+IHtcbiAgICAgIGlmIChvcHRpb25zLmRlc2VyaWFsaXplcikge1xuICAgICAgICByZXR1cm4gb3B0aW9ucy5kZXNlcmlhbGl6ZXIodmFsdWUpO1xuICAgICAgfVxuICAgICAgaWYgKHZhbHVlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIHJldHVybiB2b2lkIDA7XG4gICAgICB9XG4gICAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBpbml0aWFsVmFsdWUgaW5zdGFuY2VvZiBGdW5jdGlvbiA/IGluaXRpYWxWYWx1ZSgpIDogaW5pdGlhbFZhbHVlO1xuICAgICAgbGV0IHBhcnNlZDtcbiAgICAgIHRyeSB7XG4gICAgICAgIHBhcnNlZCA9IEpTT04ucGFyc2UodmFsdWUpO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHBhcnNpbmcgSlNPTjpcIiwgZXJyb3IpO1xuICAgICAgICByZXR1cm4gZGVmYXVsdFZhbHVlO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHBhcnNlZDtcbiAgICB9LFxuICAgIFtvcHRpb25zLCBpbml0aWFsVmFsdWVdXG4gICk7XG4gIGNvbnN0IHJlYWRWYWx1ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBjb25zdCBpbml0aWFsVmFsdWVUb1VzZSA9IGluaXRpYWxWYWx1ZSBpbnN0YW5jZW9mIEZ1bmN0aW9uID8gaW5pdGlhbFZhbHVlKCkgOiBpbml0aWFsVmFsdWU7XG4gICAgaWYgKElTX1NFUlZFUikge1xuICAgICAgcmV0dXJuIGluaXRpYWxWYWx1ZVRvVXNlO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmF3ID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICByZXR1cm4gcmF3ID8gZGVzZXJpYWxpemVyKHJhdykgOiBpbml0aWFsVmFsdWVUb1VzZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGBFcnJvciByZWFkaW5nIGxvY2FsU3RvcmFnZSBrZXkgXFx1MjAxQyR7a2V5fVxcdTIwMUQ6YCwgZXJyb3IpO1xuICAgICAgcmV0dXJuIGluaXRpYWxWYWx1ZVRvVXNlO1xuICAgIH1cbiAgfSwgW2luaXRpYWxWYWx1ZSwga2V5LCBkZXNlcmlhbGl6ZXJdKTtcbiAgY29uc3QgW3N0b3JlZFZhbHVlLCBzZXRTdG9yZWRWYWx1ZV0gPSB1c2VTdGF0ZSgoKSA9PiB7XG4gICAgaWYgKGluaXRpYWxpemVXaXRoVmFsdWUpIHtcbiAgICAgIHJldHVybiByZWFkVmFsdWUoKTtcbiAgICB9XG4gICAgcmV0dXJuIGluaXRpYWxWYWx1ZSBpbnN0YW5jZW9mIEZ1bmN0aW9uID8gaW5pdGlhbFZhbHVlKCkgOiBpbml0aWFsVmFsdWU7XG4gIH0pO1xuICBjb25zdCBzZXRWYWx1ZSA9IHVzZUV2ZW50Q2FsbGJhY2soKHZhbHVlKSA9PiB7XG4gICAgaWYgKElTX1NFUlZFUikge1xuICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICBgVHJpZWQgc2V0dGluZyBsb2NhbFN0b3JhZ2Uga2V5IFxcdTIwMUMke2tleX1cXHUyMDFEIGV2ZW4gdGhvdWdoIGVudmlyb25tZW50IGlzIG5vdCBhIGNsaWVudGBcbiAgICAgICk7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBuZXdWYWx1ZSA9IHZhbHVlIGluc3RhbmNlb2YgRnVuY3Rpb24gPyB2YWx1ZShyZWFkVmFsdWUoKSkgOiB2YWx1ZTtcbiAgICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2Uuc2V0SXRlbShrZXksIHNlcmlhbGl6ZXIobmV3VmFsdWUpKTtcbiAgICAgIHNldFN0b3JlZFZhbHVlKG5ld1ZhbHVlKTtcbiAgICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBTdG9yYWdlRXZlbnQoXCJsb2NhbC1zdG9yYWdlXCIsIHsga2V5IH0pKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS53YXJuKGBFcnJvciBzZXR0aW5nIGxvY2FsU3RvcmFnZSBrZXkgXFx1MjAxQyR7a2V5fVxcdTIwMUQ6YCwgZXJyb3IpO1xuICAgIH1cbiAgfSk7XG4gIGNvbnN0IHJlbW92ZVZhbHVlID0gdXNlRXZlbnRDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKElTX1NFUlZFUikge1xuICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICBgVHJpZWQgcmVtb3ZpbmcgbG9jYWxTdG9yYWdlIGtleSBcXHUyMDFDJHtrZXl9XFx1MjAxRCBldmVuIHRob3VnaCBlbnZpcm9ubWVudCBpcyBub3QgYSBjbGllbnRgXG4gICAgICApO1xuICAgIH1cbiAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBpbml0aWFsVmFsdWUgaW5zdGFuY2VvZiBGdW5jdGlvbiA/IGluaXRpYWxWYWx1ZSgpIDogaW5pdGlhbFZhbHVlO1xuICAgIHdpbmRvdy5sb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShrZXkpO1xuICAgIHNldFN0b3JlZFZhbHVlKGRlZmF1bHRWYWx1ZSk7XG4gICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IFN0b3JhZ2VFdmVudChcImxvY2FsLXN0b3JhZ2VcIiwgeyBrZXkgfSkpO1xuICB9KTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRTdG9yZWRWYWx1ZShyZWFkVmFsdWUoKSk7XG4gIH0sIFtrZXldKTtcbiAgY29uc3QgaGFuZGxlU3RvcmFnZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxuICAgIChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSAmJiBldmVudC5rZXkgIT09IGtleSkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBzZXRTdG9yZWRWYWx1ZShyZWFkVmFsdWUoKSk7XG4gICAgfSxcbiAgICBba2V5LCByZWFkVmFsdWVdXG4gICk7XG4gIHVzZUV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuICB1c2VFdmVudExpc3RlbmVyKFwibG9jYWwtc3RvcmFnZVwiLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcbiAgcmV0dXJuIFtzdG9yZWRWYWx1ZSwgc2V0VmFsdWUsIHJlbW92ZVZhbHVlXTtcbn1cbnZhciBJU19TRVJWRVIyID0gdHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIjtcbmZ1bmN0aW9uIHVzZU1lZGlhUXVlcnkocXVlcnksIHtcbiAgZGVmYXVsdFZhbHVlID0gZmFsc2UsXG4gIGluaXRpYWxpemVXaXRoVmFsdWUgPSB0cnVlXG59ID0ge30pIHtcbiAgY29uc3QgZ2V0TWF0Y2hlcyA9IChxdWVyeTIpID0+IHtcbiAgICBpZiAoSVNfU0VSVkVSMikge1xuICAgICAgcmV0dXJuIGRlZmF1bHRWYWx1ZTtcbiAgICB9XG4gICAgcmV0dXJuIHdpbmRvdy5tYXRjaE1lZGlhKHF1ZXJ5MikubWF0Y2hlcztcbiAgfTtcbiAgY29uc3QgW21hdGNoZXMsIHNldE1hdGNoZXNdID0gdXNlU3RhdGUoKCkgPT4ge1xuICAgIGlmIChpbml0aWFsaXplV2l0aFZhbHVlKSB7XG4gICAgICByZXR1cm4gZ2V0TWF0Y2hlcyhxdWVyeSk7XG4gICAgfVxuICAgIHJldHVybiBkZWZhdWx0VmFsdWU7XG4gIH0pO1xuICBmdW5jdGlvbiBoYW5kbGVDaGFuZ2UoKSB7XG4gICAgc2V0TWF0Y2hlcyhnZXRNYXRjaGVzKHF1ZXJ5KSk7XG4gIH1cbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgbWF0Y2hNZWRpYSA9IHdpbmRvdy5tYXRjaE1lZGlhKHF1ZXJ5KTtcbiAgICBoYW5kbGVDaGFuZ2UoKTtcbiAgICBpZiAobWF0Y2hNZWRpYS5hZGRMaXN0ZW5lcikge1xuICAgICAgbWF0Y2hNZWRpYS5hZGRMaXN0ZW5lcihoYW5kbGVDaGFuZ2UpO1xuICAgIH0gZWxzZSB7XG4gICAgICBtYXRjaE1lZGlhLmFkZEV2ZW50TGlzdGVuZXIoXCJjaGFuZ2VcIiwgaGFuZGxlQ2hhbmdlKTtcbiAgICB9XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChtYXRjaE1lZGlhLnJlbW92ZUxpc3RlbmVyKSB7XG4gICAgICAgIG1hdGNoTWVkaWEucmVtb3ZlTGlzdGVuZXIoaGFuZGxlQ2hhbmdlKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIG1hdGNoTWVkaWEucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImNoYW5nZVwiLCBoYW5kbGVDaGFuZ2UpO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFtxdWVyeV0pO1xuICByZXR1cm4gbWF0Y2hlcztcbn1cblxuLy8gc3JjL3VzZURhcmtNb2RlL3VzZURhcmtNb2RlLnRzXG52YXIgQ09MT1JfU0NIRU1FX1FVRVJZID0gXCIocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspXCI7XG52YXIgTE9DQUxfU1RPUkFHRV9LRVkgPSBcInVzZWhvb2tzLXRzLWRhcmstbW9kZVwiO1xuZnVuY3Rpb24gdXNlRGFya01vZGUob3B0aW9ucyA9IHt9KSB7XG4gIGNvbnN0IHtcbiAgICBkZWZhdWx0VmFsdWUsXG4gICAgbG9jYWxTdG9yYWdlS2V5ID0gTE9DQUxfU1RPUkFHRV9LRVksXG4gICAgaW5pdGlhbGl6ZVdpdGhWYWx1ZSA9IHRydWVcbiAgfSA9IG9wdGlvbnM7XG4gIGNvbnN0IGlzRGFya09TID0gdXNlTWVkaWFRdWVyeShDT0xPUl9TQ0hFTUVfUVVFUlksIHtcbiAgICBpbml0aWFsaXplV2l0aFZhbHVlLFxuICAgIGRlZmF1bHRWYWx1ZVxuICB9KTtcbiAgY29uc3QgW2lzRGFya01vZGUsIHNldERhcmtNb2RlXSA9IHVzZUxvY2FsU3RvcmFnZShcbiAgICBsb2NhbFN0b3JhZ2VLZXksXG4gICAgZGVmYXVsdFZhbHVlID8/IGlzRGFya09TID8/IGZhbHNlLFxuICAgIHsgaW5pdGlhbGl6ZVdpdGhWYWx1ZSB9XG4gICk7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc0RhcmtPUyAhPT0gaXNEYXJrTW9kZSkge1xuICAgICAgc2V0RGFya01vZGUoaXNEYXJrT1MpO1xuICAgIH1cbiAgfSwgW2lzRGFya09TXSk7XG4gIHJldHVybiB7XG4gICAgaXNEYXJrTW9kZSxcbiAgICB0b2dnbGU6ICgpID0+IHtcbiAgICAgIHNldERhcmtNb2RlKChwcmV2KSA9PiAhcHJldik7XG4gICAgfSxcbiAgICBlbmFibGU6ICgpID0+IHtcbiAgICAgIHNldERhcmtNb2RlKHRydWUpO1xuICAgIH0sXG4gICAgZGlzYWJsZTogKCkgPT4ge1xuICAgICAgc2V0RGFya01vZGUoZmFsc2UpO1xuICAgIH0sXG4gICAgc2V0OiAodmFsdWUpID0+IHtcbiAgICAgIHNldERhcmtNb2RlKHZhbHVlKTtcbiAgICB9XG4gIH07XG59XG5mdW5jdGlvbiB1c2VVbm1vdW50KGZ1bmMpIHtcbiAgY29uc3QgZnVuY1JlZiA9IHVzZVJlZihmdW5jKTtcbiAgZnVuY1JlZi5jdXJyZW50ID0gZnVuYztcbiAgdXNlRWZmZWN0KFxuICAgICgpID0+ICgpID0+IHtcbiAgICAgIGZ1bmNSZWYuY3VycmVudCgpO1xuICAgIH0sXG4gICAgW11cbiAgKTtcbn1cblxuLy8gc3JjL3VzZURlYm91bmNlQ2FsbGJhY2svdXNlRGVib3VuY2VDYWxsYmFjay50c1xuZnVuY3Rpb24gdXNlRGVib3VuY2VDYWxsYmFjayhmdW5jLCBkZWxheSA9IDUwMCwgb3B0aW9ucykge1xuICBjb25zdCBkZWJvdW5jZWRGdW5jID0gdXNlUmVmKCk7XG4gIHVzZVVubW91bnQoKCkgPT4ge1xuICAgIGlmIChkZWJvdW5jZWRGdW5jLmN1cnJlbnQpIHtcbiAgICAgIGRlYm91bmNlZEZ1bmMuY3VycmVudC5jYW5jZWwoKTtcbiAgICB9XG4gIH0pO1xuICBjb25zdCBkZWJvdW5jZWQgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBkZWJvdW5jZWRGdW5jSW5zdGFuY2UgPSBkZWJvdW5jZShmdW5jLCBkZWxheSwgb3B0aW9ucyk7XG4gICAgY29uc3Qgd3JhcHBlZEZ1bmMgPSAoLi4uYXJncykgPT4ge1xuICAgICAgcmV0dXJuIGRlYm91bmNlZEZ1bmNJbnN0YW5jZSguLi5hcmdzKTtcbiAgICB9O1xuICAgIHdyYXBwZWRGdW5jLmNhbmNlbCA9ICgpID0+IHtcbiAgICAgIGRlYm91bmNlZEZ1bmNJbnN0YW5jZS5jYW5jZWwoKTtcbiAgICB9O1xuICAgIHdyYXBwZWRGdW5jLmlzUGVuZGluZyA9ICgpID0+IHtcbiAgICAgIHJldHVybiAhIWRlYm91bmNlZEZ1bmMuY3VycmVudDtcbiAgICB9O1xuICAgIHdyYXBwZWRGdW5jLmZsdXNoID0gKCkgPT4ge1xuICAgICAgcmV0dXJuIGRlYm91bmNlZEZ1bmNJbnN0YW5jZS5mbHVzaCgpO1xuICAgIH07XG4gICAgcmV0dXJuIHdyYXBwZWRGdW5jO1xuICB9LCBbZnVuYywgZGVsYXksIG9wdGlvbnNdKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBkZWJvdW5jZWRGdW5jLmN1cnJlbnQgPSBkZWJvdW5jZShmdW5jLCBkZWxheSwgb3B0aW9ucyk7XG4gIH0sIFtmdW5jLCBkZWxheSwgb3B0aW9uc10pO1xuICByZXR1cm4gZGVib3VuY2VkO1xufVxuZnVuY3Rpb24gdXNlRGVib3VuY2VWYWx1ZShpbml0aWFsVmFsdWUsIGRlbGF5LCBvcHRpb25zKSB7XG4gIGNvbnN0IGVxID0gKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZXF1YWxpdHlGbikgPz8gKChsZWZ0LCByaWdodCkgPT4gbGVmdCA9PT0gcmlnaHQpO1xuICBjb25zdCB1bndyYXBwZWRJbml0aWFsVmFsdWUgPSBpbml0aWFsVmFsdWUgaW5zdGFuY2VvZiBGdW5jdGlvbiA/IGluaXRpYWxWYWx1ZSgpIDogaW5pdGlhbFZhbHVlO1xuICBjb25zdCBbZGVib3VuY2VkVmFsdWUsIHNldERlYm91bmNlZFZhbHVlXSA9IHVzZVN0YXRlKHVud3JhcHBlZEluaXRpYWxWYWx1ZSk7XG4gIGNvbnN0IHByZXZpb3VzVmFsdWVSZWYgPSB1c2VSZWYodW53cmFwcGVkSW5pdGlhbFZhbHVlKTtcbiAgY29uc3QgdXBkYXRlRGVib3VuY2VkVmFsdWUgPSB1c2VEZWJvdW5jZUNhbGxiYWNrKFxuICAgIHNldERlYm91bmNlZFZhbHVlLFxuICAgIGRlbGF5LFxuICAgIG9wdGlvbnNcbiAgKTtcbiAgaWYgKCFlcShwcmV2aW91c1ZhbHVlUmVmLmN1cnJlbnQsIHVud3JhcHBlZEluaXRpYWxWYWx1ZSkpIHtcbiAgICB1cGRhdGVEZWJvdW5jZWRWYWx1ZSh1bndyYXBwZWRJbml0aWFsVmFsdWUpO1xuICAgIHByZXZpb3VzVmFsdWVSZWYuY3VycmVudCA9IHVud3JhcHBlZEluaXRpYWxWYWx1ZTtcbiAgfVxuICByZXR1cm4gW2RlYm91bmNlZFZhbHVlLCB1cGRhdGVEZWJvdW5jZWRWYWx1ZV07XG59XG5mdW5jdGlvbiB1c2VEb2N1bWVudFRpdGxlKHRpdGxlLCBvcHRpb25zID0ge30pIHtcbiAgY29uc3QgeyBwcmVzZXJ2ZVRpdGxlT25Vbm1vdW50ID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgY29uc3QgZGVmYXVsdFRpdGxlID0gdXNlUmVmKG51bGwpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBkZWZhdWx0VGl0bGUuY3VycmVudCA9IHdpbmRvdy5kb2N1bWVudC50aXRsZTtcbiAgfSwgW10pO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICB3aW5kb3cuZG9jdW1lbnQudGl0bGUgPSB0aXRsZTtcbiAgfSwgW3RpdGxlXSk7XG4gIHVzZVVubW91bnQoKCkgPT4ge1xuICAgIGlmICghcHJlc2VydmVUaXRsZU9uVW5tb3VudCAmJiBkZWZhdWx0VGl0bGUuY3VycmVudCkge1xuICAgICAgd2luZG93LmRvY3VtZW50LnRpdGxlID0gZGVmYXVsdFRpdGxlLmN1cnJlbnQ7XG4gICAgfVxuICB9KTtcbn1cbmZ1bmN0aW9uIHVzZUhvdmVyKGVsZW1lbnRSZWYpIHtcbiAgY29uc3QgW3ZhbHVlLCBzZXRWYWx1ZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGhhbmRsZU1vdXNlRW50ZXIgPSAoKSA9PiB7XG4gICAgc2V0VmFsdWUodHJ1ZSk7XG4gIH07XG4gIGNvbnN0IGhhbmRsZU1vdXNlTGVhdmUgPSAoKSA9PiB7XG4gICAgc2V0VmFsdWUoZmFsc2UpO1xuICB9O1xuICB1c2VFdmVudExpc3RlbmVyKFwibW91c2VlbnRlclwiLCBoYW5kbGVNb3VzZUVudGVyLCBlbGVtZW50UmVmKTtcbiAgdXNlRXZlbnRMaXN0ZW5lcihcIm1vdXNlbGVhdmVcIiwgaGFuZGxlTW91c2VMZWF2ZSwgZWxlbWVudFJlZik7XG4gIHJldHVybiB2YWx1ZTtcbn1cbmZ1bmN0aW9uIHVzZUludGVyc2VjdGlvbk9ic2VydmVyKHtcbiAgdGhyZXNob2xkID0gMCxcbiAgcm9vdCA9IG51bGwsXG4gIHJvb3RNYXJnaW4gPSBcIjAlXCIsXG4gIGZyZWV6ZU9uY2VWaXNpYmxlID0gZmFsc2UsXG4gIGluaXRpYWxJc0ludGVyc2VjdGluZyA9IGZhbHNlLFxuICBvbkNoYW5nZVxufSA9IHt9KSB7XG4gIHZhciBfYTtcbiAgY29uc3QgW3JlZiwgc2V0UmVmXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZVN0YXRlKCgpID0+ICh7XG4gICAgaXNJbnRlcnNlY3Rpbmc6IGluaXRpYWxJc0ludGVyc2VjdGluZyxcbiAgICBlbnRyeTogdm9pZCAwXG4gIH0pKTtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSB1c2VSZWYoKTtcbiAgY2FsbGJhY2tSZWYuY3VycmVudCA9IG9uQ2hhbmdlO1xuICBjb25zdCBmcm96ZW4gPSAoKF9hID0gc3RhdGUuZW50cnkpID09IG51bGwgPyB2b2lkIDAgOiBfYS5pc0ludGVyc2VjdGluZykgJiYgZnJlZXplT25jZVZpc2libGU7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFyZWYpXG4gICAgICByZXR1cm47XG4gICAgaWYgKCEoXCJJbnRlcnNlY3Rpb25PYnNlcnZlclwiIGluIHdpbmRvdykpXG4gICAgICByZXR1cm47XG4gICAgaWYgKGZyb3plbilcbiAgICAgIHJldHVybjtcbiAgICBsZXQgdW5vYnNlcnZlO1xuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKFxuICAgICAgKGVudHJpZXMpID0+IHtcbiAgICAgICAgY29uc3QgdGhyZXNob2xkcyA9IEFycmF5LmlzQXJyYXkob2JzZXJ2ZXIudGhyZXNob2xkcykgPyBvYnNlcnZlci50aHJlc2hvbGRzIDogW29ic2VydmVyLnRocmVzaG9sZHNdO1xuICAgICAgICBlbnRyaWVzLmZvckVhY2goKGVudHJ5KSA9PiB7XG4gICAgICAgICAgY29uc3QgaXNJbnRlcnNlY3RpbmcgPSBlbnRyeS5pc0ludGVyc2VjdGluZyAmJiB0aHJlc2hvbGRzLnNvbWUoKHRocmVzaG9sZDIpID0+IGVudHJ5LmludGVyc2VjdGlvblJhdGlvID49IHRocmVzaG9sZDIpO1xuICAgICAgICAgIHNldFN0YXRlKHsgaXNJbnRlcnNlY3RpbmcsIGVudHJ5IH0pO1xuICAgICAgICAgIGlmIChjYWxsYmFja1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICBjYWxsYmFja1JlZi5jdXJyZW50KGlzSW50ZXJzZWN0aW5nLCBlbnRyeSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGlmIChpc0ludGVyc2VjdGluZyAmJiBmcmVlemVPbmNlVmlzaWJsZSAmJiB1bm9ic2VydmUpIHtcbiAgICAgICAgICAgIHVub2JzZXJ2ZSgpO1xuICAgICAgICAgICAgdW5vYnNlcnZlID0gdm9pZCAwO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9LFxuICAgICAgeyB0aHJlc2hvbGQsIHJvb3QsIHJvb3RNYXJnaW4gfVxuICAgICk7XG4gICAgb2JzZXJ2ZXIub2JzZXJ2ZShyZWYpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBvYnNlcnZlci5kaXNjb25uZWN0KCk7XG4gICAgfTtcbiAgfSwgW1xuICAgIHJlZixcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gICAgSlNPTi5zdHJpbmdpZnkodGhyZXNob2xkKSxcbiAgICByb290LFxuICAgIHJvb3RNYXJnaW4sXG4gICAgZnJvemVuLFxuICAgIGZyZWV6ZU9uY2VWaXNpYmxlXG4gIF0pO1xuICBjb25zdCBwcmV2UmVmID0gdXNlUmVmKG51bGwpO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHZhciBfYTI7XG4gICAgaWYgKCFyZWYgJiYgKChfYTIgPSBzdGF0ZS5lbnRyeSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hMi50YXJnZXQpICYmICFmcmVlemVPbmNlVmlzaWJsZSAmJiAhZnJvemVuICYmIHByZXZSZWYuY3VycmVudCAhPT0gc3RhdGUuZW50cnkudGFyZ2V0KSB7XG4gICAgICBwcmV2UmVmLmN1cnJlbnQgPSBzdGF0ZS5lbnRyeS50YXJnZXQ7XG4gICAgICBzZXRTdGF0ZSh7IGlzSW50ZXJzZWN0aW5nOiBpbml0aWFsSXNJbnRlcnNlY3RpbmcsIGVudHJ5OiB2b2lkIDAgfSk7XG4gICAgfVxuICB9LCBbcmVmLCBzdGF0ZS5lbnRyeSwgZnJlZXplT25jZVZpc2libGUsIGZyb3plbiwgaW5pdGlhbElzSW50ZXJzZWN0aW5nXSk7XG4gIGNvbnN0IHJlc3VsdCA9IFtcbiAgICBzZXRSZWYsXG4gICAgISFzdGF0ZS5pc0ludGVyc2VjdGluZyxcbiAgICBzdGF0ZS5lbnRyeVxuICBdO1xuICByZXN1bHQucmVmID0gcmVzdWx0WzBdO1xuICByZXN1bHQuaXNJbnRlcnNlY3RpbmcgPSByZXN1bHRbMV07XG4gIHJlc3VsdC5lbnRyeSA9IHJlc3VsdFsyXTtcbiAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIHVzZUlzQ2xpZW50KCkge1xuICBjb25zdCBbaXNDbGllbnQsIHNldENsaWVudF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0Q2xpZW50KHRydWUpO1xuICB9LCBbXSk7XG4gIHJldHVybiBpc0NsaWVudDtcbn1cbmZ1bmN0aW9uIHVzZUlzTW91bnRlZCgpIHtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlUmVmKGZhbHNlKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpc01vdW50ZWQuY3VycmVudCA9IHRydWU7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgfTtcbiAgfSwgW10pO1xuICByZXR1cm4gdXNlQ2FsbGJhY2soKCkgPT4gaXNNb3VudGVkLmN1cnJlbnQsIFtdKTtcbn1cbmZ1bmN0aW9uIHVzZU1hcChpbml0aWFsU3RhdGUgPSAvKiBAX19QVVJFX18gKi8gbmV3IE1hcCgpKSB7XG4gIGNvbnN0IFttYXAsIHNldE1hcF0gPSB1c2VTdGF0ZShuZXcgTWFwKGluaXRpYWxTdGF0ZSkpO1xuICBjb25zdCBhY3Rpb25zID0ge1xuICAgIHNldDogdXNlQ2FsbGJhY2soKGtleSwgdmFsdWUpID0+IHtcbiAgICAgIHNldE1hcCgocHJldikgPT4ge1xuICAgICAgICBjb25zdCBjb3B5ID0gbmV3IE1hcChwcmV2KTtcbiAgICAgICAgY29weS5zZXQoa2V5LCB2YWx1ZSk7XG4gICAgICAgIHJldHVybiBjb3B5O1xuICAgICAgfSk7XG4gICAgfSwgW10pLFxuICAgIHNldEFsbDogdXNlQ2FsbGJhY2soKGVudHJpZXMpID0+IHtcbiAgICAgIHNldE1hcCgoKSA9PiBuZXcgTWFwKGVudHJpZXMpKTtcbiAgICB9LCBbXSksXG4gICAgcmVtb3ZlOiB1c2VDYWxsYmFjaygoa2V5KSA9PiB7XG4gICAgICBzZXRNYXAoKHByZXYpID0+IHtcbiAgICAgICAgY29uc3QgY29weSA9IG5ldyBNYXAocHJldik7XG4gICAgICAgIGNvcHkuZGVsZXRlKGtleSk7XG4gICAgICAgIHJldHVybiBjb3B5O1xuICAgICAgfSk7XG4gICAgfSwgW10pLFxuICAgIHJlc2V0OiB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgICBzZXRNYXAoKCkgPT4gLyogQF9fUFVSRV9fICovIG5ldyBNYXAoKSk7XG4gICAgfSwgW10pXG4gIH07XG4gIHJldHVybiBbbWFwLCBhY3Rpb25zXTtcbn1cblxuLy8gc3JjL3VzZU9uQ2xpY2tPdXRzaWRlL3VzZU9uQ2xpY2tPdXRzaWRlLnRzXG5mdW5jdGlvbiB1c2VPbkNsaWNrT3V0c2lkZShyZWYsIGhhbmRsZXIsIGV2ZW50VHlwZSA9IFwibW91c2Vkb3duXCIsIGV2ZW50TGlzdGVuZXJPcHRpb25zID0ge30pIHtcbiAgdXNlRXZlbnRMaXN0ZW5lcihcbiAgICBldmVudFR5cGUsXG4gICAgKGV2ZW50KSA9PiB7XG4gICAgICBjb25zdCB0YXJnZXQgPSBldmVudC50YXJnZXQ7XG4gICAgICBpZiAoIXRhcmdldCB8fCAhdGFyZ2V0LmlzQ29ubmVjdGVkKSB7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGlzT3V0c2lkZSA9IEFycmF5LmlzQXJyYXkocmVmKSA/IHJlZi5maWx0ZXIoKHIpID0+IEJvb2xlYW4oci5jdXJyZW50KSkuZXZlcnkoKHIpID0+IHIuY3VycmVudCAmJiAhci5jdXJyZW50LmNvbnRhaW5zKHRhcmdldCkpIDogcmVmLmN1cnJlbnQgJiYgIXJlZi5jdXJyZW50LmNvbnRhaW5zKHRhcmdldCk7XG4gICAgICBpZiAoaXNPdXRzaWRlKSB7XG4gICAgICAgIGhhbmRsZXIoZXZlbnQpO1xuICAgICAgfVxuICAgIH0sXG4gICAgdm9pZCAwLFxuICAgIGV2ZW50TGlzdGVuZXJPcHRpb25zXG4gICk7XG59XG52YXIgSVNfU0VSVkVSMyA9IHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCI7XG5mdW5jdGlvbiB1c2VSZWFkTG9jYWxTdG9yYWdlKGtleSwgb3B0aW9ucyA9IHt9KSB7XG4gIGxldCB7IGluaXRpYWxpemVXaXRoVmFsdWUgPSB0cnVlIH0gPSBvcHRpb25zO1xuICBpZiAoSVNfU0VSVkVSMykge1xuICAgIGluaXRpYWxpemVXaXRoVmFsdWUgPSBmYWxzZTtcbiAgfVxuICBjb25zdCBkZXNlcmlhbGl6ZXIgPSB1c2VDYWxsYmFjayhcbiAgICAodmFsdWUpID0+IHtcbiAgICAgIGlmIChvcHRpb25zLmRlc2VyaWFsaXplcikge1xuICAgICAgICByZXR1cm4gb3B0aW9ucy5kZXNlcmlhbGl6ZXIodmFsdWUpO1xuICAgICAgfVxuICAgICAgaWYgKHZhbHVlID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgIHJldHVybiB2b2lkIDA7XG4gICAgICB9XG4gICAgICBsZXQgcGFyc2VkO1xuICAgICAgdHJ5IHtcbiAgICAgICAgcGFyc2VkID0gSlNPTi5wYXJzZSh2YWx1ZSk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgcGFyc2luZyBKU09OOlwiLCBlcnJvcik7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHBhcnNlZDtcbiAgICB9LFxuICAgIFtvcHRpb25zXVxuICApO1xuICBjb25zdCByZWFkVmFsdWUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKElTX1NFUlZFUjMpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmF3ID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKGtleSk7XG4gICAgICByZXR1cm4gcmF3ID8gZGVzZXJpYWxpemVyKHJhdykgOiBudWxsO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oYEVycm9yIHJlYWRpbmcgbG9jYWxTdG9yYWdlIGtleSBcXHUyMDFDJHtrZXl9XFx1MjAxRDpgLCBlcnJvcik7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gIH0sIFtrZXksIGRlc2VyaWFsaXplcl0pO1xuICBjb25zdCBbc3RvcmVkVmFsdWUsIHNldFN0b3JlZFZhbHVlXSA9IHVzZVN0YXRlKCgpID0+IHtcbiAgICBpZiAoaW5pdGlhbGl6ZVdpdGhWYWx1ZSkge1xuICAgICAgcmV0dXJuIHJlYWRWYWx1ZSgpO1xuICAgIH1cbiAgICByZXR1cm4gdm9pZCAwO1xuICB9KTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRTdG9yZWRWYWx1ZShyZWFkVmFsdWUoKSk7XG4gIH0sIFtrZXldKTtcbiAgY29uc3QgaGFuZGxlU3RvcmFnZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxuICAgIChldmVudCkgPT4ge1xuICAgICAgaWYgKGV2ZW50LmtleSAmJiBldmVudC5rZXkgIT09IGtleSkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBzZXRTdG9yZWRWYWx1ZShyZWFkVmFsdWUoKSk7XG4gICAgfSxcbiAgICBba2V5LCByZWFkVmFsdWVdXG4gICk7XG4gIHVzZUV2ZW50TGlzdGVuZXIoXCJzdG9yYWdlXCIsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UpO1xuICB1c2VFdmVudExpc3RlbmVyKFwibG9jYWwtc3RvcmFnZVwiLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcbiAgcmV0dXJuIHN0b3JlZFZhbHVlO1xufVxudmFyIGluaXRpYWxTaXplID0ge1xuICB3aWR0aDogdm9pZCAwLFxuICBoZWlnaHQ6IHZvaWQgMFxufTtcbmZ1bmN0aW9uIHVzZVJlc2l6ZU9ic2VydmVyKG9wdGlvbnMpIHtcbiAgY29uc3QgeyByZWYsIGJveCA9IFwiY29udGVudC1ib3hcIiB9ID0gb3B0aW9ucztcbiAgY29uc3QgW3sgd2lkdGgsIGhlaWdodCB9LCBzZXRTaXplXSA9IHVzZVN0YXRlKGluaXRpYWxTaXplKTtcbiAgY29uc3QgaXNNb3VudGVkID0gdXNlSXNNb3VudGVkKCk7XG4gIGNvbnN0IHByZXZpb3VzU2l6ZSA9IHVzZVJlZih7IC4uLmluaXRpYWxTaXplIH0pO1xuICBjb25zdCBvblJlc2l6ZSA9IHVzZVJlZih2b2lkIDApO1xuICBvblJlc2l6ZS5jdXJyZW50ID0gb3B0aW9ucy5vblJlc2l6ZTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXJlZi5jdXJyZW50KVxuICAgICAgcmV0dXJuO1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiIHx8ICEoXCJSZXNpemVPYnNlcnZlclwiIGluIHdpbmRvdykpXG4gICAgICByZXR1cm47XG4gICAgY29uc3Qgb2JzZXJ2ZXIgPSBuZXcgUmVzaXplT2JzZXJ2ZXIoKFtlbnRyeV0pID0+IHtcbiAgICAgIGNvbnN0IGJveFByb3AgPSBib3ggPT09IFwiYm9yZGVyLWJveFwiID8gXCJib3JkZXJCb3hTaXplXCIgOiBib3ggPT09IFwiZGV2aWNlLXBpeGVsLWNvbnRlbnQtYm94XCIgPyBcImRldmljZVBpeGVsQ29udGVudEJveFNpemVcIiA6IFwiY29udGVudEJveFNpemVcIjtcbiAgICAgIGNvbnN0IG5ld1dpZHRoID0gZXh0cmFjdFNpemUoZW50cnksIGJveFByb3AsIFwiaW5saW5lU2l6ZVwiKTtcbiAgICAgIGNvbnN0IG5ld0hlaWdodCA9IGV4dHJhY3RTaXplKGVudHJ5LCBib3hQcm9wLCBcImJsb2NrU2l6ZVwiKTtcbiAgICAgIGNvbnN0IGhhc0NoYW5nZWQgPSBwcmV2aW91c1NpemUuY3VycmVudC53aWR0aCAhPT0gbmV3V2lkdGggfHwgcHJldmlvdXNTaXplLmN1cnJlbnQuaGVpZ2h0ICE9PSBuZXdIZWlnaHQ7XG4gICAgICBpZiAoaGFzQ2hhbmdlZCkge1xuICAgICAgICBjb25zdCBuZXdTaXplID0geyB3aWR0aDogbmV3V2lkdGgsIGhlaWdodDogbmV3SGVpZ2h0IH07XG4gICAgICAgIHByZXZpb3VzU2l6ZS5jdXJyZW50LndpZHRoID0gbmV3V2lkdGg7XG4gICAgICAgIHByZXZpb3VzU2l6ZS5jdXJyZW50LmhlaWdodCA9IG5ld0hlaWdodDtcbiAgICAgICAgaWYgKG9uUmVzaXplLmN1cnJlbnQpIHtcbiAgICAgICAgICBvblJlc2l6ZS5jdXJyZW50KG5ld1NpemUpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGlmIChpc01vdW50ZWQoKSkge1xuICAgICAgICAgICAgc2V0U2l6ZShuZXdTaXplKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgICBvYnNlcnZlci5vYnNlcnZlKHJlZi5jdXJyZW50LCB7IGJveCB9KTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgb2JzZXJ2ZXIuZGlzY29ubmVjdCgpO1xuICAgIH07XG4gIH0sIFtib3gsIHJlZiwgaXNNb3VudGVkXSk7XG4gIHJldHVybiB7IHdpZHRoLCBoZWlnaHQgfTtcbn1cbmZ1bmN0aW9uIGV4dHJhY3RTaXplKGVudHJ5LCBib3gsIHNpemVUeXBlKSB7XG4gIGlmICghZW50cnlbYm94XSkge1xuICAgIGlmIChib3ggPT09IFwiY29udGVudEJveFNpemVcIikge1xuICAgICAgcmV0dXJuIGVudHJ5LmNvbnRlbnRSZWN0W3NpemVUeXBlID09PSBcImlubGluZVNpemVcIiA/IFwid2lkdGhcIiA6IFwiaGVpZ2h0XCJdO1xuICAgIH1cbiAgICByZXR1cm4gdm9pZCAwO1xuICB9XG4gIHJldHVybiBBcnJheS5pc0FycmF5KGVudHJ5W2JveF0pID8gZW50cnlbYm94XVswXVtzaXplVHlwZV0gOiAoXG4gICAgLy8gQHRzLWlnbm9yZSBTdXBwb3J0IEZpcmVmb3gncyBub24tc3RhbmRhcmQgYmVoYXZpb3JcbiAgICBlbnRyeVtib3hdW3NpemVUeXBlXVxuICApO1xufVxudmFyIElTX1NFUlZFUjQgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiO1xuZnVuY3Rpb24gdXNlU2NyZWVuKG9wdGlvbnMgPSB7fSkge1xuICBsZXQgeyBpbml0aWFsaXplV2l0aFZhbHVlID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgaWYgKElTX1NFUlZFUjQpIHtcbiAgICBpbml0aWFsaXplV2l0aFZhbHVlID0gZmFsc2U7XG4gIH1cbiAgY29uc3QgcmVhZFNjcmVlbiA9ICgpID0+IHtcbiAgICBpZiAoSVNfU0VSVkVSNCkge1xuICAgICAgcmV0dXJuIHZvaWQgMDtcbiAgICB9XG4gICAgcmV0dXJuIHdpbmRvdy5zY3JlZW47XG4gIH07XG4gIGNvbnN0IFtzY3JlZW4sIHNldFNjcmVlbl0gPSB1c2VTdGF0ZSgoKSA9PiB7XG4gICAgaWYgKGluaXRpYWxpemVXaXRoVmFsdWUpIHtcbiAgICAgIHJldHVybiByZWFkU2NyZWVuKCk7XG4gICAgfVxuICAgIHJldHVybiB2b2lkIDA7XG4gIH0pO1xuICBjb25zdCBkZWJvdW5jZWRTZXRTY3JlZW4gPSB1c2VEZWJvdW5jZUNhbGxiYWNrKFxuICAgIHNldFNjcmVlbixcbiAgICBvcHRpb25zLmRlYm91bmNlRGVsYXlcbiAgKTtcbiAgZnVuY3Rpb24gaGFuZGxlU2l6ZSgpIHtcbiAgICBjb25zdCBuZXdTY3JlZW4gPSByZWFkU2NyZWVuKCk7XG4gICAgY29uc3Qgc2V0U2l6ZSA9IG9wdGlvbnMuZGVib3VuY2VEZWxheSA/IGRlYm91bmNlZFNldFNjcmVlbiA6IHNldFNjcmVlbjtcbiAgICBpZiAobmV3U2NyZWVuKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIHdpZHRoLFxuICAgICAgICBoZWlnaHQsXG4gICAgICAgIGF2YWlsSGVpZ2h0LFxuICAgICAgICBhdmFpbFdpZHRoLFxuICAgICAgICBjb2xvckRlcHRoLFxuICAgICAgICBvcmllbnRhdGlvbixcbiAgICAgICAgcGl4ZWxEZXB0aFxuICAgICAgfSA9IG5ld1NjcmVlbjtcbiAgICAgIHNldFNpemUoe1xuICAgICAgICB3aWR0aCxcbiAgICAgICAgaGVpZ2h0LFxuICAgICAgICBhdmFpbEhlaWdodCxcbiAgICAgICAgYXZhaWxXaWR0aCxcbiAgICAgICAgY29sb3JEZXB0aCxcbiAgICAgICAgb3JpZW50YXRpb24sXG4gICAgICAgIHBpeGVsRGVwdGhcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICB1c2VFdmVudExpc3RlbmVyKFwicmVzaXplXCIsIGhhbmRsZVNpemUpO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICBoYW5kbGVTaXplKCk7XG4gIH0sIFtdKTtcbiAgcmV0dXJuIHNjcmVlbjtcbn1cbnZhciBjYWNoZWRTY3JpcHRTdGF0dXNlcyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKCk7XG5mdW5jdGlvbiBnZXRTY3JpcHROb2RlKHNyYykge1xuICBjb25zdCBub2RlID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihcbiAgICBgc2NyaXB0W3NyYz1cIiR7c3JjfVwiXWBcbiAgKTtcbiAgY29uc3Qgc3RhdHVzID0gbm9kZSA9PSBudWxsID8gdm9pZCAwIDogbm9kZS5nZXRBdHRyaWJ1dGUoXCJkYXRhLXN0YXR1c1wiKTtcbiAgcmV0dXJuIHtcbiAgICBub2RlLFxuICAgIHN0YXR1c1xuICB9O1xufVxuZnVuY3Rpb24gdXNlU2NyaXB0KHNyYywgb3B0aW9ucykge1xuICBjb25zdCBbc3RhdHVzLCBzZXRTdGF0dXNdID0gdXNlU3RhdGUoKCkgPT4ge1xuICAgIGlmICghc3JjIHx8IChvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnNob3VsZFByZXZlbnRMb2FkKSkge1xuICAgICAgcmV0dXJuIFwiaWRsZVwiO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgcmV0dXJuIFwibG9hZGluZ1wiO1xuICAgIH1cbiAgICByZXR1cm4gY2FjaGVkU2NyaXB0U3RhdHVzZXMuZ2V0KHNyYykgPz8gXCJsb2FkaW5nXCI7XG4gIH0pO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghc3JjIHx8IChvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnNob3VsZFByZXZlbnRMb2FkKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBjYWNoZWRTY3JpcHRTdGF0dXMgPSBjYWNoZWRTY3JpcHRTdGF0dXNlcy5nZXQoc3JjKTtcbiAgICBpZiAoY2FjaGVkU2NyaXB0U3RhdHVzID09PSBcInJlYWR5XCIgfHwgY2FjaGVkU2NyaXB0U3RhdHVzID09PSBcImVycm9yXCIpIHtcbiAgICAgIHNldFN0YXR1cyhjYWNoZWRTY3JpcHRTdGF0dXMpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBzY3JpcHQgPSBnZXRTY3JpcHROb2RlKHNyYyk7XG4gICAgbGV0IHNjcmlwdE5vZGUgPSBzY3JpcHQubm9kZTtcbiAgICBpZiAoIXNjcmlwdE5vZGUpIHtcbiAgICAgIHNjcmlwdE5vZGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIpO1xuICAgICAgc2NyaXB0Tm9kZS5zcmMgPSBzcmM7XG4gICAgICBzY3JpcHROb2RlLmFzeW5jID0gdHJ1ZTtcbiAgICAgIGlmIChvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLmlkKSB7XG4gICAgICAgIHNjcmlwdE5vZGUuaWQgPSBvcHRpb25zLmlkO1xuICAgICAgfVxuICAgICAgc2NyaXB0Tm9kZS5zZXRBdHRyaWJ1dGUoXCJkYXRhLXN0YXR1c1wiLCBcImxvYWRpbmdcIik7XG4gICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKHNjcmlwdE5vZGUpO1xuICAgICAgY29uc3Qgc2V0QXR0cmlidXRlRnJvbUV2ZW50ID0gKGV2ZW50KSA9PiB7XG4gICAgICAgIGNvbnN0IHNjcmlwdFN0YXR1cyA9IGV2ZW50LnR5cGUgPT09IFwibG9hZFwiID8gXCJyZWFkeVwiIDogXCJlcnJvclwiO1xuICAgICAgICBzY3JpcHROb2RlID09IG51bGwgPyB2b2lkIDAgOiBzY3JpcHROb2RlLnNldEF0dHJpYnV0ZShcImRhdGEtc3RhdHVzXCIsIHNjcmlwdFN0YXR1cyk7XG4gICAgICB9O1xuICAgICAgc2NyaXB0Tm9kZS5hZGRFdmVudExpc3RlbmVyKFwibG9hZFwiLCBzZXRBdHRyaWJ1dGVGcm9tRXZlbnQpO1xuICAgICAgc2NyaXB0Tm9kZS5hZGRFdmVudExpc3RlbmVyKFwiZXJyb3JcIiwgc2V0QXR0cmlidXRlRnJvbUV2ZW50KTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0U3RhdHVzKHNjcmlwdC5zdGF0dXMgPz8gY2FjaGVkU2NyaXB0U3RhdHVzID8/IFwibG9hZGluZ1wiKTtcbiAgICB9XG4gICAgY29uc3Qgc2V0U3RhdGVGcm9tRXZlbnQgPSAoZXZlbnQpID0+IHtcbiAgICAgIGNvbnN0IG5ld1N0YXR1cyA9IGV2ZW50LnR5cGUgPT09IFwibG9hZFwiID8gXCJyZWFkeVwiIDogXCJlcnJvclwiO1xuICAgICAgc2V0U3RhdHVzKG5ld1N0YXR1cyk7XG4gICAgICBjYWNoZWRTY3JpcHRTdGF0dXNlcy5zZXQoc3JjLCBuZXdTdGF0dXMpO1xuICAgIH07XG4gICAgc2NyaXB0Tm9kZS5hZGRFdmVudExpc3RlbmVyKFwibG9hZFwiLCBzZXRTdGF0ZUZyb21FdmVudCk7XG4gICAgc2NyaXB0Tm9kZS5hZGRFdmVudExpc3RlbmVyKFwiZXJyb3JcIiwgc2V0U3RhdGVGcm9tRXZlbnQpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAoc2NyaXB0Tm9kZSkge1xuICAgICAgICBzY3JpcHROb2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJsb2FkXCIsIHNldFN0YXRlRnJvbUV2ZW50KTtcbiAgICAgICAgc2NyaXB0Tm9kZS5yZW1vdmVFdmVudExpc3RlbmVyKFwiZXJyb3JcIiwgc2V0U3RhdGVGcm9tRXZlbnQpO1xuICAgICAgfVxuICAgICAgaWYgKHNjcmlwdE5vZGUgJiYgKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMucmVtb3ZlT25Vbm1vdW50KSkge1xuICAgICAgICBzY3JpcHROb2RlLnJlbW92ZSgpO1xuICAgICAgICBjYWNoZWRTY3JpcHRTdGF0dXNlcy5kZWxldGUoc3JjKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbc3JjLCBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnNob3VsZFByZXZlbnRMb2FkLCBvcHRpb25zID09IG51bGwgPyB2b2lkIDAgOiBvcHRpb25zLnJlbW92ZU9uVW5tb3VudCwgb3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5pZF0pO1xuICByZXR1cm4gc3RhdHVzO1xufVxudmFyIElTX1NFUlZFUjUgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiO1xuZnVuY3Rpb24gdXNlU2Nyb2xsTG9jayhvcHRpb25zID0ge30pIHtcbiAgY29uc3QgeyBhdXRvTG9jayA9IHRydWUsIGxvY2tUYXJnZXQsIHdpZHRoUmVmbG93ID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgY29uc3QgW2lzTG9ja2VkLCBzZXRJc0xvY2tlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IHRhcmdldCA9IHVzZVJlZihudWxsKTtcbiAgY29uc3Qgb3JpZ2luYWxTdHlsZSA9IHVzZVJlZihudWxsKTtcbiAgY29uc3QgbG9jayA9ICgpID0+IHtcbiAgICBpZiAodGFyZ2V0LmN1cnJlbnQpIHtcbiAgICAgIGNvbnN0IHsgb3ZlcmZsb3csIHBhZGRpbmdSaWdodCB9ID0gdGFyZ2V0LmN1cnJlbnQuc3R5bGU7XG4gICAgICBvcmlnaW5hbFN0eWxlLmN1cnJlbnQgPSB7IG92ZXJmbG93LCBwYWRkaW5nUmlnaHQgfTtcbiAgICAgIGlmICh3aWR0aFJlZmxvdykge1xuICAgICAgICBjb25zdCBvZmZzZXRXaWR0aCA9IHRhcmdldC5jdXJyZW50ID09PSBkb2N1bWVudC5ib2R5ID8gd2luZG93LmlubmVyV2lkdGggOiB0YXJnZXQuY3VycmVudC5vZmZzZXRXaWR0aDtcbiAgICAgICAgY29uc3QgY3VycmVudFBhZGRpbmdSaWdodCA9IHBhcnNlSW50KHdpbmRvdy5nZXRDb21wdXRlZFN0eWxlKHRhcmdldC5jdXJyZW50KS5wYWRkaW5nUmlnaHQsIDEwKSB8fCAwO1xuICAgICAgICBjb25zdCBzY3JvbGxiYXJXaWR0aCA9IG9mZnNldFdpZHRoIC0gdGFyZ2V0LmN1cnJlbnQuc2Nyb2xsV2lkdGg7XG4gICAgICAgIHRhcmdldC5jdXJyZW50LnN0eWxlLnBhZGRpbmdSaWdodCA9IGAke3Njcm9sbGJhcldpZHRoICsgY3VycmVudFBhZGRpbmdSaWdodH1weGA7XG4gICAgICB9XG4gICAgICB0YXJnZXQuY3VycmVudC5zdHlsZS5vdmVyZmxvdyA9IFwiaGlkZGVuXCI7XG4gICAgICBzZXRJc0xvY2tlZCh0cnVlKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IHVubG9jayA9ICgpID0+IHtcbiAgICBpZiAodGFyZ2V0LmN1cnJlbnQgJiYgb3JpZ2luYWxTdHlsZS5jdXJyZW50KSB7XG4gICAgICB0YXJnZXQuY3VycmVudC5zdHlsZS5vdmVyZmxvdyA9IG9yaWdpbmFsU3R5bGUuY3VycmVudC5vdmVyZmxvdztcbiAgICAgIGlmICh3aWR0aFJlZmxvdykge1xuICAgICAgICB0YXJnZXQuY3VycmVudC5zdHlsZS5wYWRkaW5nUmlnaHQgPSBvcmlnaW5hbFN0eWxlLmN1cnJlbnQucGFkZGluZ1JpZ2h0O1xuICAgICAgfVxuICAgIH1cbiAgICBzZXRJc0xvY2tlZChmYWxzZSk7XG4gIH07XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChJU19TRVJWRVI1KVxuICAgICAgcmV0dXJuO1xuICAgIGlmIChsb2NrVGFyZ2V0KSB7XG4gICAgICB0YXJnZXQuY3VycmVudCA9IHR5cGVvZiBsb2NrVGFyZ2V0ID09PSBcInN0cmluZ1wiID8gZG9jdW1lbnQucXVlcnlTZWxlY3Rvcihsb2NrVGFyZ2V0KSA6IGxvY2tUYXJnZXQ7XG4gICAgfVxuICAgIGlmICghdGFyZ2V0LmN1cnJlbnQpIHtcbiAgICAgIHRhcmdldC5jdXJyZW50ID0gZG9jdW1lbnQuYm9keTtcbiAgICB9XG4gICAgaWYgKGF1dG9Mb2NrKSB7XG4gICAgICBsb2NrKCk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB1bmxvY2soKTtcbiAgICB9O1xuICB9LCBbYXV0b0xvY2ssIGxvY2tUYXJnZXQsIHdpZHRoUmVmbG93XSk7XG4gIHJldHVybiB7IGlzTG9ja2VkLCBsb2NrLCB1bmxvY2sgfTtcbn1cbnZhciBJU19TRVJWRVI2ID0gdHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIjtcbmZ1bmN0aW9uIHVzZVNlc3Npb25TdG9yYWdlKGtleSwgaW5pdGlhbFZhbHVlLCBvcHRpb25zID0ge30pIHtcbiAgY29uc3QgeyBpbml0aWFsaXplV2l0aFZhbHVlID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgY29uc3Qgc2VyaWFsaXplciA9IHVzZUNhbGxiYWNrKFxuICAgICh2YWx1ZSkgPT4ge1xuICAgICAgaWYgKG9wdGlvbnMuc2VyaWFsaXplcikge1xuICAgICAgICByZXR1cm4gb3B0aW9ucy5zZXJpYWxpemVyKHZhbHVlKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBKU09OLnN0cmluZ2lmeSh2YWx1ZSk7XG4gICAgfSxcbiAgICBbb3B0aW9uc11cbiAgKTtcbiAgY29uc3QgZGVzZXJpYWxpemVyID0gdXNlQ2FsbGJhY2soXG4gICAgKHZhbHVlKSA9PiB7XG4gICAgICBpZiAob3B0aW9ucy5kZXNlcmlhbGl6ZXIpIHtcbiAgICAgICAgcmV0dXJuIG9wdGlvbnMuZGVzZXJpYWxpemVyKHZhbHVlKTtcbiAgICAgIH1cbiAgICAgIGlmICh2YWx1ZSA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICByZXR1cm4gdm9pZCAwO1xuICAgICAgfVxuICAgICAgY29uc3QgZGVmYXVsdFZhbHVlID0gaW5pdGlhbFZhbHVlIGluc3RhbmNlb2YgRnVuY3Rpb24gPyBpbml0aWFsVmFsdWUoKSA6IGluaXRpYWxWYWx1ZTtcbiAgICAgIGxldCBwYXJzZWQ7XG4gICAgICB0cnkge1xuICAgICAgICBwYXJzZWQgPSBKU09OLnBhcnNlKHZhbHVlKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIEpTT046XCIsIGVycm9yKTtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHRWYWx1ZTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBwYXJzZWQ7XG4gICAgfSxcbiAgICBbb3B0aW9ucywgaW5pdGlhbFZhbHVlXVxuICApO1xuICBjb25zdCByZWFkVmFsdWUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgY29uc3QgaW5pdGlhbFZhbHVlVG9Vc2UgPSBpbml0aWFsVmFsdWUgaW5zdGFuY2VvZiBGdW5jdGlvbiA/IGluaXRpYWxWYWx1ZSgpIDogaW5pdGlhbFZhbHVlO1xuICAgIGlmIChJU19TRVJWRVI2KSB7XG4gICAgICByZXR1cm4gaW5pdGlhbFZhbHVlVG9Vc2U7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByYXcgPSB3aW5kb3cuc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbShrZXkpO1xuICAgICAgcmV0dXJuIHJhdyA/IGRlc2VyaWFsaXplcihyYXcpIDogaW5pdGlhbFZhbHVlVG9Vc2U7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybihgRXJyb3IgcmVhZGluZyBzZXNzaW9uU3RvcmFnZSBrZXkgXFx1MjAxQyR7a2V5fVxcdTIwMUQ6YCwgZXJyb3IpO1xuICAgICAgcmV0dXJuIGluaXRpYWxWYWx1ZVRvVXNlO1xuICAgIH1cbiAgfSwgW2luaXRpYWxWYWx1ZSwga2V5LCBkZXNlcmlhbGl6ZXJdKTtcbiAgY29uc3QgW3N0b3JlZFZhbHVlLCBzZXRTdG9yZWRWYWx1ZV0gPSB1c2VTdGF0ZSgoKSA9PiB7XG4gICAgaWYgKGluaXRpYWxpemVXaXRoVmFsdWUpIHtcbiAgICAgIHJldHVybiByZWFkVmFsdWUoKTtcbiAgICB9XG4gICAgcmV0dXJuIGluaXRpYWxWYWx1ZSBpbnN0YW5jZW9mIEZ1bmN0aW9uID8gaW5pdGlhbFZhbHVlKCkgOiBpbml0aWFsVmFsdWU7XG4gIH0pO1xuICBjb25zdCBzZXRWYWx1ZSA9IHVzZUV2ZW50Q2FsbGJhY2soKHZhbHVlKSA9PiB7XG4gICAgaWYgKElTX1NFUlZFUjYpIHtcbiAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgYFRyaWVkIHNldHRpbmcgc2Vzc2lvblN0b3JhZ2Uga2V5IFxcdTIwMUMke2tleX1cXHUyMDFEIGV2ZW4gdGhvdWdoIGVudmlyb25tZW50IGlzIG5vdCBhIGNsaWVudGBcbiAgICAgICk7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICBjb25zdCBuZXdWYWx1ZSA9IHZhbHVlIGluc3RhbmNlb2YgRnVuY3Rpb24gPyB2YWx1ZShyZWFkVmFsdWUoKSkgOiB2YWx1ZTtcbiAgICAgIHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5zZXRJdGVtKGtleSwgc2VyaWFsaXplcihuZXdWYWx1ZSkpO1xuICAgICAgc2V0U3RvcmVkVmFsdWUobmV3VmFsdWUpO1xuICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IFN0b3JhZ2VFdmVudChcInNlc3Npb24tc3RvcmFnZVwiLCB7IGtleSB9KSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUud2FybihgRXJyb3Igc2V0dGluZyBzZXNzaW9uU3RvcmFnZSBrZXkgXFx1MjAxQyR7a2V5fVxcdTIwMUQ6YCwgZXJyb3IpO1xuICAgIH1cbiAgfSk7XG4gIGNvbnN0IHJlbW92ZVZhbHVlID0gdXNlRXZlbnRDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKElTX1NFUlZFUjYpIHtcbiAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgYFRyaWVkIHJlbW92aW5nIHNlc3Npb25TdG9yYWdlIGtleSBcXHUyMDFDJHtrZXl9XFx1MjAxRCBldmVuIHRob3VnaCBlbnZpcm9ubWVudCBpcyBub3QgYSBjbGllbnRgXG4gICAgICApO1xuICAgIH1cbiAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBpbml0aWFsVmFsdWUgaW5zdGFuY2VvZiBGdW5jdGlvbiA/IGluaXRpYWxWYWx1ZSgpIDogaW5pdGlhbFZhbHVlO1xuICAgIHdpbmRvdy5zZXNzaW9uU3RvcmFnZS5yZW1vdmVJdGVtKGtleSk7XG4gICAgc2V0U3RvcmVkVmFsdWUoZGVmYXVsdFZhbHVlKTtcbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgU3RvcmFnZUV2ZW50KFwic2Vzc2lvbi1zdG9yYWdlXCIsIHsga2V5IH0pKTtcbiAgfSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U3RvcmVkVmFsdWUocmVhZFZhbHVlKCkpO1xuICB9LCBba2V5XSk7XG4gIGNvbnN0IGhhbmRsZVN0b3JhZ2VDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgJiYgZXZlbnQua2V5ICE9PSBrZXkpIHtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgICAgc2V0U3RvcmVkVmFsdWUocmVhZFZhbHVlKCkpO1xuICAgIH0sXG4gICAgW2tleSwgcmVhZFZhbHVlXVxuICApO1xuICB1c2VFdmVudExpc3RlbmVyKFwic3RvcmFnZVwiLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcbiAgdXNlRXZlbnRMaXN0ZW5lcihcInNlc3Npb24tc3RvcmFnZVwiLCBoYW5kbGVTdG9yYWdlQ2hhbmdlKTtcbiAgcmV0dXJuIFtzdG9yZWRWYWx1ZSwgc2V0VmFsdWUsIHJlbW92ZVZhbHVlXTtcbn1cbmZ1bmN0aW9uIHVzZVN0ZXAobWF4U3RlcCkge1xuICBjb25zdCBbY3VycmVudFN0ZXAsIHNldEN1cnJlbnRTdGVwXSA9IHVzZVN0YXRlKDEpO1xuICBjb25zdCBjYW5Hb1RvTmV4dFN0ZXAgPSBjdXJyZW50U3RlcCArIDEgPD0gbWF4U3RlcDtcbiAgY29uc3QgY2FuR29Ub1ByZXZTdGVwID0gY3VycmVudFN0ZXAgLSAxID4gMDtcbiAgY29uc3Qgc2V0U3RlcCA9IHVzZUNhbGxiYWNrKFxuICAgIChzdGVwKSA9PiB7XG4gICAgICBjb25zdCBuZXdTdGVwID0gc3RlcCBpbnN0YW5jZW9mIEZ1bmN0aW9uID8gc3RlcChjdXJyZW50U3RlcCkgOiBzdGVwO1xuICAgICAgaWYgKG5ld1N0ZXAgPj0gMSAmJiBuZXdTdGVwIDw9IG1heFN0ZXApIHtcbiAgICAgICAgc2V0Q3VycmVudFN0ZXAobmV3U3RlcCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIHRocm93IG5ldyBFcnJvcihcIlN0ZXAgbm90IHZhbGlkXCIpO1xuICAgIH0sXG4gICAgW21heFN0ZXAsIGN1cnJlbnRTdGVwXVxuICApO1xuICBjb25zdCBnb1RvTmV4dFN0ZXAgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKGNhbkdvVG9OZXh0U3RlcCkge1xuICAgICAgc2V0Q3VycmVudFN0ZXAoKHN0ZXApID0+IHN0ZXAgKyAxKTtcbiAgICB9XG4gIH0sIFtjYW5Hb1RvTmV4dFN0ZXBdKTtcbiAgY29uc3QgZ29Ub1ByZXZTdGVwID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmIChjYW5Hb1RvUHJldlN0ZXApIHtcbiAgICAgIHNldEN1cnJlbnRTdGVwKChzdGVwKSA9PiBzdGVwIC0gMSk7XG4gICAgfVxuICB9LCBbY2FuR29Ub1ByZXZTdGVwXSk7XG4gIGNvbnN0IHJlc2V0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldEN1cnJlbnRTdGVwKDEpO1xuICB9LCBbXSk7XG4gIHJldHVybiBbXG4gICAgY3VycmVudFN0ZXAsXG4gICAge1xuICAgICAgZ29Ub05leHRTdGVwLFxuICAgICAgZ29Ub1ByZXZTdGVwLFxuICAgICAgY2FuR29Ub05leHRTdGVwLFxuICAgICAgY2FuR29Ub1ByZXZTdGVwLFxuICAgICAgc2V0U3RlcCxcbiAgICAgIHJlc2V0XG4gICAgfVxuICBdO1xufVxuXG4vLyBzcmMvdXNlVGVybmFyeURhcmtNb2RlL3VzZVRlcm5hcnlEYXJrTW9kZS50c1xudmFyIENPTE9SX1NDSEVNRV9RVUVSWTIgPSBcIihwcmVmZXJzLWNvbG9yLXNjaGVtZTogZGFyaylcIjtcbnZhciBMT0NBTF9TVE9SQUdFX0tFWTIgPSBcInVzZWhvb2tzLXRzLXRlcm5hcnktZGFyay1tb2RlXCI7XG5mdW5jdGlvbiB1c2VUZXJuYXJ5RGFya01vZGUoe1xuICBkZWZhdWx0VmFsdWUgPSBcInN5c3RlbVwiLFxuICBsb2NhbFN0b3JhZ2VLZXkgPSBMT0NBTF9TVE9SQUdFX0tFWTIsXG4gIGluaXRpYWxpemVXaXRoVmFsdWUgPSB0cnVlXG59ID0ge30pIHtcbiAgY29uc3QgaXNEYXJrT1MgPSB1c2VNZWRpYVF1ZXJ5KENPTE9SX1NDSEVNRV9RVUVSWTIsIHsgaW5pdGlhbGl6ZVdpdGhWYWx1ZSB9KTtcbiAgY29uc3QgW21vZGUsIHNldE1vZGVdID0gdXNlTG9jYWxTdG9yYWdlKGxvY2FsU3RvcmFnZUtleSwgZGVmYXVsdFZhbHVlLCB7XG4gICAgaW5pdGlhbGl6ZVdpdGhWYWx1ZVxuICB9KTtcbiAgY29uc3QgaXNEYXJrTW9kZSA9IG1vZGUgPT09IFwiZGFya1wiIHx8IG1vZGUgPT09IFwic3lzdGVtXCIgJiYgaXNEYXJrT1M7XG4gIGNvbnN0IHRvZ2dsZVRlcm5hcnlEYXJrTW9kZSA9ICgpID0+IHtcbiAgICBjb25zdCBtb2RlcyA9IFtcImxpZ2h0XCIsIFwic3lzdGVtXCIsIFwiZGFya1wiXTtcbiAgICBzZXRNb2RlKChwcmV2TW9kZSkgPT4ge1xuICAgICAgY29uc3QgbmV4dEluZGV4ID0gKG1vZGVzLmluZGV4T2YocHJldk1vZGUpICsgMSkgJSBtb2Rlcy5sZW5ndGg7XG4gICAgICByZXR1cm4gbW9kZXNbbmV4dEluZGV4XTtcbiAgICB9KTtcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBpc0RhcmtNb2RlLFxuICAgIHRlcm5hcnlEYXJrTW9kZTogbW9kZSxcbiAgICBzZXRUZXJuYXJ5RGFya01vZGU6IHNldE1vZGUsXG4gICAgdG9nZ2xlVGVybmFyeURhcmtNb2RlXG4gIH07XG59XG5mdW5jdGlvbiB1c2VUaW1lb3V0KGNhbGxiYWNrLCBkZWxheSkge1xuICBjb25zdCBzYXZlZENhbGxiYWNrID0gdXNlUmVmKGNhbGxiYWNrKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgc2F2ZWRDYWxsYmFjay5jdXJyZW50ID0gY2FsbGJhY2s7XG4gIH0sIFtjYWxsYmFja10pO1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZGVsYXkgJiYgZGVsYXkgIT09IDApIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgY29uc3QgaWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIHNhdmVkQ2FsbGJhY2suY3VycmVudCgpO1xuICAgIH0sIGRlbGF5KTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY2xlYXJUaW1lb3V0KGlkKTtcbiAgICB9O1xuICB9LCBbZGVsYXldKTtcbn1cbmZ1bmN0aW9uIHVzZVRvZ2dsZShkZWZhdWx0VmFsdWUpIHtcbiAgY29uc3QgW3ZhbHVlLCBzZXRWYWx1ZV0gPSB1c2VTdGF0ZSghIWRlZmF1bHRWYWx1ZSk7XG4gIGNvbnN0IHRvZ2dsZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRWYWx1ZSgoeCkgPT4gIXgpO1xuICB9LCBbXSk7XG4gIHJldHVybiBbdmFsdWUsIHRvZ2dsZSwgc2V0VmFsdWVdO1xufVxudmFyIElTX1NFUlZFUjcgPSB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiO1xuZnVuY3Rpb24gdXNlV2luZG93U2l6ZShvcHRpb25zID0ge30pIHtcbiAgbGV0IHsgaW5pdGlhbGl6ZVdpdGhWYWx1ZSA9IHRydWUgfSA9IG9wdGlvbnM7XG4gIGlmIChJU19TRVJWRVI3KSB7XG4gICAgaW5pdGlhbGl6ZVdpdGhWYWx1ZSA9IGZhbHNlO1xuICB9XG4gIGNvbnN0IFt3aW5kb3dTaXplLCBzZXRXaW5kb3dTaXplXSA9IHVzZVN0YXRlKCgpID0+IHtcbiAgICBpZiAoaW5pdGlhbGl6ZVdpdGhWYWx1ZSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgd2lkdGg6IHdpbmRvdy5pbm5lcldpZHRoLFxuICAgICAgICBoZWlnaHQ6IHdpbmRvdy5pbm5lckhlaWdodFxuICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIHdpZHRoOiB2b2lkIDAsXG4gICAgICBoZWlnaHQ6IHZvaWQgMFxuICAgIH07XG4gIH0pO1xuICBjb25zdCBkZWJvdW5jZWRTZXRXaW5kb3dTaXplID0gdXNlRGVib3VuY2VDYWxsYmFjayhcbiAgICBzZXRXaW5kb3dTaXplLFxuICAgIG9wdGlvbnMuZGVib3VuY2VEZWxheVxuICApO1xuICBmdW5jdGlvbiBoYW5kbGVTaXplKCkge1xuICAgIGNvbnN0IHNldFNpemUgPSBvcHRpb25zLmRlYm91bmNlRGVsYXkgPyBkZWJvdW5jZWRTZXRXaW5kb3dTaXplIDogc2V0V2luZG93U2l6ZTtcbiAgICBzZXRTaXplKHtcbiAgICAgIHdpZHRoOiB3aW5kb3cuaW5uZXJXaWR0aCxcbiAgICAgIGhlaWdodDogd2luZG93LmlubmVySGVpZ2h0XG4gICAgfSk7XG4gIH1cbiAgdXNlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVTaXplKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaGFuZGxlU2l6ZSgpO1xuICB9LCBbXSk7XG4gIHJldHVybiB3aW5kb3dTaXplO1xufVxuXG5leHBvcnQgeyB1c2VCb29sZWFuLCB1c2VDbGlja0FueVdoZXJlLCB1c2VDb3B5VG9DbGlwYm9hcmQsIHVzZUNvdW50ZG93biwgdXNlQ291bnRlciwgdXNlRGFya01vZGUsIHVzZURlYm91bmNlQ2FsbGJhY2ssIHVzZURlYm91bmNlVmFsdWUsIHVzZURvY3VtZW50VGl0bGUsIHVzZUV2ZW50Q2FsbGJhY2ssIHVzZUV2ZW50TGlzdGVuZXIsIHVzZUhvdmVyLCB1c2VJbnRlcnNlY3Rpb25PYnNlcnZlciwgdXNlSW50ZXJ2YWwsIHVzZUlzQ2xpZW50LCB1c2VJc01vdW50ZWQsIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QsIHVzZUxvY2FsU3RvcmFnZSwgdXNlTWFwLCB1c2VNZWRpYVF1ZXJ5LCB1c2VPbkNsaWNrT3V0c2lkZSwgdXNlUmVhZExvY2FsU3RvcmFnZSwgdXNlUmVzaXplT2JzZXJ2ZXIsIHVzZVNjcmVlbiwgdXNlU2NyaXB0LCB1c2VTY3JvbGxMb2NrLCB1c2VTZXNzaW9uU3RvcmFnZSwgdXNlU3RlcCwgdXNlVGVybmFyeURhcmtNb2RlLCB1c2VUaW1lb3V0LCB1c2VUb2dnbGUsIHVzZVVubW91bnQsIHVzZVdpbmRvd1NpemUgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js\n");

/***/ })

};
;