interface FormatSizeOptions {
  /**
   * Inserts a space between the number and the unit.
   * @default false
   */
  space?: boolean;
}

/**
 * Converts bytes to a human readable size.
 *
 * @example `1337` →  `1,34 Ko`
 */
export default function formatSize(
  sizeInBytes: number | null | undefined,
  options?: FormatSizeOptions
): string | false {
  const { space = false } = options ?? {};

  if (typeof sizeInBytes !== "number") {
    return false;
  }

  const units = ["o", "Ko", "Mo", "Go", "To"];
  let size = sizeInBytes;
  let unit = 0;

  while (size >= 1024 && unit < units.length - 1) {
    size /= 1024;
    unit++;
  }

  const formattedSize = size.toLocaleString("fr", { trailingZeroDisplay: "stripIfInteger", maximumFractionDigits: 1 });

  return `${formattedSize}${space ? " " : ""}${units[unit]}`;
}
