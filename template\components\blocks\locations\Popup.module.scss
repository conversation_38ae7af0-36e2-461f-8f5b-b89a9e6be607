@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.root {
  z-index: 1;
  display: flex;
  flex-direction: column;
  background-color: $color-white;
  border-radius: $rounded-lg $rounded-lg 0 0;

  &.visible {
    transform: translateY(-8px);
  }

  @include breakpoint(medium up) {
    position: absolute;
    top: 24px;
    bottom: 0;
    left: 24px;
    width: 100%;
    max-width: 375px;
    max-height: calc(100% - 24px);
    overflow-y: hidden;
    box-shadow: $shadow-md;
    transform: translateY(100%);
    transition: transform 0.3s ease;

    &.visible {
      transform: translateY(0);
    }
  }
}

.header {
  display: flex;
  gap: 6px;
  align-items: center;

  @include breakpoint(medium up) {
    gap: 8px;
  }
}

.title {
  flex-grow: 1;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-black;
  text-align: center;
}

.prevButton,
.closeButton {
  flex-shrink: 0;
  aspect-ratio: 1;
}

.closeButton {
  margin-left: auto;
}

.content {
  display: flex;
  flex: 1;
  max-height: 100%;
  padding: 0 24px;
  overflow-y: auto;

  @include scrollbar(5px, $color-neutral-300, $color-neutral-100);
}
