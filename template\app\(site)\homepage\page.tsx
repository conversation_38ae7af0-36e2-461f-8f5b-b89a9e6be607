import BlockRenderer from "@/components/blocks/BlockRenderer";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import clsx from "clsx";
import type { Metadata } from "next";
import { notFound } from "next/navigation";
import styles from "./page.module.scss";

const HOMEPAGE_QUERY = graphql(`
  query GetHomepage {
    page(url: "/") {
      structuredContent
      metadata {
        title
        description
      }
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: HOMEPAGE_QUERY,
  });

  return {
    title: data.page?.metadata?.title,
    description: data.page?.metadata?.description,
  };
}

export default async function Home() {
  // Fetch data from GraphQL
  const { data } = await query({
    query: HOMEPAGE_QUERY,
  });

  // No homepage has been found
  if (!data.page) {
    notFound();
  }

  return (
    <div className="layout-1column-fullwidth">
      <div className={clsx("column main", styles.main)}>
        <BlockRenderer structuredContent={data.page.structuredContent} />
      </div>
    </div>
  );
}
