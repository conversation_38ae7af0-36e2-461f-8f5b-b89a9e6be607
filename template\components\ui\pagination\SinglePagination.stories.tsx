import type { Meta, StoryObj } from "@storybook/nextjs";
import SinglePagination from "./SinglePagination";

const meta: Meta<typeof SinglePagination> = {
  title: "Components/SinglePagination",
  component: SinglePagination,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof SinglePagination>;

export const Default: Story = {
  args: {
    pager: {
      list: {
        class: null,
        icon: null,
        rel: null,
        target: null,
        text: "Retour vers la vue liste des actualités",
        url: "#",
      },
      prev: {
        class: null,
        icon: null,
        rel: null,
        target: null,
        text: "Lorem ipsum dolor sit amet",
        url: "#",
      },
      next: {
        class: null,
        icon: null,
        rel: null,
        target: null,
        text: "Lorem ipsum dolor sit amet",
        url: "#",
      },
    },
  },
};
