import Button from "@/components/ui/button/Button";
import clsx from "clsx";
import { useCallback, useMemo } from "react";
import { useStateWithDeps } from "use-state-with-deps";
import CardDetail from "./CardDetail";
import CardList from "./CardList";
import { DirectoryMarker } from "./Locations";
import styles from "./Popup.module.scss";

interface PopupProps {
  selectedMarkers: DirectoryMarker[];
  onClose: () => void;
}

export default function Popup({ onClose, selectedMarkers }: PopupProps) {
  const [selectedDetail, setSelectedDetail] = useStateWithDeps(
    selectedMarkers.length === 1 ? selectedMarkers[0] : null,
    [selectedMarkers]
  );

  const isVisible = useMemo(() => selectedMarkers.length > 0, [selectedMarkers]);

  const handleSelectCard = useCallback(
    (index: number) => setSelectedDetail(selectedMarkers[index]),
    [selectedMarkers, setSelectedDetail]
  );

  return (
    <div
      className={clsx(styles.root, {
        [styles.visible]: isVisible,
      })}
    >
      <div className={styles.header}>
        {selectedDetail && selectedMarkers.length > 1 && (
          <Button
            size="sm"
            color="primary"
            variant="text"
            aria-label="Retour à la liste des points"
            onClick={() => setSelectedDetail(null)}
            className={styles.prevButton}
          >
            <i className="far fa-arrow-left" aria-hidden="true" />
          </Button>
        )}

        {!selectedDetail && <h2 className={styles.title}>À cet endroit</h2>}

        <Button
          size="sm"
          color="primary"
          variant="text"
          aria-label="Fermer"
          onClick={onClose}
          className={styles.closeButton}
        >
          <i className="far fa-close" aria-hidden="true" />
        </Button>
      </div>

      <div className={styles.content}>
        {selectedDetail ? (
          <CardDetail onClose={onClose} data={selectedDetail?.data ?? undefined} />
        ) : (
          <CardList onSelectCard={handleSelectCard} selectedMarkers={selectedMarkers} />
        )}
      </div>
    </div>
  );
}
