"use client";

import { Image as ImageType, Link as LinkType, MenuItem } from "@/generated/graphql/graphql";
import { PartialDeep } from "type-fest";
import Modal from "../modal/Modal";
import useSlideInModal from "../modal/useSlideInModal";
import MapMenuNavigationContent from "./MapMenuNavigationContent";
import styles from "./MapNavigationPopup.module.scss";

interface MapMenuButtonProps {
  logoDark: ImageType | null;
  quickAccess2: PartialDeep<LinkType, { recurseIntoArrays: true }>[];
  mapLinks: PartialDeep<LinkType, { recurseIntoArrays: true }>[];
  menuItems?: PartialDeep<MenuItem, { recurseIntoArrays: true }>[];
}

export default function MapMenuButton({
  logoDark,
  quickAccess2,
  mapLinks,
  menuItems,
  children,
}: React.PropsWithChildren<MapMenuButtonProps>) {
  const { Trigger } = useSlideInModal(
    <Modal
      title="Menu"
      hideTitle={true}
      className={styles.MapNavigationPopup}
      focusStrategy="content"
      closeButtonText="Fermer le menu"
    >
      <MapMenuNavigationContent
        logoDark={logoDark}
        quickAccess2={quickAccess2}
        mapLinks={mapLinks}
        menuItems={menuItems}
      />
    </Modal>
  );

  return <Trigger>{children}</Trigger>;
}
