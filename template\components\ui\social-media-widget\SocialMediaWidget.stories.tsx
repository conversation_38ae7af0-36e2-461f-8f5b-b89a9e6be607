import { SocialNetwork } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import SocialMediaWidget from "./SocialMediaWidget";

const meta: Meta<typeof SocialMediaWidget> = {
  title: "Components/Widget/SocialMedia",
  component: SocialMediaWidget,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof SocialMediaWidget>;

export const Default: Story = {
  args: {
    socialLinks: [
      {
        network: SocialNetwork.FACEBOOK,
        text: "Facebook",
        url: "#",
      },
      {
        network: SocialNetwork.TWITTER,
        text: "Twitter",
        url: "#",
      },
      {
        network: SocialNetwork.INSTAGRAM,
        text: "Instagram",
        url: "#",
      },
      {
        network: SocialNetwork.YOUTUBE,
        text: "YouTube",
        url: "#",
      },
      {
        network: SocialNetwork.LINKEDIN,
        text: "LinkedIn",
        url: "#",
      },
    ],
  },
};
