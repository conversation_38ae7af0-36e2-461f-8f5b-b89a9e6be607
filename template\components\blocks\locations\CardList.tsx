import Card from "./Card";
import styles from "./CardList.module.scss";
import { DirectoryMarker } from "./Locations";

interface CardListProps {
  selectedMarkers: DirectoryMarker[];
  onSelectCard: (id: number) => void;
}

export default function CardList({ selectedMarkers, onSelectCard }: CardListProps) {
  return (
    <ul className={styles.list}>
      {selectedMarkers.map((marker: DirectoryMarker, index: number) => {
        const { title, address } = marker?.data || {};

        if (!title || !address) return;

        return (
          <li key={title} className={styles.item}>
            <Card id={index} title={title} address={address} handleClick={() => onSelectCard(index)} />
          </li>
        );
      })}
    </ul>
  );
}
