import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Collapsible from "./Collapsible";

const meta: Meta<typeof Collapsible> = {
  title: "Components/Collapsible",
  component: Collapsible,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Collapsible>;

export const Default: Story = {
  args: {
    label: "Lorem ipsum dolor sit amet",
    children: (
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum non quam at mi dictum vulputate vel sed
        ipsum. Nullam quam ligula, interdum ut quam a, accumsan vulputate enim. Etiam posuere ex leo, nec dictum ex
        varius in. Ut porta, tellus sit amet congue aliquam, sapien ipsum pharetra nisl, eget volutpat dui ipsum nec
        sem. In hac habitasse platea dictumst. Phasellus ut velit ullamcorper, tristique velit at, lobortis est. Vivamus
        pharetra suscipit facilisis. Praesent vitae nulla non quam aliquet commodo eget quis mauris. Morbi porttitor
        rhoncus felis vel elementum. Integer sit amet nisl et arcu gravida ornare pulvinar sit amet ipsum. Curabitur at
        pharetra lectus. Nulla pretium blandit ante, vitae pulvinar tortor.
      </p>
    ),
  },
};

export const WithIcon: Story = {
  args: {
    icon: <i className="far fa-subtitles" aria-hidden="true"></i>,
    label: "Lorem ipsum dolor sit amet",
    children: (
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum non quam at mi dictum vulputate vel sed
        ipsum. Nullam quam ligula, interdum ut quam a, accumsan vulputate enim. Etiam posuere ex leo, nec dictum ex
        varius in. Ut porta, tellus sit amet congue aliquam, sapien ipsum pharetra nisl, eget volutpat dui ipsum nec
        sem. In hac habitasse platea dictumst. Phasellus ut velit ullamcorper, tristique velit at, lobortis est. Vivamus
        pharetra suscipit facilisis. Praesent vitae nulla non quam aliquet commodo eget quis mauris. Morbi porttitor
        rhoncus felis vel elementum. Integer sit amet nisl et arcu gravida ornare pulvinar sit amet ipsum. Curabitur at
        pharetra lectus. Nulla pretium blandit ante, vitae pulvinar tortor.
      </p>
    ),
  },
};

export const LongTitle: Story = {
  args: {
    label:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum non quam at mi dictum vulputate vel sed ipsum. Nullam quam ligula, interdum ut quam a, accumsan vulputate enim. Etiam posuere ex leo, nec dictum ex",
    children: (
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum non quam at mi dictum vulputate vel sed
        ipsum. Nullam quam ligula, interdum ut quam a, accumsan vulputate enim. Etiam posuere ex leo, nec dictum ex
        varius in. Ut porta, tellus sit amet congue aliquam, sapien ipsum pharetra nisl, eget volutpat dui ipsum nec
        sem. In hac habitasse platea dictumst. Phasellus ut velit ullamcorper, tristique velit at, lobortis est. Vivamus
        pharetra suscipit facilisis. Praesent vitae nulla non quam aliquet commodo eget quis mauris. Morbi porttitor
        rhoncus felis vel elementum. Integer sit amet nisl et arcu gravida ornare pulvinar sit amet ipsum. Curabitur at
        pharetra lectus. Nulla pretium blandit ante, vitae pulvinar tortor.
      </p>
    ),
  },
};

/**
 * Removes the heading around the trigger. (A11Y)
 */
export const NoHeading: Story = {
  args: {
    label: "Lorem ipsum dolor sit amet",
    noHeading: true,
    children: (
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum non quam at mi dictum vulputate vel sed
        ipsum. Nullam quam ligula, interdum ut quam a, accumsan vulputate enim. Etiam posuere ex leo, nec dictum ex
        varius in. Ut porta, tellus sit amet congue aliquam, sapien ipsum pharetra nisl, eget volutpat dui ipsum nec
        sem. In hac habitasse platea dictumst. Phasellus ut velit ullamcorper, tristique velit at, lobortis est. Vivamus
        pharetra suscipit facilisis. Praesent vitae nulla non quam aliquet commodo eget quis mauris. Morbi porttitor
        rhoncus felis vel elementum. Integer sit amet nisl et arcu gravida ornare pulvinar sit amet ipsum. Curabitur at
        pharetra lectus. Nulla pretium blandit ante, vitae pulvinar tortor.
      </p>
    ),
  },
};
