import { OneThirdColumnsDecorator, TwoThirdColumnsDecorator } from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Download from "./Download";

const meta: Meta<typeof Download> = {
  title: "Blocks/Download",
  component: Download,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Download>;

export const Default: Story = {
  args: {
    files: [
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "pdf",
        size: 1_000_000, // 1MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/pdf",
      },
    ],
  },
};

export const MultipleFiles: Story = {
  args: {
    files: [
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "pdf",
        size: 1_500_000, // 1.5MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/pdf",
      },
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "xlsx",
        size: 500_000, // 500KB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      },
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "pptx",
        size: 3_500_000, // 3.5MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      },
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "png",
        size: 2_000_000, // 2MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "image/png",
      },
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "mp4",
        size: 15_000_000, // 15MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "video/mp4",
      },
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "mp3",
        size: 5_000_000, // 5MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "audio/mpeg",
      },
    ],
  },
};

export const LongFilename: Story = {
  args: {
    files: [
      {
        label:
          "Lorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur elis jacta estLorem dolor sit amet consectur est",
        extname: "pdf",
        size: 1_000_000, // 1MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/pdf",
      },
    ],
  },
};

export const WithoutExtension: Story = {
  args: {
    files: [
      {
        label: "Lorem dolor sit amet consectur elis jacta est",
        extname: "",
        size: 1_000_000, // 1MB
        downloadUrl: "/assets/placeholder-720x480.png",
        mime: "application/octet-stream",
      },
    ],
  },
};

export const TwoColumns: Story = {
  args: {
    ...Default.args,
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    ...Default.args,
  },
  decorators: [OneThirdColumnsDecorator],
};
