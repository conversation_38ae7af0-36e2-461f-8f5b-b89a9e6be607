import Tooltip from "@/components/ui/tooltip/Tooltip";
import { SocialLink as BaseSocialLinkProps, SocialNetwork } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./SocialLink.module.scss";

type SocialLinkProps = Partial<BaseSocialLinkProps> & {
  size?: "xxs" | "xs" | "lg" | "xl";
};

const networkIcons: Partial<Record<SocialNetwork, string>> = {
  FACEBOOK: "fa-brands fa-facebook-f",
  TWITTER: "fa-brands fa-x-twitter",
  INSTAGRAM: "fa-brands fa-instagram",
  YOUTUBE: "fa-brands fa-youtube",
  LINKEDIN: "fa-brands fa-linkedin",
};

export default function SocialLink({ network, text, url, size = "xs" }: SocialLinkProps) {
  return (
    <Tooltip content={`Suivez nous sur ${text}`}>
      <a
        href={url}
        className={clsx(styles.socialLink, size && styles[`size-${size}`])}
        target="_blank"
        rel="noopener noreferrer"
      >
        <span className="sr-only">{`Suivez nous sur ${text}`}</span>
        <i className={network ? networkIcons[network] : "far fa-share-nodes"} aria-hidden="true"></i>
      </a>
    </Tooltip>
  );
}
