import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Heading from "@/components/ui/heading/HeadingImageRight";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const NEWS_QUERY = graphql(`
  query GetNews($url: URL!) {
    route(url: $url) {
      ... on News {
        structuredContent
        title
        leadText
        publicationDate
        modifiedDate
        images {
          ratio_3x2 {
            url
            width
            height
            alt
          }
        }
        url
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        __typename
        categories {
          description
          relativeUrl
          title
          parent {
            __typename
          }
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: NEWS_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "News");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: NEWS_QUERY,
    variables: { url: await getCurrentServerUrl() },
  });

  assert.ok(data.route?.__typename === "News");

  const { title, leadText, images, structuredContent, publicationDate, modifiedDate, breadcrumbs, pager, categories } =
    data.route;

  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");

  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <Heading
        surtitle={surtitle}
        title={title ?? "Sans titre"}
        leadText={leadText}
        tags={tags}
        tagsRoleDescription="Thématiques"
        imageSrc={images?.ratio_3x2?.url}
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />
      <div className="layout-1column-fullwidth">
        <div className="column main">
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
      </div>
      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
