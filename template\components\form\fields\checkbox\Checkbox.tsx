"use client";

import CheckboxInput from "@/components/ui/checkbox/Checkbox";
import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import { CheckboxField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";
import styles from "./Checkbox.module.scss";

type CheckboxProps = Omit<CheckboxField, "__typename">;

export default function Checkbox({
  choices,
  label,
  description,
  name,
  condition,
  columnSpan,
  required,
}: CheckboxProps) {
  const { visible } = useFormCondition(name, condition);
  const { register } = useFormContext();
  const error = useFormError(name);
  const legendId = useId();
  const errorId = useId();

  if (!visible) return null;

  return (
    <FormControl role="group" aria-labelledby={legendId} columnSpan={columnSpan}>
      <Label asChild id={legendId} description={description ?? undefined} required={required}>
        <p>{label}</p>
      </Label>

      <div>
        {choices?.map(
          (choice, index) =>
            choice && (
              <label className={styles.labelledCheckbox} key={choice.value ?? index}>
                <CheckboxInput
                  error={!!error}
                  defaultChecked={choice.defaultSelected ?? false}
                  aria-describedby={error ? errorId : undefined}
                  aria-invalid={error ? true : undefined}
                  {...register(name, { setValueAs: (v) => v === true })}
                />
                <span>{choice.label}</span>
              </label>
            )
        )}
      </div>
      {error && (
        <FormHelper id={errorId} variant="error">
          {error.message?.toString()}
        </FormHelper>
      )}
    </FormControl>
  );
}
