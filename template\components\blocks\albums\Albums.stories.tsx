import type { Album } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Albums, { AlbumsProps } from "./Albums";

const meta: Meta<typeof Albums> = {
  title: "blocks/Albums",
  component: Albums,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof Albums>;

// Mock data
const mockImage = {
  url: "/assets/placeholder-720x480.png",
  width: 400,
  height: 300,
  alt: "Album cover",
};

const albumsData: Album[] = [
  {
    title: "Summer Vibes",
    url: "/albums/summer-vibes",
    // @ts-expect-error Incomplete image collection
    images: {
      ratio_3x2: mockImage,
    },

    categories: [
      // @ts-expect-error Incomplete category
      {
        title: "Seasonal",
      },
    ],
    media: [
      // @ts-expect-error Incomplete media
      {
        __typename: "AlbumPhoto",
      },
    ],
  },
  {
    title: "Urban Exploration",
    url: "/albums/urban-exploration",
    // @ts-expect-error Incomplete image collection
    images: {
      ratio_3x2: mockImage,
    },
    categories: [
      // @ts-expect-error Incomplete category
      {
        title: "City Life",
      },
    ],
    media: [
      // @ts-expect-error Incomplete media
      {
        __typename: "AlbumVideo",
      },
    ],
  },
];

const baseProps: AlbumsProps = {
  id: "albums-section",
  title: "Voir & revoir",
  listUrl: "/all-albums",
  albums: albumsData,
};

export const Default: Story = {
  args: baseProps,
};
