"use client";

import DirectoryCard from "@/components/ui/directory-card/DirectoryCard";
import Hx from "@/components/ui/title/Hx";
import type { DirectoriesBlock } from "@/generated/graphql/graphql";
import { SubtitleLevelProvider } from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import styles from "./Directories.module.scss";

type DirectoriesProps = Omit<DirectoriesBlock, "__typename" | "innerBlocks">;

export default function Directories({ directories, title, titleLevel = 2 }: DirectoriesProps) {
  if (directories.length === 0) {
    return null;
  }

  return (
    <section className={clsx("block-directories contained", styles.directories)}>
      {title && (
        <Hx level={titleLevel} className={styles.title}>
          {title}
        </Hx>
      )}
      <SubtitleLevelProvider level={titleLevel}>
        <ul className={styles.list}>
          {directories.map((directory) => (
            <li key={directory.id}>
              <DirectoryCard directory={directory} />
            </li>
          ))}
        </ul>
      </SubtitleLevelProvider>
    </section>
  );
}
