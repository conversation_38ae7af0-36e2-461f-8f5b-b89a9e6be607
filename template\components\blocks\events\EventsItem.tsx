"use client";

import Hx from "@/components/ui/title/Hx";
import type { Event } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import styles from "./EventsItem.module.scss";

const DateInterval = dynamic(() => import("@/components/ui/date/DateInterval"));

interface EventsItemProps {
  event: Event;
}

export default function EventsItem({
  event: { images, url, title, startDate, endDate, location, categories },
}: EventsItemProps) {
  const titleLevel = useTitleLevel();
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;

  return (
    <article className={styles.eventsItem}>
      <div className={styles.details}>
        <Hx level={titleLevel} className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </Hx>
        {location && (
          <div className={styles.location}>
            {location?.address?.city && (
              <p className={styles.city} aria-roledescription="Ville">
                {location.address.city}
              </p>
            )}
            {location?.title && (
              <p className={styles.place} aria-roledescription="Lieu">
                {location.title}
              </p>
            )}
          </div>
        )}
      </div>
      <div className={styles.top}>
        {startDate && <DateInterval className={styles.date} from={startDate} to={endDate} />}
        {image?.url && (
          <Image
            className={styles.image}
            src={image.url}
            width={image.width}
            height={image.height}
            alt={image.alt ?? ""}
            sizes="(max-width: 767px) 74px, (max-width: 1301px) 224px, 384px"
          />
        )}
      </div>
    </article>
  );
}
