"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_apollo_client-react-stream_04166d0d33e8adfc335aa646c6884-526e87"],{

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js":
/*!************************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js ***!
  \************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   built_for_browser: () => (/* binding */ built_for_browser),\n/* harmony export */   \"default\": () => (/* binding */ SimulatePreloadedQuery)\n/* harmony export */ });\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useApolloClient.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js\");\n/* harmony import */ var _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @apollo/client/react/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useBackgroundQuery.js\");\n/* harmony import */ var _apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @apollo/client/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js\");\n/* harmony import */ var _apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @apollo/client/utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var ts_invariant__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ts-invariant */ \"(app-pages-browser)/../node_modules/.pnpm/ts-invariant@0.10.3/node_modules/ts-invariant/lib/invariant.js\");\n/* __next_internal_client_entry_do_not_use__ default,built_for_browser auto */ var _s = $RefreshSig$();\n// src/SimulatePreloadedQuery.cc.ts\n\n// src/DataTransportAbstraction/transportedOptions.ts\n\n\n\nfunction deserializeOptions(options) {\n    return {\n        ...options,\n        // `gql` memoizes results, but based on the input string.\n        // We parse-stringify-parse here to ensure that our minified query\n        // has the best chance of being the referential same query as the one used in\n        // client-side code.\n        query: (0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__.gql)((0,_apollo_client_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.print)((0,_apollo_client_index_js__WEBPACK_IMPORTED_MODULE_0__.gql)(options.query)))\n    };\n}\n// src/SimulatePreloadedQuery.cc.ts\n\n\nvar handledRequests = /* @__PURE__ */ new WeakMap();\nfunction SimulatePreloadedQuery(param) {\n    let { options, result, children, queryKey } = param;\n    _s();\n    const client = (0,_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_4__.useApolloClient)();\n    if (!handledRequests.has(options)) {\n        const id = \"preloadedQuery:\".concat(client[\"queryManager\"].generateQueryId());\n        handledRequests.set(options, id);\n        ts_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"].debug(\"Preloaded query %s started on the server, simulating ongoing request\", id);\n        client.onQueryStarted({\n            type: \"started\",\n            id,\n            options\n        });\n        result.then((results)=>{\n            ts_invariant__WEBPACK_IMPORTED_MODULE_3__[\"default\"].debug(\"Preloaded query %s: received events: %o\", id, results);\n            for (const event of results){\n                client.onQueryProgress({\n                    ...event,\n                    id\n                });\n            }\n        });\n    }\n    const bgQueryArgs = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"SimulatePreloadedQuery.useMemo[bgQueryArgs]\": ()=>{\n            const { query, ...hydratedOptions } = deserializeOptions(options);\n            return [\n                query,\n                // If we didn't pass in a `queryKey` prop, the user didn't use the render props form and we don't\n                // need to create a real `queryRef` => skip.\n                // Otherwise we call `useBackgroundQuery` with options in this component to create a `queryRef`\n                // and have it soft-retained in the SuspenseCache.\n                queryKey ? {\n                    ...hydratedOptions,\n                    queryKey\n                } : _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_5__.skipToken\n            ];\n        }\n    }[\"SimulatePreloadedQuery.useMemo[bgQueryArgs]\"], [\n        options,\n        queryKey\n    ]);\n    (0,_apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_6__.useBackgroundQuery)(...bgQueryArgs);\n    return children;\n}\n_s(SimulatePreloadedQuery, \"pQU/KcPVZsfOe1Due4WXy8ftRUE=\", false, function() {\n    return [\n        _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_4__.useApolloClient,\n        _apollo_client_react_index_js__WEBPACK_IMPORTED_MODULE_6__.useBackgroundQuery\n    ];\n});\n_c = SimulatePreloadedQuery;\n\nconst built_for_browser = true; //# sourceMappingURL=SimulatePreloadedQuery.cc.js.map\nvar _c;\n$RefreshReg$(_c, \"SimulatePreloadedQuery\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997/node_modules/@apollo/client-react-streaming/dist/SimulatePreloadedQuery.cc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js":
/*!***********************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js ***!
  \***********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skipToken: () => (/* binding */ skipToken)\n/* harmony export */ });\nvar skipToken = Symbol.for(\"apollo.skipToken\");\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyL25vZGVfbW9kdWxlcy9AYXBvbGxvL2NsaWVudC9yZWFjdC9ob29rcy9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyXFxub2RlX21vZHVsZXNcXEBhcG9sbG9cXGNsaWVudFxccmVhY3RcXGhvb2tzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBza2lwVG9rZW4gPSBTeW1ib2wuZm9yKFwiYXBvbGxvLnNraXBUb2tlblwiKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/__use.js":
/*!****************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/__use.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("var rehackt__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __use: () => (/* binding */ __use)\n/* harmony export */ });\n/* harmony import */ var _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js\");\n/* harmony import */ var rehackt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rehackt */ \"(app-pages-browser)/../node_modules/.pnpm/rehackt@0.1.0_@types+react@19.1.9_react@19.1.1/node_modules/rehackt/index.js\");\n\n\n// Prevent webpack from complaining about our feature detection of the\n// use property of the React namespace, which is expected not\n// to exist when using current stable versions, and that's fine.\nvar useKey = \"use\";\nvar realHook = /*#__PURE__*/ (rehackt__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (rehackt__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(rehackt__WEBPACK_IMPORTED_MODULE_0__, 2)))[useKey];\n// This is named with two underscores to allow this hook to evade typical rules of\n// hooks (i.e. it can be used conditionally)\nvar __use = realHook ||\n    function __use(promise) {\n        var statefulPromise = (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.wrapPromiseWithState)(promise);\n        switch (statefulPromise.status) {\n            case \"pending\":\n                throw statefulPromise;\n            case \"rejected\":\n                throw statefulPromise.reason;\n            case \"fulfilled\":\n                return statefulPromise.value;\n        }\n    };\n//# sourceMappingURL=__use.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/__use.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/useDeepMemo.js":
/*!**********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/useDeepMemo.js ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepMemo: () => (/* binding */ useDeepMemo)\n/* harmony export */ });\n/* harmony import */ var rehackt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rehackt */ \"(app-pages-browser)/../node_modules/.pnpm/rehackt@0.1.0_@types+react@19.1.9_react@19.1.1/node_modules/rehackt/index.js\");\n/* harmony import */ var _wry_equality__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wry/equality */ \"(app-pages-browser)/../node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js\");\n\n\nfunction useDeepMemo(memoFn, deps) {\n    var ref = rehackt__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    if (!ref.current || !(0,_wry_equality__WEBPACK_IMPORTED_MODULE_1__.equal)(ref.current.deps, deps)) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        ref.current = { value: memoFn(), deps: deps };\n    }\n    return ref.current.value;\n}\n//# sourceMappingURL=useDeepMemo.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyL25vZGVfbW9kdWxlcy9AYXBvbGxvL2NsaWVudC9yZWFjdC9ob29rcy9pbnRlcm5hbC91c2VEZWVwTWVtby5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBaUM7QUFDSztBQUMvQjtBQUNQLGNBQWMsMkNBQVk7QUFDMUIseUJBQXlCLG9EQUFLO0FBQzlCO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhcG9sbG8rY2xpZW50QDMuMTMuOV9AdHlwZV8zNDI5NzQ5MGM0MWQ5NzBkMDRmM2E0NzNiMTQzNTgwMlxcbm9kZV9tb2R1bGVzXFxAYXBvbGxvXFxjbGllbnRcXHJlYWN0XFxob29rc1xcaW50ZXJuYWxcXHVzZURlZXBNZW1vLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWhhY2t0XCI7XG5pbXBvcnQgeyBlcXVhbCB9IGZyb20gXCJAd3J5L2VxdWFsaXR5XCI7XG5leHBvcnQgZnVuY3Rpb24gdXNlRGVlcE1lbW8obWVtb0ZuLCBkZXBzKSB7XG4gICAgdmFyIHJlZiA9IFJlYWN0LnVzZVJlZih2b2lkIDApO1xuICAgIGlmICghcmVmLmN1cnJlbnQgfHwgIWVxdWFsKHJlZi5jdXJyZW50LmRlcHMsIGRlcHMpKSB7XG4gICAgICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSByZWFjdC1jb21waWxlci9yZWFjdC1jb21waWxlclxuICAgICAgICByZWYuY3VycmVudCA9IHsgdmFsdWU6IG1lbW9GbigpLCBkZXBzOiBkZXBzIH07XG4gICAgfVxuICAgIHJldHVybiByZWYuY3VycmVudC52YWx1ZTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXVzZURlZXBNZW1vLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/useDeepMemo.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useBackgroundQuery.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useBackgroundQuery.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBackgroundQuery: () => (/* binding */ useBackgroundQuery)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var rehackt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rehackt */ \"(app-pages-browser)/../node_modules/.pnpm/rehackt@0.1.0_@types+react@19.1.9_react@19.1.1/node_modules/rehackt/index.js\");\n/* harmony import */ var _useApolloClient_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useApolloClient.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useApolloClient.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/wrapHook.js\");\n/* harmony import */ var _useSuspenseQuery_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./useSuspenseQuery.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useSuspenseQuery.js\");\n/* harmony import */ var _cache_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../cache/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/canonicalStringify.js\");\n\n\n\n\n\n\n\nfunction useBackgroundQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    return (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_1__.wrapHook)(\"useBackgroundQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useBackgroundQuery_, (0,_useApolloClient_js__WEBPACK_IMPORTED_MODULE_2__.useApolloClient)(typeof options === \"object\" ? options.client : undefined))(query, options);\n}\nfunction useBackgroundQuery_(query, options) {\n    var client = (0,_useApolloClient_js__WEBPACK_IMPORTED_MODULE_2__.useApolloClient)(options.client);\n    var suspenseCache = (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_3__.getSuspenseCache)(client);\n    var watchQueryOptions = (0,_useSuspenseQuery_js__WEBPACK_IMPORTED_MODULE_4__.useWatchQueryOptions)({ client: client, query: query, options: options });\n    var fetchPolicy = watchQueryOptions.fetchPolicy, variables = watchQueryOptions.variables;\n    var _a = options.queryKey, queryKey = _a === void 0 ? [] : _a;\n    // This ref tracks the first time query execution is enabled to determine\n    // whether to return a query ref or `undefined`. When initialized\n    // in a skipped state (either via `skip: true` or `skipToken`) we return\n    // `undefined` for the `queryRef` until the query has been enabled. Once\n    // enabled, a query ref is always returned regardless of whether the query is\n    // skipped again later.\n    var didFetchResult = rehackt__WEBPACK_IMPORTED_MODULE_0__.useRef(fetchPolicy !== \"standby\");\n    didFetchResult.current || (didFetchResult.current = fetchPolicy !== \"standby\");\n    var cacheKey = (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__spreadArray)([\n        query,\n        (0,_cache_index_js__WEBPACK_IMPORTED_MODULE_6__.canonicalStringify)(variables)\n    ], [].concat(queryKey), true);\n    var queryRef = suspenseCache.getQueryRef(cacheKey, function () {\n        return client.watchQuery(watchQueryOptions);\n    });\n    var _b = rehackt__WEBPACK_IMPORTED_MODULE_0__.useState((0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.wrapQueryRef)(queryRef)), wrappedQueryRef = _b[0], setWrappedQueryRef = _b[1];\n    if ((0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.unwrapQueryRef)(wrappedQueryRef) !== queryRef) {\n        setWrappedQueryRef((0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.wrapQueryRef)(queryRef));\n    }\n    if (queryRef.didChangeOptions(watchQueryOptions)) {\n        var promise = queryRef.applyOptions(watchQueryOptions);\n        (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.updateWrappedQueryRef)(wrappedQueryRef, promise);\n    }\n    // This prevents issues where rerendering useBackgroundQuery after the\n    // queryRef has been disposed would cause the hook to return a new queryRef\n    // instance since disposal also removes it from the suspense cache. We add\n    // the queryRef back in the suspense cache so that the next render will reuse\n    // this queryRef rather than initializing a new instance.\n    rehackt__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        // Since the queryRef is disposed async via `setTimeout`, we have to wait a\n        // tick before checking it and adding back to the suspense cache.\n        var id = setTimeout(function () {\n            if (queryRef.disposed) {\n                suspenseCache.add(cacheKey, queryRef);\n            }\n        });\n        return function () { return clearTimeout(id); };\n        // Omitting the deps is intentional. This avoids stale closures and the\n        // conditional ensures we aren't running the logic on each render.\n    });\n    var fetchMore = rehackt__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (options) {\n        var promise = queryRef.fetchMore(options);\n        setWrappedQueryRef((0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.wrapQueryRef)(queryRef));\n        return promise;\n    }, [queryRef]);\n    var refetch = rehackt__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (variables) {\n        var promise = queryRef.refetch(variables);\n        setWrappedQueryRef((0,_internal_index_js__WEBPACK_IMPORTED_MODULE_7__.wrapQueryRef)(queryRef));\n        return promise;\n    }, [queryRef]);\n    rehackt__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () { return queryRef.softRetain(); }, [queryRef]);\n    return [\n        didFetchResult.current ? wrappedQueryRef : void 0,\n        {\n            fetchMore: fetchMore,\n            refetch: refetch,\n            // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n            subscribeToMore: queryRef.observable\n                .subscribeToMore,\n        },\n    ];\n}\n//# sourceMappingURL=useBackgroundQuery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useBackgroundQuery.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useSuspenseQuery.js":
/*!******************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useSuspenseQuery.js ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toApolloError: () => (/* binding */ toApolloError),\n/* harmony export */   useSuspenseQuery: () => (/* binding */ useSuspenseQuery),\n/* harmony export */   useWatchQueryOptions: () => (/* binding */ useWatchQueryOptions)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var rehackt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! rehackt */ \"(app-pages-browser)/../node_modules/.pnpm/rehackt@0.1.0_@types+react@19.1.9_react@19.1.1/node_modules/rehackt/index.js\");\n/* harmony import */ var _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utilities/globals/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/globals/index.js\");\n/* harmony import */ var _core_index_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../core/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/core/networkStatus.js\");\n/* harmony import */ var _core_index_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../core/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/errors/index.js\");\n/* harmony import */ var _utilities_index_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/arrays.js\");\n/* harmony import */ var _useApolloClient_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useApolloClient.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useApolloClient.js\");\n/* harmony import */ var _parser_index_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../parser/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/parser/index.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/wrapHook.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/__use.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/internal/useDeepMemo.js\");\n/* harmony import */ var _internal_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../internal/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js\");\n/* harmony import */ var _cache_index_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../cache/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/canonicalStringify.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./constants.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/constants.js\");\n\n\n\n\n\n\n\n\n\n\n\nfunction useSuspenseQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    return (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_2__.wrapHook)(\"useSuspenseQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseQuery_, (0,_useApolloClient_js__WEBPACK_IMPORTED_MODULE_3__.useApolloClient)(typeof options === \"object\" ? options.client : undefined))(query, options);\n}\nfunction useSuspenseQuery_(query, options) {\n    var client = (0,_useApolloClient_js__WEBPACK_IMPORTED_MODULE_3__.useApolloClient)(options.client);\n    var suspenseCache = (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_4__.getSuspenseCache)(client);\n    var watchQueryOptions = useWatchQueryOptions({\n        client: client,\n        query: query,\n        options: options,\n    });\n    var fetchPolicy = watchQueryOptions.fetchPolicy, variables = watchQueryOptions.variables;\n    var _a = options.queryKey, queryKey = _a === void 0 ? [] : _a;\n    var cacheKey = (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__spreadArray)([\n        query,\n        (0,_cache_index_js__WEBPACK_IMPORTED_MODULE_6__.canonicalStringify)(variables)\n    ], [].concat(queryKey), true);\n    var queryRef = suspenseCache.getQueryRef(cacheKey, function () {\n        return client.watchQuery(watchQueryOptions);\n    });\n    var _b = rehackt__WEBPACK_IMPORTED_MODULE_0__.useState([queryRef.key, queryRef.promise]), current = _b[0], setPromise = _b[1];\n    // This saves us a re-execution of the render function when a variable changed.\n    if (current[0] !== queryRef.key) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        current[0] = queryRef.key;\n        current[1] = queryRef.promise;\n    }\n    var promise = current[1];\n    if (queryRef.didChangeOptions(watchQueryOptions)) {\n        current[1] = promise = queryRef.applyOptions(watchQueryOptions);\n    }\n    rehackt__WEBPACK_IMPORTED_MODULE_0__.useEffect(function () {\n        var dispose = queryRef.retain();\n        var removeListener = queryRef.listen(function (promise) {\n            setPromise([queryRef.key, promise]);\n        });\n        return function () {\n            removeListener();\n            dispose();\n        };\n    }, [queryRef]);\n    var skipResult = rehackt__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n        var error = toApolloError(queryRef.result);\n        return {\n            loading: false,\n            data: queryRef.result.data,\n            networkStatus: error ? _core_index_js__WEBPACK_IMPORTED_MODULE_7__.NetworkStatus.error : _core_index_js__WEBPACK_IMPORTED_MODULE_7__.NetworkStatus.ready,\n            error: error,\n        };\n    }, [queryRef.result]);\n    var result = fetchPolicy === \"standby\" ? skipResult : (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_8__.__use)(promise);\n    var fetchMore = rehackt__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (options) {\n        var promise = queryRef.fetchMore(options);\n        setPromise([queryRef.key, queryRef.promise]);\n        return promise;\n    }, [queryRef]);\n    var refetch = rehackt__WEBPACK_IMPORTED_MODULE_0__.useCallback(function (variables) {\n        var promise = queryRef.refetch(variables);\n        setPromise([queryRef.key, queryRef.promise]);\n        return promise;\n    }, [queryRef]);\n    // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n    var subscribeToMore = queryRef.observable\n        .subscribeToMore;\n    return rehackt__WEBPACK_IMPORTED_MODULE_0__.useMemo(function () {\n        return {\n            client: client,\n            data: result.data,\n            error: toApolloError(result),\n            networkStatus: result.networkStatus,\n            fetchMore: fetchMore,\n            refetch: refetch,\n            subscribeToMore: subscribeToMore,\n        };\n    }, [client, fetchMore, refetch, result, subscribeToMore]);\n}\nfunction validateOptions(options) {\n    var query = options.query, fetchPolicy = options.fetchPolicy, returnPartialData = options.returnPartialData;\n    (0,_parser_index_js__WEBPACK_IMPORTED_MODULE_9__.verifyDocumentType)(query, _parser_index_js__WEBPACK_IMPORTED_MODULE_9__.DocumentType.Query);\n    validateFetchPolicy(fetchPolicy);\n    validatePartialDataReturn(fetchPolicy, returnPartialData);\n}\nfunction validateFetchPolicy(fetchPolicy) {\n    if (fetchPolicy === void 0) { fetchPolicy = \"cache-first\"; }\n    var supportedFetchPolicies = [\n        \"cache-first\",\n        \"network-only\",\n        \"no-cache\",\n        \"cache-and-network\",\n    ];\n    (0,_utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_1__.invariant)(supportedFetchPolicies.includes(fetchPolicy), 66, fetchPolicy);\n}\nfunction validatePartialDataReturn(fetchPolicy, returnPartialData) {\n    if (fetchPolicy === \"no-cache\" && returnPartialData) {\n        globalThis.__DEV__ !== false && _utilities_globals_index_js__WEBPACK_IMPORTED_MODULE_1__.invariant.warn(67);\n    }\n}\nfunction toApolloError(result) {\n    return (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_10__.isNonEmptyArray)(result.errors) ?\n        new _core_index_js__WEBPACK_IMPORTED_MODULE_11__.ApolloError({ graphQLErrors: result.errors })\n        : result.error;\n}\nfunction useWatchQueryOptions(_a) {\n    var client = _a.client, query = _a.query, options = _a.options;\n    return (0,_internal_index_js__WEBPACK_IMPORTED_MODULE_12__.useDeepMemo)(function () {\n        var _a;\n        if (options === _constants_js__WEBPACK_IMPORTED_MODULE_13__.skipToken) {\n            return { query: query, fetchPolicy: \"standby\" };\n        }\n        var fetchPolicy = options.fetchPolicy ||\n            ((_a = client.defaultOptions.watchQuery) === null || _a === void 0 ? void 0 : _a.fetchPolicy) ||\n            \"cache-first\";\n        var watchQueryOptions = (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, options), { fetchPolicy: fetchPolicy, query: query, notifyOnNetworkStatusChange: false, nextFetchPolicy: void 0 });\n        if (globalThis.__DEV__ !== false) {\n            validateOptions(watchQueryOptions);\n        }\n        // Assign the updated fetch policy after our validation since `standby` is\n        // not a supported fetch policy on its own without the use of `skip`.\n        if (options.skip) {\n            watchQueryOptions.fetchPolicy = \"standby\";\n        }\n        return watchQueryOptions;\n    }, [client, options, query]);\n}\n//# sourceMappingURL=useSuspenseQuery.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/hooks/useSuspenseQuery.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/FragmentReference.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/FragmentReference.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FragmentReference: () => (/* binding */ FragmentReference)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _wry_equality__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/equality */ \"(app-pages-browser)/../node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js\");\n/* harmony import */ var _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js\");\n\n\n\nvar FragmentReference = /** @class */ (function () {\n    function FragmentReference(client, watchFragmentOptions, options) {\n        var _this = this;\n        this.key = {};\n        this.listeners = new Set();\n        this.references = 0;\n        this.dispose = this.dispose.bind(this);\n        this.handleNext = this.handleNext.bind(this);\n        this.handleError = this.handleError.bind(this);\n        this.observable = client.watchFragment(watchFragmentOptions);\n        if (options.onDispose) {\n            this.onDispose = options.onDispose;\n        }\n        var diff = this.getDiff(client, watchFragmentOptions);\n        // Start a timer that will automatically dispose of the query if the\n        // suspended resource does not use this fragmentRef in the given time. This\n        // helps prevent memory leaks when a component has unmounted before the\n        // query has finished loading.\n        var startDisposeTimer = function () {\n            var _a;\n            if (!_this.references) {\n                _this.autoDisposeTimeoutId = setTimeout(_this.dispose, (_a = options.autoDisposeTimeoutMs) !== null && _a !== void 0 ? _a : 30000);\n            }\n        };\n        this.promise =\n            diff.complete ?\n                (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.createFulfilledPromise)(diff.result)\n                : this.createPendingPromise();\n        this.subscribeToFragment();\n        this.promise.then(startDisposeTimer, startDisposeTimer);\n    }\n    FragmentReference.prototype.listen = function (listener) {\n        var _this = this;\n        this.listeners.add(listener);\n        return function () {\n            _this.listeners.delete(listener);\n        };\n    };\n    FragmentReference.prototype.retain = function () {\n        var _this = this;\n        this.references++;\n        clearTimeout(this.autoDisposeTimeoutId);\n        var disposed = false;\n        return function () {\n            if (disposed) {\n                return;\n            }\n            disposed = true;\n            _this.references--;\n            setTimeout(function () {\n                if (!_this.references) {\n                    _this.dispose();\n                }\n            });\n        };\n    };\n    FragmentReference.prototype.dispose = function () {\n        this.subscription.unsubscribe();\n        this.onDispose();\n    };\n    FragmentReference.prototype.onDispose = function () {\n        // noop. overridable by options\n    };\n    FragmentReference.prototype.subscribeToFragment = function () {\n        this.subscription = this.observable.subscribe(this.handleNext.bind(this), this.handleError.bind(this));\n    };\n    FragmentReference.prototype.handleNext = function (result) {\n        var _a;\n        switch (this.promise.status) {\n            case \"pending\": {\n                if (result.complete) {\n                    return (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, result.data);\n                }\n                this.deliver(this.promise);\n                break;\n            }\n            case \"fulfilled\": {\n                // This can occur when we already have a result written to the cache and\n                // we subscribe for the first time. We create a fulfilled promise in the\n                // constructor with a value that is the same as the first emitted value\n                // so we want to skip delivering it.\n                if ((0,_wry_equality__WEBPACK_IMPORTED_MODULE_0__.equal)(this.promise.value, result.data)) {\n                    return;\n                }\n                this.promise =\n                    result.complete ?\n                        (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.createFulfilledPromise)(result.data)\n                        : this.createPendingPromise();\n                this.deliver(this.promise);\n            }\n        }\n    };\n    FragmentReference.prototype.handleError = function (error) {\n        var _a;\n        (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, error);\n    };\n    FragmentReference.prototype.deliver = function (promise) {\n        this.listeners.forEach(function (listener) { return listener(promise); });\n    };\n    FragmentReference.prototype.createPendingPromise = function () {\n        var _this = this;\n        return (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.wrapPromiseWithState)(new Promise(function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        }));\n    };\n    FragmentReference.prototype.getDiff = function (client, options) {\n        var cache = client.cache;\n        var from = options.from, fragment = options.fragment, fragmentName = options.fragmentName;\n        var diff = cache.diff((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, options), { query: cache[\"getFragmentDoc\"](fragment, fragmentName), returnPartialData: true, id: from, optimistic: true }));\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, diff), { result: client[\"queryManager\"].maskFragment({\n                fragment: fragment,\n                fragmentName: fragmentName,\n                data: diff.result,\n            }) });\n    };\n    return FragmentReference;\n}());\n\n//# sourceMappingURL=FragmentReference.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/FragmentReference.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js":
/*!*************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InternalQueryReference: () => (/* binding */ InternalQueryReference),\n/* harmony export */   assertWrappedQueryRef: () => (/* binding */ assertWrappedQueryRef),\n/* harmony export */   getWrappedPromise: () => (/* binding */ getWrappedPromise),\n/* harmony export */   unwrapQueryRef: () => (/* binding */ unwrapQueryRef),\n/* harmony export */   updateWrappedQueryRef: () => (/* binding */ updateWrappedQueryRef),\n/* harmony export */   wrapQueryRef: () => (/* binding */ wrapQueryRef)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _wry_equality__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/equality */ \"(app-pages-browser)/../node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js\");\n/* harmony import */ var _utilities_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js\");\n/* harmony import */ var _utilities_globals_invariantWrappers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utilities/globals/invariantWrappers.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/globals/invariantWrappers.js\");\n\n\n\n\n\nvar QUERY_REFERENCE_SYMBOL = Symbol.for(\"apollo.internal.queryRef\");\nvar PROMISE_SYMBOL = Symbol.for(\"apollo.internal.refPromise\");\nfunction wrapQueryRef(internalQueryRef) {\n    var _a;\n    var ref = (_a = {\n            toPromise: function () {\n                // We avoid resolving this promise with the query data because we want to\n                // discourage using the server data directly from the queryRef. Instead,\n                // the data should be accessed through `useReadQuery`. When the server\n                // data is needed, its better to use `client.query()` directly.\n                //\n                // Here we resolve with the ref itself to make using this in React Router\n                // or TanStack Router `loader` functions a bit more ergonomic e.g.\n                //\n                // function loader() {\n                //   return { queryRef: await preloadQuery(query).toPromise() }\n                // }\n                return getWrappedPromise(ref).then(function () { return ref; });\n            }\n        },\n        _a[QUERY_REFERENCE_SYMBOL] = internalQueryRef,\n        _a[PROMISE_SYMBOL] = internalQueryRef.promise,\n        _a);\n    return ref;\n}\nfunction assertWrappedQueryRef(queryRef) {\n    (0,_utilities_globals_invariantWrappers_js__WEBPACK_IMPORTED_MODULE_1__.invariant)(!queryRef || QUERY_REFERENCE_SYMBOL in queryRef, 69);\n}\nfunction getWrappedPromise(queryRef) {\n    var internalQueryRef = unwrapQueryRef(queryRef);\n    return internalQueryRef.promise.status === \"fulfilled\" ?\n        internalQueryRef.promise\n        : queryRef[PROMISE_SYMBOL];\n}\nfunction unwrapQueryRef(queryRef) {\n    return queryRef[QUERY_REFERENCE_SYMBOL];\n}\nfunction updateWrappedQueryRef(queryRef, promise) {\n    queryRef[PROMISE_SYMBOL] = promise;\n}\nvar OBSERVED_CHANGED_OPTIONS = [\n    \"canonizeResults\",\n    \"context\",\n    \"errorPolicy\",\n    \"fetchPolicy\",\n    \"refetchWritePolicy\",\n    \"returnPartialData\",\n];\nvar InternalQueryReference = /** @class */ (function () {\n    function InternalQueryReference(observable, options) {\n        var _this = this;\n        this.key = {};\n        this.listeners = new Set();\n        this.references = 0;\n        this.softReferences = 0;\n        this.handleNext = this.handleNext.bind(this);\n        this.handleError = this.handleError.bind(this);\n        this.dispose = this.dispose.bind(this);\n        this.observable = observable;\n        if (options.onDispose) {\n            this.onDispose = options.onDispose;\n        }\n        this.setResult();\n        this.subscribeToQuery();\n        // Start a timer that will automatically dispose of the query if the\n        // suspended resource does not use this queryRef in the given time. This\n        // helps prevent memory leaks when a component has unmounted before the\n        // query has finished loading.\n        var startDisposeTimer = function () {\n            var _a;\n            if (!_this.references) {\n                _this.autoDisposeTimeoutId = setTimeout(_this.dispose, (_a = options.autoDisposeTimeoutMs) !== null && _a !== void 0 ? _a : 30000);\n            }\n        };\n        // We wait until the request has settled to ensure we don't dispose of the\n        // query ref before the request finishes, otherwise we would leave the\n        // promise in a pending state rendering the suspense boundary indefinitely.\n        this.promise.then(startDisposeTimer, startDisposeTimer);\n    }\n    Object.defineProperty(InternalQueryReference.prototype, \"disposed\", {\n        get: function () {\n            return this.subscription.closed;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(InternalQueryReference.prototype, \"watchQueryOptions\", {\n        get: function () {\n            return this.observable.options;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    InternalQueryReference.prototype.reinitialize = function () {\n        var observable = this.observable;\n        var originalFetchPolicy = this.watchQueryOptions.fetchPolicy;\n        var avoidNetworkRequests = originalFetchPolicy === \"no-cache\" || originalFetchPolicy === \"standby\";\n        try {\n            if (avoidNetworkRequests) {\n                observable.silentSetOptions({ fetchPolicy: \"standby\" });\n            }\n            else {\n                observable.resetLastResults();\n                observable.silentSetOptions({ fetchPolicy: \"cache-first\" });\n            }\n            this.subscribeToQuery();\n            if (avoidNetworkRequests) {\n                return;\n            }\n            observable.resetDiff();\n            this.setResult();\n        }\n        finally {\n            observable.silentSetOptions({ fetchPolicy: originalFetchPolicy });\n        }\n    };\n    InternalQueryReference.prototype.retain = function () {\n        var _this = this;\n        this.references++;\n        clearTimeout(this.autoDisposeTimeoutId);\n        var disposed = false;\n        return function () {\n            if (disposed) {\n                return;\n            }\n            disposed = true;\n            _this.references--;\n            setTimeout(function () {\n                if (!_this.references) {\n                    _this.dispose();\n                }\n            });\n        };\n    };\n    InternalQueryReference.prototype.softRetain = function () {\n        var _this = this;\n        this.softReferences++;\n        var disposed = false;\n        return function () {\n            // Tracking if this has already been called helps ensure that\n            // multiple calls to this function won't decrement the reference\n            // counter more than it should. Subsequent calls just result in a noop.\n            if (disposed) {\n                return;\n            }\n            disposed = true;\n            _this.softReferences--;\n            setTimeout(function () {\n                if (!_this.softReferences && !_this.references) {\n                    _this.dispose();\n                }\n            });\n        };\n    };\n    InternalQueryReference.prototype.didChangeOptions = function (watchQueryOptions) {\n        var _this = this;\n        return OBSERVED_CHANGED_OPTIONS.some(function (option) {\n            return option in watchQueryOptions &&\n                !(0,_wry_equality__WEBPACK_IMPORTED_MODULE_0__.equal)(_this.watchQueryOptions[option], watchQueryOptions[option]);\n        });\n    };\n    InternalQueryReference.prototype.applyOptions = function (watchQueryOptions) {\n        var _a = this.watchQueryOptions, currentFetchPolicy = _a.fetchPolicy, currentCanonizeResults = _a.canonizeResults;\n        // \"standby\" is used when `skip` is set to `true`. Detect when we've\n        // enabled the query (i.e. `skip` is `false`) to execute a network request.\n        if (currentFetchPolicy === \"standby\" &&\n            currentFetchPolicy !== watchQueryOptions.fetchPolicy) {\n            this.initiateFetch(this.observable.reobserve(watchQueryOptions));\n        }\n        else {\n            this.observable.silentSetOptions(watchQueryOptions);\n            if (currentCanonizeResults !== watchQueryOptions.canonizeResults) {\n                this.result = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, this.result), this.observable.getCurrentResult());\n                this.promise = (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.createFulfilledPromise)(this.result);\n            }\n        }\n        return this.promise;\n    };\n    InternalQueryReference.prototype.listen = function (listener) {\n        var _this = this;\n        this.listeners.add(listener);\n        return function () {\n            _this.listeners.delete(listener);\n        };\n    };\n    InternalQueryReference.prototype.refetch = function (variables) {\n        return this.initiateFetch(this.observable.refetch(variables));\n    };\n    InternalQueryReference.prototype.fetchMore = function (options) {\n        return this.initiateFetch(this.observable.fetchMore(options));\n    };\n    InternalQueryReference.prototype.dispose = function () {\n        this.subscription.unsubscribe();\n        this.onDispose();\n    };\n    InternalQueryReference.prototype.onDispose = function () {\n        // noop. overridable by options\n    };\n    InternalQueryReference.prototype.handleNext = function (result) {\n        var _a;\n        switch (this.promise.status) {\n            case \"pending\": {\n                // Maintain the last successful `data` value if the next result does not\n                // have one.\n                if (result.data === void 0) {\n                    result.data = this.result.data;\n                }\n                this.result = result;\n                (_a = this.resolve) === null || _a === void 0 ? void 0 : _a.call(this, result);\n                break;\n            }\n            default: {\n                // This occurs when switching to a result that is fully cached when this\n                // class is instantiated. ObservableQuery will run reobserve when\n                // subscribing, which delivers a result from the cache.\n                if (result.data === this.result.data &&\n                    result.networkStatus === this.result.networkStatus) {\n                    return;\n                }\n                // Maintain the last successful `data` value if the next result does not\n                // have one.\n                if (result.data === void 0) {\n                    result.data = this.result.data;\n                }\n                this.result = result;\n                this.promise = (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.createFulfilledPromise)(result);\n                this.deliver(this.promise);\n                break;\n            }\n        }\n    };\n    InternalQueryReference.prototype.handleError = function (error) {\n        var _a;\n        this.subscription.unsubscribe();\n        this.subscription = this.observable.resubscribeAfterError(this.handleNext, this.handleError);\n        switch (this.promise.status) {\n            case \"pending\": {\n                (_a = this.reject) === null || _a === void 0 ? void 0 : _a.call(this, error);\n                break;\n            }\n            default: {\n                this.promise = (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.createRejectedPromise)(error);\n                this.deliver(this.promise);\n            }\n        }\n    };\n    InternalQueryReference.prototype.deliver = function (promise) {\n        this.listeners.forEach(function (listener) { return listener(promise); });\n    };\n    InternalQueryReference.prototype.initiateFetch = function (returnedPromise) {\n        var _this = this;\n        this.promise = this.createPendingPromise();\n        this.promise.catch(function () { });\n        // If the data returned from the fetch is deeply equal to the data already\n        // in the cache, `handleNext` will not be triggered leaving the promise we\n        // created in a pending state forever. To avoid this situtation, we attempt\n        // to resolve the promise if `handleNext` hasn't been run to ensure the\n        // promise is resolved correctly.\n        returnedPromise\n            .then(function () {\n            // In the case of `fetchMore`, this promise is resolved before a cache\n            // result is emitted due to the fact that `fetchMore` sets a `no-cache`\n            // fetch policy and runs `cache.batch` in its `.then` handler. Because\n            // the timing is different, we accidentally run this update twice\n            // causing an additional re-render with the `fetchMore` result by\n            // itself. By wrapping in `setTimeout`, this should provide a short\n            // delay to allow the `QueryInfo.notify` handler to run before this\n            // promise is checked.\n            // See https://github.com/apollographql/apollo-client/issues/11315 for\n            // more information\n            setTimeout(function () {\n                var _a;\n                if (_this.promise.status === \"pending\") {\n                    // Use the current result from the observable instead of the value\n                    // resolved from the promise. This avoids issues in some cases where\n                    // the raw resolved value should not be the emitted value, such as\n                    // when a `fetchMore` call returns an empty array after it has\n                    // reached the end of the list.\n                    //\n                    // See the following for more information:\n                    // https://github.com/apollographql/apollo-client/issues/11642\n                    _this.result = _this.observable.getCurrentResult();\n                    (_a = _this.resolve) === null || _a === void 0 ? void 0 : _a.call(_this, _this.result);\n                }\n            });\n        })\n            .catch(function (error) { var _a; return (_a = _this.reject) === null || _a === void 0 ? void 0 : _a.call(_this, error); });\n        return returnedPromise;\n    };\n    InternalQueryReference.prototype.subscribeToQuery = function () {\n        var _this = this;\n        this.subscription = this.observable\n            .filter(function (result) { return !(0,_wry_equality__WEBPACK_IMPORTED_MODULE_0__.equal)(result.data, {}) && !(0,_wry_equality__WEBPACK_IMPORTED_MODULE_0__.equal)(result, _this.result); })\n            .subscribe(this.handleNext, this.handleError);\n    };\n    InternalQueryReference.prototype.setResult = function () {\n        // Don't save this result as last result to prevent delivery of last result\n        // when first subscribing\n        var result = this.observable.getCurrentResult(false);\n        if ((0,_wry_equality__WEBPACK_IMPORTED_MODULE_0__.equal)(result, this.result)) {\n            return;\n        }\n        this.result = result;\n        this.promise =\n            (result.data &&\n                (!result.partial || this.watchQueryOptions.returnPartialData)) ?\n                (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.createFulfilledPromise)(result)\n                : this.createPendingPromise();\n    };\n    InternalQueryReference.prototype.createPendingPromise = function () {\n        var _this = this;\n        return (0,_utilities_index_js__WEBPACK_IMPORTED_MODULE_3__.wrapPromiseWithState)(new Promise(function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        }));\n    };\n    return InternalQueryReference;\n}());\n\n//# sourceMappingURL=QueryReference.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/SuspenseCache.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/SuspenseCache.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SuspenseCache: () => (/* binding */ SuspenseCache)\n/* harmony export */ });\n/* harmony import */ var _wry_trie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/trie */ \"(app-pages-browser)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\");\n/* harmony import */ var _utilities_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utilities/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/common/canUse.js\");\n/* harmony import */ var _QueryReference_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./QueryReference.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/QueryReference.js\");\n/* harmony import */ var _FragmentReference_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FragmentReference.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/FragmentReference.js\");\n\n\n\n\nvar SuspenseCache = /** @class */ (function () {\n    function SuspenseCache(options) {\n        if (options === void 0) { options = Object.create(null); }\n        this.queryRefs = new _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie(_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.canUseWeakMap);\n        this.fragmentRefs = new _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie(_utilities_index_js__WEBPACK_IMPORTED_MODULE_1__.canUseWeakMap);\n        this.options = options;\n    }\n    SuspenseCache.prototype.getQueryRef = function (cacheKey, createObservable) {\n        var ref = this.queryRefs.lookupArray(cacheKey);\n        if (!ref.current) {\n            ref.current = new _QueryReference_js__WEBPACK_IMPORTED_MODULE_2__.InternalQueryReference(createObservable(), {\n                autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n                onDispose: function () {\n                    delete ref.current;\n                },\n            });\n        }\n        return ref.current;\n    };\n    SuspenseCache.prototype.getFragmentRef = function (cacheKey, client, options) {\n        var ref = this.fragmentRefs.lookupArray(cacheKey);\n        if (!ref.current) {\n            ref.current = new _FragmentReference_js__WEBPACK_IMPORTED_MODULE_3__.FragmentReference(client, options, {\n                autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n                onDispose: function () {\n                    delete ref.current;\n                },\n            });\n        }\n        return ref.current;\n    };\n    SuspenseCache.prototype.add = function (cacheKey, queryRef) {\n        var ref = this.queryRefs.lookupArray(cacheKey);\n        ref.current = queryRef;\n    };\n    return SuspenseCache;\n}());\n\n//# sourceMappingURL=SuspenseCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/SuspenseCache.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSuspenseCache: () => (/* binding */ getSuspenseCache)\n/* harmony export */ });\n/* harmony import */ var _SuspenseCache_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./SuspenseCache.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/SuspenseCache.js\");\n\nvar suspenseCacheSymbol = Symbol.for(\"apollo.suspenseCache\");\nfunction getSuspenseCache(client) {\n    var _a;\n    if (!client[suspenseCacheSymbol]) {\n        client[suspenseCacheSymbol] = new _SuspenseCache_js__WEBPACK_IMPORTED_MODULE_0__.SuspenseCache((_a = client.defaultOptions.react) === null || _a === void 0 ? void 0 : _a.suspense);\n    }\n    return client[suspenseCacheSymbol];\n}\n//# sourceMappingURL=getSuspenseCache.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyL25vZGVfbW9kdWxlcy9AYXBvbGxvL2NsaWVudC9yZWFjdC9pbnRlcm5hbC9jYWNoZS9nZXRTdXNwZW5zZUNhY2hlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EO0FBQ25EO0FBQ087QUFDUDtBQUNBO0FBQ0EsMENBQTBDLDREQUFhO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBhcG9sbG8rY2xpZW50QDMuMTMuOV9AdHlwZV8zNDI5NzQ5MGM0MWQ5NzBkMDRmM2E0NzNiMTQzNTgwMlxcbm9kZV9tb2R1bGVzXFxAYXBvbGxvXFxjbGllbnRcXHJlYWN0XFxpbnRlcm5hbFxcY2FjaGVcXGdldFN1c3BlbnNlQ2FjaGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3VzcGVuc2VDYWNoZSB9IGZyb20gXCIuL1N1c3BlbnNlQ2FjaGUuanNcIjtcbnZhciBzdXNwZW5zZUNhY2hlU3ltYm9sID0gU3ltYm9sLmZvcihcImFwb2xsby5zdXNwZW5zZUNhY2hlXCIpO1xuZXhwb3J0IGZ1bmN0aW9uIGdldFN1c3BlbnNlQ2FjaGUoY2xpZW50KSB7XG4gICAgdmFyIF9hO1xuICAgIGlmICghY2xpZW50W3N1c3BlbnNlQ2FjaGVTeW1ib2xdKSB7XG4gICAgICAgIGNsaWVudFtzdXNwZW5zZUNhY2hlU3ltYm9sXSA9IG5ldyBTdXNwZW5zZUNhY2hlKChfYSA9IGNsaWVudC5kZWZhdWx0T3B0aW9ucy5yZWFjdCkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLnN1c3BlbnNlKTtcbiAgICB9XG4gICAgcmV0dXJuIGNsaWVudFtzdXNwZW5zZUNhY2hlU3ltYm9sXTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWdldFN1c3BlbnNlQ2FjaGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/react/internal/cache/getSuspenseCache.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   print: () => (/* binding */ print)\n/* harmony export */ });\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! graphql */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printer.mjs\");\n/* harmony import */ var _caching_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../caching/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/caching/caches.js\");\n/* harmony import */ var _caching_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../caching/index.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/caching/sizes.js\");\n/* harmony import */ var _caching_getMemoryInternals_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../caching/getMemoryInternals.js */ \"(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/caching/getMemoryInternals.js\");\n\n\n\nvar printCache;\nvar print = Object.assign(function (ast) {\n    var result = printCache.get(ast);\n    if (!result) {\n        result = (0,graphql__WEBPACK_IMPORTED_MODULE_0__.print)(ast);\n        printCache.set(ast, result);\n    }\n    return result;\n}, {\n    reset: function () {\n        printCache = new _caching_index_js__WEBPACK_IMPORTED_MODULE_1__.AutoCleanedWeakCache(_caching_index_js__WEBPACK_IMPORTED_MODULE_2__.cacheSizes.print || 2000 /* defaultCacheSizes.print */);\n    },\n});\nprint.reset();\nif (globalThis.__DEV__ !== false) {\n    (0,_caching_getMemoryInternals_js__WEBPACK_IMPORTED_MODULE_3__.registerGlobalCache)(\"print\", function () { return (printCache ? printCache.size : 0); });\n}\n//# sourceMappingURL=print.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyL25vZGVfbW9kdWxlcy9AYXBvbGxvL2NsaWVudC91dGlsaXRpZXMvZ3JhcGhxbC9wcmludC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE2QztBQUMyQjtBQUNEO0FBQ3ZFO0FBQ087QUFDUDtBQUNBO0FBQ0EsaUJBQWlCLDhDQUFTO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLHlCQUF5QixtRUFBb0IsQ0FBQyx5REFBVTtBQUN4RCxLQUFLO0FBQ0wsQ0FBQztBQUNEO0FBQ0E7QUFDQSxJQUFJLG1GQUFtQix3QkFBd0IsNENBQTRDO0FBQzNGO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGFwb2xsbytjbGllbnRAMy4xMy45X0B0eXBlXzM0Mjk3NDkwYzQxZDk3MGQwNGYzYTQ3M2IxNDM1ODAyXFxub2RlX21vZHVsZXNcXEBhcG9sbG9cXGNsaWVudFxcdXRpbGl0aWVzXFxncmFwaHFsXFxwcmludC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBwcmludCBhcyBvcmlnUHJpbnQgfSBmcm9tIFwiZ3JhcGhxbFwiO1xuaW1wb3J0IHsgQXV0b0NsZWFuZWRXZWFrQ2FjaGUsIGNhY2hlU2l6ZXMsIH0gZnJvbSBcIi4uL2NhY2hpbmcvaW5kZXguanNcIjtcbmltcG9ydCB7IHJlZ2lzdGVyR2xvYmFsQ2FjaGUgfSBmcm9tIFwiLi4vY2FjaGluZy9nZXRNZW1vcnlJbnRlcm5hbHMuanNcIjtcbnZhciBwcmludENhY2hlO1xuZXhwb3J0IHZhciBwcmludCA9IE9iamVjdC5hc3NpZ24oZnVuY3Rpb24gKGFzdCkge1xuICAgIHZhciByZXN1bHQgPSBwcmludENhY2hlLmdldChhc3QpO1xuICAgIGlmICghcmVzdWx0KSB7XG4gICAgICAgIHJlc3VsdCA9IG9yaWdQcmludChhc3QpO1xuICAgICAgICBwcmludENhY2hlLnNldChhc3QsIHJlc3VsdCk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59LCB7XG4gICAgcmVzZXQ6IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcHJpbnRDYWNoZSA9IG5ldyBBdXRvQ2xlYW5lZFdlYWtDYWNoZShjYWNoZVNpemVzLnByaW50IHx8IDIwMDAgLyogZGVmYXVsdENhY2hlU2l6ZXMucHJpbnQgKi8pO1xuICAgIH0sXG59KTtcbnByaW50LnJlc2V0KCk7XG5pZiAoZ2xvYmFsVGhpcy5fX0RFVl9fICE9PSBmYWxzZSkge1xuICAgIHJlZ2lzdGVyR2xvYmFsQ2FjaGUoXCJwcmludFwiLCBmdW5jdGlvbiAoKSB7IHJldHVybiAocHJpbnRDYWNoZSA/IHByaW50Q2FjaGUuc2l6ZSA6IDApOyB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXByaW50LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/graphql/print.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createFulfilledPromise: () => (/* binding */ createFulfilledPromise),\n/* harmony export */   createRejectedPromise: () => (/* binding */ createRejectedPromise),\n/* harmony export */   isStatefulPromise: () => (/* binding */ isStatefulPromise),\n/* harmony export */   wrapPromiseWithState: () => (/* binding */ wrapPromiseWithState)\n/* harmony export */ });\nfunction createFulfilledPromise(value) {\n    var promise = Promise.resolve(value);\n    promise.status = \"fulfilled\";\n    promise.value = value;\n    return promise;\n}\nfunction createRejectedPromise(reason) {\n    var promise = Promise.reject(reason);\n    // prevent potential edge cases leaking unhandled error rejections\n    promise.catch(function () { });\n    promise.status = \"rejected\";\n    promise.reason = reason;\n    return promise;\n}\nfunction isStatefulPromise(promise) {\n    return \"status\" in promise;\n}\nfunction wrapPromiseWithState(promise) {\n    if (isStatefulPromise(promise)) {\n        return promise;\n    }\n    var pendingPromise = promise;\n    pendingPromise.status = \"pending\";\n    pendingPromise.then(function (value) {\n        if (pendingPromise.status === \"pending\") {\n            var fulfilledPromise = pendingPromise;\n            fulfilledPromise.status = \"fulfilled\";\n            fulfilledPromise.value = value;\n        }\n    }, function (reason) {\n        if (pendingPromise.status === \"pending\") {\n            var rejectedPromise = pendingPromise;\n            rejectedPromise.status = \"rejected\";\n            rejectedPromise.reason = reason;\n        }\n    });\n    return promise;\n}\n//# sourceMappingURL=decoration.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/@apollo+client@3.13.9_@type_34297490c41d970d04f3a473b1435802/node_modules/@apollo/client/utilities/promises/decoration.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js":
/*!******************************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   disableExperimentalFragmentVariables: () => (/* binding */ disableExperimentalFragmentVariables),\n/* harmony export */   disableFragmentWarnings: () => (/* binding */ disableFragmentWarnings),\n/* harmony export */   enableExperimentalFragmentVariables: () => (/* binding */ enableExperimentalFragmentVariables),\n/* harmony export */   gql: () => (/* binding */ gql),\n/* harmony export */   resetCaches: () => (/* binding */ resetCaches)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/../node_modules/.pnpm/tslib@2.8.1/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var graphql__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/parser.mjs\");\n\n\nvar docCache = new Map();\nvar fragmentSourceMap = new Map();\nvar printFragmentWarnings = true;\nvar experimentalFragmentVariables = false;\nfunction normalize(string) {\n    return string.replace(/[\\s,]+/g, ' ').trim();\n}\nfunction cacheKeyFromLoc(loc) {\n    return normalize(loc.source.body.substring(loc.start, loc.end));\n}\nfunction processFragments(ast) {\n    var seenKeys = new Set();\n    var definitions = [];\n    ast.definitions.forEach(function (fragmentDefinition) {\n        if (fragmentDefinition.kind === 'FragmentDefinition') {\n            var fragmentName = fragmentDefinition.name.value;\n            var sourceKey = cacheKeyFromLoc(fragmentDefinition.loc);\n            var sourceKeySet = fragmentSourceMap.get(fragmentName);\n            if (sourceKeySet && !sourceKeySet.has(sourceKey)) {\n                if (printFragmentWarnings) {\n                    console.warn(\"Warning: fragment with name \" + fragmentName + \" already exists.\\n\"\n                        + \"graphql-tag enforces all fragment names across your application to be unique; read more about\\n\"\n                        + \"this in the docs: http://dev.apollodata.com/core/fragments.html#unique-names\");\n                }\n            }\n            else if (!sourceKeySet) {\n                fragmentSourceMap.set(fragmentName, sourceKeySet = new Set);\n            }\n            sourceKeySet.add(sourceKey);\n            if (!seenKeys.has(sourceKey)) {\n                seenKeys.add(sourceKey);\n                definitions.push(fragmentDefinition);\n            }\n        }\n        else {\n            definitions.push(fragmentDefinition);\n        }\n    });\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_0__.__assign)({}, ast), { definitions: definitions });\n}\nfunction stripLoc(doc) {\n    var workSet = new Set(doc.definitions);\n    workSet.forEach(function (node) {\n        if (node.loc)\n            delete node.loc;\n        Object.keys(node).forEach(function (key) {\n            var value = node[key];\n            if (value && typeof value === 'object') {\n                workSet.add(value);\n            }\n        });\n    });\n    var loc = doc.loc;\n    if (loc) {\n        delete loc.startToken;\n        delete loc.endToken;\n    }\n    return doc;\n}\nfunction parseDocument(source) {\n    var cacheKey = normalize(source);\n    if (!docCache.has(cacheKey)) {\n        var parsed = (0,graphql__WEBPACK_IMPORTED_MODULE_1__.parse)(source, {\n            experimentalFragmentVariables: experimentalFragmentVariables,\n            allowLegacyFragmentVariables: experimentalFragmentVariables\n        });\n        if (!parsed || parsed.kind !== 'Document') {\n            throw new Error('Not a valid GraphQL document.');\n        }\n        docCache.set(cacheKey, stripLoc(processFragments(parsed)));\n    }\n    return docCache.get(cacheKey);\n}\nfunction gql(literals) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    if (typeof literals === 'string') {\n        literals = [literals];\n    }\n    var result = literals[0];\n    args.forEach(function (arg, i) {\n        if (arg && arg.kind === 'Document') {\n            result += arg.loc.source.body;\n        }\n        else {\n            result += arg;\n        }\n        result += literals[i + 1];\n    });\n    return parseDocument(result);\n}\nfunction resetCaches() {\n    docCache.clear();\n    fragmentSourceMap.clear();\n}\nfunction disableFragmentWarnings() {\n    printFragmentWarnings = false;\n}\nfunction enableExperimentalFragmentVariables() {\n    experimentalFragmentVariables = true;\n}\nfunction disableExperimentalFragmentVariables() {\n    experimentalFragmentVariables = false;\n}\nvar extras = {\n    gql: gql,\n    resetCaches: resetCaches,\n    disableFragmentWarnings: disableFragmentWarnings,\n    enableExperimentalFragmentVariables: enableExperimentalFragmentVariables,\n    disableExperimentalFragmentVariables: disableExperimentalFragmentVariables\n};\n(function (gql_1) {\n    gql_1.gql = extras.gql, gql_1.resetCaches = extras.resetCaches, gql_1.disableFragmentWarnings = extras.disableFragmentWarnings, gql_1.enableExperimentalFragmentVariables = extras.enableExperimentalFragmentVariables, gql_1.disableExperimentalFragmentVariables = extras.disableExperimentalFragmentVariables;\n})(gql || (gql = {}));\ngql[\"default\"] = gql;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (gql);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql-tag@2.12.6_graphql@16.11.0/node_modules/graphql-tag/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/GraphQLError.mjs":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/GraphQLError.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GraphQLError: () => (/* binding */ GraphQLError),\n/* harmony export */   formatError: () => (/* binding */ formatError),\n/* harmony export */   printError: () => (/* binding */ printError)\n/* harmony export */ });\n/* harmony import */ var _jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/isObjectLike.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/isObjectLike.mjs\");\n/* harmony import */ var _language_location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../language/location.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/location.mjs\");\n/* harmony import */ var _language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../language/printLocation.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printLocation.mjs\");\n\n\n\n\nfunction toNormalizedOptions(args) {\n  const firstArg = args[0];\n\n  if (firstArg == null || 'kind' in firstArg || 'length' in firstArg) {\n    return {\n      nodes: firstArg,\n      source: args[1],\n      positions: args[2],\n      path: args[3],\n      originalError: args[4],\n      extensions: args[5],\n    };\n  }\n\n  return firstArg;\n}\n/**\n * A GraphQLError describes an Error found during the parse, validate, or\n * execute phases of performing a GraphQL operation. In addition to a message\n * and stack trace, it also includes information about the locations in a\n * GraphQL document and/or execution result that correspond to the Error.\n */\n\nclass GraphQLError extends Error {\n  /**\n   * An array of `{ line, column }` locations within the source GraphQL document\n   * which correspond to this error.\n   *\n   * Errors during validation often contain multiple locations, for example to\n   * point out two things with the same name. Errors during execution include a\n   * single location, the field which produced the error.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array describing the JSON-path into the execution response which\n   * corresponds to this error. Only included for errors during execution.\n   *\n   * Enumerable, and appears in the result of JSON.stringify().\n   */\n\n  /**\n   * An array of GraphQL AST Nodes corresponding to this error.\n   */\n\n  /**\n   * The source GraphQL document for the first location of this error.\n   *\n   * Note that if this Error represents more than one node, the source may not\n   * represent nodes after the first node.\n   */\n\n  /**\n   * An array of character offsets within the source GraphQL document\n   * which correspond to this error.\n   */\n\n  /**\n   * The original error thrown from a field resolver during execution.\n   */\n\n  /**\n   * Extension fields to add to the formatted error.\n   */\n\n  /**\n   * @deprecated Please use the `GraphQLErrorOptions` constructor overload instead.\n   */\n  constructor(message, ...rawArgs) {\n    var _this$nodes, _nodeLocations$, _ref;\n\n    const { nodes, source, positions, path, originalError, extensions } =\n      toNormalizedOptions(rawArgs);\n    super(message);\n    this.name = 'GraphQLError';\n    this.path = path !== null && path !== void 0 ? path : undefined;\n    this.originalError =\n      originalError !== null && originalError !== void 0\n        ? originalError\n        : undefined; // Compute list of blame nodes.\n\n    this.nodes = undefinedIfEmpty(\n      Array.isArray(nodes) ? nodes : nodes ? [nodes] : undefined,\n    );\n    const nodeLocations = undefinedIfEmpty(\n      (_this$nodes = this.nodes) === null || _this$nodes === void 0\n        ? void 0\n        : _this$nodes.map((node) => node.loc).filter((loc) => loc != null),\n    ); // Compute locations in the source for the given nodes/positions.\n\n    this.source =\n      source !== null && source !== void 0\n        ? source\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : (_nodeLocations$ = nodeLocations[0]) === null ||\n          _nodeLocations$ === void 0\n        ? void 0\n        : _nodeLocations$.source;\n    this.positions =\n      positions !== null && positions !== void 0\n        ? positions\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => loc.start);\n    this.locations =\n      positions && source\n        ? positions.map((pos) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(source, pos))\n        : nodeLocations === null || nodeLocations === void 0\n        ? void 0\n        : nodeLocations.map((loc) => (0,_language_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(loc.source, loc.start));\n    const originalExtensions = (0,_jsutils_isObjectLike_mjs__WEBPACK_IMPORTED_MODULE_1__.isObjectLike)(\n      originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions,\n    )\n      ? originalError === null || originalError === void 0\n        ? void 0\n        : originalError.extensions\n      : undefined;\n    this.extensions =\n      (_ref =\n        extensions !== null && extensions !== void 0\n          ? extensions\n          : originalExtensions) !== null && _ref !== void 0\n        ? _ref\n        : Object.create(null); // Only properties prescribed by the spec should be enumerable.\n    // Keep the rest as non-enumerable.\n\n    Object.defineProperties(this, {\n      message: {\n        writable: true,\n        enumerable: true,\n      },\n      name: {\n        enumerable: false,\n      },\n      nodes: {\n        enumerable: false,\n      },\n      source: {\n        enumerable: false,\n      },\n      positions: {\n        enumerable: false,\n      },\n      originalError: {\n        enumerable: false,\n      },\n    }); // Include (non-enumerable) stack trace.\n\n    /* c8 ignore start */\n    // FIXME: https://github.com/graphql/graphql-js/issues/2317\n\n    if (\n      originalError !== null &&\n      originalError !== void 0 &&\n      originalError.stack\n    ) {\n      Object.defineProperty(this, 'stack', {\n        value: originalError.stack,\n        writable: true,\n        configurable: true,\n      });\n    } else if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, GraphQLError);\n    } else {\n      Object.defineProperty(this, 'stack', {\n        value: Error().stack,\n        writable: true,\n        configurable: true,\n      });\n    }\n    /* c8 ignore stop */\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'GraphQLError';\n  }\n\n  toString() {\n    let output = this.message;\n\n    if (this.nodes) {\n      for (const node of this.nodes) {\n        if (node.loc) {\n          output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printLocation)(node.loc);\n        }\n      }\n    } else if (this.source && this.locations) {\n      for (const location of this.locations) {\n        output += '\\n\\n' + (0,_language_printLocation_mjs__WEBPACK_IMPORTED_MODULE_2__.printSourceLocation)(this.source, location);\n      }\n    }\n\n    return output;\n  }\n\n  toJSON() {\n    const formattedError = {\n      message: this.message,\n    };\n\n    if (this.locations != null) {\n      formattedError.locations = this.locations;\n    }\n\n    if (this.path != null) {\n      formattedError.path = this.path;\n    }\n\n    if (this.extensions != null && Object.keys(this.extensions).length > 0) {\n      formattedError.extensions = this.extensions;\n    }\n\n    return formattedError;\n  }\n}\n\nfunction undefinedIfEmpty(array) {\n  return array === undefined || array.length === 0 ? undefined : array;\n}\n/**\n * See: https://spec.graphql.org/draft/#sec-Errors\n */\n\n/**\n * Prints a GraphQLError to a string, representing useful location information\n * about the error's position in the source.\n *\n * @deprecated Please use `error.toString` instead. Will be removed in v17\n */\nfunction printError(error) {\n  return error.toString();\n}\n/**\n * Given a GraphQLError, format it according to the rules described by the\n * Response Format, Errors section of the GraphQL Specification.\n *\n * @deprecated Please use `error.toJSON` instead. Will be removed in v17\n */\n\nfunction formatError(error) {\n  return error.toJSON();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/GraphQLError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/syntaxError.mjs":
/*!****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/syntaxError.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   syntaxError: () => (/* binding */ syntaxError)\n/* harmony export */ });\n/* harmony import */ var _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./GraphQLError.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/GraphQLError.mjs\");\n\n/**\n * Produces a GraphQLError representing a syntax error, containing useful\n * descriptive information about the syntax error's position in the source.\n */\n\nfunction syntaxError(source, position, description) {\n  return new _GraphQLError_mjs__WEBPACK_IMPORTED_MODULE_0__.GraphQLError(`Syntax Error: ${description}`, {\n    source,\n    positions: [position],\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMS4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2Vycm9yL3N5bnRheEVycm9yLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrRDtBQUNsRDtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQLGFBQWEsMkRBQVksa0JBQWtCLFlBQVk7QUFDdkQ7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxncmFwaHFsQDE2LjExLjBcXG5vZGVfbW9kdWxlc1xcZ3JhcGhxbFxcZXJyb3JcXHN5bnRheEVycm9yLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcmFwaFFMRXJyb3IgfSBmcm9tICcuL0dyYXBoUUxFcnJvci5tanMnO1xuLyoqXG4gKiBQcm9kdWNlcyBhIEdyYXBoUUxFcnJvciByZXByZXNlbnRpbmcgYSBzeW50YXggZXJyb3IsIGNvbnRhaW5pbmcgdXNlZnVsXG4gKiBkZXNjcmlwdGl2ZSBpbmZvcm1hdGlvbiBhYm91dCB0aGUgc3ludGF4IGVycm9yJ3MgcG9zaXRpb24gaW4gdGhlIHNvdXJjZS5cbiAqL1xuXG5leHBvcnQgZnVuY3Rpb24gc3ludGF4RXJyb3Ioc291cmNlLCBwb3NpdGlvbiwgZGVzY3JpcHRpb24pIHtcbiAgcmV0dXJuIG5ldyBHcmFwaFFMRXJyb3IoYFN5bnRheCBFcnJvcjogJHtkZXNjcmlwdGlvbn1gLCB7XG4gICAgc291cmNlLFxuICAgIHBvc2l0aW9uczogW3Bvc2l0aW9uXSxcbiAgfSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/syntaxError.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/instanceOf.mjs":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/instanceOf.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   instanceOf: () => (/* binding */ instanceOf)\n/* harmony export */ });\n/* harmony import */ var _inspect_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inspect.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/inspect.mjs\");\n\n/* c8 ignore next 3 */\n\nconst isProduction =\n  globalThis.process && // eslint-disable-next-line no-undef\n  \"development\" === 'production';\n/**\n * A replacement for instanceof which includes an error warning when multi-realm\n * constructors are detected.\n * See: https://expressjs.com/en/advanced/best-practice-performance.html#set-node_env-to-production\n * See: https://webpack.js.org/guides/production/\n */\n\nconst instanceOf =\n  /* c8 ignore next 6 */\n  // FIXME: https://github.com/graphql/graphql-js/issues/2317\n  isProduction\n    ? function instanceOf(value, constructor) {\n        return value instanceof constructor;\n      }\n    : function instanceOf(value, constructor) {\n        if (value instanceof constructor) {\n          return true;\n        }\n\n        if (typeof value === 'object' && value !== null) {\n          var _value$constructor;\n\n          // Prefer Symbol.toStringTag since it is immune to minification.\n          const className = constructor.prototype[Symbol.toStringTag];\n          const valueClassName = // We still need to support constructor's name to detect conflicts with older versions of this library.\n            Symbol.toStringTag in value // @ts-expect-error TS bug see, https://github.com/microsoft/TypeScript/issues/38009\n              ? value[Symbol.toStringTag]\n              : (_value$constructor = value.constructor) === null ||\n                _value$constructor === void 0\n              ? void 0\n              : _value$constructor.name;\n\n          if (className === valueClassName) {\n            const stringifiedValue = (0,_inspect_mjs__WEBPACK_IMPORTED_MODULE_0__.inspect)(value);\n            throw new Error(`Cannot use ${className} \"${stringifiedValue}\" from another module or realm.\n\nEnsure that there is only one instance of \"graphql\" in the node_modules\ndirectory. If different versions of \"graphql\" are the dependencies of other\nrelied on modules, use \"resolutions\" to ensure only one version is installed.\n\nhttps://yarnpkg.com/en/docs/selective-version-resolutions\n\nDuplicate \"graphql\" modules cannot be used at the same time since different\nversions may have different capabilities and behavior. The data from one\nversion used in the function from another could produce confusing and\nspurious results.`);\n          }\n        }\n\n        return false;\n      };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/instanceOf.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/invariant.mjs":
/*!****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/invariant.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   invariant: () => (/* binding */ invariant)\n/* harmony export */ });\nfunction invariant(condition, message) {\n  const booleanCondition = Boolean(condition);\n\n  if (!booleanCondition) {\n    throw new Error(\n      message != null ? message : 'Unexpected invariant triggered.',\n    );\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMS4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaW52YXJpYW50Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZ3JhcGhxbEAxNi4xMS4wXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGludmFyaWFudC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGludmFyaWFudChjb25kaXRpb24sIG1lc3NhZ2UpIHtcbiAgY29uc3QgYm9vbGVhbkNvbmRpdGlvbiA9IEJvb2xlYW4oY29uZGl0aW9uKTtcblxuICBpZiAoIWJvb2xlYW5Db25kaXRpb24pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICBtZXNzYWdlICE9IG51bGwgPyBtZXNzYWdlIDogJ1VuZXhwZWN0ZWQgaW52YXJpYW50IHRyaWdnZXJlZC4nLFxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/invariant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/isObjectLike.mjs":
/*!*******************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/isObjectLike.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isObjectLike: () => (/* binding */ isObjectLike)\n/* harmony export */ });\n/**\n * Return true if `value` is object-like. A value is object-like if it's not\n * `null` and has a `typeof` result of \"object\".\n */\nfunction isObjectLike(value) {\n  return typeof value == 'object' && value !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMS4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2pzdXRpbHMvaXNPYmplY3RMaWtlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcZ3JhcGhxbEAxNi4xMS4wXFxub2RlX21vZHVsZXNcXGdyYXBocWxcXGpzdXRpbHNcXGlzT2JqZWN0TGlrZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZXR1cm4gdHJ1ZSBpZiBgdmFsdWVgIGlzIG9iamVjdC1saWtlLiBBIHZhbHVlIGlzIG9iamVjdC1saWtlIGlmIGl0J3Mgbm90XG4gKiBgbnVsbGAgYW5kIGhhcyBhIGB0eXBlb2ZgIHJlc3VsdCBvZiBcIm9iamVjdFwiLlxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNPYmplY3RMaWtlKHZhbHVlKSB7XG4gIHJldHVybiB0eXBlb2YgdmFsdWUgPT0gJ29iamVjdCcgJiYgdmFsdWUgIT09IG51bGw7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/isObjectLike.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/blockString.mjs":
/*!*******************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/blockString.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dedentBlockStringLines: () => (/* binding */ dedentBlockStringLines),\n/* harmony export */   isPrintableAsBlockString: () => (/* binding */ isPrintableAsBlockString),\n/* harmony export */   printBlockString: () => (/* binding */ printBlockString)\n/* harmony export */ });\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/characterClasses.mjs\");\n\n/**\n * Produces the value of a block string from its parsed raw value, similar to\n * CoffeeScript's block string, Python's docstring trim or Ruby's strip_heredoc.\n *\n * This implements the GraphQL spec's BlockStringValue() static algorithm.\n *\n * @internal\n */\n\nfunction dedentBlockStringLines(lines) {\n  var _firstNonEmptyLine2;\n\n  let commonIndent = Number.MAX_SAFE_INTEGER;\n  let firstNonEmptyLine = null;\n  let lastNonEmptyLine = -1;\n\n  for (let i = 0; i < lines.length; ++i) {\n    var _firstNonEmptyLine;\n\n    const line = lines[i];\n    const indent = leadingWhitespace(line);\n\n    if (indent === line.length) {\n      continue; // skip empty lines\n    }\n\n    firstNonEmptyLine =\n      (_firstNonEmptyLine = firstNonEmptyLine) !== null &&\n      _firstNonEmptyLine !== void 0\n        ? _firstNonEmptyLine\n        : i;\n    lastNonEmptyLine = i;\n\n    if (i !== 0 && indent < commonIndent) {\n      commonIndent = indent;\n    }\n  }\n\n  return lines // Remove common indentation from all lines but first.\n    .map((line, i) => (i === 0 ? line : line.slice(commonIndent))) // Remove leading and trailing blank lines.\n    .slice(\n      (_firstNonEmptyLine2 = firstNonEmptyLine) !== null &&\n        _firstNonEmptyLine2 !== void 0\n        ? _firstNonEmptyLine2\n        : 0,\n      lastNonEmptyLine + 1,\n    );\n}\n\nfunction leadingWhitespace(str) {\n  let i = 0;\n\n  while (i < str.length && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(str.charCodeAt(i))) {\n    ++i;\n  }\n\n  return i;\n}\n/**\n * @internal\n */\n\nfunction isPrintableAsBlockString(value) {\n  if (value === '') {\n    return true; // empty string is printable\n  }\n\n  let isEmptyLine = true;\n  let hasIndent = false;\n  let hasCommonIndent = true;\n  let seenNonEmptyLine = false;\n\n  for (let i = 0; i < value.length; ++i) {\n    switch (value.codePointAt(i)) {\n      case 0x0000:\n      case 0x0001:\n      case 0x0002:\n      case 0x0003:\n      case 0x0004:\n      case 0x0005:\n      case 0x0006:\n      case 0x0007:\n      case 0x0008:\n      case 0x000b:\n      case 0x000c:\n      case 0x000e:\n      case 0x000f:\n        return false;\n      // Has non-printable characters\n\n      case 0x000d:\n        //  \\r\n        return false;\n      // Has \\r or \\r\\n which will be replaced as \\n\n\n      case 10:\n        //  \\n\n        if (isEmptyLine && !seenNonEmptyLine) {\n          return false; // Has leading new line\n        }\n\n        seenNonEmptyLine = true;\n        isEmptyLine = true;\n        hasIndent = false;\n        break;\n\n      case 9: //   \\t\n\n      case 32:\n        //  <space>\n        hasIndent || (hasIndent = isEmptyLine);\n        break;\n\n      default:\n        hasCommonIndent && (hasCommonIndent = hasIndent);\n        isEmptyLine = false;\n    }\n  }\n\n  if (isEmptyLine) {\n    return false; // Has trailing empty lines\n  }\n\n  if (hasCommonIndent && seenNonEmptyLine) {\n    return false; // Has internal indent\n  }\n\n  return true;\n}\n/**\n * Print a block string in the indented block form by adding a leading and\n * trailing blank line. However, if a block string starts with whitespace and is\n * a single-line, adding a leading blank line would strip that whitespace.\n *\n * @internal\n */\n\nfunction printBlockString(value, options) {\n  const escapedValue = value.replace(/\"\"\"/g, '\\\\\"\"\"'); // Expand a block string's raw value into independent lines.\n\n  const lines = escapedValue.split(/\\r\\n|[\\n\\r]/g);\n  const isSingleLine = lines.length === 1; // If common indentation is found we can fix some of those cases by adding leading new line\n\n  const forceLeadingNewLine =\n    lines.length > 1 &&\n    lines\n      .slice(1)\n      .every((line) => line.length === 0 || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(line.charCodeAt(0))); // Trailing triple quotes just looks confusing but doesn't force trailing new line\n\n  const hasTrailingTripleQuotes = escapedValue.endsWith('\\\\\"\"\"'); // Trailing quote (single or double) or slash forces trailing new line\n\n  const hasTrailingQuote = value.endsWith('\"') && !hasTrailingTripleQuotes;\n  const hasTrailingSlash = value.endsWith('\\\\');\n  const forceTrailingNewline = hasTrailingQuote || hasTrailingSlash;\n  const printAsMultipleLines =\n    !(options !== null && options !== void 0 && options.minimize) && // add leading and trailing new lines only if it improves readability\n    (!isSingleLine ||\n      value.length > 70 ||\n      forceTrailingNewline ||\n      forceLeadingNewLine ||\n      hasTrailingTripleQuotes);\n  let result = ''; // Format a multi-line block quote to account for leading space.\n\n  const skipLeadingNewLine = isSingleLine && (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_0__.isWhiteSpace)(value.charCodeAt(0));\n\n  if ((printAsMultipleLines && !skipLeadingNewLine) || forceLeadingNewLine) {\n    result += '\\n';\n  }\n\n  result += escapedValue;\n\n  if (printAsMultipleLines || forceTrailingNewline) {\n    result += '\\n';\n  }\n\n  return '\"\"\"' + result + '\"\"\"';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/blockString.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/characterClasses.mjs":
/*!************************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/characterClasses.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isDigit: () => (/* binding */ isDigit),\n/* harmony export */   isLetter: () => (/* binding */ isLetter),\n/* harmony export */   isNameContinue: () => (/* binding */ isNameContinue),\n/* harmony export */   isNameStart: () => (/* binding */ isNameStart),\n/* harmony export */   isWhiteSpace: () => (/* binding */ isWhiteSpace)\n/* harmony export */ });\n/**\n * ```\n * WhiteSpace ::\n *   - \"Horizontal Tab (U+0009)\"\n *   - \"Space (U+0020)\"\n * ```\n * @internal\n */\nfunction isWhiteSpace(code) {\n  return code === 0x0009 || code === 0x0020;\n}\n/**\n * ```\n * Digit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n * ```\n * @internal\n */\n\nfunction isDigit(code) {\n  return code >= 0x0030 && code <= 0x0039;\n}\n/**\n * ```\n * Letter :: one of\n *   - `A` `B` `C` `D` `E` `F` `G` `H` `I` `J` `K` `L` `M`\n *   - `N` `O` `P` `Q` `R` `S` `T` `U` `V` `W` `X` `Y` `Z`\n *   - `a` `b` `c` `d` `e` `f` `g` `h` `i` `j` `k` `l` `m`\n *   - `n` `o` `p` `q` `r` `s` `t` `u` `v` `w` `x` `y` `z`\n * ```\n * @internal\n */\n\nfunction isLetter(code) {\n  return (\n    (code >= 0x0061 && code <= 0x007a) || // A-Z\n    (code >= 0x0041 && code <= 0x005a) // a-z\n  );\n}\n/**\n * ```\n * NameStart ::\n *   - Letter\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameStart(code) {\n  return isLetter(code) || code === 0x005f;\n}\n/**\n * ```\n * NameContinue ::\n *   - Letter\n *   - Digit\n *   - `_`\n * ```\n * @internal\n */\n\nfunction isNameContinue(code) {\n  return isLetter(code) || isDigit(code) || code === 0x005f;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/characterClasses.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/directiveLocation.mjs":
/*!*************************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/directiveLocation.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectiveLocation: () => (/* binding */ DirectiveLocation)\n/* harmony export */ });\n/**\n * The set of allowed directive location values.\n */\nvar DirectiveLocation;\n\n(function (DirectiveLocation) {\n  DirectiveLocation['QUERY'] = 'QUERY';\n  DirectiveLocation['MUTATION'] = 'MUTATION';\n  DirectiveLocation['SUBSCRIPTION'] = 'SUBSCRIPTION';\n  DirectiveLocation['FIELD'] = 'FIELD';\n  DirectiveLocation['FRAGMENT_DEFINITION'] = 'FRAGMENT_DEFINITION';\n  DirectiveLocation['FRAGMENT_SPREAD'] = 'FRAGMENT_SPREAD';\n  DirectiveLocation['INLINE_FRAGMENT'] = 'INLINE_FRAGMENT';\n  DirectiveLocation['VARIABLE_DEFINITION'] = 'VARIABLE_DEFINITION';\n  DirectiveLocation['SCHEMA'] = 'SCHEMA';\n  DirectiveLocation['SCALAR'] = 'SCALAR';\n  DirectiveLocation['OBJECT'] = 'OBJECT';\n  DirectiveLocation['FIELD_DEFINITION'] = 'FIELD_DEFINITION';\n  DirectiveLocation['ARGUMENT_DEFINITION'] = 'ARGUMENT_DEFINITION';\n  DirectiveLocation['INTERFACE'] = 'INTERFACE';\n  DirectiveLocation['UNION'] = 'UNION';\n  DirectiveLocation['ENUM'] = 'ENUM';\n  DirectiveLocation['ENUM_VALUE'] = 'ENUM_VALUE';\n  DirectiveLocation['INPUT_OBJECT'] = 'INPUT_OBJECT';\n  DirectiveLocation['INPUT_FIELD_DEFINITION'] = 'INPUT_FIELD_DEFINITION';\n})(DirectiveLocation || (DirectiveLocation = {}));\n\n\n/**\n * The enum type representing the directive location values.\n *\n * @deprecated Please use `DirectiveLocation`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/directiveLocation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/lexer.mjs":
/*!*************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/lexer.mjs ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Lexer: () => (/* binding */ Lexer),\n/* harmony export */   isPunctuatorTokenKind: () => (/* binding */ isPunctuatorTokenKind)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ast.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blockString.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./characterClasses.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/characterClasses.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n/**\n * Given a Source object, creates a Lexer for that source.\n * A Lexer is a stateful stream generator in that every time\n * it is advanced, it returns the next token in the Source. Assuming the\n * source lexes, the final Token emitted by the lexer will be of kind\n * EOF, after which the lexer will repeatedly return the same EOF token\n * whenever called.\n */\n\nclass Lexer {\n  /**\n   * The previously focused non-ignored token.\n   */\n\n  /**\n   * The currently focused non-ignored token.\n   */\n\n  /**\n   * The (1-indexed) line containing the current token.\n   */\n\n  /**\n   * The character offset at which the current line begins.\n   */\n  constructor(source) {\n    const startOfFileToken = new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SOF, 0, 0, 0, 0);\n    this.source = source;\n    this.lastToken = startOfFileToken;\n    this.token = startOfFileToken;\n    this.line = 1;\n    this.lineStart = 0;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Lexer';\n  }\n  /**\n   * Advances the token stream to the next non-ignored token.\n   */\n\n  advance() {\n    this.lastToken = this.token;\n    const token = (this.token = this.lookahead());\n    return token;\n  }\n  /**\n   * Looks ahead and returns the next non-ignored token, but does not change\n   * the state of Lexer.\n   */\n\n  lookahead() {\n    let token = this.token;\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF) {\n      do {\n        if (token.next) {\n          token = token.next;\n        } else {\n          // Read the next token and form a link in the token linked-list.\n          const nextToken = readNextToken(this, token.end); // @ts-expect-error next is only mutable during parsing.\n\n          token.next = nextToken; // @ts-expect-error prev is only mutable during parsing.\n\n          nextToken.prev = token;\n          token = nextToken;\n        }\n      } while (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT);\n    }\n\n    return token;\n  }\n}\n/**\n * @internal\n */\n\nfunction isPunctuatorTokenKind(kind) {\n  return (\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE ||\n    kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R\n  );\n}\n/**\n * A Unicode scalar value is any Unicode code point except surrogate code\n * points. In other words, the inclusive ranges of values 0x0000 to 0xD7FF and\n * 0xE000 to 0x10FFFF.\n *\n * SourceCharacter ::\n *   - \"Any Unicode scalar value\"\n */\n\nfunction isUnicodeScalarValue(code) {\n  return (\n    (code >= 0x0000 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0x10ffff)\n  );\n}\n/**\n * The GraphQL specification defines source text as a sequence of unicode scalar\n * values (which Unicode defines to exclude surrogate code points). However\n * JavaScript defines strings as a sequence of UTF-16 code units which may\n * include surrogates. A surrogate pair is a valid source character as it\n * encodes a supplementary code point (above U+FFFF), but unpaired surrogate\n * code points are not valid source characters.\n */\n\nfunction isSupplementaryCodePoint(body, location) {\n  return (\n    isLeadingSurrogate(body.charCodeAt(location)) &&\n    isTrailingSurrogate(body.charCodeAt(location + 1))\n  );\n}\n\nfunction isLeadingSurrogate(code) {\n  return code >= 0xd800 && code <= 0xdbff;\n}\n\nfunction isTrailingSurrogate(code) {\n  return code >= 0xdc00 && code <= 0xdfff;\n}\n/**\n * Prints the code point (or end of file reference) at a given location in a\n * source for use in error messages.\n *\n * Printable ASCII is printed quoted, while other points are printed in Unicode\n * code point form (ie. U+1234).\n */\n\nfunction printCodePointAt(lexer, location) {\n  const code = lexer.source.body.codePointAt(location);\n\n  if (code === undefined) {\n    return _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF;\n  } else if (code >= 0x0020 && code <= 0x007e) {\n    // Printable ASCII\n    const char = String.fromCodePoint(code);\n    return char === '\"' ? \"'\\\"'\" : `\"${char}\"`;\n  } // Unicode code point\n\n  return 'U+' + code.toString(16).toUpperCase().padStart(4, '0');\n}\n/**\n * Create a token with line and column location information.\n */\n\nfunction createToken(lexer, kind, start, end, value) {\n  const line = lexer.line;\n  const col = 1 + start - lexer.lineStart;\n  return new _ast_mjs__WEBPACK_IMPORTED_MODULE_0__.Token(kind, start, end, line, col, value);\n}\n/**\n * Gets the next token from the source starting at the given position.\n *\n * This skips over whitespace until it finds the next lexable token, then lexes\n * punctuators immediately or calls the appropriate helper function for more\n * complicated tokens.\n */\n\nfunction readNextToken(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // SourceCharacter\n\n    switch (code) {\n      // Ignored ::\n      //   - UnicodeBOM\n      //   - WhiteSpace\n      //   - LineTerminator\n      //   - Comment\n      //   - Comma\n      //\n      // UnicodeBOM :: \"Byte Order Mark (U+FEFF)\"\n      //\n      // WhiteSpace ::\n      //   - \"Horizontal Tab (U+0009)\"\n      //   - \"Space (U+0020)\"\n      //\n      // Comma :: ,\n      case 0xfeff: // <BOM>\n\n      case 0x0009: // \\t\n\n      case 0x0020: // <space>\n\n      case 0x002c:\n        // ,\n        ++position;\n        continue;\n      // LineTerminator ::\n      //   - \"New Line (U+000A)\"\n      //   - \"Carriage Return (U+000D)\" [lookahead != \"New Line (U+000A)\"]\n      //   - \"Carriage Return (U+000D)\" \"New Line (U+000A)\"\n\n      case 0x000a:\n        // \\n\n        ++position;\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n\n      case 0x000d:\n        // \\r\n        if (body.charCodeAt(position + 1) === 0x000a) {\n          position += 2;\n        } else {\n          ++position;\n        }\n\n        ++lexer.line;\n        lexer.lineStart = position;\n        continue;\n      // Comment\n\n      case 0x0023:\n        // #\n        return readComment(lexer, position);\n      // Token ::\n      //   - Punctuator\n      //   - Name\n      //   - IntValue\n      //   - FloatValue\n      //   - StringValue\n      //\n      // Punctuator :: one of ! $ & ( ) ... : = @ [ ] { | }\n\n      case 0x0021:\n        // !\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BANG, position, position + 1);\n\n      case 0x0024:\n        // $\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.DOLLAR, position, position + 1);\n\n      case 0x0026:\n        // &\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AMP, position, position + 1);\n\n      case 0x0028:\n        // (\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_L, position, position + 1);\n\n      case 0x0029:\n        // )\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PAREN_R, position, position + 1);\n\n      case 0x002e:\n        // .\n        if (\n          body.charCodeAt(position + 1) === 0x002e &&\n          body.charCodeAt(position + 2) === 0x002e\n        ) {\n          return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.SPREAD, position, position + 3);\n        }\n\n        break;\n\n      case 0x003a:\n        // :\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COLON, position, position + 1);\n\n      case 0x003d:\n        // =\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EQUALS, position, position + 1);\n\n      case 0x0040:\n        // @\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.AT, position, position + 1);\n\n      case 0x005b:\n        // [\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_L, position, position + 1);\n\n      case 0x005d:\n        // ]\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACKET_R, position, position + 1);\n\n      case 0x007b:\n        // {\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_L, position, position + 1);\n\n      case 0x007c:\n        // |\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.PIPE, position, position + 1);\n\n      case 0x007d:\n        // }\n        return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BRACE_R, position, position + 1);\n      // StringValue\n\n      case 0x0022:\n        // \"\n        if (\n          body.charCodeAt(position + 1) === 0x0022 &&\n          body.charCodeAt(position + 2) === 0x0022\n        ) {\n          return readBlockString(lexer, position);\n        }\n\n        return readString(lexer, position);\n    } // IntValue | FloatValue (Digit | -)\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code) || code === 0x002d) {\n      return readNumber(lexer, position, code);\n    } // Name\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n      return readName(lexer, position);\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      code === 0x0027\n        ? 'Unexpected single quote character (\\'), did you mean to use a double quote (\")?'\n        : isUnicodeScalarValue(code) || isSupplementaryCodePoint(body, position)\n        ? `Unexpected character: ${printCodePointAt(lexer, position)}.`\n        : `Invalid character: ${printCodePointAt(lexer, position)}.`,\n    );\n  }\n\n  return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.EOF, bodyLength, bodyLength);\n}\n/**\n * Reads a comment token from the source file.\n *\n * ```\n * Comment :: # CommentChar* [lookahead != CommentChar]\n *\n * CommentChar :: SourceCharacter but not LineTerminator\n * ```\n */\n\nfunction readComment(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.COMMENT,\n    start,\n    position,\n    body.slice(start + 1, position),\n  );\n}\n/**\n * Reads a number token from the source file, either a FloatValue or an IntValue\n * depending on whether a FractionalPart or ExponentPart is encountered.\n *\n * ```\n * IntValue :: IntegerPart [lookahead != {Digit, `.`, NameStart}]\n *\n * IntegerPart ::\n *   - NegativeSign? 0\n *   - NegativeSign? NonZeroDigit Digit*\n *\n * NegativeSign :: -\n *\n * NonZeroDigit :: Digit but not `0`\n *\n * FloatValue ::\n *   - IntegerPart FractionalPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart FractionalPart [lookahead != {Digit, `.`, NameStart}]\n *   - IntegerPart ExponentPart [lookahead != {Digit, `.`, NameStart}]\n *\n * FractionalPart :: . Digit+\n *\n * ExponentPart :: ExponentIndicator Sign? Digit+\n *\n * ExponentIndicator :: one of `e` `E`\n *\n * Sign :: one of + -\n * ```\n */\n\nfunction readNumber(lexer, start, firstCode) {\n  const body = lexer.source.body;\n  let position = start;\n  let code = firstCode;\n  let isFloat = false; // NegativeSign (-)\n\n  if (code === 0x002d) {\n    code = body.charCodeAt(++position);\n  } // Zero (0)\n\n  if (code === 0x0030) {\n    code = body.charCodeAt(++position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(code)) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid number, unexpected digit after 0: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  } else {\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Full stop (.)\n\n  if (code === 0x002e) {\n    isFloat = true;\n    code = body.charCodeAt(++position);\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // E e\n\n  if (code === 0x0045 || code === 0x0065) {\n    isFloat = true;\n    code = body.charCodeAt(++position); // + -\n\n    if (code === 0x002b || code === 0x002d) {\n      code = body.charCodeAt(++position);\n    }\n\n    position = readDigits(lexer, position, code);\n    code = body.charCodeAt(position);\n  } // Numbers cannot be followed by . or NameStart\n\n  if (code === 0x002e || (0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameStart)(code)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      position,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        position,\n      )}.`,\n    );\n  }\n\n  return createToken(\n    lexer,\n    isFloat ? _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.FLOAT : _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.INT,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n/**\n * Returns the new position in the source after reading one or more digits.\n */\n\nfunction readDigits(lexer, start, firstCode) {\n  if (!(0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(firstCode)) {\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n      lexer.source,\n      start,\n      `Invalid number, expected digit but got: ${printCodePointAt(\n        lexer,\n        start,\n      )}.`,\n    );\n  }\n\n  const body = lexer.source.body;\n  let position = start + 1; // +1 to skip first firstCode\n\n  while ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isDigit)(body.charCodeAt(position))) {\n    ++position;\n  }\n\n  return position;\n}\n/**\n * Reads a single-quote string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"` [lookahead != `\"`]\n *   - `\"` StringCharacter+ `\"`\n *\n * StringCharacter ::\n *   - SourceCharacter but not `\"` or `\\` or LineTerminator\n *   - `\\u` EscapedUnicode\n *   - `\\` EscapedCharacter\n *\n * EscapedUnicode ::\n *   - `{` HexDigit+ `}`\n *   - HexDigit HexDigit HexDigit HexDigit\n *\n * EscapedCharacter :: one of `\"` `\\` `/` `b` `f` `n` `r` `t`\n * ```\n */\n\nfunction readString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n  let chunkStart = position;\n  let value = '';\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Quote (\")\n\n    if (code === 0x0022) {\n      value += body.slice(chunkStart, position);\n      return createToken(lexer, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.STRING, start, position + 1, value);\n    } // Escape Sequence (\\)\n\n    if (code === 0x005c) {\n      value += body.slice(chunkStart, position);\n      const escape =\n        body.charCodeAt(position + 1) === 0x0075 // u\n          ? body.charCodeAt(position + 2) === 0x007b // {\n            ? readEscapedUnicodeVariableWidth(lexer, position)\n            : readEscapedUnicodeFixedWidth(lexer, position)\n          : readEscapedCharacter(lexer, position);\n      value += escape.value;\n      position += escape.size;\n      chunkStart = position;\n      continue;\n    } // LineTerminator (\\n | \\r)\n\n    if (code === 0x000a || code === 0x000d) {\n      break;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n} // The string value and lexed size of an escape sequence.\n\nfunction readEscapedUnicodeVariableWidth(lexer, position) {\n  const body = lexer.source.body;\n  let point = 0;\n  let size = 3; // Cannot be larger than 12 chars (\\u{00000000}).\n\n  while (size < 12) {\n    const code = body.charCodeAt(position + size++); // Closing Brace (})\n\n    if (code === 0x007d) {\n      // Must be at least 5 chars (\\u{0}) and encode a Unicode scalar value.\n      if (size < 5 || !isUnicodeScalarValue(point)) {\n        break;\n      }\n\n      return {\n        value: String.fromCodePoint(point),\n        size,\n      };\n    } // Append this hex digit to the code point.\n\n    point = (point << 4) | readHexDigit(code);\n\n    if (point < 0) {\n      break;\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(\n      position,\n      position + size,\n    )}\".`,\n  );\n}\n\nfunction readEscapedUnicodeFixedWidth(lexer, position) {\n  const body = lexer.source.body;\n  const code = read16BitHexCode(body, position + 2);\n\n  if (isUnicodeScalarValue(code)) {\n    return {\n      value: String.fromCodePoint(code),\n      size: 6,\n    };\n  } // GraphQL allows JSON-style surrogate pair escape sequences, but only when\n  // a valid pair is formed.\n\n  if (isLeadingSurrogate(code)) {\n    // \\u\n    if (\n      body.charCodeAt(position + 6) === 0x005c &&\n      body.charCodeAt(position + 7) === 0x0075\n    ) {\n      const trailingCode = read16BitHexCode(body, position + 8);\n\n      if (isTrailingSurrogate(trailingCode)) {\n        // JavaScript defines strings as a sequence of UTF-16 code units and\n        // encodes Unicode code points above U+FFFF using a surrogate pair of\n        // code units. Since this is a surrogate pair escape sequence, just\n        // include both codes into the JavaScript string value. Had JavaScript\n        // not been internally based on UTF-16, then this surrogate pair would\n        // be decoded to retrieve the supplementary code point.\n        return {\n          value: String.fromCodePoint(code, trailingCode),\n          size: 12,\n        };\n      }\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid Unicode escape sequence: \"${body.slice(position, position + 6)}\".`,\n  );\n}\n/**\n * Reads four hexadecimal characters and returns the positive integer that 16bit\n * hexadecimal string represents. For example, \"000f\" will return 15, and \"dead\"\n * will return 57005.\n *\n * Returns a negative number if any char was not a valid hexadecimal digit.\n */\n\nfunction read16BitHexCode(body, position) {\n  // readHexDigit() returns -1 on error. ORing a negative value with any other\n  // value always produces a negative value.\n  return (\n    (readHexDigit(body.charCodeAt(position)) << 12) |\n    (readHexDigit(body.charCodeAt(position + 1)) << 8) |\n    (readHexDigit(body.charCodeAt(position + 2)) << 4) |\n    readHexDigit(body.charCodeAt(position + 3))\n  );\n}\n/**\n * Reads a hexadecimal character and returns its positive integer value (0-15).\n *\n * '0' becomes 0, '9' becomes 9\n * 'A' becomes 10, 'F' becomes 15\n * 'a' becomes 10, 'f' becomes 15\n *\n * Returns -1 if the provided character code was not a valid hexadecimal digit.\n *\n * HexDigit :: one of\n *   - `0` `1` `2` `3` `4` `5` `6` `7` `8` `9`\n *   - `A` `B` `C` `D` `E` `F`\n *   - `a` `b` `c` `d` `e` `f`\n */\n\nfunction readHexDigit(code) {\n  return code >= 0x0030 && code <= 0x0039 // 0-9\n    ? code - 0x0030\n    : code >= 0x0041 && code <= 0x0046 // A-F\n    ? code - 0x0037\n    : code >= 0x0061 && code <= 0x0066 // a-f\n    ? code - 0x0057\n    : -1;\n}\n/**\n * | Escaped Character | Code Point | Character Name               |\n * | ----------------- | ---------- | ---------------------------- |\n * | `\"`               | U+0022     | double quote                 |\n * | `\\`               | U+005C     | reverse solidus (back slash) |\n * | `/`               | U+002F     | solidus (forward slash)      |\n * | `b`               | U+0008     | backspace                    |\n * | `f`               | U+000C     | form feed                    |\n * | `n`               | U+000A     | line feed (new line)         |\n * | `r`               | U+000D     | carriage return              |\n * | `t`               | U+0009     | horizontal tab               |\n */\n\nfunction readEscapedCharacter(lexer, position) {\n  const body = lexer.source.body;\n  const code = body.charCodeAt(position + 1);\n\n  switch (code) {\n    case 0x0022:\n      // \"\n      return {\n        value: '\\u0022',\n        size: 2,\n      };\n\n    case 0x005c:\n      // \\\n      return {\n        value: '\\u005c',\n        size: 2,\n      };\n\n    case 0x002f:\n      // /\n      return {\n        value: '\\u002f',\n        size: 2,\n      };\n\n    case 0x0062:\n      // b\n      return {\n        value: '\\u0008',\n        size: 2,\n      };\n\n    case 0x0066:\n      // f\n      return {\n        value: '\\u000c',\n        size: 2,\n      };\n\n    case 0x006e:\n      // n\n      return {\n        value: '\\u000a',\n        size: 2,\n      };\n\n    case 0x0072:\n      // r\n      return {\n        value: '\\u000d',\n        size: 2,\n      };\n\n    case 0x0074:\n      // t\n      return {\n        value: '\\u0009',\n        size: 2,\n      };\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n    lexer.source,\n    position,\n    `Invalid character escape sequence: \"${body.slice(\n      position,\n      position + 2,\n    )}\".`,\n  );\n}\n/**\n * Reads a block string token from the source file.\n *\n * ```\n * StringValue ::\n *   - `\"\"\"` BlockStringCharacter* `\"\"\"`\n *\n * BlockStringCharacter ::\n *   - SourceCharacter but not `\"\"\"` or `\\\"\"\"`\n *   - `\\\"\"\"`\n * ```\n */\n\nfunction readBlockString(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let lineStart = lexer.lineStart;\n  let position = start + 3;\n  let chunkStart = position;\n  let currentLine = '';\n  const blockLines = [];\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position); // Closing Triple-Quote (\"\"\")\n\n    if (\n      code === 0x0022 &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n      const token = createToken(\n        lexer,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.BLOCK_STRING,\n        start,\n        position + 3, // Return a string of the lines joined with U+000A.\n        (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_4__.dedentBlockStringLines)(blockLines).join('\\n'),\n      );\n      lexer.line += blockLines.length - 1;\n      lexer.lineStart = lineStart;\n      return token;\n    } // Escaped Triple-Quote (\\\"\"\")\n\n    if (\n      code === 0x005c &&\n      body.charCodeAt(position + 1) === 0x0022 &&\n      body.charCodeAt(position + 2) === 0x0022 &&\n      body.charCodeAt(position + 3) === 0x0022\n    ) {\n      currentLine += body.slice(chunkStart, position);\n      chunkStart = position + 1; // skip only slash\n\n      position += 4;\n      continue;\n    } // LineTerminator\n\n    if (code === 0x000a || code === 0x000d) {\n      currentLine += body.slice(chunkStart, position);\n      blockLines.push(currentLine);\n\n      if (code === 0x000d && body.charCodeAt(position + 1) === 0x000a) {\n        position += 2;\n      } else {\n        ++position;\n      }\n\n      currentLine = '';\n      chunkStart = position;\n      lineStart = position;\n      continue;\n    } // SourceCharacter\n\n    if (isUnicodeScalarValue(code)) {\n      ++position;\n    } else if (isSupplementaryCodePoint(body, position)) {\n      position += 2;\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(\n        lexer.source,\n        position,\n        `Invalid character within String: ${printCodePointAt(\n          lexer,\n          position,\n        )}.`,\n      );\n    }\n  }\n\n  throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_3__.syntaxError)(lexer.source, position, 'Unterminated string.');\n}\n/**\n * Reads an alphanumeric + underscore name from the source.\n *\n * ```\n * Name ::\n *   - NameStart NameContinue* [lookahead != NameContinue]\n * ```\n */\n\nfunction readName(lexer, start) {\n  const body = lexer.source.body;\n  const bodyLength = body.length;\n  let position = start + 1;\n\n  while (position < bodyLength) {\n    const code = body.charCodeAt(position);\n\n    if ((0,_characterClasses_mjs__WEBPACK_IMPORTED_MODULE_2__.isNameContinue)(code)) {\n      ++position;\n    } else {\n      break;\n    }\n  }\n\n  return createToken(\n    lexer,\n    _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_1__.TokenKind.NAME,\n    start,\n    position,\n    body.slice(start, position),\n  );\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/lexer.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/location.mjs":
/*!****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/location.mjs ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLocation: () => (/* binding */ getLocation)\n/* harmony export */ });\n/* harmony import */ var _jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/invariant.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/invariant.mjs\");\n\nconst LineRegExp = /\\r\\n|[\\n\\r]/g;\n/**\n * Represents a location in a Source.\n */\n\n/**\n * Takes a Source and a UTF-8 character offset, and returns the corresponding\n * line and column as a SourceLocation.\n */\nfunction getLocation(source, position) {\n  let lastLineStart = 0;\n  let line = 1;\n\n  for (const match of source.body.matchAll(LineRegExp)) {\n    typeof match.index === 'number' || (0,_jsutils_invariant_mjs__WEBPACK_IMPORTED_MODULE_0__.invariant)(false);\n\n    if (match.index >= position) {\n      break;\n    }\n\n    lastLineStart = match.index + match[0].length;\n    line += 1;\n  }\n\n  return {\n    line,\n    column: position + 1 - lastLineStart,\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMS4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL2xvY2F0aW9uLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFxRDtBQUNyRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBLHVDQUF1QyxpRUFBUzs7QUFFaEQ7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGdyYXBocWxAMTYuMTEuMFxcbm9kZV9tb2R1bGVzXFxncmFwaHFsXFxsYW5ndWFnZVxcbG9jYXRpb24ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGludmFyaWFudCB9IGZyb20gJy4uL2pzdXRpbHMvaW52YXJpYW50Lm1qcyc7XG5jb25zdCBMaW5lUmVnRXhwID0gL1xcclxcbnxbXFxuXFxyXS9nO1xuLyoqXG4gKiBSZXByZXNlbnRzIGEgbG9jYXRpb24gaW4gYSBTb3VyY2UuXG4gKi9cblxuLyoqXG4gKiBUYWtlcyBhIFNvdXJjZSBhbmQgYSBVVEYtOCBjaGFyYWN0ZXIgb2Zmc2V0LCBhbmQgcmV0dXJucyB0aGUgY29ycmVzcG9uZGluZ1xuICogbGluZSBhbmQgY29sdW1uIGFzIGEgU291cmNlTG9jYXRpb24uXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRMb2NhdGlvbihzb3VyY2UsIHBvc2l0aW9uKSB7XG4gIGxldCBsYXN0TGluZVN0YXJ0ID0gMDtcbiAgbGV0IGxpbmUgPSAxO1xuXG4gIGZvciAoY29uc3QgbWF0Y2ggb2Ygc291cmNlLmJvZHkubWF0Y2hBbGwoTGluZVJlZ0V4cCkpIHtcbiAgICB0eXBlb2YgbWF0Y2guaW5kZXggPT09ICdudW1iZXInIHx8IGludmFyaWFudChmYWxzZSk7XG5cbiAgICBpZiAobWF0Y2guaW5kZXggPj0gcG9zaXRpb24pIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIGxhc3RMaW5lU3RhcnQgPSBtYXRjaC5pbmRleCArIG1hdGNoWzBdLmxlbmd0aDtcbiAgICBsaW5lICs9IDE7XG4gIH1cblxuICByZXR1cm4ge1xuICAgIGxpbmUsXG4gICAgY29sdW1uOiBwb3NpdGlvbiArIDEgLSBsYXN0TGluZVN0YXJ0LFxuICB9O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/location.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/parser.mjs":
/*!**************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/parser.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseConstValue: () => (/* binding */ parseConstValue),\n/* harmony export */   parseType: () => (/* binding */ parseType),\n/* harmony export */   parseValue: () => (/* binding */ parseValue)\n/* harmony export */ });\n/* harmony import */ var _error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../error/syntaxError.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/error/syntaxError.mjs\");\n/* harmony import */ var _ast_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ast.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/ast.mjs\");\n/* harmony import */ var _directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./directiveLocation.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/directiveLocation.mjs\");\n/* harmony import */ var _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./kinds.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/kinds.mjs\");\n/* harmony import */ var _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lexer.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/lexer.mjs\");\n/* harmony import */ var _source_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./source.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/source.mjs\");\n/* harmony import */ var _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenKind.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/tokenKind.mjs\");\n\n\n\n\n\n\n\n/**\n * Configuration options to control parser behavior\n */\n\n/**\n * Given a GraphQL source, parses it into a Document.\n * Throws GraphQLError if a syntax error is encountered.\n */\nfunction parse(source, options) {\n  const parser = new Parser(source, options);\n  const document = parser.parseDocument();\n  Object.defineProperty(document, 'tokenCount', {\n    enumerable: false,\n    value: parser.tokenCount,\n  });\n  return document;\n}\n/**\n * Given a string containing a GraphQL value (ex. `[42]`), parse the AST for\n * that value.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Values directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: valueFromAST().\n */\n\nfunction parseValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseValueLiteral(false);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Similar to parseValue(), but raises a parse error if it encounters a\n * variable. The return type will be a constant value.\n */\n\nfunction parseConstValue(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const value = parser.parseConstValueLiteral();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return value;\n}\n/**\n * Given a string containing a GraphQL Type (ex. `[Int!]`), parse the AST for\n * that type.\n * Throws GraphQLError if a syntax error is encountered.\n *\n * This is useful within tools that operate upon GraphQL Types directly and\n * in isolation of complete GraphQL documents.\n *\n * Consider providing the results to the utility function: typeFromAST().\n */\n\nfunction parseType(source, options) {\n  const parser = new Parser(source, options);\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF);\n  const type = parser.parseTypeReference();\n  parser.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF);\n  return type;\n}\n/**\n * This class is exported only to assist people in implementing their own parsers\n * without duplicating too much code and should be used only as last resort for cases\n * such as experimental syntax or if certain features could not be contributed upstream.\n *\n * It is still part of the internal API and is versioned, so any changes to it are never\n * considered breaking changes. If you still need to support multiple versions of the\n * library, please use the `versionInfo` variable for version detection.\n *\n * @internal\n */\n\nclass Parser {\n  constructor(source, options = {}) {\n    const sourceObj = (0,_source_mjs__WEBPACK_IMPORTED_MODULE_1__.isSource)(source) ? source : new _source_mjs__WEBPACK_IMPORTED_MODULE_1__.Source(source);\n    this._lexer = new _lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.Lexer(sourceObj);\n    this._options = options;\n    this._tokenCounter = 0;\n  }\n\n  get tokenCount() {\n    return this._tokenCounter;\n  }\n  /**\n   * Converts a name lex token into a name parse node.\n   */\n\n  parseName() {\n    const token = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAME,\n      value: token.value,\n    });\n  } // Implements the parsing rules in the Document section.\n\n  /**\n   * Document : Definition+\n   */\n\n  parseDocument() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DOCUMENT,\n      definitions: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SOF,\n        this.parseDefinition,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF,\n      ),\n    });\n  }\n  /**\n   * Definition :\n   *   - ExecutableDefinition\n   *   - TypeSystemDefinition\n   *   - TypeSystemExtension\n   *\n   * ExecutableDefinition :\n   *   - OperationDefinition\n   *   - FragmentDefinition\n   *\n   * TypeSystemDefinition :\n   *   - SchemaDefinition\n   *   - TypeDefinition\n   *   - DirectiveDefinition\n   *\n   * TypeDefinition :\n   *   - ScalarTypeDefinition\n   *   - ObjectTypeDefinition\n   *   - InterfaceTypeDefinition\n   *   - UnionTypeDefinition\n   *   - EnumTypeDefinition\n   *   - InputObjectTypeDefinition\n   */\n\n  parseDefinition() {\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.parseOperationDefinition();\n    } // Many definitions begin with a description and require a lookahead.\n\n    const hasDescription = this.peekDescription();\n    const keywordToken = hasDescription\n      ? this._lexer.lookahead()\n      : this._lexer.token;\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaDefinition();\n\n        case 'scalar':\n          return this.parseScalarTypeDefinition();\n\n        case 'type':\n          return this.parseObjectTypeDefinition();\n\n        case 'interface':\n          return this.parseInterfaceTypeDefinition();\n\n        case 'union':\n          return this.parseUnionTypeDefinition();\n\n        case 'enum':\n          return this.parseEnumTypeDefinition();\n\n        case 'input':\n          return this.parseInputObjectTypeDefinition();\n\n        case 'directive':\n          return this.parseDirectiveDefinition();\n      }\n\n      if (hasDescription) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          this._lexer.token.start,\n          'Unexpected description, descriptions are supported only on type definitions.',\n        );\n      }\n\n      switch (keywordToken.value) {\n        case 'query':\n        case 'mutation':\n        case 'subscription':\n          return this.parseOperationDefinition();\n\n        case 'fragment':\n          return this.parseFragmentDefinition();\n\n        case 'extend':\n          return this.parseTypeSystemExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  } // Implements the parsing rules in the Operations section.\n\n  /**\n   * OperationDefinition :\n   *  - SelectionSet\n   *  - OperationType Name? VariableDefinitions? Directives? SelectionSet\n   */\n\n  parseOperationDefinition() {\n    const start = this._lexer.token;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n        operation: _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY,\n        name: undefined,\n        variableDefinitions: [],\n        directives: [],\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    const operation = this.parseOperationType();\n    let name;\n\n    if (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      name = this.parseName();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_DEFINITION,\n      operation,\n      name,\n      variableDefinitions: this.parseVariableDefinitions(),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * OperationType : one of query mutation subscription\n   */\n\n  parseOperationType() {\n    const operationToken = this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME);\n\n    switch (operationToken.value) {\n      case 'query':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.QUERY;\n\n      case 'mutation':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.MUTATION;\n\n      case 'subscription':\n        return _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.OperationTypeNode.SUBSCRIPTION;\n    }\n\n    throw this.unexpected(operationToken);\n  }\n  /**\n   * VariableDefinitions : ( VariableDefinition+ )\n   */\n\n  parseVariableDefinitions() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseVariableDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * VariableDefinition : Variable : Type DefaultValue? Directives[Const]?\n   */\n\n  parseVariableDefinition() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE_DEFINITION,\n      variable: this.parseVariable(),\n      type: (this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON), this.parseTypeReference()),\n      defaultValue: this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n        ? this.parseConstValueLiteral()\n        : undefined,\n      directives: this.parseConstDirectives(),\n    });\n  }\n  /**\n   * Variable : $ Name\n   */\n\n  parseVariable() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.VARIABLE,\n      name: this.parseName(),\n    });\n  }\n  /**\n   * ```\n   * SelectionSet : { Selection+ }\n   * ```\n   */\n\n  parseSelectionSet() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SELECTION_SET,\n      selections: this.many(\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n        this.parseSelection,\n        _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n      ),\n    });\n  }\n  /**\n   * Selection :\n   *   - Field\n   *   - FragmentSpread\n   *   - InlineFragment\n   */\n\n  parseSelection() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD)\n      ? this.parseFragment()\n      : this.parseField();\n  }\n  /**\n   * Field : Alias? Name Arguments? Directives? SelectionSet?\n   *\n   * Alias : Name :\n   */\n\n  parseField() {\n    const start = this._lexer.token;\n    const nameOrAlias = this.parseName();\n    let alias;\n    let name;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON)) {\n      alias = nameOrAlias;\n      name = this.parseName();\n    } else {\n      name = nameOrAlias;\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD,\n      alias,\n      name,\n      arguments: this.parseArguments(false),\n      directives: this.parseDirectives(false),\n      selectionSet: this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L)\n        ? this.parseSelectionSet()\n        : undefined,\n    });\n  }\n  /**\n   * Arguments[Const] : ( Argument[?Const]+ )\n   */\n\n  parseArguments(isConst) {\n    const item = isConst ? this.parseConstArgument : this.parseArgument;\n    return this.optionalMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R);\n  }\n  /**\n   * Argument[Const] : Name : Value[?Const]\n   */\n\n  parseArgument(isConst = false) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ARGUMENT,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  }\n\n  parseConstArgument() {\n    return this.parseArgument(true);\n  } // Implements the parsing rules in the Fragments section.\n\n  /**\n   * Corresponds to both FragmentSpread and InlineFragment in the spec.\n   *\n   * FragmentSpread : ... FragmentName Directives?\n   *\n   * InlineFragment : ... TypeCondition? Directives? SelectionSet\n   */\n\n  parseFragment() {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.SPREAD);\n    const hasTypeCondition = this.expectOptionalKeyword('on');\n\n    if (!hasTypeCondition && this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_SPREAD,\n        name: this.parseFragmentName(),\n        directives: this.parseDirectives(false),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INLINE_FRAGMENT,\n      typeCondition: hasTypeCondition ? this.parseNamedType() : undefined,\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentDefinition :\n   *   - fragment FragmentName on TypeCondition Directives? SelectionSet\n   *\n   * TypeCondition : NamedType\n   */\n\n  parseFragmentDefinition() {\n    const start = this._lexer.token;\n    this.expectKeyword('fragment'); // Legacy support for defining variables within fragments changes\n    // the grammar of FragmentDefinition:\n    //   - fragment FragmentName VariableDefinitions? on TypeCondition Directives? SelectionSet\n\n    if (this._options.allowLegacyFragmentVariables === true) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n        name: this.parseFragmentName(),\n        variableDefinitions: this.parseVariableDefinitions(),\n        typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n        directives: this.parseDirectives(false),\n        selectionSet: this.parseSelectionSet(),\n      });\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FRAGMENT_DEFINITION,\n      name: this.parseFragmentName(),\n      typeCondition: (this.expectKeyword('on'), this.parseNamedType()),\n      directives: this.parseDirectives(false),\n      selectionSet: this.parseSelectionSet(),\n    });\n  }\n  /**\n   * FragmentName : Name but not `on`\n   */\n\n  parseFragmentName() {\n    if (this._lexer.token.value === 'on') {\n      throw this.unexpected();\n    }\n\n    return this.parseName();\n  } // Implements the parsing rules in the Values section.\n\n  /**\n   * Value[Const] :\n   *   - [~Const] Variable\n   *   - IntValue\n   *   - FloatValue\n   *   - StringValue\n   *   - BooleanValue\n   *   - NullValue\n   *   - EnumValue\n   *   - ListValue[?Const]\n   *   - ObjectValue[?Const]\n   *\n   * BooleanValue : one of `true` `false`\n   *\n   * NullValue : `null`\n   *\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseValueLiteral(isConst) {\n    const token = this._lexer.token;\n\n    switch (token.kind) {\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L:\n        return this.parseList(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L:\n        return this.parseObject(isConst);\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.INT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.FLOAT:\n        this.advanceLexer();\n        return this.node(token, {\n          kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FLOAT,\n          value: token.value,\n        });\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING:\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING:\n        return this.parseStringLiteral();\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME:\n        this.advanceLexer();\n\n        switch (token.value) {\n          case 'true':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: true,\n            });\n\n          case 'false':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.BOOLEAN,\n              value: false,\n            });\n\n          case 'null':\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NULL,\n            });\n\n          default:\n            return this.node(token, {\n              kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM,\n              value: token.value,\n            });\n        }\n\n      case _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR:\n        if (isConst) {\n          this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.DOLLAR);\n\n          if (this._lexer.token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n            const varName = this._lexer.token.value;\n            throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n              this._lexer.source,\n              token.start,\n              `Unexpected variable \"$${varName}\" in constant value.`,\n            );\n          } else {\n            throw this.unexpected(token);\n          }\n        }\n\n        return this.parseVariable();\n\n      default:\n        throw this.unexpected();\n    }\n  }\n\n  parseConstValueLiteral() {\n    return this.parseValueLiteral(true);\n  }\n\n  parseStringLiteral() {\n    const token = this._lexer.token;\n    this.advanceLexer();\n    return this.node(token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.STRING,\n      value: token.value,\n      block: token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING,\n    });\n  }\n  /**\n   * ListValue[Const] :\n   *   - [ ]\n   *   - [ Value[?Const]+ ]\n   */\n\n  parseList(isConst) {\n    const item = () => this.parseValueLiteral(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST,\n      values: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R),\n    });\n  }\n  /**\n   * ```\n   * ObjectValue[Const] :\n   *   - { }\n   *   - { ObjectField[?Const]+ }\n   * ```\n   */\n\n  parseObject(isConst) {\n    const item = () => this.parseObjectField(isConst);\n\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT,\n      fields: this.any(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L, item, _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R),\n    });\n  }\n  /**\n   * ObjectField[Const] : Name : Value[?Const]\n   */\n\n  parseObjectField(isConst) {\n    const start = this._lexer.token;\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_FIELD,\n      name,\n      value: this.parseValueLiteral(isConst),\n    });\n  } // Implements the parsing rules in the Directives section.\n\n  /**\n   * Directives[Const] : Directive[?Const]+\n   */\n\n  parseDirectives(isConst) {\n    const directives = [];\n\n    while (this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT)) {\n      directives.push(this.parseDirective(isConst));\n    }\n\n    return directives;\n  }\n\n  parseConstDirectives() {\n    return this.parseDirectives(true);\n  }\n  /**\n   * ```\n   * Directive[Const] : @ Name Arguments[?Const]?\n   * ```\n   */\n\n  parseDirective(isConst) {\n    const start = this._lexer.token;\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE,\n      name: this.parseName(),\n      arguments: this.parseArguments(isConst),\n    });\n  } // Implements the parsing rules in the Types section.\n\n  /**\n   * Type :\n   *   - NamedType\n   *   - ListType\n   *   - NonNullType\n   */\n\n  parseTypeReference() {\n    const start = this._lexer.token;\n    let type;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_L)) {\n      const innerType = this.parseTypeReference();\n      this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACKET_R);\n      type = this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.LIST_TYPE,\n        type: innerType,\n      });\n    } else {\n      type = this.parseNamedType();\n    }\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BANG)) {\n      return this.node(start, {\n        kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NON_NULL_TYPE,\n        type,\n      });\n    }\n\n    return type;\n  }\n  /**\n   * NamedType : Name\n   */\n\n  parseNamedType() {\n    return this.node(this._lexer.token, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.NAMED_TYPE,\n      name: this.parseName(),\n    });\n  } // Implements the parsing rules in the Type Definition section.\n\n  peekDescription() {\n    return this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.STRING) || this.peek(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BLOCK_STRING);\n  }\n  /**\n   * Description : StringValue\n   */\n\n  parseDescription() {\n    if (this.peekDescription()) {\n      return this.parseStringLiteral();\n    }\n  }\n  /**\n   * ```\n   * SchemaDefinition : Description? schema Directives[Const]? { OperationTypeDefinition+ }\n   * ```\n   */\n\n  parseSchemaDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.many(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_DEFINITION,\n      description,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * OperationTypeDefinition : OperationType : NamedType\n   */\n\n  parseOperationTypeDefinition() {\n    const start = this._lexer.token;\n    const operation = this.parseOperationType();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseNamedType();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OPERATION_TYPE_DEFINITION,\n      operation,\n      type,\n    });\n  }\n  /**\n   * ScalarTypeDefinition : Description? scalar Name Directives[Const]?\n   */\n\n  parseScalarTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeDefinition :\n   *   Description?\n   *   type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition?\n   */\n\n  parseObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ImplementsInterfaces :\n   *   - implements `&`? NamedType\n   *   - ImplementsInterfaces & NamedType\n   */\n\n  parseImplementsInterfaces() {\n    return this.expectOptionalKeyword('implements')\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AMP, this.parseNamedType)\n      : [];\n  }\n  /**\n   * ```\n   * FieldsDefinition : { FieldDefinition+ }\n   * ```\n   */\n\n  parseFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseFieldDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * FieldDefinition :\n   *   - Description? Name ArgumentsDefinition? : Type Directives[Const]?\n   */\n\n  parseFieldDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.FIELD_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      type,\n      directives,\n    });\n  }\n  /**\n   * ArgumentsDefinition : ( InputValueDefinition+ )\n   */\n\n  parseArgumentDefs() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PAREN_R,\n    );\n  }\n  /**\n   * InputValueDefinition :\n   *   - Description? Name : Type DefaultValue? Directives[Const]?\n   */\n\n  parseInputValueDef() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseName();\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.COLON);\n    const type = this.parseTypeReference();\n    let defaultValue;\n\n    if (this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)) {\n      defaultValue = this.parseConstValueLiteral();\n    }\n\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_VALUE_DEFINITION,\n      description,\n      name,\n      type,\n      defaultValue,\n      directives,\n    });\n  }\n  /**\n   * InterfaceTypeDefinition :\n   *   - Description? interface Name Directives[Const]? FieldsDefinition?\n   */\n\n  parseInterfaceTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_DEFINITION,\n      description,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeDefinition :\n   *   - Description? union Name Directives[Const]? UnionMemberTypes?\n   */\n\n  parseUnionTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * UnionMemberTypes :\n   *   - = `|`? NamedType\n   *   - UnionMemberTypes | NamedType\n   */\n\n  parseUnionMemberTypes() {\n    return this.expectOptionalToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EQUALS)\n      ? this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseNamedType)\n      : [];\n  }\n  /**\n   * EnumTypeDefinition :\n   *   - Description? enum Name Directives[Const]? EnumValuesDefinition?\n   */\n\n  parseEnumTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * ```\n   * EnumValuesDefinition : { EnumValueDefinition+ }\n   * ```\n   */\n\n  parseEnumValuesDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseEnumValueDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * EnumValueDefinition : Description? EnumValue Directives[Const]?\n   */\n\n  parseEnumValueDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    const name = this.parseEnumValueName();\n    const directives = this.parseConstDirectives();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_VALUE_DEFINITION,\n      description,\n      name,\n      directives,\n    });\n  }\n  /**\n   * EnumValue : Name but not `true`, `false` or `null`\n   */\n\n  parseEnumValueName() {\n    if (\n      this._lexer.token.value === 'true' ||\n      this._lexer.token.value === 'false' ||\n      this._lexer.token.value === 'null'\n    ) {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        this._lexer.token.start,\n        `${getTokenDesc(\n          this._lexer.token,\n        )} is reserved and cannot be used for an enum value.`,\n      );\n    }\n\n    return this.parseName();\n  }\n  /**\n   * InputObjectTypeDefinition :\n   *   - Description? input Name Directives[Const]? InputFieldsDefinition?\n   */\n\n  parseInputObjectTypeDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_DEFINITION,\n      description,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * InputFieldsDefinition : { InputValueDefinition+ }\n   * ```\n   */\n\n  parseInputFieldsDefinition() {\n    return this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseInputValueDef,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n  }\n  /**\n   * TypeSystemExtension :\n   *   - SchemaExtension\n   *   - TypeExtension\n   *\n   * TypeExtension :\n   *   - ScalarTypeExtension\n   *   - ObjectTypeExtension\n   *   - InterfaceTypeExtension\n   *   - UnionTypeExtension\n   *   - EnumTypeExtension\n   *   - InputObjectTypeDefinition\n   */\n\n  parseTypeSystemExtension() {\n    const keywordToken = this._lexer.lookahead();\n\n    if (keywordToken.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME) {\n      switch (keywordToken.value) {\n        case 'schema':\n          return this.parseSchemaExtension();\n\n        case 'scalar':\n          return this.parseScalarTypeExtension();\n\n        case 'type':\n          return this.parseObjectTypeExtension();\n\n        case 'interface':\n          return this.parseInterfaceTypeExtension();\n\n        case 'union':\n          return this.parseUnionTypeExtension();\n\n        case 'enum':\n          return this.parseEnumTypeExtension();\n\n        case 'input':\n          return this.parseInputObjectTypeExtension();\n      }\n    }\n\n    throw this.unexpected(keywordToken);\n  }\n  /**\n   * ```\n   * SchemaExtension :\n   *  - extend schema Directives[Const]? { OperationTypeDefinition+ }\n   *  - extend schema Directives[Const]\n   * ```\n   */\n\n  parseSchemaExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('schema');\n    const directives = this.parseConstDirectives();\n    const operationTypes = this.optionalMany(\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_L,\n      this.parseOperationTypeDefinition,\n      _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.BRACE_R,\n    );\n\n    if (directives.length === 0 && operationTypes.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCHEMA_EXTENSION,\n      directives,\n      operationTypes,\n    });\n  }\n  /**\n   * ScalarTypeExtension :\n   *   - extend scalar Name Directives[Const]\n   */\n\n  parseScalarTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('scalar');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n\n    if (directives.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.SCALAR_TYPE_EXTENSION,\n      name,\n      directives,\n    });\n  }\n  /**\n   * ObjectTypeExtension :\n   *  - extend type Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend type Name ImplementsInterfaces? Directives[Const]\n   *  - extend type Name ImplementsInterfaces\n   */\n\n  parseObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('type');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.OBJECT_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * InterfaceTypeExtension :\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]? FieldsDefinition\n   *  - extend interface Name ImplementsInterfaces? Directives[Const]\n   *  - extend interface Name ImplementsInterfaces\n   */\n\n  parseInterfaceTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('interface');\n    const name = this.parseName();\n    const interfaces = this.parseImplementsInterfaces();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseFieldsDefinition();\n\n    if (\n      interfaces.length === 0 &&\n      directives.length === 0 &&\n      fields.length === 0\n    ) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INTERFACE_TYPE_EXTENSION,\n      name,\n      interfaces,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * UnionTypeExtension :\n   *   - extend union Name Directives[Const]? UnionMemberTypes\n   *   - extend union Name Directives[Const]\n   */\n\n  parseUnionTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('union');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const types = this.parseUnionMemberTypes();\n\n    if (directives.length === 0 && types.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.UNION_TYPE_EXTENSION,\n      name,\n      directives,\n      types,\n    });\n  }\n  /**\n   * EnumTypeExtension :\n   *   - extend enum Name Directives[Const]? EnumValuesDefinition\n   *   - extend enum Name Directives[Const]\n   */\n\n  parseEnumTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('enum');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const values = this.parseEnumValuesDefinition();\n\n    if (directives.length === 0 && values.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.ENUM_TYPE_EXTENSION,\n      name,\n      directives,\n      values,\n    });\n  }\n  /**\n   * InputObjectTypeExtension :\n   *   - extend input Name Directives[Const]? InputFieldsDefinition\n   *   - extend input Name Directives[Const]\n   */\n\n  parseInputObjectTypeExtension() {\n    const start = this._lexer.token;\n    this.expectKeyword('extend');\n    this.expectKeyword('input');\n    const name = this.parseName();\n    const directives = this.parseConstDirectives();\n    const fields = this.parseInputFieldsDefinition();\n\n    if (directives.length === 0 && fields.length === 0) {\n      throw this.unexpected();\n    }\n\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.INPUT_OBJECT_TYPE_EXTENSION,\n      name,\n      directives,\n      fields,\n    });\n  }\n  /**\n   * ```\n   * DirectiveDefinition :\n   *   - Description? directive @ Name ArgumentsDefinition? `repeatable`? on DirectiveLocations\n   * ```\n   */\n\n  parseDirectiveDefinition() {\n    const start = this._lexer.token;\n    const description = this.parseDescription();\n    this.expectKeyword('directive');\n    this.expectToken(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.AT);\n    const name = this.parseName();\n    const args = this.parseArgumentDefs();\n    const repeatable = this.expectOptionalKeyword('repeatable');\n    this.expectKeyword('on');\n    const locations = this.parseDirectiveLocations();\n    return this.node(start, {\n      kind: _kinds_mjs__WEBPACK_IMPORTED_MODULE_3__.Kind.DIRECTIVE_DEFINITION,\n      description,\n      name,\n      arguments: args,\n      repeatable,\n      locations,\n    });\n  }\n  /**\n   * DirectiveLocations :\n   *   - `|`? DirectiveLocation\n   *   - DirectiveLocations | DirectiveLocation\n   */\n\n  parseDirectiveLocations() {\n    return this.delimitedMany(_tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.PIPE, this.parseDirectiveLocation);\n  }\n  /*\n   * DirectiveLocation :\n   *   - ExecutableDirectiveLocation\n   *   - TypeSystemDirectiveLocation\n   *\n   * ExecutableDirectiveLocation : one of\n   *   `QUERY`\n   *   `MUTATION`\n   *   `SUBSCRIPTION`\n   *   `FIELD`\n   *   `FRAGMENT_DEFINITION`\n   *   `FRAGMENT_SPREAD`\n   *   `INLINE_FRAGMENT`\n   *\n   * TypeSystemDirectiveLocation : one of\n   *   `SCHEMA`\n   *   `SCALAR`\n   *   `OBJECT`\n   *   `FIELD_DEFINITION`\n   *   `ARGUMENT_DEFINITION`\n   *   `INTERFACE`\n   *   `UNION`\n   *   `ENUM`\n   *   `ENUM_VALUE`\n   *   `INPUT_OBJECT`\n   *   `INPUT_FIELD_DEFINITION`\n   */\n\n  parseDirectiveLocation() {\n    const start = this._lexer.token;\n    const name = this.parseName();\n\n    if (Object.prototype.hasOwnProperty.call(_directiveLocation_mjs__WEBPACK_IMPORTED_MODULE_6__.DirectiveLocation, name.value)) {\n      return name;\n    }\n\n    throw this.unexpected(start);\n  } // Core parsing utility functions\n\n  /**\n   * Returns a node that, if configured to do so, sets a \"loc\" field as a\n   * location object, used to identify the place in the source that created a\n   * given parsed object.\n   */\n\n  node(startToken, node) {\n    if (this._options.noLocation !== true) {\n      node.loc = new _ast_mjs__WEBPACK_IMPORTED_MODULE_5__.Location(\n        startToken,\n        this._lexer.lastToken,\n        this._lexer.source,\n      );\n    }\n\n    return node;\n  }\n  /**\n   * Determines if the next token is of a given kind\n   */\n\n  peek(kind) {\n    return this._lexer.token.kind === kind;\n  }\n  /**\n   * If the next token is of the given kind, return that token after advancing the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return token;\n    }\n\n    throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Expected ${getTokenKindDesc(kind)}, found ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * If the next token is of the given kind, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalToken(kind) {\n    const token = this._lexer.token;\n\n    if (token.kind === kind) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * If the next token is a given keyword, advance the lexer.\n   * Otherwise, do not change the parser state and throw an error.\n   */\n\n  expectKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n    } else {\n      throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n        this._lexer.source,\n        token.start,\n        `Expected \"${value}\", found ${getTokenDesc(token)}.`,\n      );\n    }\n  }\n  /**\n   * If the next token is a given keyword, return \"true\" after advancing the lexer.\n   * Otherwise, do not change the parser state and return \"false\".\n   */\n\n  expectOptionalKeyword(value) {\n    const token = this._lexer.token;\n\n    if (token.kind === _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.NAME && token.value === value) {\n      this.advanceLexer();\n      return true;\n    }\n\n    return false;\n  }\n  /**\n   * Helper function for creating an error when an unexpected lexed token is encountered.\n   */\n\n  unexpected(atToken) {\n    const token =\n      atToken !== null && atToken !== void 0 ? atToken : this._lexer.token;\n    return (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n      this._lexer.source,\n      token.start,\n      `Unexpected ${getTokenDesc(token)}.`,\n    );\n  }\n  /**\n   * Returns a possibly empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  any(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    while (!this.expectOptionalToken(closeKind)) {\n      nodes.push(parseFn.call(this));\n    }\n\n    return nodes;\n  }\n  /**\n   * Returns a list of parse nodes, determined by the parseFn.\n   * It can be empty only if open token is missing otherwise it will always return non-empty list\n   * that begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  optionalMany(openKind, parseFn, closeKind) {\n    if (this.expectOptionalToken(openKind)) {\n      const nodes = [];\n\n      do {\n        nodes.push(parseFn.call(this));\n      } while (!this.expectOptionalToken(closeKind));\n\n      return nodes;\n    }\n\n    return [];\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list begins with a lex token of openKind and ends with a lex token of closeKind.\n   * Advances the parser to the next lex token after the closing token.\n   */\n\n  many(openKind, parseFn, closeKind) {\n    this.expectToken(openKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (!this.expectOptionalToken(closeKind));\n\n    return nodes;\n  }\n  /**\n   * Returns a non-empty list of parse nodes, determined by the parseFn.\n   * This list may begin with a lex token of delimiterKind followed by items separated by lex tokens of tokenKind.\n   * Advances the parser to the next lex token after last item in the list.\n   */\n\n  delimitedMany(delimiterKind, parseFn) {\n    this.expectOptionalToken(delimiterKind);\n    const nodes = [];\n\n    do {\n      nodes.push(parseFn.call(this));\n    } while (this.expectOptionalToken(delimiterKind));\n\n    return nodes;\n  }\n\n  advanceLexer() {\n    const { maxTokens } = this._options;\n\n    const token = this._lexer.advance();\n\n    if (token.kind !== _tokenKind_mjs__WEBPACK_IMPORTED_MODULE_0__.TokenKind.EOF) {\n      ++this._tokenCounter;\n\n      if (maxTokens !== undefined && this._tokenCounter > maxTokens) {\n        throw (0,_error_syntaxError_mjs__WEBPACK_IMPORTED_MODULE_4__.syntaxError)(\n          this._lexer.source,\n          token.start,\n          `Document contains more that ${maxTokens} tokens. Parsing aborted.`,\n        );\n      }\n    }\n  }\n}\n/**\n * A helper function to describe a token as a string for debugging.\n */\n\nfunction getTokenDesc(token) {\n  const value = token.value;\n  return getTokenKindDesc(token.kind) + (value != null ? ` \"${value}\"` : '');\n}\n/**\n * A helper function to describe a token kind as a string for debugging.\n */\n\nfunction getTokenKindDesc(kind) {\n  return (0,_lexer_mjs__WEBPACK_IMPORTED_MODULE_2__.isPunctuatorTokenKind)(kind) ? `\"${kind}\"` : kind;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/parser.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printLocation.mjs":
/*!*********************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printLocation.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printLocation: () => (/* binding */ printLocation),\n/* harmony export */   printSourceLocation: () => (/* binding */ printSourceLocation)\n/* harmony export */ });\n/* harmony import */ var _location_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./location.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/location.mjs\");\n\n\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\nfunction printLocation(location) {\n  return printSourceLocation(\n    location.source,\n    (0,_location_mjs__WEBPACK_IMPORTED_MODULE_0__.getLocation)(location.source, location.start),\n  );\n}\n/**\n * Render a helpful description of the location in the GraphQL Source document.\n */\n\nfunction printSourceLocation(source, sourceLocation) {\n  const firstLineColumnOffset = source.locationOffset.column - 1;\n  const body = ''.padStart(firstLineColumnOffset) + source.body;\n  const lineIndex = sourceLocation.line - 1;\n  const lineOffset = source.locationOffset.line - 1;\n  const lineNum = sourceLocation.line + lineOffset;\n  const columnOffset = sourceLocation.line === 1 ? firstLineColumnOffset : 0;\n  const columnNum = sourceLocation.column + columnOffset;\n  const locationStr = `${source.name}:${lineNum}:${columnNum}\\n`;\n  const lines = body.split(/\\r\\n|[\\n\\r]/g);\n  const locationLine = lines[lineIndex]; // Special case for minified documents\n\n  if (locationLine.length > 120) {\n    const subLineIndex = Math.floor(columnNum / 80);\n    const subLineColumnNum = columnNum % 80;\n    const subLines = [];\n\n    for (let i = 0; i < locationLine.length; i += 80) {\n      subLines.push(locationLine.slice(i, i + 80));\n    }\n\n    return (\n      locationStr +\n      printPrefixedLines([\n        [`${lineNum} |`, subLines[0]],\n        ...subLines.slice(1, subLineIndex + 1).map((subLine) => ['|', subLine]),\n        ['|', '^'.padStart(subLineColumnNum)],\n        ['|', subLines[subLineIndex + 1]],\n      ])\n    );\n  }\n\n  return (\n    locationStr +\n    printPrefixedLines([\n      // Lines specified like this: [\"prefix\", \"string\"],\n      [`${lineNum - 1} |`, lines[lineIndex - 1]],\n      [`${lineNum} |`, locationLine],\n      ['|', '^'.padStart(columnNum)],\n      [`${lineNum + 1} |`, lines[lineIndex + 1]],\n    ])\n  );\n}\n\nfunction printPrefixedLines(lines) {\n  const existingLines = lines.filter(([_, line]) => line !== undefined);\n  const padLen = Math.max(...existingLines.map(([prefix]) => prefix.length));\n  return existingLines\n    .map(([prefix, line]) => prefix.padStart(padLen) + (line ? ' ' + line : ''))\n    .join('\\n');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printLocation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printString.mjs":
/*!*******************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printString.mjs ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   printString: () => (/* binding */ printString)\n/* harmony export */ });\n/**\n * Prints a string as a GraphQL StringValue literal. Replaces control characters\n * and excluded characters (\" U+0022 and \\\\ U+005C) with escape sequences.\n */\nfunction printString(str) {\n  return `\"${str.replace(escapedRegExp, escapedReplacer)}\"`;\n} // eslint-disable-next-line no-control-regex\n\nconst escapedRegExp = /[\\x00-\\x1f\\x22\\x5c\\x7f-\\x9f]/g;\n\nfunction escapedReplacer(str) {\n  return escapeSequences[str.charCodeAt(0)];\n} // prettier-ignore\n\nconst escapeSequences = [\n  '\\\\u0000',\n  '\\\\u0001',\n  '\\\\u0002',\n  '\\\\u0003',\n  '\\\\u0004',\n  '\\\\u0005',\n  '\\\\u0006',\n  '\\\\u0007',\n  '\\\\b',\n  '\\\\t',\n  '\\\\n',\n  '\\\\u000B',\n  '\\\\f',\n  '\\\\r',\n  '\\\\u000E',\n  '\\\\u000F',\n  '\\\\u0010',\n  '\\\\u0011',\n  '\\\\u0012',\n  '\\\\u0013',\n  '\\\\u0014',\n  '\\\\u0015',\n  '\\\\u0016',\n  '\\\\u0017',\n  '\\\\u0018',\n  '\\\\u0019',\n  '\\\\u001A',\n  '\\\\u001B',\n  '\\\\u001C',\n  '\\\\u001D',\n  '\\\\u001E',\n  '\\\\u001F',\n  '',\n  '',\n  '\\\\\"',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 2F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 3F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 4F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\\\\\',\n  '',\n  '',\n  '', // 5F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '', // 6F\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '',\n  '\\\\u007F',\n  '\\\\u0080',\n  '\\\\u0081',\n  '\\\\u0082',\n  '\\\\u0083',\n  '\\\\u0084',\n  '\\\\u0085',\n  '\\\\u0086',\n  '\\\\u0087',\n  '\\\\u0088',\n  '\\\\u0089',\n  '\\\\u008A',\n  '\\\\u008B',\n  '\\\\u008C',\n  '\\\\u008D',\n  '\\\\u008E',\n  '\\\\u008F',\n  '\\\\u0090',\n  '\\\\u0091',\n  '\\\\u0092',\n  '\\\\u0093',\n  '\\\\u0094',\n  '\\\\u0095',\n  '\\\\u0096',\n  '\\\\u0097',\n  '\\\\u0098',\n  '\\\\u0099',\n  '\\\\u009A',\n  '\\\\u009B',\n  '\\\\u009C',\n  '\\\\u009D',\n  '\\\\u009E',\n  '\\\\u009F',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vZ3JhcGhxbEAxNi4xMS4wL25vZGVfbW9kdWxlcy9ncmFwaHFsL2xhbmd1YWdlL3ByaW50U3RyaW5nLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGFBQWEsNENBQTRDO0FBQ3pELEVBQUU7O0FBRUY7O0FBRUE7QUFDQTtBQUNBLEVBQUU7O0FBRUY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXGNpdGVvcG9saXMtZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGdyYXBocWxAMTYuMTEuMFxcbm9kZV9tb2R1bGVzXFxncmFwaHFsXFxsYW5ndWFnZVxccHJpbnRTdHJpbmcubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogUHJpbnRzIGEgc3RyaW5nIGFzIGEgR3JhcGhRTCBTdHJpbmdWYWx1ZSBsaXRlcmFsLiBSZXBsYWNlcyBjb250cm9sIGNoYXJhY3RlcnNcbiAqIGFuZCBleGNsdWRlZCBjaGFyYWN0ZXJzIChcIiBVKzAwMjIgYW5kIFxcXFwgVSswMDVDKSB3aXRoIGVzY2FwZSBzZXF1ZW5jZXMuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwcmludFN0cmluZyhzdHIpIHtcbiAgcmV0dXJuIGBcIiR7c3RyLnJlcGxhY2UoZXNjYXBlZFJlZ0V4cCwgZXNjYXBlZFJlcGxhY2VyKX1cImA7XG59IC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb250cm9sLXJlZ2V4XG5cbmNvbnN0IGVzY2FwZWRSZWdFeHAgPSAvW1xceDAwLVxceDFmXFx4MjJcXHg1Y1xceDdmLVxceDlmXS9nO1xuXG5mdW5jdGlvbiBlc2NhcGVkUmVwbGFjZXIoc3RyKSB7XG4gIHJldHVybiBlc2NhcGVTZXF1ZW5jZXNbc3RyLmNoYXJDb2RlQXQoMCldO1xufSAvLyBwcmV0dGllci1pZ25vcmVcblxuY29uc3QgZXNjYXBlU2VxdWVuY2VzID0gW1xuICAnXFxcXHUwMDAwJyxcbiAgJ1xcXFx1MDAwMScsXG4gICdcXFxcdTAwMDInLFxuICAnXFxcXHUwMDAzJyxcbiAgJ1xcXFx1MDAwNCcsXG4gICdcXFxcdTAwMDUnLFxuICAnXFxcXHUwMDA2JyxcbiAgJ1xcXFx1MDAwNycsXG4gICdcXFxcYicsXG4gICdcXFxcdCcsXG4gICdcXFxcbicsXG4gICdcXFxcdTAwMEInLFxuICAnXFxcXGYnLFxuICAnXFxcXHInLFxuICAnXFxcXHUwMDBFJyxcbiAgJ1xcXFx1MDAwRicsXG4gICdcXFxcdTAwMTAnLFxuICAnXFxcXHUwMDExJyxcbiAgJ1xcXFx1MDAxMicsXG4gICdcXFxcdTAwMTMnLFxuICAnXFxcXHUwMDE0JyxcbiAgJ1xcXFx1MDAxNScsXG4gICdcXFxcdTAwMTYnLFxuICAnXFxcXHUwMDE3JyxcbiAgJ1xcXFx1MDAxOCcsXG4gICdcXFxcdTAwMTknLFxuICAnXFxcXHUwMDFBJyxcbiAgJ1xcXFx1MDAxQicsXG4gICdcXFxcdTAwMUMnLFxuICAnXFxcXHUwMDFEJyxcbiAgJ1xcXFx1MDAxRScsXG4gICdcXFxcdTAwMUYnLFxuICAnJyxcbiAgJycsXG4gICdcXFxcXCInLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJywgLy8gMkZcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsIC8vIDNGXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLCAvLyA0RlxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnXFxcXFxcXFwnLFxuICAnJyxcbiAgJycsXG4gICcnLCAvLyA1RlxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJywgLy8gNkZcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJycsXG4gICcnLFxuICAnJyxcbiAgJ1xcXFx1MDA3RicsXG4gICdcXFxcdTAwODAnLFxuICAnXFxcXHUwMDgxJyxcbiAgJ1xcXFx1MDA4MicsXG4gICdcXFxcdTAwODMnLFxuICAnXFxcXHUwMDg0JyxcbiAgJ1xcXFx1MDA4NScsXG4gICdcXFxcdTAwODYnLFxuICAnXFxcXHUwMDg3JyxcbiAgJ1xcXFx1MDA4OCcsXG4gICdcXFxcdTAwODknLFxuICAnXFxcXHUwMDhBJyxcbiAgJ1xcXFx1MDA4QicsXG4gICdcXFxcdTAwOEMnLFxuICAnXFxcXHUwMDhEJyxcbiAgJ1xcXFx1MDA4RScsXG4gICdcXFxcdTAwOEYnLFxuICAnXFxcXHUwMDkwJyxcbiAgJ1xcXFx1MDA5MScsXG4gICdcXFxcdTAwOTInLFxuICAnXFxcXHUwMDkzJyxcbiAgJ1xcXFx1MDA5NCcsXG4gICdcXFxcdTAwOTUnLFxuICAnXFxcXHUwMDk2JyxcbiAgJ1xcXFx1MDA5NycsXG4gICdcXFxcdTAwOTgnLFxuICAnXFxcXHUwMDk5JyxcbiAgJ1xcXFx1MDA5QScsXG4gICdcXFxcdTAwOUInLFxuICAnXFxcXHUwMDlDJyxcbiAgJ1xcXFx1MDA5RCcsXG4gICdcXFxcdTAwOUUnLFxuICAnXFxcXHUwMDlGJyxcbl07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printString.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printer.mjs":
/*!***************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printer.mjs ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   print: () => (/* binding */ print)\n/* harmony export */ });\n/* harmony import */ var _blockString_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blockString.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/blockString.mjs\");\n/* harmony import */ var _printString_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./printString.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printString.mjs\");\n/* harmony import */ var _visitor_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./visitor.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/visitor.mjs\");\n\n\n\n/**\n * Converts an AST into a string, using one set of reasonable\n * formatting rules.\n */\n\nfunction print(ast) {\n  return (0,_visitor_mjs__WEBPACK_IMPORTED_MODULE_0__.visit)(ast, printDocASTReducer);\n}\nconst MAX_LINE_LENGTH = 80;\nconst printDocASTReducer = {\n  Name: {\n    leave: (node) => node.value,\n  },\n  Variable: {\n    leave: (node) => '$' + node.name,\n  },\n  // Document\n  Document: {\n    leave: (node) => join(node.definitions, '\\n\\n'),\n  },\n  OperationDefinition: {\n    leave(node) {\n      const varDefs = wrap('(', join(node.variableDefinitions, ', '), ')');\n      const prefix = join(\n        [\n          node.operation,\n          join([node.name, varDefs]),\n          join(node.directives, ' '),\n        ],\n        ' ',\n      ); // Anonymous queries with no directives or variable definitions can use\n      // the query short form.\n\n      return (prefix === 'query' ? '' : prefix + ' ') + node.selectionSet;\n    },\n  },\n  VariableDefinition: {\n    leave: ({ variable, type, defaultValue, directives }) =>\n      variable +\n      ': ' +\n      type +\n      wrap(' = ', defaultValue) +\n      wrap(' ', join(directives, ' ')),\n  },\n  SelectionSet: {\n    leave: ({ selections }) => block(selections),\n  },\n  Field: {\n    leave({ alias, name, arguments: args, directives, selectionSet }) {\n      const prefix = wrap('', alias, ': ') + name;\n      let argsLine = prefix + wrap('(', join(args, ', '), ')');\n\n      if (argsLine.length > MAX_LINE_LENGTH) {\n        argsLine = prefix + wrap('(\\n', indent(join(args, '\\n')), '\\n)');\n      }\n\n      return join([argsLine, join(directives, ' '), selectionSet], ' ');\n    },\n  },\n  Argument: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Fragments\n  FragmentSpread: {\n    leave: ({ name, directives }) =>\n      '...' + name + wrap(' ', join(directives, ' ')),\n  },\n  InlineFragment: {\n    leave: ({ typeCondition, directives, selectionSet }) =>\n      join(\n        [\n          '...',\n          wrap('on ', typeCondition),\n          join(directives, ' '),\n          selectionSet,\n        ],\n        ' ',\n      ),\n  },\n  FragmentDefinition: {\n    leave: (\n      { name, typeCondition, variableDefinitions, directives, selectionSet }, // Note: fragment variable definitions are experimental and may be changed\n    ) =>\n      // or removed in the future.\n      `fragment ${name}${wrap('(', join(variableDefinitions, ', '), ')')} ` +\n      `on ${typeCondition} ${wrap('', join(directives, ' '), ' ')}` +\n      selectionSet,\n  },\n  // Value\n  IntValue: {\n    leave: ({ value }) => value,\n  },\n  FloatValue: {\n    leave: ({ value }) => value,\n  },\n  StringValue: {\n    leave: ({ value, block: isBlockString }) =>\n      isBlockString ? (0,_blockString_mjs__WEBPACK_IMPORTED_MODULE_1__.printBlockString)(value) : (0,_printString_mjs__WEBPACK_IMPORTED_MODULE_2__.printString)(value),\n  },\n  BooleanValue: {\n    leave: ({ value }) => (value ? 'true' : 'false'),\n  },\n  NullValue: {\n    leave: () => 'null',\n  },\n  EnumValue: {\n    leave: ({ value }) => value,\n  },\n  ListValue: {\n    leave: ({ values }) => '[' + join(values, ', ') + ']',\n  },\n  ObjectValue: {\n    leave: ({ fields }) => '{' + join(fields, ', ') + '}',\n  },\n  ObjectField: {\n    leave: ({ name, value }) => name + ': ' + value,\n  },\n  // Directive\n  Directive: {\n    leave: ({ name, arguments: args }) =>\n      '@' + name + wrap('(', join(args, ', '), ')'),\n  },\n  // Type\n  NamedType: {\n    leave: ({ name }) => name,\n  },\n  ListType: {\n    leave: ({ type }) => '[' + type + ']',\n  },\n  NonNullType: {\n    leave: ({ type }) => type + '!',\n  },\n  // Type System Definitions\n  SchemaDefinition: {\n    leave: ({ description, directives, operationTypes }) =>\n      wrap('', description, '\\n') +\n      join(['schema', join(directives, ' '), block(operationTypes)], ' '),\n  },\n  OperationTypeDefinition: {\n    leave: ({ operation, type }) => operation + ': ' + type,\n  },\n  ScalarTypeDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') +\n      join(['scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  FieldDefinition: {\n    leave: ({ description, name, arguments: args, type, directives }) =>\n      wrap('', description, '\\n') +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      ': ' +\n      type +\n      wrap(' ', join(directives, ' ')),\n  },\n  InputValueDefinition: {\n    leave: ({ description, name, type, defaultValue, directives }) =>\n      wrap('', description, '\\n') +\n      join(\n        [name + ': ' + type, wrap('= ', defaultValue), join(directives, ' ')],\n        ' ',\n      ),\n  },\n  InterfaceTypeDefinition: {\n    leave: ({ description, name, interfaces, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(\n        [\n          'interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeDefinition: {\n    leave: ({ description, name, directives, types }) =>\n      wrap('', description, '\\n') +\n      join(\n        ['union', name, join(directives, ' '), wrap('= ', join(types, ' | '))],\n        ' ',\n      ),\n  },\n  EnumTypeDefinition: {\n    leave: ({ description, name, directives, values }) =>\n      wrap('', description, '\\n') +\n      join(['enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  EnumValueDefinition: {\n    leave: ({ description, name, directives }) =>\n      wrap('', description, '\\n') + join([name, join(directives, ' ')], ' '),\n  },\n  InputObjectTypeDefinition: {\n    leave: ({ description, name, directives, fields }) =>\n      wrap('', description, '\\n') +\n      join(['input', name, join(directives, ' '), block(fields)], ' '),\n  },\n  DirectiveDefinition: {\n    leave: ({ description, name, arguments: args, repeatable, locations }) =>\n      wrap('', description, '\\n') +\n      'directive @' +\n      name +\n      (hasMultilineItems(args)\n        ? wrap('(\\n', indent(join(args, '\\n')), '\\n)')\n        : wrap('(', join(args, ', '), ')')) +\n      (repeatable ? ' repeatable' : '') +\n      ' on ' +\n      join(locations, ' | '),\n  },\n  SchemaExtension: {\n    leave: ({ directives, operationTypes }) =>\n      join(\n        ['extend schema', join(directives, ' '), block(operationTypes)],\n        ' ',\n      ),\n  },\n  ScalarTypeExtension: {\n    leave: ({ name, directives }) =>\n      join(['extend scalar', name, join(directives, ' ')], ' '),\n  },\n  ObjectTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend type',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  InterfaceTypeExtension: {\n    leave: ({ name, interfaces, directives, fields }) =>\n      join(\n        [\n          'extend interface',\n          name,\n          wrap('implements ', join(interfaces, ' & ')),\n          join(directives, ' '),\n          block(fields),\n        ],\n        ' ',\n      ),\n  },\n  UnionTypeExtension: {\n    leave: ({ name, directives, types }) =>\n      join(\n        [\n          'extend union',\n          name,\n          join(directives, ' '),\n          wrap('= ', join(types, ' | ')),\n        ],\n        ' ',\n      ),\n  },\n  EnumTypeExtension: {\n    leave: ({ name, directives, values }) =>\n      join(['extend enum', name, join(directives, ' '), block(values)], ' '),\n  },\n  InputObjectTypeExtension: {\n    leave: ({ name, directives, fields }) =>\n      join(['extend input', name, join(directives, ' '), block(fields)], ' '),\n  },\n};\n/**\n * Given maybeArray, print an empty string if it is null or empty, otherwise\n * print all items together separated by separator if provided\n */\n\nfunction join(maybeArray, separator = '') {\n  var _maybeArray$filter$jo;\n\n  return (_maybeArray$filter$jo =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.filter((x) => x).join(separator)) !== null &&\n    _maybeArray$filter$jo !== void 0\n    ? _maybeArray$filter$jo\n    : '';\n}\n/**\n * Given array, print each item on its own line, wrapped in an indented `{ }` block.\n */\n\nfunction block(array) {\n  return wrap('{\\n', indent(join(array, '\\n')), '\\n}');\n}\n/**\n * If maybeString is not null or empty, then wrap with start and end, otherwise print an empty string.\n */\n\nfunction wrap(start, maybeString, end = '') {\n  return maybeString != null && maybeString !== ''\n    ? start + maybeString + end\n    : '';\n}\n\nfunction indent(str) {\n  return wrap('  ', str.replace(/\\n/g, '\\n  '));\n}\n\nfunction hasMultilineItems(maybeArray) {\n  var _maybeArray$some;\n\n  // FIXME: https://github.com/graphql/graphql-js/issues/2203\n\n  /* c8 ignore next */\n  return (_maybeArray$some =\n    maybeArray === null || maybeArray === void 0\n      ? void 0\n      : maybeArray.some((str) => str.includes('\\n'))) !== null &&\n    _maybeArray$some !== void 0\n    ? _maybeArray$some\n    : false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/printer.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/source.mjs":
/*!**************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/source.mjs ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Source: () => (/* binding */ Source),\n/* harmony export */   isSource: () => (/* binding */ isSource)\n/* harmony export */ });\n/* harmony import */ var _jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../jsutils/devAssert.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/devAssert.mjs\");\n/* harmony import */ var _jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../jsutils/inspect.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/inspect.mjs\");\n/* harmony import */ var _jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../jsutils/instanceOf.mjs */ \"(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/jsutils/instanceOf.mjs\");\n\n\n\n\n/**\n * A representation of source input to GraphQL. The `name` and `locationOffset` parameters are\n * optional, but they are useful for clients who store GraphQL documents in source files.\n * For example, if the GraphQL input starts at line 40 in a file named `Foo.graphql`, it might\n * be useful for `name` to be `\"Foo.graphql\"` and location to be `{ line: 40, column: 1 }`.\n * The `line` and `column` properties in `locationOffset` are 1-indexed.\n */\nclass Source {\n  constructor(\n    body,\n    name = 'GraphQL request',\n    locationOffset = {\n      line: 1,\n      column: 1,\n    },\n  ) {\n    typeof body === 'string' ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(false, `Body must be a string. Received: ${(0,_jsutils_inspect_mjs__WEBPACK_IMPORTED_MODULE_1__.inspect)(body)}.`);\n    this.body = body;\n    this.name = name;\n    this.locationOffset = locationOffset;\n    this.locationOffset.line > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'line in locationOffset is 1-indexed and must be positive.',\n      );\n    this.locationOffset.column > 0 ||\n      (0,_jsutils_devAssert_mjs__WEBPACK_IMPORTED_MODULE_0__.devAssert)(\n        false,\n        'column in locationOffset is 1-indexed and must be positive.',\n      );\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'Source';\n  }\n}\n/**\n * Test if the given value is a Source object.\n *\n * @internal\n */\n\nfunction isSource(source) {\n  return (0,_jsutils_instanceOf_mjs__WEBPACK_IMPORTED_MODULE_2__.instanceOf)(source, Source);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/source.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/tokenKind.mjs":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/tokenKind.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenKind: () => (/* binding */ TokenKind)\n/* harmony export */ });\n/**\n * An exported enum describing the different kinds of tokens that the\n * lexer emits.\n */\nvar TokenKind;\n\n(function (TokenKind) {\n  TokenKind['SOF'] = '<SOF>';\n  TokenKind['EOF'] = '<EOF>';\n  TokenKind['BANG'] = '!';\n  TokenKind['DOLLAR'] = '$';\n  TokenKind['AMP'] = '&';\n  TokenKind['PAREN_L'] = '(';\n  TokenKind['PAREN_R'] = ')';\n  TokenKind['SPREAD'] = '...';\n  TokenKind['COLON'] = ':';\n  TokenKind['EQUALS'] = '=';\n  TokenKind['AT'] = '@';\n  TokenKind['BRACKET_L'] = '[';\n  TokenKind['BRACKET_R'] = ']';\n  TokenKind['BRACE_L'] = '{';\n  TokenKind['PIPE'] = '|';\n  TokenKind['BRACE_R'] = '}';\n  TokenKind['NAME'] = 'Name';\n  TokenKind['INT'] = 'Int';\n  TokenKind['FLOAT'] = 'Float';\n  TokenKind['STRING'] = 'String';\n  TokenKind['BLOCK_STRING'] = 'BlockString';\n  TokenKind['COMMENT'] = 'Comment';\n})(TokenKind || (TokenKind = {}));\n\n\n/**\n * The enum type representing the token kinds values.\n *\n * @deprecated Please use `TokenKind`. Will be remove in v17.\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/graphql@16.11.0/node_modules/graphql/language/tokenKind.mjs\n"));

/***/ })

}]);