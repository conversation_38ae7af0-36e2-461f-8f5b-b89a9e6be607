"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import clsx from "clsx";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useIsClient, useMediaQuery } from "usehooks-ts";
import paginate, { DOTS } from "./paginate";
import styles from "./Pagination.module.scss";

interface PaginationProps {
  currentPage?: number;
  totalPages: number;
  pageParamName?: string;
}

/**
 * The index of the pages starts at 1.
 */
export default function Pagination({
  currentPage = 1,
  totalPages,
  className,
  pageParamName: pageParameterName = "p",
  ...restProps
}: React.HTMLAttributes<HTMLElement> & PaginationProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isMobileViewport = useMediaQuery("(max-width: 767px)");
  const isDesktopViewport = useMediaQuery("(min-width: 1216px)");
  const [pages, setPages] = useState<ReturnType<typeof paginate>>([]);
  const isClient = useIsClient();

  useEffect(() => {
    setPages(paginate(currentPage, totalPages, isDesktopViewport ? 2 : 1));
  }, [currentPage, totalPages, isDesktopViewport]);

  // Return the URL to the given page.
  const getPageUrl = (page: number) => {
    const params = new URLSearchParams(searchParams?.toString());

    params.set(pageParameterName, page.toString());
    return pathname + "?" + params.toString();
  };

  // Not enough pages to render the pagination
  if (totalPages < 2) {
    return null;
  }

  return (
    <nav className={clsx(styles.pagination, className)} role="navigation" aria-label="Pagination" {...restProps}>
      <ul className={styles.pageList}>
        {/* Previous Button */}
        <li className={clsx(styles.page, styles.previousPage)} aria-hidden={currentPage === 1 ? "true" : undefined}>
          {currentPage > 1 ? (
            <Tooltip enabled={!isDesktopViewport && isClient} content="Précédent">
              <Link
                className={styles.pageLink}
                href={getPageUrl(currentPage - 1)}
                aria-label={`Précédent - Page ${currentPage - 1} sur ${totalPages}`}
              >
                <i className="far fa-chevron-left" aria-hidden="true"></i>
                <span className={styles.text}>Précédent</span>
              </Link>
            </Tooltip>
          ) : (
            <span className={clsx(styles.pageLink, styles.disabled)}>
              <i className="far fa-chevron-left" aria-hidden="true"></i>
              <span className={styles.text}>Précédent</span>
            </span>
          )}
        </li>

        {/* Pages */}
        {isMobileViewport ? (
          <li className={styles.counter}>
            <span aria-current="page">{currentPage}</span>
            <span>sur</span>
            <span>{totalPages}</span>
          </li>
        ) : (
          pages.map((page, index) => (
            <li
              key={index}
              className={clsx(styles.page, page === currentPage && styles.active)}
              aria-hidden={page === DOTS ? "true" : undefined}
            >
              {page === DOTS ? (
                <span className={styles.pageLink}>...</span>
              ) : (page === currentPage ? (
                <span className={styles.pageLink} aria-current="page">
                  {page}
                </span>
              ) : (
                <Link href={getPageUrl(page)} className={styles.pageLink} aria-label={`Page ${page} sur ${totalPages}`}>
                  {page}
                </Link>
              ))}
            </li>
          ))
        )}

        {/* Next Button */}
        <li
          className={clsx(styles.page, styles.nextPage)}
          aria-hidden={currentPage === totalPages ? "true" : undefined}
        >
          {currentPage < totalPages ? (
            <Tooltip enabled={!isDesktopViewport && isClient} content="Suivant">
              <Link
                href={getPageUrl(currentPage + 1)}
                className={styles.pageLink}
                aria-label={`Suivant - Page ${currentPage + 1} sur ${totalPages}`}
              >
                <span className={styles.text}>Suivant</span>
                <i className="far fa-chevron-right" aria-hidden="true"></i>
              </Link>
            </Tooltip>
          ) : (
            <span className={clsx(styles.pageLink, styles.disabled)}>
              <span className={styles.text}>Suivant</span>
              <i className="far fa-chevron-right" aria-hidden="true"></i>
            </span>
          )}
        </li>
      </ul>
    </nav>
  );
}
