import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import StepIndicator from "./StepIndicator";

const meta: Meta<typeof StepIndicator> = {
  title: "Form/StepIndicator",
  component: StepIndicator,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof StepIndicator>;

const formStep = {
  title: "Étape #1",
  description: "Description de l'étape",
  stepNumber: 0,
  fields: [],
  prev: {
    label: "Précédent",
    condition: null,
    icon: null,
  },
  next: {
    label: "Suivant",
    condition: null,
    icon: null,
  },
  condition: null,
};

export const Default: Story = {
  args: {
    totalSteps: 4, // 3 form steps + 1 recap step
    currentStep: 0,
    steps: [
      { ...formStep, stepNumber: 0, title: "Informations personnelles" },
      { ...formStep, stepNumber: 1, title: "Adresse et contact" },
      { ...formStep, stepNumber: 2, title: "Préférences" },
    ],
  },
};

export const SecondStep: Story = {
  args: {
    totalSteps: 4,
    currentStep: 1,
    steps: [
      { ...formStep, stepNumber: 0, title: "Informations personnelles" },
      { ...formStep, stepNumber: 1, title: "Adresse et contact" },
      { ...formStep, stepNumber: 2, title: "Préférences" },
    ],
  },
};

export const LastFormStep: Story = {
  args: {
    totalSteps: 4,
    currentStep: 2,
    steps: [
      { ...formStep, stepNumber: 0, title: "Informations personnelles" },
      { ...formStep, stepNumber: 1, title: "Adresse et contact" },
      { ...formStep, stepNumber: 2, title: "Préférences" },
    ],
  },
};

export const RecapStep: Story = {
  args: {
    totalSteps: 4,
    currentStep: 3, // This will be the recap step
    steps: [
      { ...formStep, stepNumber: 0, title: "Informations personnelles" },
      { ...formStep, stepNumber: 1, title: "Adresse et contact" },
      { ...formStep, stepNumber: 2, title: "Préférences" },
    ],
  },
};
