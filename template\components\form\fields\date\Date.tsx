"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { DateField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type DateProps = Omit<DateField, "__typename">;

export default function Date({
  name,
  autocomplete,
  defaultValue,
  label,
  description,
  placeholder,
  required,
  condition,
  validationMessage,
  columnSpan,
}: DateProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const inputId = useId();
  const errorId = useId();

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <Textfield
          id={inputId}
          type="date"
          startIcon="far fa-calendar"
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          placeholder={placeholder ?? undefined}
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
          {...register(name, { required })}
        />
        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
