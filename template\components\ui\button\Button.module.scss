@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.button {
  display: inline-flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  border-radius: 4px;
  transition:
    background-color 200ms ease-in-out,
    color 200ms ease-in-out,
    border-color 200ms ease-in-out;

  &.variant-contained {
    border: 2px solid transparent;

    &.color-primary {
      color: $color-white;
      background-color: $color-primary-500;

      &:hover {
        background-color: $color-primary-400;
      }
    }

    &.color-secondary {
      color: $color-black;
      background-color: $color-secondary-500;

      &:hover {
        background-color: $color-secondary-300;
      }
    }

    &.color-tertiary {
      color: $color-black;
      background-color: $color-tertiary-500;

      &:hover {
        background-color: $color-tertiary-300;
      }
    }

    &.color-primary-inverted {
      color: $color-white;
      background-color: transparent;

      &:hover {
        color: $color-primary-500;
        background-color: $color-primary-50;
      }
    }

    &.color-secondary-inverted,
    &.color-tertiary-inverted {
      color: $color-black;
      background-color: transparent;

      &:hover {
        background-color: $color-white;
      }
    }

    &.color-danger-inverted {
      color: $color-white;
      background-color: transparent;

      &:hover {
        color: $color-red;
        background-color: $color-white;
      }
    }
  }

  // TODO: Add secondary + tertiary colors
  &.variant-outlined {
    &.color-primary {
      color: $color-primary-500;
      background-color: transparent;
      border: 2px solid currentcolor;

      &:hover {
        background-color: $color-primary-50;
      }
    }

    &.color-primary-inverted {
      color: $color-white;
      background-color: transparent;
      border: 2px solid currentcolor;

      &:hover {
        color: $color-primary-500;
        background-color: $color-primary-50;
        border-color: $color-primary-50;
      }
    }

    &.color-secondary-inverted,
    &.color-tertiary-inverted {
      color: $color-black;
      background-color: transparent;
      border: 2px solid currentcolor;

      &:hover {
        background-color: $color-primary-50;
      }
    }

    &.color-danger-inverted {
      color: $color-white;
      background-color: transparent;
      border: 2px solid currentcolor;

      &:hover {
        color: $color-red;
        background-color: $color-primary-50;
      }
    }
  }

  // TODO: Add secondary + tertiary colors
  &.variant-text {
    border: 2px solid transparent;

    &.color-primary {
      color: $color-primary-500;

      &:hover {
        background-color: $color-primary-50;
      }
    }
  }

  &.size-xs {
    padding: 6px 12px;
    font-size: 1.2rem;

    i {
      font-size: 1.4rem;
    }
  }

  &.size-sm {
    padding: 8px 16px;
    font-size: 1.4rem;

    i {
      font-size: 1.6rem;
    }
  }

  &.size-md,
  &.size-lg {
    padding: 11px 24px;
    font-size: 1.6rem;
    font-weight: 700;

    i {
      font-size: 2rem;
      font-weight: 400;
    }
  }

  // Increase large button size on desktop
  &.size-lg {
    @include breakpoint(large up) {
      gap: 16px;
      padding: 18px 32px;
      font-size: 1.8rem;

      i {
        font-size: 2.4rem;
      }
    }
  }

  .startIcon,
  .endIcon {
    line-height: 1;
  }

  // FontAwesome icons
  // TODO: Move this to reset styles
  i {
    vertical-align: middle;
  }

  &[aria-disabled="true"] {
    pointer-events: none;
    cursor: not-allowed;
    opacity: 0.3;
  }
}
