{"..\\node_modules\\.pnpm\\@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997\\node_modules\\@apollo\\client-react-streaming\\dist\\index.cc.js -> ./SimulatePreloadedQuery.cc.js": {"id": "..\\node_modules\\.pnpm\\@apollo+client-react-stream_04166d0d33e8adfc335aa646c6884997\\node_modules\\@apollo\\client-react-streaming\\dist\\index.cc.js -> ./SimulatePreloadedQuery.cc.js", "files": ["static/chunks/_app-pages-browser_node_modules_pnpm_apollo_client-react-stream_04166d0d33e8adfc335aa646c6884-526e87.js"]}, "..\\node_modules\\.pnpm\\next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\.pnpm\\next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_pnpm_next_15_3_4__babel_core_7_2_fce8dae288080ed16dfc7396f7c8-785a03.js"]}, "components\\ui\\alert\\AlertContainer.tsx -> ./AlertRenderer": {"id": "components\\ui\\alert\\AlertContainer.tsx -> ./AlertRenderer", "files": ["static/chunks/_app-pages-browser_components_ui_alert_AlertRenderer_tsx.js"]}, "components\\ui\\alert\\AlertRenderer.tsx -> ./PopUpAlert": {"id": "components\\ui\\alert\\AlertRenderer.tsx -> ./PopUpAlert", "files": ["static/css/_app-pages-browser_components_ui_alert_PopUpAlert_tsx.css", "static/chunks/_app-pages-browser_components_ui_alert_PopUpAlert_tsx.js"]}, "components\\ui\\alert\\AlertRenderer.tsx -> ./StickyNote": {"id": "components\\ui\\alert\\AlertRenderer.tsx -> ./StickyNote", "files": ["static/css/_app-pages-browser_components_ui_alert_StickyNote_tsx.css", "static/chunks/_app-pages-browser_components_ui_alert_StickyNote_tsx.js"]}, "components\\ui\\alert\\StickyNote.tsx -> @/components/ui/button/Button": {"id": "components\\ui\\alert\\StickyNote.tsx -> @/components/ui/button/Button", "files": ["static/css/_app-pages-browser_components_ui_button_Button_tsx.css", "static/chunks/_app-pages-browser_components_ui_button_Button_tsx.js"]}}