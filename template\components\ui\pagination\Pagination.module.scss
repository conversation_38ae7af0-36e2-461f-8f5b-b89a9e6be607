@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.pagination {
  max-width: 1248px;
  margin-inline: auto;
  margin-top: 24px;
  font-size: 1.4rem;
  font-weight: 400;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    margin-top: 48px;
  }

  @include breakpoint(large up) {
    margin-top: 32px;
  }

  &:first-child {
    margin-top: 0;
  }
}

.pageList {
  display: flex;
  gap: 6px;
  align-items: center;
  width: 100%;
}

.page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 48px;
  font-weight: 700;

  &.active {
    .pageLink {
      position: relative;
      color: $color-primary-500;

      &::after {
        position: absolute;
        bottom: 0;
        width: 100%;
        content: "";
        border-bottom: 4px solid $color-primary-500;
      }
    }
  }
}

.pageLink {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  min-height: 48px;
  padding-inline: 24px;
  border-radius: 4px;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

a.pageLink {
  cursor: pointer;

  &:hover {
    color: $color-primary-500;
    background: $color-primary-50;
  }
}

.previousPage {
  margin-right: auto;
}

.nextPage {
  margin-left: auto;
}

.previousPage,
.nextPage {
  flex-shrink: 0;

  i {
    @include size(20px);

    font-size: 2rem;
    text-align: center;
  }

  .text {
    font-size: 1.6rem;
    font-weight: 700;
  }

  @include breakpoint(medium down) {
    .pageLink {
      padding-inline: 0;
    }

    .text {
      @include visually-hidden;
    }
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}

.dots {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24px;
  font-weight: 700;
  color: $color-neutral-500;

  &::before {
    color: $color-neutral-500;
    content: "...";
  }
}

.counter {
  display: flex;
  gap: 12px;
  align-items: center;
  padding-inline: 24px;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: normal;
  color: $color-neutral-500;
}
