@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.directory {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: $space-6;
  width: 100%;
  padding: $space-7 $space-6;
  background-color: $color-neutral-100;

  @include breakpoint(medium up) {
    padding: 24px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    padding: 32px;
  }

  @container (min-width: 1200px) {
    flex-direction: row;
  }
}

.contentWrapper {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  gap: 24px;

  @include breakpoint(large up) {
    gap: 32px;
  }

  @container (min-width: 700px) {
    flex-direction: row;
    height: fit-content;
  }
}

.image {
  align-self: flex-start;
  order: -1;
  width: 100%;
  max-width: 176px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include breakpoint(large up) {
    gap: 32px;
  }

  @container (min-width: 700px) {
    align-self: center;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.roles {
  display: flex;
  flex-direction: column;
  gap: 6px;
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 130%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    gap: 8px;
  }

  li:first-child {
    font-size: 1.6rem;
    line-height: 110%;
    color: $color-black;

    @include breakpoint(medium up) {
      font-size: 1.8rem;
    }
  }
}

.info {
  padding-left: 12px;
  font-size: 1.6rem;
  line-height: 110%;
  border-left: 1px solid $color-neutral-300;

  @include breakpoint(medium up) {
    padding-left: 16px;
    font-size: 1.8rem;
  }

  strong {
    display: block;
    margin-bottom: 6px;
    font-weight: 700;

    @include breakpoint(large up) {
      margin-bottom: 8px;
    }
  }
}

.actions {
  position: relative;
  z-index: $layer-link-block + 1;
  display: flex;
  flex-direction: column;
  grid-area: actions;
  gap: 6px;
  width: 100%;

  @container (min-width: 700px) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(20cqi, 1fr));
  }

  @container (min-width: 700px) and (max-width: 820px) {
    // Expand the website button to full width if present
    .websiteButton:nth-child(4):last-child {
      grid-column: span 3;
    }
  }

  @container (min-width: 1200px) {
    display: flex;
    flex-direction: column;
    align-self: center;
    max-width: 250px;
  }

  @include breakpoint(large up) {
    gap: 8px;
  }
}
