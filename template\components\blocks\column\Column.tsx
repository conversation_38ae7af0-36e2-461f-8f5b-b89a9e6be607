import { ColumnBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import React from "react";
import styles from "./Column.module.scss";

type ColumnProps = Partial<Omit<ColumnBlock, "__typename" | "innerBlocks">>;

export default function Column({ anchor, children, width }: React.PropsWithChildren<ColumnProps>) {
  return (
    <div
      id={anchor ?? undefined}
      className={clsx("block-column", styles.column)}
      style={{ flexBasis: width ?? undefined, flexGrow: width ? 0 : undefined }}
    >
      {children}
    </div>
  );
}
