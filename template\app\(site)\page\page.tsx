import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Heading from "@/components/ui/heading/HeadingImageBottom";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const PAGE_QUERY = graphql(`
  query GetPage($url: URL) {
    route(url: $url) {
      ... on Page {
        title
        status
        structuredContent
        leadText
        surtitle
        publicationDate
        modifiedDate
        images {
          ratio_21x9 {
            url
            width
            height
            alt
          }
        }
        categories {
          description
          relativeUrl
          title
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }

    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: PAGE_QUERY,
    variables: { url: await getCurrentServerUrl() },
  });

  assert.ok(data.route?.__typename === "Page");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  // TODO: Handle error as 500 or maintenance page
  const { data } = await query({
    query: PAGE_QUERY,
    variables: { url: await getCurrentServerUrl() },
  });

  assert.ok(data.route?.__typename === "Page");

  const { title, leadText, images, structuredContent, publicationDate, modifiedDate, breadcrumbs, surtitle } =
    data.route;
  const { socialShare } = data.siteConfig ?? {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <Heading
        title={title ?? "Sans titre"}
        surtitle={surtitle}
        leadText={leadText}
        imageSrc={images?.ratio_21x9?.url}
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />
      <div className="layout-1column-fullwidth">
        <div className="column main">
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
      </div>
    </>
  );
}
