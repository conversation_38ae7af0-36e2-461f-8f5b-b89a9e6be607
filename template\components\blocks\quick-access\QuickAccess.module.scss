@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.quickAccess {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-block: 40px;

  @include breakpoint(large up) {
    flex-direction: row;
    gap: 32px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.swiperContainer {
  display: flex;
  width: 100%;
  min-width: 0;

  :global(.swiper) {
    width: 100%;
  }
}

.prevButton,
.nextButton {
  @include size(72px);

  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  color: $color-neutral-500;
  cursor: pointer;
  border-radius: 4px;

  i {
    font-size: 2.4rem;
    line-height: 110%;
  }
}

.nextButton {
  order: 2;
}

.item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  color: $color-neutral-700;
  cursor: pointer;

  &:hover {
    color: $color-primary-500;
  }

  @if $focus-outline-offset {
    &:focus-visible {
      outline-offset: $focus-outline-offset * -1; // Makes the outline fully visible in the slider track
    }
  }

  .icon {
    @include size(40px);

    color: $color-primary-500;
  }

  // TODO: Line clamp
  .text {
    font-size: 1.8rem;
    line-height: 110%;
  }
}

.navigationDisabled {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.4;
}

.navigationHidden {
  display: none;
}
