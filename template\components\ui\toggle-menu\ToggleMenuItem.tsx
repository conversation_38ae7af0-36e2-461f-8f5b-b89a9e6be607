"use client";

import { Content, Header, Item, Trigger } from "@radix-ui/react-accordion";
import clsx from "clsx";
import Link from "next/link";
import { useId } from "react";
import styles from "./ToggleMenuItem.module.scss";

interface ToggleMenuItemProps {
  title: string;
  url: string;
  level: number;
  children: React.ReactNode;
}

export default function ToggleMenuItem({ children, title, level, url }: ToggleMenuItemProps) {
  const value = useId();

  const levelClass = `level-${level}`;

  return (
    <li>
      <Item id={value} value={value}>
        <Header asChild className={clsx(styles.trigger, styles[levelClass])}>
          <span>
            <Link href={url} className={styles.title} aria-label={title} prefetch={false}>
              {title}
            </Link>
            <Trigger aria-labelledby={value}>
              <i className={clsx("far fa-plus", styles.openIcon)} aria-hidden="true"></i>
              <i className={clsx("far fa-minus", styles.closedIcon)} aria-hidden="true"></i>
            </Trigger>
          </span>
        </Header>
        <Content className={clsx(styles.content, styles[levelClass])} aria-labelledby={value}>
          {children}
        </Content>
      </Item>
    </li>
  );
}
