import Hx from "@/components/ui/title/Hx";
import clsx from "clsx";
import Link from "next/link";
import styles from "./HeroContent.module.scss";

interface HeroContentProps {
  className?: string;
  headingLevel?: number;
  surtitle?: string | null;
  url?: string | null;
  noIcon?: boolean;
  title?: string | null;
}

export default function HeroContent(props: HeroContentProps) {
  const { className, title, surtitle, url, noIcon = false, headingLevel } = props;

  if (!title && !surtitle && !url) return null;

  const content = (
    <>
      <Hx level={headingLevel ?? 2} className={styles.title}>
        {surtitle && (
          <span className={styles.surtitle}>
            {surtitle}
            <span className="sr-only">:</span>
          </span>
        )}
        {title}
      </Hx>
      {!noIcon && url && <i className={clsx("far fa-plus", styles.plusIcon)} aria-hidden="true" />}
    </>
  );

  return url ? (
    <div className={clsx(styles.heroContent, className)}>
      <Link href={url}>{content}</Link>
    </div>
  ) : (
    <div className={clsx(styles.heroContent, className)}>{content}</div>
  );
}
