@use "sass:list";
@use "sass:map";
@use "sass:math";
@use "sass:meta";
@use "sass:string";
@use "variables.scss" as *;

%icons-font-aliasing {
  display: inline-block;
  font-family: var(--font-awesome-typo);
  font-style: normal;
  font-weight: var(--fw-normal);
  font-variant: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@function to-list($value) {
  @return if(meta.type-of($value) != list, ($value), $value);
}

@function map-next($map, $key) {
  $values: map.keys($map);
  $i: 0;

  @if map.has-key($map, $key) {
    $i: list.index($values, $key) + 1;
  }

  @if $i > list.length($map) or $i == 0 {
    @return null;
  } @else {
    @return map.get($map, list.nth($values, $i));
  }
}

@mixin breakpoint($request) {
  @if list.nth(map.values($breakpoints), 1) != 0 {
    @error 'Your smallest breakpoint (defined in $breakpoints) must be set to "0".';
  }

  $request: to-list($request);
  $size: list.nth($request, 1);
  $direction: list.nth($request, 2);

  @if not list.index((only, down, up), $direction) {
    @error 'No such direction: #{$direction}';

    $direction: "up";
  }

  $bp: 0;

  @if map.has-key($breakpoints, $size) {
    $bp: map.get($breakpoints, $size);
  } @else {
    @error 'No such breakpoint!';
  }

  $bp2: map-next($breakpoints, $size);
  $edge-values: if($bp == 0 or $bp2 == null, true, false);

  // if smallest
  @if $bp == 0 {
    // xsmall, only
    @if $direction == only or $direction == down {
      @media screen and (max-width: #{$bp2 - 1}) {
        @content;
      }
    } @else {
      @content;
    }
  }

  // if largest
  @if not $bp2 {
    @if $direction == down {
      @content;
    } @else {
      @media screen and (min-width: $bp) {
        @content;
      }
    }
  }

  @if $direction == down and not $edge-values {
    // Any bp, and down
    @media screen and (max-width: #{$bp2 - 1}) {
      @content;
    }
  } @else if $direction == only and not $edge-values {
    // Any bp, and only
    @media screen and (min-width: $bp) and (max-width: #{$bp2 - 1}) {
      @content;
    }
  } @else if not $edge-values {
    // Any bp, and up
    @media screen and (min-width: $bp) {
      @content;
    }
  }
}

// Insert custom breakpoint with custom max viewport value.
@mixin media-max($breakpoint, $orientation: false) {
  @if $orientation {
    @media screen and (max-width: #{$breakpoint}px) and (orientation: $orientation) {
      @content;
    }
  } @else {
    @media screen and (max-width: #{$breakpoint}px) {
      @content;
    }
  }
}

// Insert custom breakpoint with custom min viewport value.
@mixin media-min($breakpoint) {
  @media screen and (min-width: #{$breakpoint}px) {
    @content;
  }
}

// Add styles for :hover/:focus state
@mixin on-event {
  &:hover,
  &:focus,
  &:focus-within {
    @content;
  }
}

// Clear thread after float;
@mixin clear {
  &::after {
    clear: both;
    display: block;
    visibility: hidden;
    height: 0;
    overflow: hidden;
    content: "";
  }
}

// Set width and height for element
@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

// Set max-width and max-height for element
@mixin max-size($width, $height: $width) {
  max-width: $width;
  max-height: $height;
}

// Set min-width and min-height for element
@mixin min-size($width, $height: $width) {
  min-width: $width;
  min-height: $height;
}

// Use this for creating scalable elements (usually images / background images) that maintain a ratio.
@mixin responsive-ratio($x, $y, $pseudo) {
  $padding: math.div($y, $x) * 100 + "%";

  @if $pseudo == "before" {
    &::before {
      display: block;
      width: 100%;
      padding-top: $padding;
      content: "";
    }
  } @else if $pseudo == "after" {
    &::after {
      display: block;
      width: 100%;
      padding-top: $padding;
      content: "";
    }
  } @else {
    padding-top: $padding;
  }
}

// Set object fit property include polyfill values
// @param {string} $fit - object-fit value
// @param {string} $position - object-fit-position value
@mixin object-fit($fit: "cover", $position: "center") {
  font-family: "object-fit: #{$fit}; object-position: #{$position};";
  object-fit: string.unquote($fit);
}

// Add styles if EDGE
@mixin if-edge() {
  @supports (-ms-ime-align: auto) {
    @content;
  }
}

// Add styles for multiline underline
@mixin multiline-underline($size: 0.09em, $color: currentColor) {
  width: calc(100%);
  text-decoration: none;
  outline: none !important;
  background-image: linear-gradient(transparent calc(100% - #{$size}), #{$color} #{$size});
  background-repeat: no-repeat;
  background-size: 0 100%;
  transition: background-size 300ms ease-in-out;
}

// Set absolute position with settings
@mixin absolute($params...) {
  position: absolute;

  @if list.length($params) > 0 {
    $params: if(list.length($params) == 1, list.nth($params, 1), $params);
    $props: (top, right, bottom, left);

    @for $index from 1 through list.length($params) {
      #{list.nth($props, $index)}: list.nth($params, $index);
    }
  }
}

// Set fixed position with settings
@mixin fixed($params...) {
  position: fixed;

  @if list.length($params) > 0 {
    $params: if(list.length($params) == 1, list.nth($params, 1), $params);
    $props: (top, right, bottom, left);

    @for $index from 1 through list.length($params) {
      #{list.nth($props, $index)}: list.nth($params, $index);
    }
  }
}

// Set relative position with settings
@mixin relative($params...) {
  position: relative;

  @if list.length($params) > 0 {
    $params: if(list.length($params) == 1, list.nth($params, 1), $params);
    $props: (top, right, bottom, left);

    @for $index from 1 through list.length($params) {
      #{list.nth($props, $index)}: list.nth($params, $index);
    }
  }
}

// Reset position to static
@mixin reset-position {
  position: static;
  inset: auto;
}

// Center element inside block (absolute position)
@mixin abs-center {
  @include absolute(50%, null, null, 50%);

  transform: translate(-50%, -50%);
}

// Center block horizontally
@mixin center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}

// Stretches the block from the container to full width
@mixin full-width-block {
  position: relative;
  left: 50%;
  width: calc(100vw - var(--scrollbar-width));
  max-width: 1920px;
  padding: 0;
  margin: 0;
  transform: translateX(-50%);
}

// Create cover block
@mixin coverer {
  @include size(100%);
  @include absolute(0, null, null, 0);
}

// Mixin for creation title decor
// styles: default, primary, center`
@mixin title-decor($style: default) {
  position: relative;
  z-index: 1;

  &::before {
    z-index: -1;
    content: "";
    background-color: var(--color-2--1);
  }

  @if $style == default {
    &::before {
      @include size(68px, 12px);
      @include absolute(null, null, 0, 20px);

      @include breakpoint(medium down) {
        @include size(50px, 8px);
      }

      @include breakpoint(small down) {
        left: 50%;
        transform: translateX(-50%);
      }
    }
  } @else if $style == primary {
    &::before {
      @include size(195px, 41px);
      @include absolute(45px, null, null, -77px);

      @include breakpoint(medium down) {
        @include size(151px, 32px);

        top: 32px;
        left: -62px;
      }

      @include breakpoint(small down) {
        @include size(114px, 28px);

        top: 23px;
        left: -20px;
        transform: translateX(0);
      }
    }
  } @else if $style == center {
    &::before {
      @include size(68px, 12px);
      @include absolute(null, null, 0, 50%);

      transform: translateX(-50%);
    }
  }
}

// Mixin for creation background patterns with svg
@mixin bg-pattern($color, $opacity: 1, $property: "background-image") {
  $svg: '<svg fill="#{$color}" fill-opacity="#{$opacity}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3 3" width="3" height="3"><rect width="1" height="1"/></svg>';

  #{$property}: inline-svg($svg);
  background-size: 3px;
}

// Fancy and smooth linear gradient
// @url https://css-tricks.com/easing-linear-gradients/
@mixin scrim-gradient($color: $color-white, $direction: "to top") {
  $scrim-coordinates: (
    0: 1,
    19: 0.738,
    34: 0.541,
    47: 0.382,
    56.5: 0.278,
    65: 0.194,
    73: 0.126,
    80.2: 0.075,
    86.1: 0.042,
    91: 0.021,
    95.2: 0.008,
    98.2: 0.002,
    100: 0,
  );
  $stops: ();

  @each $stop, $alpha in $scrim-coordinates {
    $stops: list.append($stops, rgba($color, $alpha) $stop * 1%, comma);
  }

  background: linear-gradient(string.unquote($direction), $stops);
}

// get-triangle function
// @url http://apps.eky.hk/css-triangle-generator/
// @param type - one of allowed params listed in $triangles map
// @param - $w - width, optional, only for kind = 2
// @param - $h - height, optional, only for kind = 2
// @param kind - what to get border-color position or border-width? 1 or 2
@function get-triangle($type, $w: 10, $h: 10, $kind: 2) {
  $triangles: (
    "top": (
      "bottom",
      0 ($w * 0.5) $h ($w * 0.5),
    ),
    "right": (
      "left",
      ($h * 0.5) 0 ($h * 0.5) $w,
    ),
    "bottom": (
      "top",
      $h ($w * 0.5) 0 ($w * 0.5),
    ),
    "left": (
      "right",
      ($h * 0.5) $w ($h * 0.5) 0,
    ),
    "top-left": (
      "top",
      $h $w 0 0,
    ),
    "top-right": (
      "right",
      0 $w $h 0,
    ),
    "bottom-right": (
      "bottom",
      0 0 $h $w,
    ),
    "bottom-left": (
      "left",
      $h 0 0 $w,
    ),
  );
  $list: map.get($triangles, $type);

  @if not $list {
    @error 'Unknown key $type: #{$type}, specify the one of listed in $triangles map.';
  }

  @return list.nth($list, $kind);
}

@mixin triangle($type, $color, $w, $h) {
  z-index: 1;
  border-color: transparent;
  border-style: solid;
  border-width: get-triangle($type, $w, $h);
  border-#{get-triangle($type, 0, 0, 1)}-color: $color;
}

// Hide text without icon (old-version)
@mixin hide-text {
  overflow: hidden;
  text-align: left;
  text-indent: -9999px;
  white-space: nowrap;
}

// Hide text with icon
@mixin hide-text-with-icon {
  @include hide-text;

  position: relative;

  &::before,
  &::after {
    @include abs-center;

    text-indent: 0;
  }
}

// Add inline icon before
@mixin icon($position, $icon, $ff: null, $va: null, $fw: null) {
  &::#{$position} {
    @extend %icons-font-aliasing;

    font-family: $ff;
    font-weight: $fw;
    vertical-align: $va;
    content: if(string.index($icon, "attr") or string.index($icon, "var"), $icon, string.unquote('"#{ $icon }"'));
  }
}

@mixin icon-before($icon, $ff: null, $va: null, $fw: null) {
  @include icon("before", $icon, $ff, $va, $fw);
}

@mixin icon-after($icon, $ff: null, $va: null, $fw: null) {
  @include icon("after", $icon, $ff, $va, $fw);
}

// Shadow for section
@mixin section-shadow() {
  position: relative;

  &::before,
  &::after {
    @include size(calc(40% - 30px), 15px);

    position: absolute;
    bottom: 20px;
    z-index: -1;
    display: block;
    content: "";
    background-color: transparent;
    box-shadow: 0 20px 15px 0 rgba($color-black, 0.5);

    @include breakpoint(medium down) {
      width: calc(52% - 30px);
    }

    @include breakpoint(small down) {
      @include size(calc(57% - 32px), 5px);

      box-shadow: 0 20px 5px 0 rgb(0 0 0 / 35%);
    }
  }

  &::before {
    left: 70px;
    transform: rotate(-3deg) skewX(-60deg);

    @include breakpoint(medium down) {
      left: 60px;
      transform: rotate(-2deg) skewX(-60deg);
    }

    @include breakpoint(small down) {
      left: 45px;
    }
  }

  &::after {
    right: 70px;
    visibility: visible;
    transform: rotate(3deg) skewX(60deg);

    @include breakpoint(medium down) {
      right: 60px;
      transform: rotate(2deg) skewX(60deg);
    }

    @include breakpoint(small down) {
      right: 45px;
    }
  }
}

// Add styles for font-awesome icon inside block
@mixin fa-icon-style($pseudo: true) {
  span[class*="fa-"] {
    @if $pseudo == true {
      &::before {
        @content;
      }
    } @else {
      @content;
    }
  }
}

// Add inverted styles for element
@mixin add-inverted-styles {
  .inverted &,
  .is-inverted &,
  &.is-inverted {
    @content;
  }
}

// Make an element visually hidden, but still readable by assistive technology (a11y)
@mixin visually-hidden {
  position: absolute !important;
  top: auto !important;
  left: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  overflow: hidden !important;
  border: 0 !important;
  clip: rect(1px, 1px, 1px, 1px);
}

// Hide element on provided breakpoint
@mixin hide-on($breakpoint: small down) {
  @include breakpoint($breakpoint) {
    display: none;
  }
}

// Show element on provided breakpoint
@mixin show-on($breakpoint: small down, $display: block) {
  @include breakpoint($breakpoint) {
    display: $display;
  }
}

// Align the info block to the left and center
@mixin set-infos-middle {
  position: relative;
  left: 50%;
  display: block;
  flex-grow: 0;
  width: auto;
  text-align: left;
  transform: translateX(-50%);
}

// Line decor
@mixin line-decor($lw: 35px, $lh: 4px, $position: "before", $color: var(--color-2--1)) {
  &::#{$position} {
    @include size($lw, $lh);

    display: block;
    content: "";
    background-color: $color;
  }
}

// Change color of background svg images
@mixin decor-mask($name, $color, $width, $height: $width, $position: "before") {
  &::#{$position} {
    @include size($width, $height);

    display: block;
    pointer-events: none;
    content: "";
    background-color: $color;
    mask-image: image("icons/#{$name}.svg");
    mask-repeat: no-repeat;
    mask-size: contain;
  }
}

// overlay gradient
@mixin gradient-overlay(
  $from: rgba(0, 0, 0, 0.6),
  $mid1: rgba(0, 0, 0, 0.52),
  $mid2: rgba(0, 0, 0, 0.52),
  $to: rgba(0, 0, 0, 0)
) {
  position: absolute;
  inset: 0;
  z-index: 1;
  pointer-events: none;
  content: "";
  background: linear-gradient(0deg, #{$from} 0%, #{$mid1} 28.58%, #{$mid2} 45.02%, #{$to} 100%);
}

// Remove the marker on a ol, ul, menu element items.
// The property `list-style(-type)` removes the list role on these elements.
// https://www.havardbrynjulfsen.design/writing/a-new-way-to-remove-list-styles-in-safari
@mixin remove-list-styles {
  list-style-type: "";
}

@mixin restore-list-styles {
  list-style-type: revert;
}

@mixin container-breakpoint($breakpoint, $coverage) {
  @if not map.has-key($breakpoints, $breakpoint) {
    @error "Invalid breakpoint: #{$breakpoint}";
  }

  @if $coverage != "only" and $coverage != "up" and $coverage != "down" {
    @error "Invalid breakpoint coverage: #{$coverage}. Must be one of: 'only', 'up', 'down'.";
  }

  $breakpoint-value: map.get($breakpoints, $breakpoint);
  $bp2: map-next($breakpoints, $breakpoint);
  $edge-values: if($breakpoint-value == 0 or $bp2 == null, true, false);

  @if $breakpoint-value == 0 {
    @if $coverage == "only" or $coverage == "down" {
      @media screen and (max-width: #{$bp2 - 1}) {
        @content;
      }
    } @else {
      @content;
    }
  }

  @if not $bp2 {
    @if $coverage == "down" {
      @content;
    } @else {
      @media screen and (min-width: $breakpoint-value) {
        @content;
      }
    }
  }

  @if $coverage == "down" and not $edge-values {
    @media screen and (max-width: #{$bp2 - 1}) {
      @content;
    }
  } @else if $coverage == "only" and not $edge-values {
    @media screen and (min-width: $breakpoint-value) and (max-width: #{$bp2 - 1}) {
      @content;
    }
  } @else if not $edge-values {
    @media screen and (min-width: $breakpoint-value) {
      @content;
    }
  }
}

// SCROLLBAR
@mixin scrollbar($size, $foreground-color, $background-color) {
  // For Google Chrome
  &::-webkit-scrollbar {
    width: $size;
    height: $size;
  }

  &::-webkit-scrollbar-thumb {
    background: $foreground-color;
  }

  &::-webkit-scrollbar-track {
    background: $background-color;
  }

  // For Internet Explorer
  & {
    scrollbar-face-color: $foreground-color;
    scrollbar-track-color: $background-color;
  }
}
