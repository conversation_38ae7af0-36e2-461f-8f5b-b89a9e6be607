"use client";

// feature-start flash-infos
import FlashInfoBanner from "@/components/ui/flash-info/FlashInfoBanner";
// feature-end flash-infos
// feature-start google-translate
import GoogleTranslate from "@/components/ui/google-translate/GoogleTranslate";
// feature-end google-translate
import { FlashInfo, Menu, MenuItem, SocialLink as SocialLinkType } from "@/generated/graphql/graphql";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef } from "react";
import { PartialDeep } from "type-fest";
import SocialLink from "../social-link/SocialLink";
import styles from "./Header.module.scss";
import MenuButton from "./MenuButton";
import SearchButton from "./SearchButton";

interface HeaderProps {
  menu?: PartialDeep<Menu, { recurseIntoArrays: true }>;
  socialLinks?: PartialDeep<SocialLinkType, { recurseIntoArrays: true }>[];
  siteName?: string;
  logo?: string;
  flashInfoItems?: PartialDeep<FlashInfo, { recurseIntoArrays: true }>[];
  searchUrl?: string;
}

export default function Header({ searchUrl, siteName, menu, socialLinks, logo, flashInfoItems }: HeaderProps) {
  const header = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!header.current) return;

    const observer = new ResizeObserver(() => {
      const height = header.current?.offsetHeight ?? 0;

      document.documentElement.style.setProperty("--header-height", `${height}px`);
    });

    observer.observe(header.current);

    return () => observer.disconnect();
  }, []);

  return (
    <header id="header" role="banner" className={styles.header} ref={header}>
      {/* feature-start flash-infos */}
      {flashInfoItems && flashInfoItems.length > 0 && <FlashInfoBanner flashInfos={flashInfoItems} />}
      {/* feature-end flash-infos */}
      <div className={styles.top}>
        {socialLinks && socialLinks.length > 0 && (
          <div className={styles.socialLinks}>
            {socialLinks.map((link, index) => (
              <SocialLink key={index} size="xxs" {...link} />
            ))}
          </div>
        )}
      </div>
      <div className={styles.inner}>
        {logo && (
          <div className={styles.wrapper}>
            <Link href="/" className={styles.logo} title="Aller à la page d'accueil">
              <Image
                src={logo}
                width={279}
                height={62}
                alt={siteName ?? ""}
                sizes="(max-width: 767px) 140px, (max-width: 1301px) 186px, 279px"
              />
            </Link>
            {/* feature-start google-translate */}
            <GoogleTranslate pageLanguage="fr" includedLanguages={["fr", "en"]} />
            {/* feature-end google-translate */}
          </div>
        )}
        <div className={styles.navigationLinks}></div>
        <nav role="navigation" className={styles.actions}>
          {searchUrl && <SearchButton searchUrl={searchUrl} />}
          {menu && <MenuButton menuItems={(menu.items ?? []) as MenuItem[]} />}
        </nav>
      </div>
    </header>
  );
}
