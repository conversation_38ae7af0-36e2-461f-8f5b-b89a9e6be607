@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.button {
  position: relative;
  display: flex;
  flex-flow: row-reverse;
  gap: 12px;
  align-items: flex-start;
  justify-content: flex-end;
  width: 100%;
  min-height: 50px;
  padding: 6px;
  cursor: pointer;
  border-radius: 8px;

  &:hover {
    background-color: $color-primary-50;
  }
}

.thumbnailWrapper {
  display: flex;
  align-items: flex-start;
  max-width: 74px;
  max-height: 50px;
}

.thumbnail {
  display: block;
  width: 100%;
  height: 100%;

  @include object-fit;
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  line-height: 110%;
  color: $color-primary-500;
}

.title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 1.4rem;
  line-height: 110%;
}

.titleText {
  text-align: left;
}
