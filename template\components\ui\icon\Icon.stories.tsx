import { IconType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Icon from "./Icon";

const meta: Meta<typeof Icon> = {
  title: "Components/Icon",
  component: Icon,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Icon>;

export const Font: Story = {
  args: {
    type: IconType.FONT,
    src: "fas fa-users",
  },
};

export const Image: Story = {
  args: {
    type: IconType.URL,
    src: "/assets/family.svg",
  },
};
