{"name": "@citeopolis/template", "version": "0.6.2", "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "template"}, "files": [".", ".giti<PERSON>re"], "scripts": {"prebuild": "pnpm run generate", "build": "next build", "build-storybook": "storybook build", "chromatic": "npx chromatic --only-changed", "predev": "pnpm run generate", "dev": "next dev", "format": "prettier -w .", "generate": "dotenv -- graphql-codegen", "lint": "pnpm run /^lint:.*/", "lint:next": "pnpm run generate && next lint", "lint:styles": "stylelint \"**/*.{scss,css}\"", "start": "next start", "storybook": "storybook dev -p 6006", "test": "vitest run"}, "prettier": "@citeopolis/prettier-config", "stylelint": {"extends": "@citeopolis/stylelint-config"}, "dependencies": {"@bprogress/next": "^3.2.12", "@google-recaptcha/react": "^2.2.0", "@hcaptcha/react-hcaptcha": "^1.12.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@svgr/webpack": "^8.1.0", "@types/highlight-words-core": "^1.2.3", "altcha": "^2.0.3", "change-case": "^5.4.4", "clsx": "^2.1.1", "css.escape": "^1.5.1", "date-fns": "^4.1.0", "highlight-words-core": "^1.2.3", "maplibre-gl": "^5.6.1", "next": "15.3.4", "nookies": "^2.5.2", "react": "^19.1.0", "react-datepicker": "^8.2.1", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-is": "^19.0.0", "react-keyed-flatten-children": "^5.0.0", "react-select": "^5.10.1", "ssr-window": "^5.0.0", "swiper": "^11.2.8", "tabbable": "^6.2.0", "truncate-html": "^1.2.1", "use-state-with-deps": "^1.1.3", "usehooks-ts": "^3.1.1", "yet-another-react-lightbox": "^3.24.0"}, "devDependencies": {"@apollo/client": "^3.12.5", "@apollo/experimental-nextjs-app-support": "^0.11.7", "@citeopolis-fontawesome/optimized": "^1.0.5", "@citeopolis/eslint-config": "workspace:^", "@citeopolis/prettier-config": "workspace:^", "@citeopolis/stylelint-config": "workspace:^", "@eslint/eslintrc": "^3", "@graphql-codegen/cli": "5.0.3", "@graphql-typed-document-node/core": "^3.2.0", "@next/eslint-plugin-next": "^15.4.3", "@storybook/addon-a11y": "^9.0.12", "@storybook/addon-docs": "^9.0.12", "@storybook/nextjs": "^9.0.12", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.17.14", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/webpack-env": "^1.18.8", "@vitejs/plugin-react": "^5.0.0", "chromatic": "^13.1.2", "dotenv-cli": "^8.0.0", "eslint": "^9.31.0", "eslint-config-next": "15.1.4", "eslint-plugin-storybook": "^9.0.12", "globby": "^14.1.0", "graphql": "^16.10.0", "jsdom": "^26.1.0", "lint-staged": "^15.4.3", "prettier": "^3", "sass-embedded": "^1.85.1", "server-only": "^0.0.1", "storybook": "^9.0.12", "striptags": "^3.2.0", "stylelint": "^16", "type-fest": "^4.38.0", "typescript": "^5", "vitest": "^3.2.4"}}