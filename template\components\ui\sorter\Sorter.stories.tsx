import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Sorter from "./Sorter";

const meta: Meta<typeof Sorter> = {
  title: "Components/Sorter",
  component: Sorter,
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["sm", "md"],
    },
  },
  parameters: {
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof Sorter>;

export const Default: Story = {
  args: {
    choices: [
      {
        label: "Par pertinence",
        value: "relevance",
        defaultSelected: true,
      },
      {
        label: "Par titre",
        value: "title",
      },
      {
        label: "Par type de contenu",
        value: "content_type",
      },
      {
        label: "Par date de publication",
        value: "publication_date",
      },
    ],
  },
};

export const Small: Story = {
  args: {
    ...Default.args,
    size: "sm",
  },
};
