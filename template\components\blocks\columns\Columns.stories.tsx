import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Column from "../column/Column";
import Columns from "./Columns";

// A column number indicator
function Indicator({ n }: { n: number }) {
  return (
    <div
      style={{
        background: n % 2 ? "#ccc" : "#aaa",
        width: "100%",
        textAlign: "center",
        padding: "1rem",
        boxSizing: "border-box",
      }}
    >
      {n}
    </div>
  );
}

const meta: Meta<typeof Columns> = {
  title: "Blocks/Columns",
  component: Columns,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Columns>;

export const OneColumn: Story = {
  args: {
    children: (
      <>
        <Column>
          <Indicator n={1} />
        </Column>
      </>
    ),
  },
};

export const TwoColumns: Story = {
  args: {
    children: (
      <>
        <Column>
          <Indicator n={1} />
        </Column>
        <Column>
          <Indicator n={2} />
        </Column>
      </>
    ),
  },
};

export const ThreeColumns: Story = {
  args: {
    children: (
      <>
        <Column>
          <Indicator n={1} />
        </Column>
        <Column>
          <Indicator n={2} />
        </Column>
        <Column>
          <Indicator n={3} />
        </Column>
      </>
    ),
  },
};

export const FourColumns: Story = {
  args: {
    children: (
      <>
        <Column>
          <Indicator n={1} />
        </Column>
        <Column>
          <Indicator n={2} />
        </Column>
        <Column>
          <Indicator n={3} />
        </Column>
        <Column>
          <Indicator n={4} />
        </Column>
      </>
    ),
  },
};

export const OneThirdTwoThird: Story = {
  args: {
    children: (
      <>
        <Column width="33%">
          <Indicator n={1} />
        </Column>
        <Column width="66%">
          <Indicator n={2} />
        </Column>
      </>
    ),
  },
};

export const TwoThirdOneThird: Story = {
  args: {
    children: (
      <>
        <Column width="66%">
          <Indicator n={1} />
        </Column>
        <Column width="33%">
          <Indicator n={2} />
        </Column>
      </>
    ),
  },
};

export const ThreeColumnsLargeCenter: Story = {
  args: {
    children: (
      <>
        <Column width="25%">
          <Indicator n={1} />
        </Column>
        <Column width="50%">
          <Indicator n={2} />
        </Column>
        <Column width="25%">
          <Indicator n={3} />
        </Column>
      </>
    ),
  },
};

export const CustomLeftColumnWidth: Story = {
  args: {
    children: (
      <>
        <Column width="100px">
          <Indicator n={1} />
        </Column>
        <Column>
          <Indicator n={2} />
        </Column>
        <Column>
          <Indicator n={3} />
        </Column>
      </>
    ),
  },
};
