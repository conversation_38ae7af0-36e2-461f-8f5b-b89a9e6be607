import AdminBarContainer from "@/components/ui/admin-bar/AdminBarContainer";
// feature-start alerts
import Al<PERSON><PERSON>ontainer from "@/components/ui/alert/AlertContainer";
// feature-end alerts
import SkipLinksContainer from "@/components/ui/skip-links/SkipLinksContainer";
import { ToastViewportWrapper } from "@/components/ui/toast/ToastViewportWrapper";
import "@/styles/global.scss";
import { rethinkSans } from "@/utils/typography";
import "@citeopolis-fontawesome/optimized/css/brands.css";
import "@citeopolis-fontawesome/optimized/css/fontawesome.css";
import "@citeopolis-fontawesome/optimized/css/regular.css";
import type { Metadata, Viewport } from "next";
import { ApolloWrapper } from "./ApolloWrapper";
import { ProgressBarWrapper } from "./ProgressBarWrapper";

export const metadata: Metadata = {
  title: "Citéopolis",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fr">
      <body className={rethinkSans.variable}>
        <ApolloWrapper>
          <ProgressBarWrapper>
            <ToastViewportWrapper>
              <div id="top" className="site-wrapper" tabIndex={-1}>
                <SkipLinksContainer />
                <AdminBarContainer />
                {children}
                {/* feature-start alerts */}
                <AlertContainer />
                {/* feature-end alerts */}
              </div>
            </ToastViewportWrapper>
          </ProgressBarWrapper>
        </ApolloWrapper>
      </body>
    </html>
  );
}
