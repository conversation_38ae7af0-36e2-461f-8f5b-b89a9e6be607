import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Text from "./Text";

const meta: Meta<typeof Text> = {
  title: "Form/Text",
  component: Text,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Text>;

export const Default: Story = {
  args: {
    name: "input_1",
    label: "Firstname",
    description: "Texte de description additionel",
    placeholder: "Please enter your firstname",
  },
};
