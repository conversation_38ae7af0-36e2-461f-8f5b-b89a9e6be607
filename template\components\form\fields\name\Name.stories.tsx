import StoryFormProvider from "@/stories/StoryFormProvider";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Name from "./Name";

const meta: Meta<typeof Name> = {
  title: "Form/Name",
  component: Name,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Name>;

export const Default: Story = {
  args: {
    name: "name_1",
    label: "Nom complet",
    description: "Saisissez votre nom complet",
    placeholder: "Nom complet",
  },
};

export const WithSubFields: Story = {
  args: {
    name: "name_2",
    label: "Nom complet avec détails",
    description: "Formulaire détaillé pour le nom",
    firstName: {
      required: true,
      placeholder: "Prénom",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Prénom",
      name: "firstName",
    },
    lastName: {
      required: true,
      placeholder: "Nom de famille",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Nom de famille",
      name: "lastName",
    },
    additionalName: {
      required: false,
      placeholder: "Deuxième prénom",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Deuxième prénom",
      name: "additionalName",
    },
    prefix: {
      required: false,
      placeholder: "Titre (M., Mme, Dr.)",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      showLabel: true,
      size: null,
      validationMessage: null,
      label: "Titre",
      name: "prefix",
      pattern: null,
      type: "select",
      enhancedUI: false,
      choices: [
        { value: "mr", label: "M.", defaultSelected: false, icon: null },
        { value: "mrs", label: "Mme", defaultSelected: false, icon: null },
        { value: "miss", label: "Mlle", defaultSelected: false, icon: null },
        { value: "dr", label: "Dr.", defaultSelected: false, icon: null },
        { value: "prof", label: "Prof.", defaultSelected: false, icon: null },
      ],
    },
    suffix: {
      required: false,
      placeholder: "Suffixe (Jr., Sr.)",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Suffixe",
      name: "suffix",
    },
  },
};

export const MinimalFields: Story = {
  args: {
    name: "name_3",
    label: "Prénom et nom",
    description: "Champs obligatoires uniquement",
    firstName: {
      required: true,
      placeholder: "Prénom",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Prénom",
      name: "firstName",
    },
    lastName: {
      required: true,
      placeholder: "Nom",
      autocomplete: null,
      columnSpan: 6,
      condition: null,
      cssClass: null,
      defaultValue: null,
      description: null,
      descriptionPlacement: null,
      hidden: false,
      pattern: null,
      showLabel: true,
      size: null,
      type: "text",
      validationMessage: null,
      label: "Nom",
      name: "lastName",
    },
  },
};
