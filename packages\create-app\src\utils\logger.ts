import { blueBright, green, red, yellow } from "colorette";
import console from "node:console";

export const logger = {
  error(...args: unknown[]) {
    console.log(red(args.join(" ")));
  },
  warn(...args: unknown[]) {
    console.log(yellow(args.join(" ")));
  },
  info(...args: unknown[]) {
    console.log(blueBright(args.join(" ")));
  },
  success(...args: unknown[]) {
    console.log(green(args.join(" ")));
  },
  log(...args: unknown[]) {
    console.log(args.join(" "));
  },
  break() {
    console.log();
  },
};
