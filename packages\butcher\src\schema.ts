import { z } from "zod";

const FeatureSchema = z.object({
  name: z.string().nonempty({
    message: "The feature name cannot be empty.",
  }),
  files: z.array(z.string()).optional(),
});

export const FeaturesListSchema = z.array(FeatureSchema).min(1, {
  message: "The modules list cannot be empty.",
});

export type FeaturesList = z.infer<typeof FeaturesListSchema>;

export type Feature = z.infer<typeof FeatureSchema>;
