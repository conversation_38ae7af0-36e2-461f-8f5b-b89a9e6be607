"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import { FileUploadField } from "@/generated/graphql/graphql";
import formatSize from "@/utils/formatSize";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";
import styles from "./FileUpload.module.scss";

type FileUploadProps = Omit<FileUploadField, "__typename"> & {
  maxFileSize?: number;
  maxFiles?: number;
  multiple?: boolean;
};

export default function FileUpload({
  name,
  label,
  description,
  required,
  condition,
  validationMessage,
  allowedExtensions,
  maxFileSize,
  maxFiles,
  multiple,
  columnSpan,
}: FileUploadProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const inputId = useId();
  const errorId = useId();

  const accept =
    allowedExtensions?.map((extension) => (extension.startsWith(".") ? extension : `.${extension}`)).join(",") ||
    undefined;

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <input
          id={inputId}
          type="file"
          accept={accept}
          multiple={multiple}
          aria-invalid={!!error}
          className={styles.inputTypeFile}
          aria-describedby={error ? errorId : undefined}
          {...register(name, {
            required,
            validate: (files: FileList) => {
              if (!files || files.length === 0) return true;

              if (maxFiles && files.length > maxFiles) {
                return `Maximum ${maxFiles} fichier(s) autorisé(s)`;
              }

              if (maxFileSize) {
                for (const file of files) {
                  if (file.size > maxFileSize) {
                    return `La taille du fichier ne doit pas dépasser ${formatSize(maxFileSize)}`;
                  }
                }
              }

              return true;
            },
          })}
        />

        {maxFileSize && (
          <FormHelper variant="info">Taille maximale : {Math.round(maxFileSize / 1024 / 1024)}MB</FormHelper>
        )}

        {allowedExtensions?.length > 0 && (
          <FormHelper variant="info">Formats autorisés : {allowedExtensions.join(", ")}</FormHelper>
        )}

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
