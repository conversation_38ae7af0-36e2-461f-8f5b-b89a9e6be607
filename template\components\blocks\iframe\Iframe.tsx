"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import { IframeBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import { useRef } from "react";
import styles from "./Iframe.module.scss";

type IframeProps = Omit<IframeBlock, "__typename" | "innerBlocks">;

/**
 * An accessible `<iframe>` which content is excluded from global tab order.
 */
export default function Iframe({ title, url, height, outlined = true }: IframeProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  /* eslint-disable jsx-a11y/no-noninteractive-tabindex, jsx-a11y/no-noninteractive-element-interactions -- The section is not fully interactible */
  return (
    <Tooltip content="Appuyer sur entrer pour accéder au contenu" trigger="focus">
      <section
        className={clsx("block-iframe contained", styles.iframe, outlined && styles.outlined)}
        style={{ height }}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === " " || e.key === "Enter") {
            e.preventDefault();
            iframeRef.current?.contentWindow?.focus();
          }
        }}
        aria-label="Appuyer sur entrer pour accéder au contenu"
      >
        <iframe ref={iframeRef} src={url ?? undefined} tabIndex={-1} loading="lazy" title={title ?? undefined} />
      </section>
    </Tooltip>
  );
  /* eslint-enable jsx-a11y/no-noninteractive-tabindex, jsx-a11y/no-noninteractive-element-interactions */
}
