import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import <PERSON>rame from "./Iframe";

const meta: Meta<typeof Iframe> = {
  title: "Blocks/Iframe",
  component: Iframe,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Iframe>;

export const Default: Story = {
  args: {
    title: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    url: "https://react.dev/",
    height: 500,
    outlined: true,
  },
};

export const Tall: Story = {
  args: {
    title: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    url: "https://react.dev/",
    height: 5000,
    outlined: true,
  },
};
