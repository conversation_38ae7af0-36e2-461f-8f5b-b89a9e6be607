"use client";

import Hx from "@/components/ui/title/Hx";
import type { Album } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import styles from "./AlbumsItem.module.scss";

interface AlbumsItemProps {
  album: Album;
}

export default function AlbumsItem({ album: { images, url, title, categories, media } }: AlbumsItemProps) {
  const titleLevel = useTitleLevel();
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;

  const hasVideo = media?.some((item) => item.__typename === "AlbumVideo");
  const color = hasVideo ? "isVideo" : "isImage";

  return (
    <article className={styles.albumsItem}>
      <div className={styles.details}>
        <div className={styles.titleWrapper}>
          <div className={clsx(styles.icon, styles[`color-${color}`])}>
            <i className={`far fa-${color === "isVideo" ? "play" : "eye"}`} aria-hidden="true" />
            <span className="sr-only">En savoir plus</span>
          </div>
          <Hx level={titleLevel} className={styles.title}>
            {category && (
              <span className={styles.category}>
                {category.title}
                <span className="sr-only">:</span>
              </span>
            )}
            {url ? (
              <Link href={url} className={styles.titleLink}>
                {title}
              </Link>
            ) : (
              title
            )}
          </Hx>
        </div>
      </div>
      <div className={styles.top}>
        {image?.url && (
          <Image
            className={styles.image}
            src={image.url}
            width={image.width}
            height={image.height}
            alt={image.alt ?? ""}
            sizes="(max-width: 1301px) 224px, 384px"
          />
        )}
      </div>
    </article>
  );
}
