@use "mixins.scss" as *;
@use "variables.scss" as *;

html {
  font-size: 62.5%;
  scroll-behavior: smooth;
  scroll-padding: var(--scroll-padding);
}

body {
  font-family: var(--typo-1), <PERSON><PERSON>, <PERSON><PERSON>a, sans-serif;
  font-size: 1.6rem;
  line-height: 150%;
  color: $color-black;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

*:focus-visible {
  outline: $focus-outline;
  outline-offset: $focus-outline-offset;
}

.sr-only {
  @include visually-hidden;
}

%container {
  max-width: 1216px;
  margin-inline: auto;

  @include breakpoint(small only) {
    padding-inline: 16px;
  }

  @include breakpoint(medium only) {
    padding-inline: 24px;
  }
}

.contained {
  width: 100%;
  max-width: 1216px;
  margin-inline: auto;

  // Add inline padding to contained elements,
  // when rendered in a full width layout.
  .layout-1column-fullwidth .column.main > & {
    @include breakpoint(small only) {
      padding-inline: 16px;
    }

    @include breakpoint(medium only) {
      padding-inline: 24px;
    }
  }
}

.container {
  @extend %container;
}

.site-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
}

.site-content {
  flex: 1;

  @if $focus-outline-offset {
    outline-offset: $focus-outline-offset * -1; // Makes the outline fully visible
  }
}

:root {
  --bprogress-height: 4px;
  --bprogress-color: #{$color-primary-500};
  --header-height: 108px;

  @include breakpoint(medium up) {
    --header-height: 112px;
  }

  @include breakpoint(large up) {
    --header-height: 168px;
  }
}
