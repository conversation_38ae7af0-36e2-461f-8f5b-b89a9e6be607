"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import { HCaptchaField } from "@/generated/graphql/graphql";
import HCaptchaElement from "@hcaptcha/react-hcaptcha";
import { useEffect, useId, useRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import styles from "./captcha.module.scss";
import FormHelper from "@/components/ui/form-helper/FormHelper";

type HCaptchaProps = Omit<HCaptchaField, "__typename">;

const captchaErrorMessage = "Please complete the captcha verification";

export default function HCaptcha({ siteKey, columnSpan, name, condition }: HCaptchaProps) {
  const { control, setValue, getValues } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const captchaRef = useRef<HCaptchaElement>(null);
  const errorId = useId();

  useEffect(() => {
    const payload = getValues(name);

    if (payload && captchaRef.current) {
      captchaRef.current.execute();
    }
  }, [captchaRef, getValues, name]);

  return (
    visible && (
      <FormControl columnSpan={columnSpan} className={styles.captcha}>
        <Controller
          name={name}
          control={control}
          rules={{ required: captchaErrorMessage }}
          render={({ fieldState }) => (
            <>
              <HCaptchaElement
                ref={captchaRef}
                sitekey={siteKey}
                loadAsync={true}
                onVerify={(token: string) => {
                  setValue(name, token);
                }}
                onExpire={() => {
                  setValue(name, null);
                }}
                onError={() => {
                  setValue(name, null);
                }}
              />

              {fieldState.error && (
                <FormHelper id={errorId} variant="error">
                  {fieldState.error.message?.toString()}
                </FormHelper>
              )}
            </>
          )}
        />
      </FormControl>
    )
  );
}
