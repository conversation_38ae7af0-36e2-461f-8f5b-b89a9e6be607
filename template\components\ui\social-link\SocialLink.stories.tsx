import { SocialNetwork } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import SocialLink from "./SocialLink";

const meta: Meta<typeof SocialLink> = {
  title: "Components/SocialLink",
  component: SocialLink,
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div style={{ padding: "3rem", textAlign: "center" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof SocialLink>;

export const Facebook: Story = {
  args: {
    network: SocialNetwork.FACEBOOK,
    text: "Facebook",
    url: "#",
  },
};

export const Twitter: Story = {
  args: {
    network: SocialNetwork.TWITTER,
    text: "Twitter",
    url: "#",
  },
};

export const Instagram: Story = {
  args: {
    network: SocialNetwork.INSTAGRAM,
    text: "Instagram",
    url: "#",
  },
};

export const YouTube: Story = {
  args: {
    network: SocialNetwork.YOUTUBE,
    text: "YouTube",
    url: "#",
  },
};

export const LinkedIn: Story = {
  args: {
    network: SocialNetwork.LINKEDIN,
    text: "LinkedIn",
    url: "#",
  },
};

export const Other: Story = {
  args: {
    network: undefined,
    text: "Other",
    url: "#",
  },
};
