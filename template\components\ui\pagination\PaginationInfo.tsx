import styles from "./PaginationInfo.module.scss";

interface PaginationInfoProps {
  currentPage: number;
  pageSize: number;
  totalCount: number;
}

/**
 * Displays additional information about search results.
 */
export default function PaginationInfo({ currentPage, pageSize, totalCount }: PaginationInfoProps) {
  const from = Math.min((currentPage - 1) * pageSize + 1, totalCount);
  const to = Math.min(currentPage * pageSize, totalCount);

  return (
    <p role="heading" aria-level={2} className={styles.paginationInfo} aria-live="polite" aria-atomic="true">
      {totalCount > 0
        ? `Affichage des résultats ${from} à ${to} sur ${totalCount} au total`
        : "Aucun résultat ne correspond à votre recherche"}
    </p>
  );
}
