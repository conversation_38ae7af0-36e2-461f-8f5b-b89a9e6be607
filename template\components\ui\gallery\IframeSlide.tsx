import normalizeEmbedUrl from "@/utils/normalizeEmbedUrl";
import type { SlideIframe } from "yet-another-react-lightbox";

interface IframeSlideProps {
  slide: SlideIframe;
  offset: number;
}

/**
 * Inserts the iframe and autoplay its content.
 * This is due to a limitation with Dailymotion that always autoplay embed videos.
 */
export default function IframeSlide({ slide: { src, titleAttr }, offset }: IframeSlideProps) {
  const normalizedSource = normalizeEmbedUrl(src);
  const offscreen = offset !== 0;

  return (
    !offscreen && (
      <iframe
        src={normalizedSource + "?autoplay=1"}
        style={{ width: "100%", height: "100%" }}
        allow="autoplay"
        title={titleAttr}
        loading="eager"
      />
    )
  );
}
