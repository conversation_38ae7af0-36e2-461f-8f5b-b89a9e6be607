import { globby } from "globby";
import { mkdir, mkdtemp, readdir, readFile, rm, writeFile } from "node:fs/promises";
import { tmpdir } from "node:os";
import path from "node:path";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { butcher, Butcher } from "../src/butcher";
import { logger } from "../src/utils/logger";

describe("butcher", () => {
  let tmpDir: string;

  beforeEach(async () => {
    tmpDir = await mkdtemp(path.join(tmpdir(), "butcher-test-"));
  });

  afterEach(async () => {
    await rm(tmpDir, { recursive: true, force: true });
    vi.clearAllMocks();
  });

  it("deletes files for features", async () => {
    const dir = path.join(tmpDir, "featureA");

    await mkdir(dir, { recursive: true });

    const filePath = path.join(dir, "fileA.txt");

    await writeFile(filePath, "test content", "utf8");

    const feature = { name: "featureA", files: ["fileA.txt"] };

    const b: Butcher = await butcher(tmpDir, [feature]);

    await b.execute([]);

    const files = await readdir(tmpDir);

    expect(files).not.toContain("fileA.txt");
  });

  it("skips excluded features", async () => {
    const feature = { name: "featureA", files: ["fileA.txt"] };

    const dir = path.join(tmpDir, "featureA");

    await mkdir(dir, { recursive: true });

    const filePath = path.join(dir, "fileA.txt");

    await writeFile(filePath, "test content", "utf8");

    const b: Butcher = await butcher(tmpDir, [feature]);

    await b.execute(["featureA"]);

    const files = await globby("**", { cwd: tmpDir });

    expect(files).toContain("featureA/fileA.txt");

    expect(logger.info).toHaveBeenCalledWith(expect.stringContaining("🧩 featureA"));
  });

  it("removes empty parent directories after file deletion", async () => {
    const feature = { name: "featureB", files: ["subdir/fileB.txt"] };
    const dir = path.join(tmpDir, "subdir");

    await mkdir(dir, { recursive: true });

    await writeFile(path.join(dir, "fileB.txt"), "test", "utf8");

    const b: Butcher = await butcher(tmpDir, [feature]);

    await b.execute([]);

    const exists = await readdir(tmpDir);

    expect(exists).not.toContain("subdir");
  });

  it("transforms files in target directories", async () => {
    const directories = ["app", "components", "styles"];

    for (const dir of directories) {
      const dirPath = path.join(tmpDir, dir);
      const content = `
        // feature-start featureC
        PLACEHOLDER
        // feature-end featureC
      `;

      await mkdir(dirPath, { recursive: true });
      await writeFile(path.join(dirPath, "file.txt"), content, "utf8");
    }

    const feature = { name: "featureC" };
    const b: Butcher = await butcher(tmpDir, [feature]);

    await b.execute([]);

    for (const dir of directories) {
      const content = await readFile(path.join(tmpDir, dir, "file.txt"), "utf8");
      const lines = content
        .split(/\r?\n/)
        .map((v) => v.trim())
        .filter((v) => !!v);

      expect(content).not.toBe("PLACEHOLDER");
      expect(lines).toEqual([]);
    }
  });
});
