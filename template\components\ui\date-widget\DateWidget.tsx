"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import { format as formatDate, isSameDay, isSameYear } from "date-fns";
import { fr } from "date-fns/locale";
import { useMemo } from "react";
import styles from "./DateWidget.module.scss";

interface DateWidgetProps {
  dates: { startDate: string; fullday: boolean; endDate: string }[];
  loading: boolean;
  onLoadMore: () => void;
  recurrenceSummary?: string | null;
  totalCount?: number;
}

// FIXME: When loading more elements, the focus is lost
export default function DateWidget({
  dates = [],
  loading,
  onLoadMore,
  recurrenceSummary,
  totalCount,
}: DateWidgetProps) {
  const hasMoreDates = useMemo(() => totalCount && totalCount > dates.length, [dates, totalCount]);
  const now = new Date();
  return (
    <div className={styles.dateWidget}>
      <h2 className={styles.title}>
        <i className="far fa-calendar" aria-hidden="true" />
        Dates
      </h2>
      {recurrenceSummary && <p className={styles.summary}>{recurrenceSummary}</p>}
      {dates.length > 0 && (
        <ul className={styles.dateList}>
          {dates.map((date, index) => {
            const isRange = date.endDate && !isSameDay(date.endDate, date.startDate);
            return (
              <li key={index} className={styles.dateItem} tabIndex={-1}>
                <Tooltip content="Ajouter à mon calendrier">
                  <span className={styles.addToCalendar}>
                    <i className="far fa-calendar-plus" aria-hidden="true"></i>
                    <span className="sr-only">Ajouter à mon calendrier</span>
                  </span>
                </Tooltip>
                <span>
                  {date.startDate && (
                    <>
                      {isRange ? <span className="sr-only">Du </span> : <span className="sr-only">Le </span>}
                      <time dateTime={formatDate(date.startDate, "yyyy-MM-dd")} className={styles.startDate}>
                        {formatDate(date.startDate, "eeee dd MMMM", { locale: fr })}
                        {!isSameYear(date.startDate, now) && formatDate(date.startDate, " yyyy")}
                      </time>
                    </>
                  )}
                  {isRange && (
                    <>
                      {date.startDate && " au "}
                      <time dateTime={formatDate(date.endDate, "yyyy-MM-dd")} className={styles.endDate}>
                        {formatDate(date.endDate, "eeee dd MMMM", { locale: fr })}
                        {!isSameYear(date.endDate, now) && formatDate(date.endDate, " yyyy")}
                      </time>
                    </>
                  )}
                  {!date.fullday && (
                    <>
                      <span aria-label="De"> - </span>
                      <time dateTime={formatDate(date.startDate, "HH:mm")}>{formatDate(date.startDate, "H'h'")}</time>
                      {date.endDate && (
                        <>
                          <span aria-label="à">-</span>
                          <time dateTime={formatDate(date.endDate, "HH:mm")}>{formatDate(date.endDate, "H'h'")}</time>
                        </>
                      )}
                    </>
                  )}
                </span>
              </li>
            );
          })}
          {hasMoreDates && (
            <li aria-hidden="true" className={styles.dots}>
              ...
            </li>
          )}
        </ul>
      )}
      {hasMoreDates && (
        <button type="button" onClick={onLoadMore} className={styles.loadMoreButton} aria-disabled={loading}>
          {loading ? "Chargement..." : "Plus de dates"}
        </button>
      )}
    </div>
  );
}
