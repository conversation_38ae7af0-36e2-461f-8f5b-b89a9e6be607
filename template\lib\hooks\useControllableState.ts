import { useCallback, useState } from "react";

type SetStateFunction<T> = React.Dispatch<React.SetStateAction<T>>;

interface UseControllableStateParams<T> {
  prop?: T;
  defaultProp: T;
  onChange?: (state: T) => void;
}

/**
 * A `useState` hook that can be controlled by its parent.
 */
export default function useControllableState<T>({
  prop,
  defaultProp,
  onChange = () => {},
}: UseControllableStateParams<T>): [T, SetStateFunction<T>] {
  const [uncontrolledProperty, setUncontrolledProperty] = useState<T>(defaultProp);

  const isControlled = prop !== undefined;
  const value = isControlled ? prop : uncontrolledProperty;

  const setValue = useCallback<SetStateFunction<T>>(
    (nextValue) => {
      if (isControlled) {
        onChange?.(isFunction(nextValue) ? nextValue(prop) : nextValue);
      } else {
        setUncontrolledProperty(nextValue);
      }
    },
    [isControlled, onChange]
  );

  return [value, setValue];
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- Unkown function definition
function isFunction(value: unknown): value is (...args: any[]) => any {
  return typeof value === "function";
}
