import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Heading from "@/components/ui/heading/Heading";
import ContactHeading from "@/components/ui/heading/HeadingContact";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { DirectoryViewMode } from "@/generated/graphql/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";
import styles from "./page.module.scss";

const LocationContactWidget = dynamic(() => import("@/components/ui/location-contact-widget/LocationContactWidget"));
const LocationDetailsWidget = dynamic(() => import("@/components/ui/location-details-widget/LocationDetailsWidget"));
const SocialMediaWidget = dynamic(() => import("@/components/ui/social-media-widget/SocialMediaWidget"));
const PersonContactWidget = dynamic(() => import("@/components/ui/person-contact-widget/PersonContactWidget"));
const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const DIRECTORY_QUERY = graphql(`
  query GetDirectory($url: URL!) {
    route(url: $url) {
      ... on Directory {
        viewMode
        title
        leadText
        structuredContent
        publicationDate
        modifiedDate
        email
        website
        openingHours
        offices
        municipalityArea
        municipalityPopulation
        mayorName
        contactFirstName
        contactLastName
        contactEmail
        phones {
          number
          deviceType
          country
          internationalNumber
          label
        }
        accessibility {
          hearingImpairment
          intellectualImpairment
          mentalImpairment
          reducedMobility
          signLanguageReception
          strollers
          visualImpairment
        }
        images {
          ratio_1x1 {
            alt
            height
            url
            width
          }
        }
        socialLinks {
          network
          text
          url
        }
        categories {
          relativeUrl
          title
          description
          parent {
            __typename
          }
        }
        location {
          address {
            street
            city
            zip
          }
          longitude
          latitude
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: DIRECTORY_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "Directory");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: DIRECTORY_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "all",
  });

  assert.ok(data.route?.__typename === "Directory");

  const {
    viewMode,
    title,
    leadText,
    categories,
    pager,
    breadcrumbs,
    modifiedDate,
    publicationDate,
    offices,
    phones,
    socialLinks,
    email,
    website,
    openingHours,
    images,
    municipalityArea,
    municipalityPopulation,
    mayorName,
    contactFirstName,
    contactLastName,
    contactEmail,
    accessibility,
    location,
    structuredContent,
  } = data.route;

  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");

  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items || []} />
      {viewMode === DirectoryViewMode.PERSON ? (
        <ContactHeading
          title={title ?? "Sans titre"}
          surtitle={surtitle}
          roles={offices}
          rolesRoleDescription="Fonctions"
          tags={tags}
          tagsRoleDescription="Thématiques"
          publicationDate={publicationDate}
          modifiedDate={modifiedDate}
          imageSrc={images?.ratio_1x1?.url}
        />
      ) : (
        <Heading
          title={title ?? "Sans titre"}
          surtitle={surtitle}
          leadText={leadText}
          tags={tags}
          tagsRoleDescription="Thématiques"
          publicationDate={publicationDate}
          modifiedDate={modifiedDate}
        />
      )}
      <div className="layout-2columns-right">
        <div className="column main">
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
        <aside className={clsx("column sidebar", styles.sidebar)}>
          {viewMode === DirectoryViewMode.LOCATION && (
            <>
              <LocationDetailsWidget
                area={municipalityArea}
                population={municipalityPopulation}
                mayorName={mayorName}
                openingHours={openingHours}
                accessibility={accessibility}
              />
              <LocationContactWidget phones={phones} email={email} website={website} location={location} />
            </>
          )}
          {viewMode === DirectoryViewMode.PERSON && (
            <PersonContactWidget
              name={[contactFirstName, contactLastName].filter(Boolean).join(" ")}
              email={contactEmail}
            />
          )}
          <SocialMediaWidget socialLinks={socialLinks} />
        </aside>
      </div>
      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
