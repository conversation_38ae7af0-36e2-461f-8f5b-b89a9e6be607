@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

$accordion-closed-bg: white;
$accordion-open-bg: $color-primary-50;
$accordion-closed-text: $color-neutral-1000;
$accordion-open-text: $color-primary-500;
$accordion-closed-border: $color-neutral-500;
$accordion-open-border: $color-primary-500;

.sitemap {
  display: flex;
  flex-direction: column;
  margin-bottom: 48px;
}

.list {
  display: flex;
  flex-direction: column;

  &.level-0 {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 12px;

    @include breakpoint(medium up) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include breakpoint(large up) {
      grid-template-columns: repeat(4, 1fr);
      gap: 32px;
    }
  }

  &.level-1 {
    padding: 6px 16px 0;
    border-top: 1px solid $color-neutral-400;

    @include breakpoint(large up) {
      padding: 8px 16px 0;
    }
  }
}

.listItem {
  &.level-0 {
    margin-bottom: 48px;
  }
}

.title {
  display: block;
  line-height: 120%;

  &.level-0 {
    position: relative;
    width: 100%;
    padding: 0 16px 0 0;
    font-size: 2.4rem;
    font-weight: 700;
    line-height: 120%;
    color: $accordion-closed-text;

    .accordionHeader[data-state="open"] & {
      color: $accordion-open-text;
    }
  }

  &.level-1 {
    padding: 16px;
    font-weight: 700;
  }

  &.level-2 {
    padding: 8px 0;
    font-size: 1.4rem;
    font-weight: 400;
  }
}

.openIcon {
  .accordionHeader[data-state="open"] & {
    display: none;
  }
}

.closedIcon {
  .accordionHeader[data-state="closed"] & {
    display: none;
  }
}

.accordion {
  width: 100%;
}

.accordionHeader {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 24px 0 24px 12px;
  text-transform: uppercase;
  background-color: $accordion-closed-bg;
  border-bottom: 2px solid $accordion-closed-border;

  @include breakpoint(large up) {
    padding: 24px 0 24px 16px;
  }

  &[data-state="open"] {
    background-color: $accordion-open-bg;
    border-bottom: 2px solid $accordion-open-border;
  }
}

.accordionTrigger {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 30px;
  font-size: 2rem;
  cursor: pointer;
  border-color: $accordion-closed-border;
  border-left-style: solid;
  border-left-width: 1px;

  i {
    color: $accordion-closed-text;
  }

  .accordionHeader[data-state="open"] & {
    border-color: $accordion-open-border;

    i {
      color: $accordion-open-text;
    }
  }
}

.accordionContent {
  padding: 16px 0 0;
  border-bottom: 0;

  &[data-state="closed"] {
    display: none;
  }
}
