import type { News } from "@/generated/graphql/graphql";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

interface ItemProps {
  news: PartialDeep<News, { recurseIntoArrays: true }>;
}

export default function Item({ news: { images, url, title, categories } }: ItemProps) {
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;

  title ??= "Sans titre";

  return (
    <article className={styles.item}>
      <h3 className={styles.title}>
        {category && (
          <span className={styles.category}>
            {category.title}
            <span className="sr-only">:</span>
          </span>
        )}
        {url ? (
          <Link href={url} className={styles.titleLink}>
            {title}
          </Link>
        ) : (
          title
        )}
      </h3>
      {image?.url && (
        <div className={styles.imageWrapper}>
          <Image
            src={image.url}
            width={200}
            height={200}
            alt={image?.alt ?? ""}
            sizes="(max-width: 767px) 74px, (max-width: 1301px) 224px, 384px"
          />
        </div>
      )}
    </article>
  );
}
