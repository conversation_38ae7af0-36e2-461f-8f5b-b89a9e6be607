export default {
  parserPreset: {
    parserOpts: {
      headerPattern: /^\[([A-Z]+)\](?: #(\d+) -)? (.+)$/,
      headerCorrespondence: ["type", "ticket", "subject"],
    },
  },
  rules: {
    "type-enum": [
      2,
      "always",
      [
        "A11Y",
        "BUGFIX",
        "CHORE",
        "DOCS",
        "FEATURE",
        "QUALITY",
        "REFACTOR",
        "SECURITY",
        "TASK",
        "TEST",
      ],
    ],
    "type-case": [2, "always", "upper-case"],
    "subject-empty": [2, "never"],
    "subject-case": [
      2,
      "always",
      ["sentence-case", "start-case", "pascal-case", "upper-case"],
    ],
  },
};
