---
title: 'GraphQL'
weight: 1
---

# GraphQL

The citéopolis template uses [graphql-codegen](https://the-guild.dev/graphql/codegen) to generate type definitions for queries and mutations.

The types are generated in the `generated` directory of your project.

## CLI

To generate the types, run the following command:

```sh
npm run generate
```

{{% hint info %}}
Under the hood, this will load the environment variables from your `.env` file and run `graphql-codegen`.
{{% /hint %}}

You can also run the generator in watch mode:

```sh
npm run generate -w
```

## Usage

To generate types from your queries or mutations, do the following:

### In a server component

```tsx
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";

const FOOTER_QUERY = graphql(`
  query GetFooter {
    siteConfig {
      logo
      siteName
      footer {
        title
        quickAccess1
      }
    }
  }
`);

export default async function Page() {
  // `data` is typed with the query result 🎉
  const { data } = await query({ query: FOOTER_QUERY });
  // ...
}

```

### In a client component

```tsx
"use client";

import { graphql } from "@/generated/graphql";
import { useQuery } from "@apollo/client";

const FOOTER_QUERY = graphql(`
  query GetFooter {
    siteConfig {
      logo
      siteName
      footer {
        title
        quickAccess1
      }
    }
  }
`);

export default function Footer() {
  // `data` is typed with the query result 🎉
  const { data } = useQuery(FOOTER_QUERY);
  // ...
}
```
