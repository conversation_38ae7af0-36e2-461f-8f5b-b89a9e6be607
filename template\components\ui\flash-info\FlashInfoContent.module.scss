@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.flashInfo {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  width: 100%;
  text-align: center;

  @include breakpoint(medium up) {
    flex-direction: row;
    gap: 24px;
    justify-content: center;
    text-align: left;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.topIcon {
  font-size: 3.2rem;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 6px;

  @include breakpoint(large up) {
    gap: 8px;
  }
}

.underline {
  @extend %underline;
  @extend %link-block;
}

a.content {
  .underline {
    @extend %underline;
  }

  &:focus-visible,
  &:hover {
    .underline {
      background-size: 100% 100% !important;
    }
  }
}

.title {
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 120%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.description {
  display: inline-block;
  font-size: 1.2rem;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.plusIcon {
  font-size: 1.4rem;
}

.wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;

  @include breakpoint(medium up) {
    flex-direction: row;
    gap: 24px;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}
