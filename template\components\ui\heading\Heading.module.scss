@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heading {
  padding: 56px 16px;
  background-color: $color-primary-50;

  @include breakpoint(medium up) {
    padding: 56px;
  }

  @include breakpoint(large up) {
    padding: 72px;
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 1216px;
  margin-inline: auto;

  @include breakpoint(large up) {
    gap: 32px;
  }
}

.titleWrapper {
  position: relative;
}

.title {
  font-size: 3.2rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 4rem;
  }

  @include breakpoint(large up) {
    font-size: 5.6rem;
  }
}

.surtitle {
  display: block;
  margin-bottom: 16px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.actions {
  position: absolute;
  top: 0;
  right: 0;

  @include breakpoint(small down) {
    :global(.text) {
      @include visually-hidden;
    }
  }
}

.teaser {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 160%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.dates {
  font-size: 1.2rem;
  line-height: 100%;
  color: $color-neutral-700;
  text-transform: uppercase;

  &::before {
    display: block;
    width: 57px;
    margin-bottom: 12px;
    content: "";
    border-bottom: 1px solid $color-neutral-300;

    @include breakpoint(large up) {
      margin-bottom: 16px;
    }
  }

  span ~ span {
    &::before {
      margin-inline: 1ch;
      content: "-";
    }
  }
}
