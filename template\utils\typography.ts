import localFont from "next/font/local";

/**
 * Font Configuration
 *
 * This configuration defines the RethinkSans font family with multiple weights and styles.
 * It uses the `localFont` function from Next.js to load local font files.
 *
 * To add another font:
 * 1. Copy the `rethinkSans` configuration below.
 * 2. Replace `rethinkSans` with a new name (e.g., `anotherFont`).
 * 3. Update the `src` array with the paths, weights, and styles for the new font.
 * 4. Update the `variable` name to avoid conflicts (e.g., `--typo-2`).
 * 5. Export the new font configuration.
 *
 * Example:
 * export const anotherFont: NextFontWithVariable = localFont({
 *   src: [
 *     { path: "../fonts/AnotherFont-Regular.woff2", weight: "400", style: "normal" },
 *     { path: "../fonts/AnotherFont-Bold.woff2", weight: "700", style: "normal" },
 *   ],
 *   variable: "--typo-2",
 * });
 */
export const rethinkSans = localFont({
  src: [
    {
      path: "../fonts/RethinkSans-Regular.woff2",
      weight: "400",
      style: "normal",
    },
    {
      path: "../fonts/RethinkSans-Medium.woff2",
      weight: "500",
      style: "normal",
    },
    {
      path: "../fonts/RethinkSans-SemiBold.woff2",
      weight: "600",
      style: "normal",
    },
    {
      path: "../fonts/RethinkSans-Bold.woff2",
      weight: "700",
      style: "normal",
    },
    {
      path: "../fonts/RethinkSans-ExtraBold.woff2",
      weight: "800",
      style: "normal",
    },
  ],
  variable: "--typo-1", // CSS variable name for rethinkSans font
});
