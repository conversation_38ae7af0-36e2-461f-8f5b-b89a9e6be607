import { FooterTop as FooterTopGQL } from "@/generated/graphql/graphql";
import dynamic from "next/dynamic";
import Link from "next/link";
import styles from "./FooterTop.module.scss";

const Hx = dynamic(() => import("@/components/ui/title/Hx"));
const Button = dynamic(() => import("@/components/ui/button/Button"));

interface FooterTopProps {
  top: FooterTopGQL;
}

export default async function FooterTop({ top }: FooterTopProps) {
  const { title, description, action } = top;

  return (
    <div className={styles.footerTop}>
      <div className={styles.wrapper}>
        <div className={styles.content}>
          {title && (
            <Hx level={2} className={styles.title}>
              {title}
            </Hx>
          )}
          <p className={styles.description}>{description}</p>
        </div>
        {action?.url && (
          <div className={styles.actions}>
            <Button
              className={styles.actionButton}
              size="md"
              startIcon={<i className="far fa-envelope" aria-hidden="true"></i>}
              asChild
            >
              <Link href={action.url}>{action.text}</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
