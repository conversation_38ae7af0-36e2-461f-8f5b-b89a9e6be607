import { EventAccessibilityStatus } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Accessibility from "./Accessibility";

const meta: Meta<typeof Accessibility> = {
  title: "Components/Accessibility",
  component: Accessibility,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof Accessibility>;

export const Default: Story = {
  args: {
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.SUPPORTED,
      visualImpairment: EventAccessibilityStatus.SUPPORTED,
      mentalImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      intellectualImpairment: EventAccessibilityStatus.UNKNOWN,
      signLanguageReception: EventAccessibilityStatus.SUPPORTED,
      strollers: EventAccessibilityStatus.SUPPORTED,
      reducedMobility: EventAccessibilityStatus.SUPPORTED,
    },
  },
};

export const Supported: Story = {
  args: {
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.SUPPORTED,
      visualImpairment: EventAccessibilityStatus.SUPPORTED,
      mentalImpairment: EventAccessibilityStatus.SUPPORTED,
      intellectualImpairment: EventAccessibilityStatus.SUPPORTED,
      signLanguageReception: EventAccessibilityStatus.SUPPORTED,
      strollers: EventAccessibilityStatus.SUPPORTED,
      reducedMobility: EventAccessibilityStatus.SUPPORTED,
    },
  },
};

export const NotSupported: Story = {
  args: {
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      visualImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      mentalImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      intellectualImpairment: EventAccessibilityStatus.NOT_SUPPORTED,
      signLanguageReception: EventAccessibilityStatus.NOT_SUPPORTED,
      strollers: EventAccessibilityStatus.NOT_SUPPORTED,
      reducedMobility: EventAccessibilityStatus.NOT_SUPPORTED,
    },
  },
};

export const Unknown: Story = {
  args: {
    accessibility: {
      hearingImpairment: EventAccessibilityStatus.UNKNOWN,
      visualImpairment: EventAccessibilityStatus.UNKNOWN,
      mentalImpairment: EventAccessibilityStatus.UNKNOWN,
      intellectualImpairment: EventAccessibilityStatus.UNKNOWN,
      signLanguageReception: EventAccessibilityStatus.UNKNOWN,
      strollers: EventAccessibilityStatus.UNKNOWN,
      reducedMobility: EventAccessibilityStatus.UNKNOWN,
    },
  },
};
