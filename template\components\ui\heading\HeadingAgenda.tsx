import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import striptags from "striptags";
import styles from "./HeadingAgenda.module.scss";

const DateInterval = dynamic(() => import("@/components/ui/date/DateInterval"));
const Tag = dynamic(() => import("@/components/ui/tag/Tag"));

interface HeadingAgendaProps {
  endDate?: string;
  imageSrc?: string;
  leadText?: string | null;
  modifiedDate?: string;
  publicationDate?: string;
  startDate?: string;
  surtitle?: string | null;
  tagsRoleDescription?: string;
  tags?: { text: string; url?: string }[];
  title: string;
}

/**
 * The heading for an event.
 */
export default function HeadingAgenda({
  endDate,
  imageSrc,
  leadText,
  modifiedDate,
  publicationDate,
  startDate,
  surtitle,
  tags,
  tagsRoleDescription,
  title,
}: HeadingAgendaProps) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.content}>
          <div className={styles.titleWrapper}>
            <h1 className={styles.title}>
              {surtitle && (
                <span className={styles.surtitle}>
                  {surtitle}
                  <span className="sr-only">:</span>
                </span>
              )}
              {title}
            </h1>
            {startDate && <DateInterval className={styles.date} size="lg" from={startDate} to={endDate} />}
          </div>
          <div className={styles.imageWrapper}>
            {imageSrc && (
              <Image
                className={styles.image}
                src={imageSrc}
                width={488}
                height={325}
                alt=""
                sizes="(max-width: 768px) 344px, 488px"
              />
            )}
          </div>
        </div>
        {leadText && <p className={styles.teaser}>{striptags(leadText)}</p>}
        {tags && tags.length > 0 && (
          <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
            {tags.map((tag, index) => (
              <li key={index}>
                {tag.url ? (
                  <Tag variant="primary" asChild>
                    <Link href={tag.url}>{tag.text}</Link>
                  </Tag>
                ) : (
                  <Tag variant="primary">{tag.text}</Tag>
                )}
              </li>
            ))}
          </ul>
        )}
        {(publicationDate || modifiedDate) && (
          <p className={styles.dates}>
            {publicationDate && (
              <>
                <span>Publié le </span>
                <time dateTime={publicationDate}>
                  {new Date(publicationDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
            {modifiedDate && (
              <>
                <span>Mis à jour le </span>
                <time dateTime={modifiedDate}>
                  {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
          </p>
        )}
      </div>
    </header>
  );
}
