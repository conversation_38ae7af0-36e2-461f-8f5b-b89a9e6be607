import { beforeEach, describe, expect, it, vi } from "vitest";
import transform from "../src/transform";

describe("transform", () => {
  beforeEach(() => vi.clearAllMocks());

  it("should remove feature blocks from files", async () => {
    const content = `
      before
      // feature-start test
      remove this
      // feature-end test
      after
    `;

    const expected = `
      before
      after
    `;

    expect(transform(content, "test")).toBe(expected);
  });

  it("should remove nested features from files", async () => {
    const content = `
      before
      // feature-start test
        remove this
        // feature-start nested
        keep this or remove this?
        // feature-end nested 
      // feature-end test
      after
    `;

    const expected = `
      before
      after
    `;

    expect(transform(content, "test")).toBe(expected);
  });

  it("should not modify files without any feature blocks", async () => {
    const content = `
      just some content
      no features here
    `;

    expect(transform(content, "test")).toBe(content);
  });

  it("should remove multiple separate feature blocks for the same feature", async () => {
    const content = `
      before
      // feature-start test
      remove1
      // feature-end test
      middle
      // feature-start test
      remove2
      // feature-end test
      after
    `.trim();

    const expected = `
      before
      middle
      after
    `.trim();

    expect(transform(content, "test")).toBe(expected);
  });

  it("should remove feature blocks using JSX-style comments", async () => {
    const content = `
      before
      {/* feature-start test */}
      remove this
      {/* feature-end test */}
      after
    `;

    const expected = `
      before
      after
    `;

    expect(transform(content, "test")).toBe(expected);
  });
});
