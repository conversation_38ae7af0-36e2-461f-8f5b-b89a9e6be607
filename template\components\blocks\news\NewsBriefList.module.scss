@use "@/styles/lib/mixins.scss" as *;

.newsBriefList {
  margin-top: 32px;

  @include breakpoint(medium up) {
    margin-top: 40px;
  }

  @include breakpoint(large up) {
    margin-top: 48px;
  }
}

.title {
  margin-bottom: 16px;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    margin-bottom: 24px;
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 3.2rem;
  }
}

.list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  @include breakpoint(large up) {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}
