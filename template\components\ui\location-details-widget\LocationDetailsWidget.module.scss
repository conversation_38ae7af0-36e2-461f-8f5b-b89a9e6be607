@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.locationDetailsWidget {
  @extend %text-wrap;

  display: flex;
  flex-direction: column;
  gap: 24px;
  line-height: 110%;

  @include breakpoint(medium up) {
    gap: 32px;
  }
}

.label {
  display: block;
  margin-bottom: 4px;
  font-weight: $fw-bold;

  @include breakpoint(medium up) {
    margin-bottom: 6px;
  }
}

.title {
  font-size: 1.8rem;
  font-weight: $fw-bold;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 3.2rem;
  }

  i {
    margin-right: 6px;
    font-size: 2.4rem;
    color: $color-secondary-500;
  }
}
