import type { Meta, StoryObj } from "@storybook/nextjs";
import AccordionItem from "../accordion-item/AccordionItem";
import Accordion from "./Accordion";

const meta: Meta<typeof Accordion> = {
  title: "Blocks/Accordion",
  component: Accordion,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Accordion>;

export const Multiple: Story = {
  args: {
    multiple: true,
    children: (
      <>
        <AccordionItem id="1" title="Lorem ipsum dolor sit ame consectur elis" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem
          id="2"
          title="Lorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elis"
          titleLevel={3}
        >
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem id="3" title="Ex his quidam aeternitati" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
      </>
    ),
  },
};

export const Single: Story = {
  args: {
    multiple: false,
    children: (
      <>
        <AccordionItem id="1" title="Lorem ipsum dolor sit ame consectur elis" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem
          id="2"
          title="Lorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elis"
          titleLevel={3}
        >
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem id="3" title="Ex his quidam aeternitati" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
      </>
    ),
  },
};

export const OpenByDefault: Story = {
  args: {
    multiple: false,
    children: (
      <>
        <AccordionItem id="1" title="Lorem ipsum dolor sit ame consectur elis" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem
          id="2"
          title="Lorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elisLorem ipsum dolor sit ame consectur elis"
          titleLevel={3}
          open={true}
        >
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
        <AccordionItem id="3" title="Ex his quidam aeternitati" titleLevel={3}>
          <p>
            Ex his quidam aeternitati se commendari posse per statuas aestimantes eas ardenter adfectant quasi plus
            praemii de figmentis aereis sensu carentibus adepturi, quam ex conscientia honeste recteque factorum, easque
            auro curant inbracteari, quod Acilio Glabrioni delatum est primo, cum consiliis armisque regem superasset
            Antiochum.
          </p>
        </AccordionItem>
      </>
    ),
  },
};
