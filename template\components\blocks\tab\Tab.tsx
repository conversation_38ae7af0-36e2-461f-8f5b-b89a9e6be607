"use client";

import { TabBlock } from "@/generated/graphql/graphql";
import { TabsContent, TabsTrigger } from "@radix-ui/react-tabs";
import clsx from "clsx";
import { use } from "react";
import { TabsContext } from "../tabs/TabsContext";
import styles from "./Tab.module.scss";

type TabProps = Partial<Omit<TabBlock, "__typename" | "innerBlocks">>;

export default function Tab({ id, anchor, title, children }: React.PropsWithChildren<TabProps>) {
  const { as, name } = use(TabsContext);

  if (!id) {
    return;
  }

  if (as === "accordion-item") {
    return (
      <details name={name} className={clsx("block-tab", styles.accordionItem)} id={anchor ?? undefined}>
        <summary className={styles.trigger}>{title}</summary>
        <div className={styles.content}>{children}</div>
      </details>
    );
  }

  if (as === "tab") {
    return (
      <TabsTrigger className={styles.tab} value={id}>
        {title}
      </TabsTrigger>
    );
  }

  if (as === "tabpanel") {
    return (
      <TabsContent className={clsx("block-tab", styles.tabPanel)} id={anchor ?? undefined} value={id}>
        {children}
      </TabsContent>
    );
  }
}
