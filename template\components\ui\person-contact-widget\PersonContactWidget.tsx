import Button from "@/components/ui/button/Button";
import clsx from "clsx";
import Link from "next/link";
import styles from "./PersonContactWidget.module.scss";

interface PersonContactWidgetProps {
  name?: string;
  email?: string | null;
}

export default function PersonContactWidget({ name, email }: PersonContactWidgetProps) {
  if (!name && !email) {
    return null;
  }

  return (
    <div className={clsx("widget widget-person-contact", styles.personContactWidget)}>
      <h3 className={styles.title}>
        <i className="far fa-user" aria-hidden="true"></i> Contact
      </h3>
      <div className={styles.contactItem}>
        {name && <p className={styles.contactName}>{name}</p>}
        <div className={styles.actions}>
          {email && (
            <Button asChild variant="outlined" startIcon="far fa-at">
              <Link href={`mailto:${email}`}><PERSON><PERSON>riel</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
