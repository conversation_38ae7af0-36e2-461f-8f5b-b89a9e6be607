/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./styles/ErrorPage.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.ErrorPage_errorPage__Jf741 {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  margin-top: 24px;
  text-align: start;
}
@media screen and (min-width: 768px) {
  .ErrorPage_errorPage__Jf741 {
    max-width: 80%;
    margin: 24px auto;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_errorPage__Jf741 {
    flex-direction: row-reverse;
    gap: 82px;
    justify-content: space-between;
    max-width: none;
    text-align: left;
  }
}

.ErrorPage_content__L1sqT {
  order: -1;
}
.ErrorPage_content__L1sqT h1 {
  font-size: 1.6rem;
  line-height: 130%;
  color: #214fab;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT h1 {
    font-size: 1.8rem;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_content__L1sqT h1 {
    font-size: 2.4rem;
  }
}
.ErrorPage_content__L1sqT h2 {
  margin-block: 12px;
  font-size: 4rem;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT h2 {
    font-size: 4.8rem;
    letter-spacing: -0.96px;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_content__L1sqT h2 {
    margin-block: 16px;
    font-size: 6.4rem;
    letter-spacing: -1.28px;
  }
}
.ErrorPage_content__L1sqT h2 em {
  font-style: normal;
  color: #214fab;
}
.ErrorPage_content__L1sqT p {
  max-width: 505px;
  margin-block: 12px;
  font-size: 1.6rem;
  line-height: 150%;
}
@media screen and (min-width: 768px) {
  .ErrorPage_content__L1sqT p {
    margin-block: 16px;
    font-size: 1.8rem;
  }
}

.ErrorPage_returnLink__W24tW {
  display: inline-block;
  font-size: 1.6rem;
  line-height: 120%;
  color: #214fab;
  text-decoration: underline;
  cursor: pointer;
}
.ErrorPage_returnLink__W24tW i {
  margin-right: 3px;
  font-size: 1.4rem;
}

.ErrorPage_illustration__Y4MaR {
  flex-shrink: 0;
  width: 100%;
  max-width: 396px;
}
@media screen and (min-width: 768px) {
  .ErrorPage_illustration__Y4MaR {
    max-width: 538px;
  }
}

.ErrorPage_searchForm__mIol1 {
  position: relative;
  display: flex;
  padding: 16px 24px;
  margin-top: 32px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}
@media screen and (min-width: 768px) {
  .ErrorPage_searchForm__mIol1 {
    margin-top: 40px;
  }
}
@media screen and (min-width: 1302px) {
  .ErrorPage_searchForm__mIol1 {
    margin-top: 48px;
  }
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchInput__IUV47 input:focus-visible {
  outline: none;
}
.ErrorPage_searchForm__mIol1:has(.ErrorPage_searchInput__IUV47 input:focus-visible) {
  outline: 2px solid #000;
  outline-offset: 2px;
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchButton__Ot5tc {
  pointer-events: all;
  cursor: pointer;
  border-radius: 4px;
}
.ErrorPage_searchForm__mIol1 .ErrorPage_searchButton__Ot5tc:hover {
  color: #3269d7;
}

.ErrorPage_contact__xdEXS {
  margin-block: 24px;
  font-size: 1.2rem;
  line-height: 110%;
  color: #707070;
  text-align: center;
}
.ErrorPage_contact__xdEXS a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
  text-decoration-thickness: 8%;
  -webkit-text-decoration-style: solid;
          text-decoration-style: solid;
  text-underline-offset: 13.5%;
  -webkit-text-decoration-skip: ink;
          text-decoration-skip-ink: auto;
  cursor: pointer;
}
@media screen and (min-width: 768px) {
  .ErrorPage_contact__xdEXS {
    display: block;
    max-width: 80%;
    margin: 50px auto;
    font-size: 1.4rem;
  }
}
