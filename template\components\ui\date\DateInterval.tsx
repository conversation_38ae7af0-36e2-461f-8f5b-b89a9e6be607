"use client";

import formatMonth from "@/utils/formatMonth";
import clsx from "clsx";
import { format as formatDate } from "date-fns";
import { fr } from "date-fns/locale";
import React from "react";
import styles from "./DateInterval.module.scss";

interface DateIntervalProps {
  from: string;
  to?: string;
  size?: "md" | "lg";
}

/**
 * Displays the starting and ending dates of a period.
 * Usually for events content types.
 */
export default function DateInterval({
  from,
  to,
  size = "md",
  className,
  ...restProps
}: DateIntervalProps & React.HTMLAttributes<HTMLDivElement>) {
  if (!from) return null;

  return (
    <div className={clsx(styles.dateInterval, size && styles[`size-${size}`], className)} {...restProps}>
      <time className={styles.dateTime} dateTime={from}>
        {formatDate(from, "dd")} {formatMonth(from, { locale: fr })}
        <br />
        {formatDate(from, "yyyy")}
      </time>
      {to && (
        <>
          <span className="sr-only"> au </span>
          <i className={clsx("fas fa-arrow-right", styles.arrowIcon)} aria-hidden="true"></i>
          <time className={styles.dateTime} dateTime={to}>
            {formatDate(to, "dd")} {formatMonth(to, { locale: fr })}
            <br />
            {formatDate(to, "yyyy")}
          </time>
        </>
      )}
    </div>
  );
}
