import Address from "@/components/ui/address/Address";
import Hx from "@/components/ui/title/Hx";
import Image from "next/image";
import styles from "./Card.module.scss";

interface CardProps {
  id: number;
  title: string;
  address: string;
  handleClick: () => void;
}

export default function Card({ title, address, handleClick }: CardProps) {
  const parsedAddress = JSON.parse(address);

  return (
    <div className={styles.root}>
      <button type="button" className={styles.button} onClick={handleClick}>
        <Hx level={3} className={styles.title}>
          {title}
        </Hx>

        <Address className={styles.address} address={parsedAddress} />

        <Image
          fill
          sizes="74px"
          className={styles.thumbnail}
          alt="An example image"
          src="/assets/placeholder-720x480.png"
        />
      </button>
    </div>
  );
}
