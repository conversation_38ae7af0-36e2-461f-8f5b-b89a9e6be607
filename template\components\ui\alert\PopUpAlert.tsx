"use client";

import Button from "@/components/ui/button/Button";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import type { Alert } from "@/generated/graphql/graphql";
import { Dialog, DialogClose, DialogContent, DialogOverlay, DialogPortal, DialogTitle } from "@radix-ui/react-dialog";
import clsx from "clsx";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import type { PartialDeep } from "type-fest";
import { useIsClient, useLocalStorage } from "usehooks-ts";
import styles from "./PopUpAlert.module.scss";

interface PopUpAlertProps {
  alert: PartialDeep<Alert, { recurseIntoArrays: true }>;
}

export const POP_UP_ALERT_CLOSED_KEY = "citeopolis:pop-up-alert:closed";

function getPopUpAlertKey({ id, modifiedDate }: Pick<Partial<Alert>, "id" | "modifiedDate">) {
  return `pop-up-alert|${id}|${modifiedDate}`;
}

export default function PopUpAlert({ alert: { id, modifiedDate, title, description, action } }: PopUpAlertProps) {
  const alertKey = getPopUpAlertKey({ id, modifiedDate });

  const contentRef = useRef<HTMLDivElement>(null);
  const isClient = useIsClient();
  const [isOpen, setIsOpen] = useState(false);
  const [closedPopUpAlertKey, setClosedPopUpAlertKey] = useLocalStorage<string>(POP_UP_ALERT_CLOSED_KEY, "");

  useEffect(() => {
    setIsOpen(isClient && closedPopUpAlertKey !== alertKey);
  }, [setIsOpen, isClient, closedPopUpAlertKey, alertKey]);

  const handleOpenAutoFocus = (event: Event) => {
    event.preventDefault();
    contentRef.current?.focus();
  };

  const handleCloseAutoFocus = (event: Event) => {
    event.preventDefault();
    setClosedPopUpAlertKey(alertKey);
    document.querySelector<HTMLElement>("#top")?.focus();
  };

  return (
    <Dialog modal={true} open={isOpen} onOpenChange={setIsOpen}>
      <DialogPortal>
        <DialogOverlay className={styles.overlay} />
        <div className={styles.modalContainer}>
          <DialogContent
            className={styles.popupAlert}
            aria-modal={true}
            onOpenAutoFocus={handleOpenAutoFocus}
            onCloseAutoFocus={handleCloseAutoFocus}
            onPointerDownOutside={(event) => event.preventDefault()}
            onInteractOutside={(event) => event.preventDefault()}
            tabIndex={-1}
          >
            <div className={styles.header}>
              <Tooltip content="Fermer l'alerte">
                <DialogClose asChild>
                  <Button className={styles.closeButton} color="primary-inverted" aria-label="Fermer l'alerte">
                    <i className="fas fa-xmark" aria-hidden="true"></i>
                  </Button>
                </DialogClose>
              </Tooltip>
            </div>
            <div className={styles.content}>
              <DialogTitle className="sr-only">Alerte</DialogTitle>
              <div className={styles.details}>
                <i className={clsx("far fa-triangle-exclamation", styles.topIcon)} aria-hidden="true"></i>
                {title && <h3 className={styles.title}>{title}</h3>}
                {description && <p className={styles.description}>{description}</p>}
              </div>
              {action?.url && (
                <div className={styles.actions}>
                  <Button asChild variant="outlined" color="primary-inverted" startIcon="far fa-check" size="sm">
                    <Link href={action.url}>{action.text || "En savoir plus"}</Link>
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </div>
      </DialogPortal>
    </Dialog>
  );
}
