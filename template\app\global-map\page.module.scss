@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.main {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 100vh;
  padding: 16px;
  color: $color-white;
  background:
    linear-gradient(rgba($color-primary-500, 0.9), rgba($color-primary-500, 0.9)),
    url("./map-bg.webp") center / cover no-repeat;

  @include breakpoint(medium up) {
    gap: 56px;
    padding: 16px 32px;
  }

  @include breakpoint(large up) {
    gap: 64px;
  }
}

// FIXME: Remove position: absolute (bad for responsive)
.header {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  padding: 16px 6px;
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.logo {
  width: 142px;
  height: 56px;

  @include breakpoint(medium up) {
    width: 182px;
    height: 72px;
  }

  @include breakpoint(large up) {
    width: 229px;
    height: 90px;
  }
}

.logoImage {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  text-align: center;
}

.title {
  font-weight: 700;
  line-height: 150%;

  @include breakpoint(large up) {
    font-size: 1.8rem;
  }
}

.leadText {
  max-width: 306px;
  font-size: 1.4rem;
  line-height: 130%;
  color: $color-white;
  text-align: center;

  @include breakpoint(medium up) {
    max-width: 704px;
    font-size: 1.8rem;
    line-height: 150%;
  }

  @include breakpoint(large up) {
    max-width: 1004px;
  }
}

.mapTypeList {
  display: flex;
  flex-direction: column;
  width: 100%;

  @include breakpoint(medium up) {
    flex-direction: row;
    gap: 40px;
    justify-content: center;
  }

  @include breakpoint(large up) {
    gap: 48px;
    padding: 24px 32px;
  }
}

.mapTypeItem {
  display: flex;
  gap: 5px;
  align-items: center;
  height: 48px;
  font-size: 2rem;
  font-weight: 700;
}

.mapTypeLink {
  display: inline-flex;
  align-items: center;
  font-weight: inherit;
  color: inherit;
  border-bottom: 1px solid transparent;
  transition: border-color 0.3s ease;

  &:hover {
    border-bottom: 1px solid $color-white;
  }
}

.mapTypeIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 51px;
  height: 48px;
  line-height: 100%;
  cursor: pointer;

  @include breakpoint(medium up) {
    font-size: 2.4rem;
  }

  @include breakpoint(large up) {
    font-size: 3.2rem;
  }
}

.tutorialButton {
  width: 100%;
  height: 40px;

  @include breakpoint(large up) {
    width: 344px;
  }
}
