"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuPortal,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import { usePathname, useSearchParams } from "next/navigation";
import styles from "./Sorter.module.scss";

const RadioInput = dynamic(() => import("@/components/ui/radio/Radio"));

interface Choice {
  label: string;
  value: string;
  defaultSelected?: boolean;
}

interface SorterProps {
  className?: string;
  choices: Choice[];
  size?: "sm" | "md";
  sortParamName?: string;
}

export default function Sorter({
  choices,
  size = "md",
  sortParamName: sortParameterName = "sort",
  className,
}: SorterProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const selectedValue = (
    choices.find((choice) => choice.value === searchParams?.get(sortParameterName)) ??
    choices.find((choice) => choice.defaultSelected)
  )?.value;

  // Return the URL to the given page.
  const getPageUrl = (sortBy: string) => {
    const params = new URLSearchParams(searchParams?.toString());

    params.set(sortParameterName, sortBy);
    return pathname + "?" + params.toString();
  };

  if (choices.length === 0) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className={styles.trigger} asChild>
        <button className={clsx(styles.button, size && styles[`size-${size}`], className)}>
          <i className="far fa-arrow-down-wide-short" aria-hidden="true"></i>
          Trier
          <i className={clsx("far fa-caret-down", styles.arrowIcon)} aria-hidden="true"></i>
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuPortal>
        <DropdownMenuContent className={styles.content} sideOffset={0} align="start">
          {choices?.map((choice, index) => (
            <DropdownMenuItem key={index} className={styles.labelledRadio} asChild>
              <Link href={getPageUrl(choice.value)} scroll={false}>
                <RadioInput
                  size="small"
                  value={choice.value}
                  checked={choice.value === selectedValue}
                  readOnly={true}
                  aria-hidden="true"
                />
                <span>{choice.label}</span>
              </Link>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenuPortal>
    </DropdownMenu>
  );
}
