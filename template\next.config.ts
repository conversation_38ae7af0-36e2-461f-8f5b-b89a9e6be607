import type { NextConfig } from "next";
import process from "node:process";
import { URL } from "node:url";

const url = new URL(process.env.BACKEND_URL!);
const protocol = url.protocol.replace(/:$/, "");

const nextConfig: NextConfig = {
  eslint: {
    dirs: ["app", "components", "lib", "utils"],
  },

  skipTrailingSlashRedirect: true,

  sassOptions: {
    implementation: "sass-embedded",
  },

  images: {
    remotePatterns: [
      {
        protocol: protocol as "http" | "https",
        hostname: url.hostname,
        port: url.port,
        pathname: "/**",
      },
    ],
    dangerouslyAllowSVG: true,
  },

  webpack(config) {
    // @see https://react-svgr.com/docs/next/
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find((rule: any) => rule.test?.test?.(".svg"));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        test: /\.svg$/i,
        resourceQuery: /url/, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        test: /\.svg$/i,
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
        use: ["@svgr/webpack"],
      }
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },

  // Forces the order of imported CSS to be consistent
  // @see https://github.com/vercel/next.js/issues/64921
  experimental: {
    cssChunking: "strict",
  },
};

export default nextConfig;
