"use client";

import Hx from "@/components/ui/title/Hx";
import { AccordionItemBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import { use } from "react";
import { AccordionContext } from "../accordion/AccordionContext";
import styles from "./AccordionItem.module.scss";

type AccordionItemProps = Partial<Omit<AccordionItemBlock, "__typename" | "innerBlocks">>;

export default function AccordionItem({
  anchor,
  children,
  title,
  titleLevel,
  open,
}: React.PropsWithChildren<AccordionItemProps>) {
  const { name } = use(AccordionContext);
  return (
    <details
      id={anchor ?? undefined}
      name={name}
      className={clsx("block-accordion-item", styles.accordionItem)}
      open={open}
    >
      <summary className={styles.trigger}>
        <Hx level={titleLevel} className={styles.title}>
          {title}
        </Hx>
        <i className={clsx("far fa-plus", styles.openIcon)} aria-hidden="true"></i>
        <i className={clsx("far fa-minus", styles.closedIcon)} aria-hidden="true"></i>
      </summary>
      <div className={styles.content}>{children}</div>
    </details>
  );
}
