import type { Meta, StoryObj } from "@storybook/nextjs";
import { fn } from "storybook/test";
import Tag from "./Tag";

const meta: Meta<typeof Tag> = {
  title: "Components/Tag",
  component: Tag,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Tag>;

export const Default: Story = {
  args: {
    children: "Lorem Ipsum",
  },
};

export const Primary: Story = {
  args: {
    variant: "primary",
    children: "Lorem Ipsum",
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: "Lorem Ipsum",
  },
};

export const Tertiary: Story = {
  args: {
    variant: "tertiary",
    children: "Lorem Ipsum",
  },
};

export const Rounded: Story = {
  args: {
    rounded: true,
    children: "Lorem Ipsum",
  },
};

export const Small: Story = {
  args: {
    size: "sm",
    children: "Lorem Ipsum",
  },
};

export const Large: Story = {
  args: {
    size: "lg",
    children: "Lorem Ipsum",
  },
};

export const Dismissable: Story = {
  args: {
    children: "Lorem Ipsum",
    dismissable: true,
    onDismiss: fn(),
  },
};

export const Link: Story = {
  args: {
    // eslint-disable-next-line jsx-a11y/anchor-is-valid -- Example link
    children: <a href="#">Lorem Ipsum</a>,
    asChild: true,
  },
};
