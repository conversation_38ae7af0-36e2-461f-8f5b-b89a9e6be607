@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.download {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-block: 24px;
  margin-top: 12px;
  margin-bottom: 32px;
  container: block-download / inline-size;

  @include breakpoint(medium up) {
    margin-bottom: 40px;
  }

  @include breakpoint(large up) {
    gap: 8px;
    padding-block: 32px;
    margin-bottom: 48px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}
