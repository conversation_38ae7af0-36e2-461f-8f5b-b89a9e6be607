import { DirectoryAccessibility } from "@/generated/graphql/graphql";
import clsx from "clsx";
import React from "react";
import Accessibility from "../accessibility/Accessibility";
import styles from "./LocationDetailsWidget.module.scss";

interface LocationDetailsWidget {
  area?: string | null;
  description?: string | null;
  mayorName?: string | null;
  openingHours?: string[] | null;
  population?: number | null;
  accessibility?: Partial<DirectoryAccessibility> | null;
}

export default function LocationDetailsWidget({
  area,
  description,
  mayorName,
  openingHours,
  population,
  accessibility,
}: LocationDetailsWidget) {
  if (
    !area &&
    !description &&
    !mayorName &&
    (!openingHours || openingHours.length === 0) &&
    typeof population !== "number" &&
    !accessibility
  ) {
    return null;
  }

  return (
    <div className={clsx("widget widget-location-details", styles.locationDetailsWidget)}>
      <h3 className={styles.title}>
        <i className="far fa-circle-info" aria-hidden="true"></i> À savoir
      </h3>
      {description && <p className={styles.infoText}>{description}</p>}

      {accessibility && <Accessibility className={styles.accessibility} accessibility={accessibility ?? undefined} />}

      {openingHours && openingHours.length > 0 && (
        <p className={styles.openingHours}>
          <strong className={styles.label}>Horaires :</strong>
          {openingHours?.map((time, index) => (
            <React.Fragment key={index}>
              {index > 0 && <br />}
              {time}
            </React.Fragment>
          ))}
        </p>
      )}

      {mayorName && (
        <p className={styles.mayor}>
          <strong className={styles.label}>Maire :</strong>
          {mayorName}
        </p>
      )}

      {area && (
        <p className={styles.area}>
          <strong className={styles.label}>Superficie :</strong>
          {area}
        </p>
      )}

      {typeof population === "number" && (
        <p className={styles.population}>
          <strong className={styles.label}>Population :</strong>
          {population.toLocaleString("fr")} habitants
        </p>
      )}
    </div>
  );
}
