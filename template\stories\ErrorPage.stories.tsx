import Error from "@/app/error";
import NotFound from "@/app/not-found";
import type { Meta, StoryObj } from "@storybook/nextjs";

function ErrorPage() {
  return <></>;
}

const meta: Meta<typeof ErrorPage> = {
  title: "ErrorPage",
  component: ErrorPage,
  parameters: {
    nextjs: {
      appDirectory: true,
    },
  },
};

export default meta;

type Story = StoryObj<typeof ErrorPage>;

export const Error404: Story = {
  render: () => <NotFound />,
};

export const Error500: Story = {
  render: () => <Error />,
};
