@use "@/styles/lib/mixins.scss" as *;

.sidebar {
  @include breakpoint(large up) {
    width: 344px;
    padding-right: 40px;
  }
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @include breakpoint(medium up) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.flex {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-block: 64px;

  @include breakpoint(medium up) {
    gap: 40px;
    margin-block: 24px;
  }

  @include breakpoint(large up) {
    gap: 48px;
  }
}
