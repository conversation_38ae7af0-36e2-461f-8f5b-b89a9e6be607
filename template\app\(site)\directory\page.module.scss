@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 32px;

  :global(.widget) {
    padding-bottom: 32px;
    border-bottom: 1px solid $color-neutral-300;

    @include breakpoint(large up) {
      padding-bottom: 48px;
    }

    &:last-child {
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  @include breakpoint(medium up) {
    gap: 40px;
  }

  @include breakpoint(large up) {
    gap: 48px;
    width: 100%;
    max-width: 344px;
    padding-left: 40px;
  }
}
