{
  outputs = { nixpkgs, ... }:
    let
      system = "x86_64-linux";
      pkgs = nixpkgs.legacyPackages."${system}";
    in
    {
      apps."${system}".default = {
        type = "app";
        program = pkgs.lib.getExe (pkgs.writeShellApplication {
          name = "run";
          runtimeInputs = with pkgs; [ nodejs pnpm mkcert ];
          text = ''
            (
            NODE_EXTRA_CA_CERTS="$(mkcert -CAROOT)/rootCA.pem"
            export NODE_EXTRA_CA_CERTS
            cd templates/citeopolis/
            pnpm i
            pnpm dev
            )
          '';
        });
      };

      devShells."${system}".default = pkgs.mkShell {
        buildInputs = with pkgs; [ nodejs pnpm ];
      };
    };
}
