/** @type {import('stylelint').Config} */
export default {
  extends: ["stylelint-config-standard-scss", "stylelint-config-recess-order"],
  rules: {
    // Enforce the extension in `@import` statements.
    // Enforce it for `@use` as well, since Storybook SCSS loader requires it.
    "scss/load-partial-extension": "always",
    "scss/load-no-partial-leading-underscore": true,
    "scss/at-import-partial-extension-allowed-list": ["css", "scss"],
    // Allow for camelCase classnames
    "selector-class-pattern": null,
    // Avoid false positive on pure CSS variables
    "custom-property-pattern": null,
    // Allow the `:global` pseudo for CSS modules
    "selector-pseudo-class-no-unknown": [
      true,
      {
        ignorePseudoClasses: [/^global$/],
      },
    ],
    // Allow inline elements in general that are suceptible of being targeted
    // Especially the `<i>` element that targets FA icons.
    "no-descending-specificity": null,
    // Disallow transition=all to avoid any layout shifts
    "declaration-property-value-disallowed-list": {
      transition: /all/,
    },
    // Disallow line-clamp, an informative text should always be visible (a11y)
    "property-disallowed-list": [/line-clamp/],
    // Avoid important at all costs
    "declaration-no-important": [true, { severity: "warning" }],
  },
};
