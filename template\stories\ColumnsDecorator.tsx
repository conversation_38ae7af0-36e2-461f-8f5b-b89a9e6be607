import Column from "@/components/blocks/column/Column";
import Columns from "@/components/blocks/columns/Columns";
import { Decorator } from "@storybook/nextjs";
import { Flow_Block } from "next/font/google";

const flowBlock = Flow_Block({ weight: "400", subsets: ["latin"] });

/**
 * Wrap the component in the first column of a 66%/33% layout.
 */
export const OneThirdColumnsDecorator: Decorator = (Story) => {
  return (
    <Columns>
      <Column width="33%">
        <Story />
      </Column>
      <Column width="33%">
        <p style={{ ...flowBlock.style, color: "#c5c5c5" }} data-storybook-a11y="ignore">
          Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non, ultrices consequat felis. Curabitur sit
          amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus fermentum non, scelerisque eget nulla.
          Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit amet dolor eu elementum. Phasellus
          rhoncus nulla sed varius placerat.
        </p>
      </Column>
      <Column width="33%">
        <p style={{ ...flowBlock.style, color: "#c5c5c5" }} data-storybook-a11y="ignore">
          Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non, ultrices consequat felis. Curabitur sit
          amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus fermentum non, scelerisque eget nulla.
          Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit amet dolor eu elementum. Phasellus
          rhoncus nulla sed varius placerat.
        </p>
      </Column>
    </Columns>
  );
};

/**
 * Wrap the component in the first column of a 66%/33% layout.
 */
export const TwoThirdColumnsDecorator: Decorator = (Story) => {
  return (
    <Columns>
      <Column width="66%">
        <Story />
      </Column>
      <Column width="33%">
        <p style={{ ...flowBlock.style, color: "#c5c5c5" }} data-storybook-a11y="ignore">
          Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non, ultrices consequat felis. Curabitur sit
          amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus fermentum non, scelerisque eget nulla.
          Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit amet dolor eu elementum. Phasellus
          rhoncus nulla sed varius placerat. Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non,
          ultrices consequat felis. Curabitur sit amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus
          fermentum non, scelerisque eget nulla. Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit
          amet dolor eu elementum. Phasellus rhoncus nulla sed varius placerat.
        </p>
      </Column>
    </Columns>
  );
};

/**
 * Wrap the component in the first column of a 50%/50% layout.
 */
export const OneHalfColumnsDecorator: Decorator = (Story) => {
  return (
    <Columns>
      <Column width="50%">
        <Story />
      </Column>
      <Column width="50%">
        <p style={{ ...flowBlock.style, color: "#c5c5c5" }} data-storybook-a11y="ignore">
          Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non, ultrices consequat felis. Curabitur sit
          amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus fermentum non, scelerisque eget nulla.
          Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit amet dolor eu elementum. Phasellus
          rhoncus nulla sed varius placerat. Lorem ipsum adipiscing elit. Etiam lacus diam, aliquet eget arcu non,
          ultrices consequat felis. Curabitur sit amet ante quis ex placerat gravida. Aenean urna justo, gravida tempus
          fermentum non, scelerisque eget nulla. Vestibulum semper faucibus ipsum ac rhoncus. Quisque pellentesque sit
          amet dolor eu elementum. Phasellus rhoncus nulla sed varius placerat.
        </p>
      </Column>
    </Columns>
  );
};
