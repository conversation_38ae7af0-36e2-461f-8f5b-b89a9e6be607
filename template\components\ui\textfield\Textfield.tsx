"use client";

import clsx from "clsx";
import React, { useId } from "react";
import styles from "./Textfield.module.scss";

/**
 * This trick is used to tell that the input is filled, without using `:not(:focus):valid`.
 * The previous rule only works if the input is required, which is not always the case.
 * Setting the placeholder to a space allows us to use `:placeholder-shown` pseudo.
 */
const EMPTY_PLACEHOLDER = " ";

interface BaseProps {
  label?: string;
  size?: "small" | "medium" | "large";
  variant?: "outlined" | "underline" | "filled" | "text";
  error?: boolean;
  startIcon?: string | React.ReactNode;
  endIcon?: string | React.ReactNode;
  multiline?: boolean;
}

type TextfieldProps = BaseProps &
  (
    | (Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, "size"> & { multiline: true })
    | (Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> & { multiline?: false })
  );

export default function Textfield({
  label,
  size = "medium",
  variant = "outlined",
  error,
  startIcon,
  endIcon,
  multiline,
  id: baseId,
  className: containerClassName,
  placeholder,
  required,
  ...restProps
}: TextfieldProps) {
  // Generate an id if the input does not have one, but have an inner label
  const fallbackId = useId();
  const id = baseId || (label ? fallbackId : undefined);

  if (multiline) {
    return (
      <div className={clsx(styles.container, containerClassName)}>
        {label && (
          <label className={styles.innerLabel} htmlFor={id}>
            {label}
            {required && (
              <span className={styles.required} aria-hidden="true">
                {" "}
                (Obligatoire)
              </span>
            )}
          </label>
        )}
        <textarea
          className={clsx(
            styles.input,
            size && styles[`size-${size}`],
            variant && styles[`variant-${variant}`],
            error && styles.error
          )}
          id={id}
          placeholder={label ? EMPTY_PLACEHOLDER : placeholder}
          required={required}
          {...(restProps as React.TextareaHTMLAttributes<HTMLTextAreaElement>)}
        />
      </div>
    );
  }

  return (
    <div className={clsx(styles.container, containerClassName)}>
      {startIcon &&
        (typeof startIcon === "string" ? (
          <i className={clsx(styles.startIcon, startIcon)} aria-hidden="true"></i>
        ) : (
          <span className={styles.startIcon}>{startIcon}</span>
        ))}
      {label && (
        <label className={styles.innerLabel} htmlFor={id}>
          {label}
          {required && (
            <span className={styles.required} aria-hidden="true">
              {" "}
              (Obligatoire)
            </span>
          )}
        </label>
      )}
      <input
        className={clsx(
          styles.input,
          size && styles[`size-${size}`],
          variant && styles[`variant-${variant}`],
          error && styles.error
        )}
        id={id}
        placeholder={label ? EMPTY_PLACEHOLDER : placeholder}
        required={required}
        {...(restProps as React.InputHTMLAttributes<HTMLInputElement>)}
      />
      {endIcon &&
        (typeof endIcon === "string" ? (
          <i className={clsx(styles.endIcon, endIcon)} aria-hidden="true"></i>
        ) : (
          <span className={styles.endIcon}>{endIcon}</span>
        ))}
    </div>
  );
}
