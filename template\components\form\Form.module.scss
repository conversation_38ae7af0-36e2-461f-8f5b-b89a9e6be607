@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.form,
.formStep,
.formContainer {
  display: flex;
  flex-direction: column;
  gap: 32px;

  @include breakpoint(medium up) {
    gap: 40px;
  }

  @include breakpoint(large up) {
    gap: 48px;
  }
}

.formStep {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 22px;
  width: 100%;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-bottom: 32px;

  @include breakpoint(medium up) {
    flex-direction: row;
    justify-content: space-between;
    padding-bottom: 40px;
  }

  @include breakpoint(large up) {
    padding-bottom: 48px;
  }
}

.previousButton {
  @include breakpoint(medium only) {
    flex: 1;
  }

  @include breakpoint(large up) {
    min-width: 250px;
    margin-right: auto;
  }
}

.nextButton {
  @include breakpoint(medium only) {
    flex: 1;
  }

  @include breakpoint(large up) {
    min-width: 250px;
    margin-left: auto;
  }
}

.submitButton {
  @include breakpoint(medium only) {
    flex: 1;
  }

  @include breakpoint(large up) {
    min-width: 250px;
    margin-left: auto;
  }
}
