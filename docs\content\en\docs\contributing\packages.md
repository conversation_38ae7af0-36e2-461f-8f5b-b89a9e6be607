---
title: Packages
weight: 5
---

# Packages

Citéopolis 5 is organized into a [monorepo](https://en.wikipedia.org/wiki/Monorepo).

The sources of all the packages are located into a single GIT repository: https://code.stratis.fr/citeopolis-5/citeopolis-frontend.

## @citeopolis/create-app

A command to create projects. This is the source behind `npm create @citeopolis/app`.

## @citeopolis/template

The application template using [Next.js](https://nextjs.org/), it contains the files that will be copied over when creating a new project.

## @citeopolis/eslint-config

The configuration for [ESLint](https://eslint.org/), a linter for `*.js` and `*.ts` files that checks for type errors upfront.

## @citeopolis/prettier-config

The configuration for [Prettier](https://prettier.io/), a formatter that works for `*.ts`, `*.scss` and `*.json` files.

## @citeopolis/stylelint-config

The configuration for [<PERSON>lint](https://stylelint.io/), a linter for `*.css` and `*.scss` files that check for style errors and naming convention.

## @citeopolis/commitlint-config

The configuration for [Commitlint](https://commitlint.js.org/), a linter for commit messages, that ensure properly formatted commit types and subjects.
