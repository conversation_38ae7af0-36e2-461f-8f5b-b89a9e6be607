import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Image from "./Image";

const meta: Meta<typeof Image> = {
  title: "Blocks/Image",
  component: Image,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Image>;

export const Default: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 720,
    height: 780,
  },
};

export const Caption: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 720,
    height: 780,
    caption:
      "Image caption aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Ut vel ex sit amet leo tempor placerat vel eget erat.",
  },
};

export const Link: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 720,
    height: 780,
    link: {
      url: "#",
      class: null,
      icon: null,
      rel: null,
      target: null,
      text: "",
    },
  },
};

export const ExternalLink: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 720,
    height: 780,
    alt: "Texte alternatif de l'image",
    link: {
      url: "#",
      class: null,
      icon: null,
      rel: null,
      target: "_blank",
      text: "",
    },
  },
};

export const Lightbox: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 720,
    height: 780,
    caption:
      "Image caption aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Ut vel ex sit amet leo tempor placerat vel eget erat.",
    lightbox: true,
  },
};

export const AspectRatioOriginal: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 400,
    height: 300,
    caption: "Image avec aspect ratio original",
  },
};

export const AspectRatio1x1: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 400,
    height: 400,
    aspectRatio: 1,
    scale: "cover",
    caption: "Image avec aspect ratio 1:1",
  },
};

export const AspectRatio4x3: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 400,
    height: 300,
    aspectRatio: 4 / 3,
    scale: "cover",
    caption: "Image avec aspect ratio 4:3",
  },
};

export const AspectRatio3x4: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 300,
    height: 400,
    aspectRatio: 3 / 4,
    scale: "cover",
    caption: "Image avec aspect ratio 3:4 (portrait)",
  },
};

export const AspectRatio3x2: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 450,
    height: 300,
    aspectRatio: 3 / 2,
    scale: "cover",
    caption: "Image avec aspect ratio 3:2",
  },
};

export const AspectRatio2x3: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 300,
    height: 450,
    aspectRatio: 2 / 3,
    scale: "cover",
    caption: "Image avec aspect ratio 2:3 (portrait)",
  },
};

export const AspectRatio16x9: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 500,
    height: 281,
    aspectRatio: 16 / 9,
    scale: "cover",
    caption: "Image avec aspect ratio 16:9 (cinéma)",
  },
};

export const AspectRatio9x16: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 281,
    height: 500,
    aspectRatio: 9 / 16,
    scale: "cover",
    caption: "Image avec aspect ratio 9:16 (mobile)",
  },
};

export const ScaleContain: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 400,
    height: 400,
    aspectRatio: 1,
    scale: "contain",
    caption: "Image avec object-fit: contain",
  },
};

export const ScaleCover: Story = {
  args: {
    src: "/assets/placeholder-720x480.png",
    width: 400,
    height: 400,
    aspectRatio: 1,
    scale: "cover",
    caption: "Image avec object-fit: cover",
  },
};
