@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.root {
  position: absolute;
  bottom: -8px;
  left: calc(50% - 188px);
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 375px;
  max-height: 45vh;
  padding-top: 6px;
  overflow-y: hidden;
  background-color: $color-white;
  border-radius: $rounded-lg $rounded-lg 0 0;
  box-shadow: $shadow-md;
  transform: translateY(100%);
  transition: transform 0.3s ease;

  &.visible {
    transform: translateY(-8px);
  }

  @include breakpoint(medium up) {
    max-height: calc(100% - 350px);

    &.visible {
      transform: translateY(0);
    }
  }

  @include breakpoint(large up) {
    bottom: 0;
    left: 24px;
    height: 100%;
    max-height: calc(100% - 24px);

    &.visible {
      transform: translateY(0);
    }
  }

  &.collapsed {
    max-height: 10%;
    overflow: hidden;
  }
}

.trigger {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 16px;
  padding: 6px 0;
  cursor: pointer;
}

.trigger::before {
  display: flex;
  flex-shrink: 0;
  width: 40px;
  height: 4px;
  content: "";
  background-color: $color-neutral-300;
  border-radius: 1500px;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 12px;

  @include breakpoint(medium up) {
    gap: 8px;
  }
}

.visible + .header::after {
  display: flex;
  width: 100%;
  height: 12px;
  margin-bottom: 11px;
  content: "";
  border-bottom: $color-neutral-300 1px solid;
}

.headerContent {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.title {
  flex-grow: 1;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-black;
  text-align: center;
}

.closeButton {
  margin-left: auto;
}

.content {
  display: flex;
  flex: 1;
  max-height: 100%;
  padding: 0 12px;
  overflow-y: auto;

  @include scrollbar(5px, $color-neutral-300, $color-neutral-100);
}
