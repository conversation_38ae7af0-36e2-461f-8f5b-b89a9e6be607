export interface MapProps {
  /**
   * Default coordinates for the map's center.
   * Format: [longitude, latitude].
   * @default [2.3522, 48.8566] for Paris.
   */
  defaultCenter?: [number, number];

  /**
   * Default zoom level for the map on load.
   * @default 10
   */
  defaultZoom?: number;

  /**
   * Minimum allowed zoom level for the map.
   * @default 0
   */
  minZoom?: number;

  /**
   * Maximum allowed zoom level for the map.
   * @default 22
   */
  maxZoom?: number;

  /**
   * List of markers to display on the map.
   * Each marker is defined by its ID, coordinates, an onClick function, and an icon.
   */
  markers?: Marker[];

  /**
   * An array of IDs for the currently selected markers.
   * When this prop is provided, the map's selection state becomes **controlled**
   * by the parent component. The map will display the markers
   * corresponding to these IDs as selected.
   *
   * If `undefined`, the map manages its selection state internally.
   */
  selectedMarkerIds?: Marker["id"][];

  /**
   * Callback function triggered when the selection state changes within the map.
   * This includes selecting a new point, deselecting an existing point,
   * or deselecting all points by clicking an empty area.
   *
   * The `markers` array contains the full `Marker` objects that are now selected.
   * If nothing is selected, `markers` will be an empty array.
   */
  onSelectionChange?: (markers: Marker[]) => void;

  /**
   * Function called when the map is ready.
   */
  onMapLoad?: (map: maplibregl.Map) => void;

  /**
   * Configuration options for marker clustering.
   * Allows enabling, setting the radius, max zoom, and style for clusters.
   */
  clustering?: ClusteringOptions;

  /**
   * Configuration options for grouping markers that are very close at high zoom levels.
   * This is a "density grouping" mechanism, distinct from traditional clustering.
   */
  densityGrouping?: DensityGroupingOptions;

  /**
   * Enables or disables all map user interactions (pan, zoom, rotation) via mouse or touch.
   * When set to 'false', the map will not respond to user input, and controls will be hidden.
   * This property overrides `controls` and `scrollOverlay`.
   * @default true
   */
  interactive?: boolean;

  /**
   * Enables or disables the display of the overlay informing the user how to interact with the map
   * (e.g., "Use Ctrl + scroll to zoom" on desktop, "Pinch to zoom" on mobile).
   * Useful when the map is embedded on a page.
   * This property is ignored if `interactive` is false.
   */
  scrollOverlay?: boolean;

  /**
   * Adjusts the map view to include all markers on load.
   * If set to 'true', this overrides 'defaultCenter' and 'defaultZoom'.
   */
  fitBoundsToMarkers?: boolean;

  /**
   * Options for displaying standard map user interface controls.
   * Controls the visibility of zoom buttons and the fullscreen button.
   * This property is ignored if `interactive` is false.
   */
  controls?: ControlOptions;
}

export interface Marker<T = Record<string, string | number | boolean | null | undefined>> {
  /**
   * Unique identifier for the marker.
   * It is highly recommended to provide a unique ID for each marker.
   */
  id: string | number;

  /**
   * Coordinates of the marker.
   * Format: [longitude, latitude].
   */
  coordinates: [number, number];

  /**
   * URL of a custom image to use as the marker's icon.
   */
  icon?: string;

  /**
   * Custom data associated with this marker. This can be any object structure
   * and can be extended using TypeScript generics.
   */
  data?: T;
}

interface ClusteringOptions {
  /**
   * Enables or disables the marker clustering feature.
   */
  enabled?: boolean;

  /**
   * Radius in pixels within which markers will be clustered.
   * @default 50
   */
  radius?: number;

  /**
   * Maximum zoom level at which clusters are displayed.
   * Beyond this zoom, individual markers will appear.
   * @default 14
   */
  maxZoom?: number;

  /**
   * Mapbox GL JS style object to customize the appearance of clusters.
   */
  style?: object;
}

interface DensityGroupingOptions {
  /**
   * Enables or disables the density grouping feature.
   */
  enabled?: boolean;

  /**
   * Radius in pixels within which markers will be grouped if they are too close at high zoom levels.
   * @default 20
   */
  radius?: number;
}

interface ControlOptions {
  /**
   * Shows or hides the zoom in/out buttons on the map.
   * @default true
   */
  zoom?: boolean;

  /**
   * Shows or hides the button for switching the map to fullscreen mode.
   * @default true
   */
  fullscreen?: boolean;
  /**
   * Controls the orientation of the zoom box.
   * You can choose where to display the zoom control box on the map.
   * Accepted values: "top-left", "top-right", "bottom-left", "bottom-right"
   * @default "top-right"
   */
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
}
