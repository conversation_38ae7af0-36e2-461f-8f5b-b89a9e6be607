@use "@/styles/lib/variables.scss" as *;

.root {
  display: block;
}

.summary {
  display: flex;
  gap: 0.8rem;
  align-items: baseline;
  width: 100%;
  padding: 0 0 1.6rem;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-black;
  text-align: left;
  cursor: pointer;

  &::marker {
    display: none !important;
  }

  i {
    color: $color-neutral-500;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;

    .root[open] & {
      transform: rotate(0);
    }
  }
}

.content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.list {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0 0 0 2.4rem;
  margin: 0.4rem 0;

  &::before {
    position: absolute;
    top: 0;
    left: 0.7rem;
    width: 0.1rem;
    height: 100%;
    content: "";
    background-color: $color-neutral-300;
  }

  &.rootList {
    padding: 0;
    margin: 0;

    &::before {
      display: none;
    }

    > li > ul {
      margin: 0.8rem 0;
    }
  }
}

.item {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.label {
  display: flex;
  justify-content: space-between;
}

.link {
  display: flex;
  gap: 0.8rem;
  align-items: center;
  padding: 0.3rem 0;
}

.text {
  font-size: 1.4rem;
  font-style: normal;
  font-weight: 400;
  font-feature-settings: "liga" off;
  line-height: 110%;
  text-transform: capitalize;

  &.root {
    font-size: 1.6rem;
  }
}

.count {
  padding: 0.2rem 0 0;
  font-size: 1.2rem;
  font-style: normal;
  font-weight: 400;
  font-feature-settings: "liga" off;
  line-height: 120%;
  color: $color-neutral-500;
}

.box {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 1.6rem;
  height: 1.6rem;
  overflow: hidden;
  font-size: 1.2rem;
  background-color: white;
  border: 0.1rem solid $color-neutral-500;
  border-radius: 0.4rem;

  &.partial {
    color: $color-primary-500;
    background-color: $color-primary-50;
    border-color: $color-primary-500;
  }

  &.active {
    color: $color-white;
    background-color: $color-primary-500;
    border-color: $color-primary-500;
  }
}

.toggle {
  width: 12px;
  height: 24px;
  font-size: 1.2rem;
  color: $color-neutral-500;
  cursor: pointer;
}
