---
title: Contributing
---

# Contributing

To contribute to the citeopolis namespace you must have the necessary permissions.

## Setup the access token

Go to https://code.stratis.fr/-/user_settings/personal_access_tokens and create a new access token for your account. Set the permissions: **api**, **api_read**, **read_registry** and **write_registry**.

In the `.npmrc` file in your home directory, add the following line:

```
//code.stratis.fr/api/v4/projects/5490/packages/npm/:_authToken=$GITLAB_NPM_TOKEN
```

Replace the `$GITLAB_NPM_TOKEN` with yours.

## Version

The versionning of the packages is handled by [@changesets/cli](https://github.com/changesets/changesets).

Before creating a commit, it is recommended to create a new **changeset**.

Run the following command, at the root of the workspace:

```sh
pnpm changeset
```

You will be asked, for all the affected packages, what kind of version bump should be applied.

To learn more about **changesets**, check the [documentation](https://github.com/changesets/changesets/blob/main/docs/adding-a-changeset.md).

## Publish

{{% hint info %}}
Citeopolis packages are published to Gitlab using its internal NPM registry, you can see the list [here](https://code.stratis.fr/citeopolis-5/citeopolis-frontend/-/packages).
{{% /hint %}}

Before publishing, we need to update the versions of the packages. Run the following command at the root of the workspace:

```sh
pnpm changeset version
```

The changesets will be consumed, and the package versions in the monorepo will be updated. Commit the changes and publish the packages by running the following command:

```sh
pnpm -r publish
```
