"use client";

import Button from "@/components/ui/button/Button";
import MenuModal from "@/components/ui/menu/MenuModal";
import useModal from "@/components/ui/modal/useModal";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { MenuItem } from "@/generated/graphql/graphql";
import styles from "./Header.module.scss";

interface MenuButtonProps {
  menuItems?: MenuItem[];
}

export default function MenuButton({ menuItems }: MenuButtonProps) {
  const { Trigger } = useModal(<MenuModal menuItems={menuItems} />);

  return (
    <Trigger>
      <Tooltip content="Ouvrir le menu principal">
        <Button className={styles.menuButton} color="secondary" startIcon="far fa-bars">
          <span className="sr-only">Ouvrir le menu principal</span>
        </Button>
      </Tooltip>
    </Trigger>
  );
}
