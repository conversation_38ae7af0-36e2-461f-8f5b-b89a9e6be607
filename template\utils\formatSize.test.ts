import { describe, expect, it } from "vitest";
import formatSize from "./formatSize";

describe("formatSize", () => {
  it("converts bytes into a human readable size", () => {
    expect(formatSize(2_197_170)).toBe("2,1Mo");
  });

  it("converts to a rounded size", () => {
    expect(formatSize(880_803_840)).toBe("840Mo");
    expect(formatSize(2_097_170)).toBe("2Mo");
  });

  it("can have space between size and unit", () => {
    expect(formatSize(52_428_800, { space: true })).toBe("50 Mo");
  });
});
