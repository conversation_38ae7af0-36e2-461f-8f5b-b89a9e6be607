import Button from "@/components/ui/button/Button";
import styles from "./Form.module.scss";

function FormSubmitButton({
  isSubmitting,
  label,
  disabled,
}: {
  isSubmitting: boolean;
  label?: string;
  disabled?: boolean;
}) {
  return (
    <Button className={styles.submitButton} type="submit" disabled={isSubmitting || disabled}>
      {isSubmitting ? (
        <>
          <i className="far fa-spinner fa-spin" aria-hidden="true" style={{ marginRight: "0.5rem" }}></i>
          Envoi en cours...
        </>
      ) : (disabled ? (
        <>
          <i className="far fa-check-circle" aria-hidden="true" style={{ marginRight: "0.5rem" }}></i>
          Formulaire envoyé
        </>
      ) : (
        <>
          <i className="far fa-paper-plane" aria-hidden="true" style={{ marginRight: "0.5rem" }}></i>
          {label || "Envoyer le formulaire"}
        </>
      ))}
    </Button>
  );
}

export default function FormNavigation({
  currentStep,
  totalSteps,
  actualTotalSteps,
  isRecapStep,
  onPrev,
  onNext,
  isSubmitting,
  submitLabel,
  disabled,
}: {
  currentStep: number;
  totalSteps: number;
  actualTotalSteps: number;
  isRecapStep: boolean;
  onPrev: () => void;
  onNext: () => void;
  isSubmitting: boolean;
  submitLabel?: string;
  disabled?: boolean;
}) {
  return (
    <div className={styles.actions}>
      {actualTotalSteps > 1 && currentStep > 0 && !disabled && (
        <Button
          type="button"
          className={styles.previousButton}
          variant="outlined"
          onClick={onPrev}
          startIcon={<i className="far fa-chevron-left" aria-hidden="true"></i>}
        >
          Étape précédente
        </Button>
      )}

      {!isRecapStep && actualTotalSteps > 1 && currentStep < totalSteps && !disabled && (
        <Button
          type="button"
          className={styles.nextButton}
          onClick={onNext}
          endIcon={<i className="far fa-chevron-right" aria-hidden="true"></i>}
        >
          {currentStep === totalSteps - 1 ? "Voir le récapitulatif" : "Étape suivante"}
        </Button>
      )}

      {(isRecapStep || actualTotalSteps === 1) && (
        <FormSubmitButton isSubmitting={isSubmitting} label={submitLabel} disabled={disabled} />
      )}
    </div>
  );
}
