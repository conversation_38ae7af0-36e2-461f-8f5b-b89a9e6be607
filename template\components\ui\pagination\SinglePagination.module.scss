@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.singlePagination {
  margin-block: $space-12;

  @include breakpoint(medium up) {
    margin-block: $space-13;
  }

  @include breakpoint(large up) {
    max-width: 1248px;
    margin-block: $space-15;
  }
}

.toolbar {
  margin-bottom: $space-5;

  @include breakpoint(medium up) {
    text-align: center;
  }

  @include breakpoint(large up) {
    margin-bottom: $space-6;
  }
}

.returnLink {
  margin-bottom: $space-5;
  font-size: 1.2rem;
  color: $color-neutral-500;
  text-decoration: underline;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.pages {
  display: flex;
  flex-direction: column;
  gap: 12px;

  li {
    flex: 1;
  }

  @include breakpoint(medium up) {
    flex-direction: row;
    gap: $space-13;
  }

  @include breakpoint(large up) {
    gap: $space-14;
  }
}

.page {
  display: flex;
  align-items: center;
  width: 100%;

  i {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    width: 48px;
    aspect-ratio: 1;
    font-size: 2rem;
    color: $color-primary-500;

    @include breakpoint(large up) {
      width: 64px;
      font-size: 2.4rem;
    }
  }

  .text {
    font-size: 1.2rem;
    line-height: 1;
    color: $color-neutral-500;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }

    strong {
      display: block;
      font-size: 1.6rem;
      font-weight: 700;
      line-height: 150%;
      color: $color-primary-500;

      @include breakpoint(medium up) {
        font-size: 1.8rem;
      }
    }
  }

  &.nextPage {
    @include breakpoint(medium up) {
      flex-direction: row-reverse;
      text-align: right;
    }
  }
}
