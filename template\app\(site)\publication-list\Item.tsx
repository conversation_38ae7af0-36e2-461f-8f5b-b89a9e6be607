"use client";

import Hx from "@/components/ui/title/Hx";
import type { Publication } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import Image from "next/image";
import Link from "next/link";
import type { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

interface ItemProps {
  publication: PartialDeep<Publication, { recurseIntoArrays: true }>;
}

export default function Item({
  publication: { images, url, title, subtitle, files, categories, publicationDate },
}: ItemProps) {
  const image = images?.original ?? null;
  const [category] = categories ?? [];
  const titleLevel = useTitleLevel();

  title ??= "Sans titre";

  return (
    <article className={styles.publicationItem}>
      <div className={styles.titleWrapper}>
        <Hx level={titleLevel} className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </Hx>
        {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
      </div>
      <div className={styles.infos}>
        {files && files.length > 0 && (
          <p className={styles.fileCount} aria-roledescription="Nombre de documents">
            {files.length} document{files.length > 1 ? "s" : ""}
          </p>
        )}
        {publicationDate && (
          <p className={styles.publicationDate}>
            Publié le{" "}
            <time dateTime={publicationDate}>{format(new Date(publicationDate), "dd/MM/yyyy", { locale: fr })}</time>
          </p>
        )}
      </div>
      {image?.url && (
        <div className={styles.imageWrapper}>
          <Image
            className={styles.image}
            src={image.url}
            width={image.width}
            height={image.height}
            alt={image?.alt ?? ""}
          />
        </div>
      )}
    </article>
  );
}
