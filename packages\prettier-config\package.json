{"name": "@citeopolis/prettier-config", "version": "0.2.1", "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/prettier-config"}, "type": "commonjs", "exports": {".": {"require": "./prettier.config.cjs"}}, "files": ["./prettier.config.js", "./prettier.config.cjs"], "scripts": {"format": "prettier -w ."}, "lint-staged": {"*.{cjs,js,json,md}": "prettier --write"}, "dependencies": {"prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-packagejson": "^2.5.10"}, "peerDependencies": {"prettier": "^3"}}