"use client";

import type { Resolution } from "@/generated/graphql/graphql";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import Link from "next/link";
import type { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

interface ItemProps {
  resolution: PartialDeep<Resolution, { recurseIntoArrays: true }>;
}

export default function Item({
  resolution: { url, title, categories, issueDate, publicationDate, fileCount },
}: ItemProps) {
  const [category] = categories ?? [];

  title ??= "Sans titre";

  return (
    <article className={styles.resolutionItem}>
      <div className={styles.details}>
        <h3 className={styles.title}>
          {category && (
            <span className={styles.category}>
              {category.title}
              <span className="sr-only">:</span>
            </span>
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {title}
            </Link>
          ) : (
            title
          )}
        </h3>
        {issueDate && (
          <p className={styles.issueDate}>
            Date de parution : le {format(new Date(issueDate), "eeee dd MMMM yyyy", { locale: fr })}
          </p>
        )}
      </div>
      <div className={styles.infos}>
        {fileCount && fileCount > 0 && (
          <p className={styles.fileCount} aria-roledescription="Nombre de documents">
            {fileCount} document{fileCount > 1 ? "s" : ""}
          </p>
        )}
        {publicationDate && (
          <p className={styles.publicationDate}>
            Publié le{" "}
            <time dateTime={publicationDate}>{format(new Date(publicationDate), "dd/MM/yyyy", { locale: fr })}</time>
          </p>
        )}
      </div>
    </article>
  );
}
