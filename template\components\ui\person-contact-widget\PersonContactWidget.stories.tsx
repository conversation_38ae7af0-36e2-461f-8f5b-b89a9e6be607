import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import PersonContactWidget from "./PersonContactWidget";

const meta: Meta<typeof PersonContactWidget> = {
  title: "Components/Widget/PersonContact",
  component: PersonContactWidget,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof PersonContactWidget>;

export const Default: Story = {
  args: {
    name: "<PERSON><PERSON> Lorem Ipsum",
    email: "<EMAIL>",
  },
};
