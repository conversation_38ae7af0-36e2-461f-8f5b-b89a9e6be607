@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.fieldset {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  grid-column: span 12;
  gap: 22px;
  width: 100%;
}

.legend {
  grid-column: span 12;
  width: 100%;
  margin-bottom: 24px;
  font-size: 1.6rem;
  line-height: 130%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 2.4rem;
  }

  &:has(+ .description) {
    margin-bottom: 0;
  }
}

.description {
  grid-column: span 12;
  margin-bottom: 24px;
  font-size: 1.4rem;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 1.6rem;
  }
}
