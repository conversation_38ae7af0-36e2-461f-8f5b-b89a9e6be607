import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Heading from "./Heading";

const meta: Meta<typeof Heading> = {
  title: "Blocks/Heading",
  component: Heading,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Heading>;

export const One: Story = {
  name: "H1",
  args: {
    level: 1,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Two: Story = {
  name: "H2",
  args: {
    level: 2,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Three: Story = {
  name: "H3",
  args: {
    level: 3,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Four: Story = {
  name: "H4",
  args: {
    level: 4,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Five: Story = {
  name: "H5",
  args: {
    level: 5,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const Six: Story = {
  name: "H6",
  args: {
    level: 6,
    html: "Sunt igitur firmi et stabiles et constantes eligendi",
  },
};

export const RichContent: Story = {
  args: {
    html: "Sunt <b>igitur</b> firmi <em>et</em> <a href='#'>stabiles</a> et constantes eligendi",
  },
};
