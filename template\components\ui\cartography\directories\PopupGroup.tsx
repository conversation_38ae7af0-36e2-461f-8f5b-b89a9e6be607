import Button from "@/components/ui/button/Button";
import clsx from "clsx";
import { useCallback, useMemo, useState } from "react";
import CardList from "./CardList";
import { LocationMarker } from "./DirectoriesMap";
import styles from "./Popup.module.scss";

interface PopupGroupProps {
  selectedMarkers: LocationMarker[];
  onClose: () => void;
  setSelectedId: (id: number | null) => void;
}

export default function PopupGroup({ onClose, selectedMarkers, setSelectedId }: PopupGroupProps) {
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  const isVisible = useMemo(() => selectedMarkers.length > 0, [selectedMarkers]);

  const handleSelectCard = useCallback(
    (id: number) => {
      setSelectedId(id);
    },
    [setSelectedId]
  );
  return (
    <div
      className={clsx(
        styles.root,
        {
          [styles.visible]: isVisible,
        },
        isCollapsed && styles.collapsed
      )}
    >
      {selectedMarkers.length > 1 && (
        <button
          type="button"
          className={styles.trigger}
          aria-label="Réduire la fenêtre contextuelle d'emplacement"
          onClick={() => setIsCollapsed(!isCollapsed)}
        />
      )}

      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h2 className={styles.title}>À cet endroit</h2>

          <Button
            className={styles.closeButton}
            size="sm"
            color="primary"
            variant="text"
            aria-label="Fermer"
            onClick={onClose}
            startIcon="far fa-close"
          />
        </div>
      </div>

      <div className={styles.content}>
        <CardList onSelectCard={handleSelectCard} selectedMarkers={selectedMarkers} />
      </div>
    </div>
  );
}
