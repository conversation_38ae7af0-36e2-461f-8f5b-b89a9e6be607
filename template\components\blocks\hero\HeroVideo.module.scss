@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heroVideo {
  position: relative;
  width: 100%;
  height: 100vh;
}

.video {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.contentWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  &::after {
    @include gradient-overlay;
  }
}

.content {
  bottom: 98px;
  padding-bottom: 0;

  @include breakpoint(medium up) {
    bottom: 146px;
  }

  @include breakpoint(large up) {
    bottom: 170px;
  }
}

.playButton {
  position: absolute;
  bottom: 16px;
  left: 32px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  font-size: 1.6rem;
  color: $color-black;
  cursor: pointer;
  background-color: $color-white;
  border-radius: $rounded-full;
  opacity: 0.6;
  transition: background-color 0.3s ease-in-out;

  @include breakpoint(medium up) {
    bottom: 56px;
    left: 72px;
  }

  @include breakpoint(large up) {
    bottom: 50px;
    left: 120px;
  }
}
