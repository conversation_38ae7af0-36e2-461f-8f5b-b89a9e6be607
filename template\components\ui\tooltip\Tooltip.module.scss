@use "@/styles/lib/variables.scss" as *;

.tooltipContent {
  position: relative;
  z-index: $layer-tooltip;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 344px;
  height: auto;
  user-select: none;
  box-shadow: 0 3px 10px 0 rgb(0 0 0 / 10%);
}

.tooltipArrow {
  visibility: visible;
}

.variant-neutral {
  color: $color-neutral-700;
  background: $color-neutral-100;
  background-color: $color-white;
  border: 1px solid $color-neutral-500;

  .tooltipArrow {
    fill: $color-white;
  }
}

.variant-primary {
  color: $color-white;
  background-color: $color-primary-500;

  .tooltipArrow {
    fill: $color-primary-500;
  }
}

.variant-secondary {
  background-color: $color-secondary-300;

  .tooltipArrow {
    fill: $color-secondary-300;
  }
}

.variant-tertiary {
  background-color: $color-tertiary-300;

  .tooltipArrow {
    fill: $color-tertiary-300;
  }
}

.size-md {
  padding: 12px 16px;
  font-size: 1.4rem;
  line-height: 110%;
  border-radius: 4px;

  &[data-side="top"] {
    &[data-align="start"] {
      left: 0;
      margin-bottom: 8px;
      transform: none;

      & > span {
        left: 12px !important;
      }
    }
  }
}

.size-lg {
  padding: 16px 32px;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 130%;
  border-radius: 4px;

  &[data-side="top"] {
    &[data-align="start"] {
      left: 0;
      border-radius: 4px 4px 4px 0;
      transform: none;

      & > span {
        left: -1px !important;
      }
    }

    &[data-align="end"] {
      border-radius: 4px 4px 0;

      & > span {
        right: -1px !important;
        left: auto !important;
        transform: translateY(100%) rotateY(180deg) !important;
      }
    }
  }

  &[data-side="bottom"] {
    &[data-align="start"] {
      left: 0;
      border-radius: 0 4px 4px;
      transform: none;

      & > span {
        left: -1px !important;
        transform: rotateX(180deg) !important;
      }
    }

    &[data-align="end"] {
      right: 0;
      border-radius: 4px 0 4px 4px;
      transform: none;

      & > span {
        right: -1px !important;
        left: auto !important;
      }
    }
  }
}
