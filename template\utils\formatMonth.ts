import { capitalCase } from "change-case";
import { DateArg, format as formatDate, FormatDateOptions } from "date-fns";

/**
 * Add a trailing dot if the month has been cut off.
 * @example `Déc.`, `Mars`, `Janv.`
 */
export default function formatMonth(date: DateArg<Date> & {}, options?: FormatDateOptions): string {
  const long = formatDate(date, "MMMM", options);
  const short = formatDate(date, "MMM", options);
  return capitalCase(short) + (long.length > short.length ? "." : "");
}
