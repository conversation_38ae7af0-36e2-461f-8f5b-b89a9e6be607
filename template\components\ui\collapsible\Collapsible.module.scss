@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.collapsible {
  width: 100%;
}

.trigger {
  display: flex;
  gap: 8px;
  align-items: baseline;
  width: 100%;
  padding: 16px 12px;
  font-size: 1.8rem;
  color: $color-primary-500;
  text-align: left;
  cursor: pointer;
  background-color: $color-neutral-100;

  @include breakpoint(medium up) {
    padding: 16px;
  }

  .collapsible[open] & {
    color: $color-white;
    background-color: $color-primary-500;

    .iconChevron {
      transform: rotate(180deg);
    }
  }
}

.iconChevron {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.content {
  padding: 16px;
  font-size: 1.4rem;
  line-height: 130%;
  white-space: pre-wrap;
  background-color: $color-neutral-100;
  border: 1px solid $color-neutral-200;
  border-top: none;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }
}
