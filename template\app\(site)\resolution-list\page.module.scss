@use "@/styles/lib/mixins.scss" as *;

.sidebar {
  @include breakpoint(large up) {
    width: 344px;
    padding-right: 40px;
  }
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 12px;

  @include breakpoint(medium up) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-block: 24px;

  @include breakpoint(large up) {
    gap: 32px;
    margin-block: 32px;
  }
}
