@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.dateWidget {
  margin-top: 16px;
  color: $color-primary-500;

  @extend %text-wrap;
}

.title {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-bottom: 24px;
  font-size: 1.8rem;
  font-weight: $fw-bold;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 3.2rem;
  }

  i {
    min-width: 32px;
    font-size: 2.4rem;
    color: $color-secondary-500;
    text-align: center;
  }
}

.summary {
  padding-bottom: 12px;
  margin-bottom: 6px;
  font-size: 1.8rem;
  font-weight: $fw-bold;
  line-height: 110%;
  color: $color-primary-500;

  @include breakpoint(large up) {
    padding-bottom: 18px;
    margin-bottom: 8px;
  }
}

.date {
  font-size: 1.6rem;
  font-weight: $fw-bold;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.dateList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 2px;
}

.dateItem {
  display: flex;
  align-items: baseline;
  min-height: 27px;
  font-size: 1.4rem;
  line-height: 130%;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }
}

.dots {
  line-height: 110%;
}

.addToCalendar {
  flex-shrink: 0;
  align-self: flex-start;
  min-width: 32px;
  font-size: 1.6rem;
  line-height: 130%;
  text-align: center;

  i {
    vertical-align: baseline;
  }
}

.loadMoreButton {
  margin-top: 6px;
  line-height: 150%;
  text-decoration-line: underline;
  text-decoration-thickness: 8%;
  text-decoration-style: solid;
  text-underline-position: from-font;
  text-underline-offset: 17.5%;
  cursor: pointer;
  text-decoration-skip-ink: auto;

  @include breakpoint(large up) {
    margin-top: 8px;
  }
}
