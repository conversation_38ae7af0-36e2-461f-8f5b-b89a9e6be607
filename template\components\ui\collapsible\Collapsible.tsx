import Hx from "@/components/ui/title/Hx";
import clsx from "clsx";
import styles from "./Collapsible.module.scss";

interface CollapsibleProps {
  icon?: React.ReactNode;
  label: string;
  headingLevel?: number;
  noHeading?: boolean;
}

/**
 * A component that implements the disclosure pattern.
 */
export default function Collapsible({
  children,
  className,
  icon,
  label,
  headingLevel,
  noHeading = false,
  ...restProps
}: React.PropsWithChildren<CollapsibleProps> & React.HTMLAttributes<HTMLDetailsElement>) {
  return (
    <details className={clsx(styles.collapsible, className)} {...restProps}>
      <summary className={styles.trigger}>
        {icon}
        {noHeading ? label : <Hx level={headingLevel ?? 2}>{label}</Hx>}
        <i className={clsx("fas fa-chevron-down", styles.iconChevron)} aria-hidden="true"></i>
      </summary>
      <div className={styles.content}>{children}</div>
    </details>
  );
}
