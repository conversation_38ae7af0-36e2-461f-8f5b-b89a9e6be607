import dynamic from "next/dynamic";
import { ComponentType } from "react";

// Lazy load components dynamically
const Address = dynamic(() => import("./address/Address"));
const Altcha = dynamic(() => import("./captcha/Altcha"));
const Checkbox = dynamic(() => import("./checkbox/Checkbox"));
const Date = dynamic(() => import("./date/Date"));
const Email = dynamic(() => import("./email/Email"));
const Fieldset = dynamic(() => import("./fieldset/Fieldset"));
const FileUpload = dynamic(() => import("./fileupload/FileUpload"));
const HCaptcha = dynamic(() => import("./captcha/HCaptcha"));
const Name = dynamic(() => import("./name/Name"));
const Number = dynamic(() => import("./number/Number"));
const Phone = dynamic(() => import("./phone/Phone"));
const Radio = dynamic(() => import("./radio/Radio"));
const ReCaptcha = dynamic(() => import("./captcha/ReCaptcha"));
const Select = dynamic(() => import("./select/Select"));
const TextArea = dynamic(() => import("./textarea/TextArea"));
const Text = dynamic(() => import("./text/Text"));
const Time = dynamic(() => import("./time/Time"));

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- Unknown component type
const fieldComponents: Record<string, ComponentType<any>> = {
  AddressField: Address,
  AltchaField: Altcha,
  CheckboxField: Checkbox,
  DateField: Date,
  EmailField: Email,
  Fieldset: Fieldset,
  FileUploadField: FileUpload,
  HcaptchaField: HCaptcha,
  NameField: Name,
  NumberField: Number,
  PhoneField: Phone,
  RadioField: Radio,
  ReCaptchaField: ReCaptcha,
  SelectField: Select,
  TextAreaField: TextArea,
  TextField: Text,
  TimeField: Time,
};

type Field = Record<string, unknown> & {
  __typename?: string;
  fields?: Field[];
};

interface FieldRendererProps {
  fields: Field[];
}

function FieldRenderer({ fields }: FieldRendererProps) {
  return (
    <>
      {fields.map((block, index) => {
        if (!block.__typename) {
          console.warn(`Cannot resolve the field component without a __typename`);

          return null;
        }

        const { __typename: typename, fields, id = index, ...data } = block;

        const Component = fieldComponents[typename];

        if (!Component) {
          console.warn(`No component found for field: ${typename}`);

          return null;
        }

        return (
          <Component key={block.name ?? id} {...data}>
            {fields && <FieldRenderer fields={fields} />}
          </Component>
        );
      })}
    </>
  );
}

export default FieldRenderer;
