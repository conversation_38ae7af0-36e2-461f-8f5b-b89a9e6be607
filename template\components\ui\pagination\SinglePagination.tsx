import { ContentPager } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./SinglePagination.module.scss";

interface SinglePaginationProps {
  className?: string;
  pager: PartialDeep<Omit<ContentPager, "__typename">>;
}

export default function SinglePagination({ className, pager }: SinglePaginationProps) {
  return (
    <nav className={clsx(styles.singlePagination, className)} aria-label="Pagination" role="navigation">
      {pager.list?.url && (
        <div className={styles.toolbar}>
          <Link href={pager.list.url} className={styles.returnLink}>
            {pager.list.text}
          </Link>
        </div>
      )}
      <ul className={styles.pages}>
        {pager.prev?.url && (
          <li>
            <Link
              href={pager.prev.url}
              className={clsx(styles.page, styles.previousPage)}
              rel={pager.prev.rel ?? undefined}
              target={pager.prev.target ?? undefined}
              scroll={false}
            >
              <i className="far fa-chevron-left" aria-hidden="true"></i>
              <span className={styles.text}>
                Précédent
                <strong>{pager.prev.text}</strong>
              </span>
            </Link>
          </li>
        )}
        {pager.next?.url && (
          <li>
            <Link
              href={pager.next.url}
              className={clsx(styles.page, styles.nextPage)}
              rel={pager.next.rel ?? undefined}
              target={pager.next.target ?? undefined}
              scroll={false}
            >
              <i className="far fa-chevron-right" aria-hidden="true"></i>
              <span className={styles.text}>
                Suivant
                <strong>{pager.next.text}</strong>
              </span>
            </Link>
          </li>
        )}
      </ul>
    </nav>
  );
}
