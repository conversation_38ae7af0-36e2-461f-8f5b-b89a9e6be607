@use "@/styles/lib/variables.scss" as *;

.labelWrapper {
  margin-bottom: 16px;

  .label {
    display: block;
    font-weight: 400;
    line-height: 1.5;
  }

  .description {
    display: block;
    font-size: 1.4rem;
    line-height: 1.2;
    color: $color-neutral-500;
  }
}

.labelledRadio {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 12px;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }

  span {
    font-size: 1.6rem;
    line-height: 1.3;
  }
}
