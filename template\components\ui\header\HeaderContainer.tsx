import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import Header from "./Header";

const HEADER_QUERY = graphql(`
  query GetHeader {
    menu(position: "header") {
      items {
        ...MenuItemFragment
        children {
          ...MenuItemFragment
          children {
            ...MenuItemFragment
          }
        }
      }
    }
    siteConfig {
      siteName
      header {
        logoLight {
          url
        }
      }
      socialLinks {
        network
        text
        url
      }
      searchPage
    }
    flashInfoSearch {
      items {
        id
        url
        description
        title
        modifiedDate
      }
    }
  }

  fragment MenuItemFragment on MenuItem {
    url
    title
    target
    linkTitle
    level
    description
    className
  }
`);

export default async function HeaderContainer() {
  const { data } = await query({ query: HEADER_QUERY, errorPolicy: "all" });
  const { menu, siteConfig } = data;
  const { searchPage, socialLinks, siteName, header } = siteConfig ?? {};
  const { logoLight } = header ?? {};
  const flashInfoItems = data.flashInfoSearch?.items ?? [];

  return (
    <Header
      menu={menu ?? undefined}
      socialLinks={socialLinks}
      siteName={siteName}
      logo={logoLight?.url}
      flashInfoItems={flashInfoItems}
      searchUrl={searchPage}
    />
  );
}
