@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.audio {
  display: flex;
  flex-direction: column;
  gap: 6px;
  justify-content: flex-start;
  width: 100%;
  padding-block: 24px;
  margin-block: 12px;

  @include breakpoint(medium up) {
    padding-block: 32px;
    margin-block: 16px;
  }

  @include breakpoint(large up) {
    gap: 8px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.caption {
  line-height: 130%;
  color: $color-neutral-700;

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.element {
  width: 100%;
}
