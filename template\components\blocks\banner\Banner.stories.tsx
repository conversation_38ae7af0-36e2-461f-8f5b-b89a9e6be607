import { BlockLayout, type Image } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Banner, { BannerBlockVariant } from "./Banner";

const imageData: Image = {
  url: "/assets/placeholder-720x480.png",
  width: 720,
  height: 480,
  alt: "Image d'exemple",
};

const meta: Meta<typeof Banner> = {
  title: "Blocks/Banner",
  component: Banner,
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: {
        type: "select",
      },
      options: Object.values(BannerBlockVariant),
    },
    layout: {
      control: {
        type: "radio",
      },
      options: ["CONTAINED", "FULLWIDTH"],
    },
    textColor: {
      control: "color",
      description: "Text color for the text section",
    },
    backgroundColor: {
      control: "color",
      description: "Background color of the text section",
    },
    title: {
      control: "text",
    },
    description: {
      control: "text",
    },
    image: {
      control: "object",
    },
    action: {
      control: "object",
    },
  },
  args: {
    variant: BannerBlockVariant.IMAGE_LEFT,
    layout: BlockLayout.CONTAINED,
    title: "Découvrez la nouvelle saison culturelle aux Herbiers !",
    description:
      "D'octobre 2025 à mai 2025, retour de la saison culturelle aux Herbiers ! Cette année, une belle programmation de 26 artistes vous attend.",
    image: imageData,
    // @ts-expect-error Incomplete action
    action: {
      url: "#",
      text: "En savoir plus",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Banner>;

export const Default: Story = {};

export const ImageRight: Story = {
  args: {
    ...Default.args,
    variant: BannerBlockVariant.IMAGE_RIGHT,
  },
};

export const ImageLeft: Story = {
  args: {
    ...Default.args,
    variant: BannerBlockVariant.IMAGE_LEFT,
  },
};

export const ImageOnly: Story = {
  args: {
    ...Default.args,
    variant: BannerBlockVariant.IMAGE_ONLY,
    image: imageData,
    // @ts-expect-error Incomplete action
    action: {
      url: "#",
      text: "",
    },
  },
};

export const TextOnly: Story = {
  args: {
    ...Default.args,
    variant: BannerBlockVariant.TEXT_ONLY,
    backgroundColor: "#312d79",
  },
};

export const WithCustomBackground: Story = {
  args: {
    ...Default.args,
    backgroundColor: "#e6f7ff",
    textColor: "#000",
  },
};

export const FullWidthBanner: Story = {
  args: {
    ...Default.args,
    layout: BlockLayout.FULLWIDTH,
    image: imageData,
  },
};

export const WithoutDescription: Story = {
  args: {
    ...Default.args,
    description: undefined,
  },
};

export const WithoutAction: Story = {
  args: {
    ...Default.args,
    action: undefined,
  },
};
