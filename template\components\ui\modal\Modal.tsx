"use client";

import Button from "@/components/ui/button/Button";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { DialogClose, DialogContent, DialogContentProps, DialogTitle } from "@radix-ui/react-dialog";
import clsx from "clsx";
import React, { useRef } from "react";
import { focusable } from "tabbable";
import styles from "./Modal.module.scss";

interface ModalProps {
  focusStrategy?: "close-button" | "content";
  fullscreen?: boolean;
  hideTitle?: boolean;
  size?: "sm" | "md" | "lg";
  title: string;
  closeButtonText?: string;
}

export default function Modal({
  children,
  className,
  fullscreen,
  focusStrategy,
  hideTitle,
  size = "md",
  closeButtonText = "Fermer le dialogue",
  title,
  ...restProps
}: React.PropsWithChildren<ModalProps> & DialogContentProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  return (
    <DialogContent
      aria-modal="true"
      aria-describedby={undefined}
      className={clsx(styles.modal, size && styles[`size-${size}`], fullscreen && styles.fullscreen, className)}
      onOpenAutoFocus={(event) => {
        if (focusStrategy === "content" && contentRef.current) {
          const elements = focusable(contentRef.current, { includeContainer: false });

          if (elements.length > 0) {
            event.preventDefault();
            elements[0]?.focus();
          }
        }
      }}
      {...restProps}
    >
      <div className={styles.header}>
        <DialogClose asChild>
          <Tooltip content={closeButtonText}>
            <Button className={styles.closeButton} variant="text">
              <i className="fas fa-xmark" aria-hidden="true"></i>
              <span className="sr-only">{closeButtonText}</span>
            </Button>
          </Tooltip>
        </DialogClose>
      </div>

      <div className={styles.content} ref={contentRef}>
        <DialogTitle asChild>
          <h2 className={clsx(styles.title, hideTitle && "sr-only")}>{title}</h2>
        </DialogTitle>

        {children}
      </div>
    </DialogContent>
  );
}
