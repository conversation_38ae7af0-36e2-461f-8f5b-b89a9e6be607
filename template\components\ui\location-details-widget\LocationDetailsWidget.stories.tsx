import { DirectoryAccessibilityStatus } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import LocationDetailsWidget from "./LocationDetailsWidget";

const meta: Meta<typeof LocationDetailsWidget> = {
  title: "Components/Widget/LocationDetails",
  component: LocationDetailsWidget,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof LocationDetailsWidget>;

export const Default: Story = {
  args: {
    area: "12 345 km ²",
    description:
      "Texte libre 190 caractères max dolor sit amet, consectetur adipiscing elit. Fusce euismod mi mi malesuada lobortis mauris venenatis a. Nam nulla dui porttitor eu cursus in, porta sed lectus.",
    mayorName: "M. Lorem ipsum",
    openingHours: ["Lundi : 15h-18h", "Mardi : 9h30-12h30", "Jeudi : 9h30-12h30 / 14h30-18h30"],
    population: 123_456,
    accessibility: {
      mentalImpairment: DirectoryAccessibilityStatus.SUPPORTED,
      signLanguageReception: DirectoryAccessibilityStatus.NOT_SUPPORTED,
      hearingImpairment: DirectoryAccessibilityStatus.UNKNOWN,
      intellectualImpairment: DirectoryAccessibilityStatus.NOT_SUPPORTED,
      reducedMobility: DirectoryAccessibilityStatus.SUPPORTED,
      strollers: DirectoryAccessibilityStatus.UNKNOWN,
      visualImpairment: DirectoryAccessibilityStatus.UNKNOWN,
    },
  },
};
