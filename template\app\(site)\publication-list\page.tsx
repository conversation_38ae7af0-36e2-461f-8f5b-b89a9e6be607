import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { PublicationFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

const PUBLICATION_LIST_QUERY = graphql(`
  query GetPublicationListConfig($url: URL) {
    route(url: $url) {
      ... on PublicationList {
        defaultPageSize
        leadText
        filters {
          __typename
          attribute
        }
        proposeUrl
        rssUrl
        title
        url
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const PUBLICATION_SEARCH_QUERY = graphql(`
  query GetPublicationList(
    $filter: PublicationFilterInput
    $sort: PublicationSortInput
    $pageSize: Int
    $currentPage: Int
  ) {
    publicationSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        categories {
          description
          relativeUrl
          title
        }
        files {
          downloadUrl
          extname
          label
          mime
          size
          viewUrl
        }
        id
        images {
          original {
            alt
            height
            url
            width
          }
        }
        leadText
        modifiedDate
        publicationDate
        slug
        status
        title
        url
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          placeholder
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: PUBLICATION_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "PublicationList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<FilterInputParams<PublicationFilterInput> & { p?: string }>;
}) {
  const { data: publicationList } = await query({
    query: PUBLICATION_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(publicationList.route?.__typename === "PublicationList");

  const { title, leadText, breadcrumbs, filters: defaultFilters, rssUrl, url, defaultPageSize } = publicationList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<PublicationFilterInput>({
    params,
    filters: defaultFilters,
  });

  const { data: publicationSearch } = await query({
    query: PUBLICATION_SEARCH_QUERY,
    variables: {
      pageSize,
      currentPage,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = publicationSearch?.publicationSearch ?? {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Publications" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les publications" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />
          </div>

          {items.length > 0 && (
            <ol className={styles.grid}>
              {items.map((publication) => (
                <li key={publication.id}>
                  <Item publication={publication} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
