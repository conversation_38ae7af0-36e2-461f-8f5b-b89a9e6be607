import { Image } from "@/generated/graphql/graphql";
import { OneThirdColumnsDecorator, TwoThirdColumnsDecorator } from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Publications from "./Publications";

const meta: Meta<typeof Publications> = {
  title: "Blocks/Publications",
  component: Publications,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Publications>;

const imageData: Image = {
  url: "/assets/placeholder-A4.png",
  width: 212,
  height: 298,
  alt: "Document d'exemple",
};

export const Default: Story = {
  args: {
    title: "Publications",
    titleLevel: 2,
    listUrl: "#",
    publications: [
      {
        id: 1,
        title: "Publication à un fichier",
        leadText: "Cette publication n'a qu'un seul fichier",
        // @ts-expect-error Incomplete image collection
        images: {
          ratio_A4_portrait: imageData,
        },
        url: "#",
        categories: [
          // @ts-expect-error Incomplete category
          {
            title: "Catégorie",
            relativeUrl: "#",
          },
        ],
        files: [
          // @ts-expect-error Incomplete file
          {
            downloadUrl: "#",
            viewUrl: "#",
            size: 1_000_000,
            extname: "pdf",
          },
        ],
      },
      {
        id: 2,
        title: "Publication à fichiers multiples",
        leadText: "Cette publication a plusieurs fichiers",
        // @ts-expect-error Incomplete image collection
        images: {
          ratio_A4_portrait: imageData,
        },
        url: "#fichiers-multiples",
        categories: [
          // @ts-expect-error Incomplete category
          {
            title: "Catégorie",
            relativeUrl: "#",
          },
        ],
        files: [
          // @ts-expect-error Incomplete file
          {
            downloadUrl: "#",
            viewUrl: "#",
            size: 1_500_000,
            extname: "pdf",
          },
          // @ts-expect-error Incomplete file
          {
            downloadUrl: "#",
            size: 2_000_000,
            extname: "docx",
          },
          // @ts-expect-error Incomplete file
          {
            viewUrl: "#voir-fichier3",
            size: 1_800_000,
            extname: "pdf",
          },
        ],
      },
      {
        id: 3,
        title: "Publication en visualisation seule",
        leadText: "Cette publication ne peut être que visualisée en ligne",
        // @ts-expect-error Incomplete image collection
        images: {
          ratio_A4_portrait: imageData,
        },
        url: "#",
        categories: [
          // @ts-expect-error Incomplete category
          {
            title: "Catégorie",
            relativeUrl: "#",
          },
        ],
        files: [
          // @ts-expect-error Incomplete file
          {
            viewUrl: "#",
            size: 2_500_000,
            extname: "pdf",
          },
        ],
      },
    ],
  },
};

export const TwoColumns: Story = {
  args: {
    publications: [Default.args!.publications![0]],
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    publications: [Default.args!.publications![0]],
  },
  decorators: [OneThirdColumnsDecorator],
};
