/**
 * A unique identifier for the dots.
 */
export const DOTS = Symbol("DOTS");

/**
 * A function that calculates page entries for a total number of pages.
 * The delta is the amount of pages to show around the current page.
 * Including edges ensure that the first and last pages are always present.
 * Fills up to tho maximum available page entries to ensure layout consistency.
 */
export default function paginate(current: number, total: number, delta = 2, includeEdges = true) {
  const range = [];
  const pages = [];

  let start = Math.max(1, current - delta);
  let end = Math.min(total, current + delta);

  const maxVisible = Math.min(2 * delta + 1, total);
  const maxLength = Math.min(maxVisible + (includeEdges ? 4 : 0), total);

  if (maxLength === total) {
    start = 1;
    end = total;
  } else if (start <= 2 + 1 && current <= 2 + 1 + delta) {
    start = 1;
    end = 2 + maxVisible; // 2 = [1] + [dots]
  } else if (current > total - delta - 3) {
    start = 1 + total - (2 + maxVisible); // 2 = [dots] + [total]
    end = total;
  }

  for (let index = start; index <= end; index++) {
    range.push(index);
  }

  if (includeEdges) {
    // First page
    if (range[0] > 1) {
      pages.push(1);

      if (range[0] > 2) {
        pages.push(DOTS);
      }
    }

    // Middle range
    pages.push(...range);

    // Last page
    if (range.at(-1)! < total) {
      if (range.at(-1)! < total - 1) {
        pages.push(DOTS);
      }

      pages.push(total);
    }
  } else {
    // Only middle range
    pages.push(...range);
  }

  return pages;
}
