/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  query GetAlbumListConfig($url: URL) {\n    route(url: $url) {\n      ... on AlbumList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetAlbumListConfigDocument,
    "\n  query GetAlbumList($filter: AlbumFilterInput, $sort: AlbumSortInput, $pageSize: Int, $currentPage: Int) {\n    albumSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        id\n        leadText\n        url\n        title\n        videoCount\n        photoCount\n        mediaCount\n        media {\n          ... on AlbumPhoto {\n            alt\n            caption\n          }\n          ... on AlbumVideo {\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n        }\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetAlbumListDocument,
    "\n  query GetAlbum($url: URL!) {\n    route(url: $url) {\n      ... on Album {\n        id\n        mediaCount\n        photoCount\n        videoCount\n        media {\n          ... on AlbumVideo {\n            url\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n          ... on AlbumPhoto {\n            alt\n            caption\n            src\n            height\n            width\n          }\n        }\n        title\n        status\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetAlbumDocument,
    "\n  query GetDirectoryDetails($url: URL) {\n    route(url: $url) {\n      ... on DirectoryList {\n        defaultPageSize\n        leadText\n        proposeUrl\n        rssUrl\n        title\n        url\n        viewMode\n        type\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          __typename\n          attribute\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetDirectoryDetailsDocument,
    "\n  query GetDirectoryList(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        categories {\n          relativeUrl\n          title\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        location {\n          address {\n            city\n            country\n            street\n            zip\n          }\n        }\n        openingHours\n        offices\n        phones {\n          deviceType\n          number\n        }\n        title\n        url\n        viewMode\n        website\n        email\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetDirectoryListDocument,
    "\n  query GetDirectory($url: URL!) {\n    route(url: $url) {\n      ... on Directory {\n        viewMode\n        title\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        email\n        website\n        openingHours\n        offices\n        municipalityArea\n        municipalityPopulation\n        mayorName\n        contactFirstName\n        contactLastName\n        contactEmail\n        phones {\n          number\n          deviceType\n          country\n          internationalNumber\n          label\n        }\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        images {\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        socialLinks {\n          network\n          text\n          url\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        location {\n          address {\n            street\n            city\n            zip\n          }\n          longitude\n          latitude\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetDirectoryDocument,
    "\n  query GetEventListConfig($url: URL) {\n    route(url: $url) {\n      ... on EventList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetEventListConfigDocument,
    "\n  query GetEventList(\n    $filter: EventFilterInput\n    $period: EventPeriodFilterInput\n    $sort: EventSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    eventSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        endDate\n        id\n        leadText\n        startDate\n        title\n        url\n        location {\n          title\n          address {\n            city\n          }\n        }\n        categories {\n          title\n        }\n        periods(filter: $period) {\n          items {\n            startDate\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetEventListDocument,
    "\n  query GetEvent($url: URL!) {\n    route(url: $url) {\n      ... on Event {\n        id\n        title\n        status\n        leadText\n        structuredContent\n        endDate\n        startDate\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        recurrenceSummary\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        audience\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetEventDocument,
    "\n  query GetHomepage {\n    page(url: \"/\") {\n      structuredContent\n      metadata {\n        title\n        description\n      }\n    }\n  }\n": typeof types.GetHomepageDocument,
    "\n  query GetNewsListConfig($url: URL) {\n    route(url: $url) {\n      ... on NewsList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetNewsListConfigDocument,
    "\n  query GetNewsList($filter: NewsFilterInput, $sort: NewsSortInput, $pageSize: Int, $currentPage: Int) {\n    newsSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        leadText\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        slug\n        status\n        title\n        url\n        categories {\n          description\n          relativeUrl\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetNewsListDocument,
    "\n  query GetNews($url: URL!) {\n    route(url: $url) {\n      ... on News {\n        structuredContent\n        title\n        leadText\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        url\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        __typename\n        categories {\n          description\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetNewsDocument,
    "\n  query GetPage($url: URL) {\n    route(url: $url) {\n      ... on Page {\n        title\n        status\n        structuredContent\n        leadText\n        surtitle\n        publicationDate\n        modifiedDate\n        images {\n          ratio_21x9 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetPageDocument,
    "\n  query GetPublicationListConfig($url: URL) {\n    route(url: $url) {\n      ... on PublicationList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetPublicationListConfigDocument,
    "\n  query GetPublicationList(\n    $filter: PublicationFilterInput\n    $sort: PublicationSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    publicationSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        id\n        images {\n          original {\n            alt\n            height\n            url\n            width\n          }\n        }\n        leadText\n        modifiedDate\n        publicationDate\n        slug\n        status\n        title\n        url\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          placeholder\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetPublicationListDocument,
    "\n  query GetPublication($url: URL!) {\n    route(url: $url) {\n      ... on Publication {\n        title\n        status\n        structuredContent\n        leadText\n        publicationDate\n        modifiedDate\n        files {\n          label\n          downloadUrl\n          extname\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_A4_portrait {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetPublicationDocument,
    "\n  query GetResolutionListConfig($url: URL) {\n    route(url: $url) {\n      ... on ResolutionList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": typeof types.GetResolutionListConfigDocument,
    "\n  query GetResolutionList(\n    $filter: ResolutionFilterInput\n    $sort: ResolutionSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    resolutionSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        title\n        fileCount\n        issueDate\n        publicationDate\n        url\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": typeof types.GetResolutionListDocument,
    "\n  query GetResolution($url: URL!) {\n    route(url: $url) {\n      ... on Resolution {\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        issueDate\n        leadText\n        modifiedDate\n        publicationDate\n        structuredContent\n        surtitle\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        title\n        metadata {\n          description\n          title\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": typeof types.GetResolutionDocument,
    "\n  query GetSearchConfig($url: URL) {\n    route(url: $url) {\n      ... on GlobalSearch {\n        __typename\n        defaultPageSize\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          attribute\n          label\n          ... on SelectFilter {\n            attribute\n            label\n            options {\n              label\n              value\n              count\n              children {\n                label\n                value\n                count\n                children {\n                  label\n                  value\n                  count\n                  children {\n                    label\n                    value\n                    count\n                  }\n                }\n              }\n            }\n            placeholder\n          }\n          ... on TextFilter {\n            label\n            attribute\n          }\n        }\n        searchFilter {\n          attribute\n        }\n        title\n        url\n      }\n    }\n  }\n": typeof types.GetSearchConfigDocument,
    "\n  query GetSearchList(\n    $filter: GlobalSearchFilterInput\n    $sort: GlobalSearchSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    search(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      totalCount\n      items {\n        __typename\n        ... on Page {\n          title\n          surtitle\n          leadText\n          url\n          modifiedDate\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on News {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Event {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Publication {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Directory {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Album {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Resolution {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetSearchListDocument,
    "\n  query GetDirectoriesLocation(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize) {\n      totalCount\n      items {\n        id\n        title\n        location {\n          latitude\n          longitude\n        }\n        categories {\n          title\n          relativeUrl\n          description\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n      }\n    }\n  }\n": typeof types.GetDirectoriesLocationDocument,
    "\n  query GetRouteAndType($url: URL) {\n    route(url: $url) {\n      ... on DirectoryMap {\n        types\n        metadata {\n          title\n          description\n        }\n        filters {\n          __typename\n          attribute\n          ... on TextFilter {\n            attribute\n          }\n        }\n        url\n      }\n    }\n  }\n": typeof types.GetRouteAndTypeDocument,
    "\n  query GetDirectoriesAndMapLinks($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        mapLinks {\n          url\n          text\n          icon {\n            src\n            type\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetDirectoriesAndMapLinksDocument,
    "\n  query GetSiteAndMapDetails($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        __typename\n        title\n        leadText\n        metadata {\n          title\n          description\n        }\n        mapLinks {\n          url\n          text\n          icon {\n            src\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetSiteAndMapDetailsDocument,
    "\n  query GetAdminBar($url: String) {\n    barConfig(url: $url) {\n      currentUser {\n        displayName\n        avatar\n      }\n      entries {\n        ...BarMenuEntryFragment\n        children {\n          ...BarMenuEntryFragment\n          children {\n            ...BarMenuEntryFragment\n          }\n        }\n      }\n    }\n  }\n\n  fragment BarMenuEntryFragment on BarMenuEntry {\n    icon {\n      type\n      src\n    }\n    title\n    url\n    screenReaderTitle\n  }\n": typeof types.GetAdminBarDocument,
    "\n  query GetAlerts {\n    alertSearch {\n      totalCount\n      items {\n        id\n        variant\n        title\n        description\n        modifiedDate\n        action {\n          url\n          text\n        }\n      }\n    }\n  }\n": typeof types.GetAlertsDocument,
    "\n  query GetDirectoryMarkersDetails($id: Int!) {\n    directory(id: $id) {\n      accessibility {\n        hearingImpairment\n        intellectualImpairment\n        mentalImpairment\n        reducedMobility\n        signLanguageReception\n        strollers\n        visualImpairment\n      }\n      categories {\n        description\n        relativeUrl\n        title\n      }\n      email\n      images {\n        ratio_3x2 {\n          alt\n          height\n          url\n          width\n        }\n      }\n      location {\n        title\n        address {\n          city\n          country\n          street\n          zip\n        }\n      }\n      phones {\n        country\n        deviceType\n        internationalNumber\n        label\n        number\n      }\n      title\n      website\n    }\n  }\n": typeof types.GetDirectoryMarkersDetailsDocument,
    "\n  query GetEventPeriods($id: Int!, $pageSize: Int, $currentPage: Int) {\n    event(id: $id) {\n      recurrenceSummary\n      periods(pageSize: $pageSize, currentPage: $currentPage) {\n        items {\n          startDate\n          fullday\n          endDate\n        }\n        totalCount\n        pageInfo {\n          currentPage\n        }\n      }\n    }\n  }\n": typeof types.GetEventPeriodsDocument,
    "\n  query GetFooter {\n    siteConfig {\n      footer {\n        top {\n          action {\n            text\n            url\n            class\n            icon {\n              src\n              type\n            }\n            rel\n            target\n          }\n          description\n          title\n        }\n        logoLight {\n          url\n          width\n          height\n        }\n        title\n        buttons {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        clientInfo {\n          address\n          openingHours\n          tel\n        }\n        quickAccess1 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        quickAccess2 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n    }\n  }\n": typeof types.GetFooterDocument,
    "\n  query GetHeader {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoLight {\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n      searchPage\n    }\n    flashInfoSearch {\n      items {\n        id\n        url\n        description\n        title\n        modifiedDate\n      }\n    }\n  }\n\n  fragment MenuItemFragment on MenuItem {\n    url\n    title\n    target\n    linkTitle\n    level\n    description\n    className\n  }\n": typeof types.GetHeaderDocument,
    "\n  query GetRoute($url: URL!) {\n    route(url: $url) {\n      __typename\n\n      ... on Redirect {\n        redirectCode\n        relativeUrl\n      }\n    }\n  }\n": typeof types.GetRouteDocument,
};
const documents: Documents = {
    "\n  query GetAlbumListConfig($url: URL) {\n    route(url: $url) {\n      ... on AlbumList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetAlbumListConfigDocument,
    "\n  query GetAlbumList($filter: AlbumFilterInput, $sort: AlbumSortInput, $pageSize: Int, $currentPage: Int) {\n    albumSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        id\n        leadText\n        url\n        title\n        videoCount\n        photoCount\n        mediaCount\n        media {\n          ... on AlbumPhoto {\n            alt\n            caption\n          }\n          ... on AlbumVideo {\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n        }\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetAlbumListDocument,
    "\n  query GetAlbum($url: URL!) {\n    route(url: $url) {\n      ... on Album {\n        id\n        mediaCount\n        photoCount\n        videoCount\n        media {\n          ... on AlbumVideo {\n            url\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n          ... on AlbumPhoto {\n            alt\n            caption\n            src\n            height\n            width\n          }\n        }\n        title\n        status\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetAlbumDocument,
    "\n  query GetDirectoryDetails($url: URL) {\n    route(url: $url) {\n      ... on DirectoryList {\n        defaultPageSize\n        leadText\n        proposeUrl\n        rssUrl\n        title\n        url\n        viewMode\n        type\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          __typename\n          attribute\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetDirectoryDetailsDocument,
    "\n  query GetDirectoryList(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        categories {\n          relativeUrl\n          title\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        location {\n          address {\n            city\n            country\n            street\n            zip\n          }\n        }\n        openingHours\n        offices\n        phones {\n          deviceType\n          number\n        }\n        title\n        url\n        viewMode\n        website\n        email\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetDirectoryListDocument,
    "\n  query GetDirectory($url: URL!) {\n    route(url: $url) {\n      ... on Directory {\n        viewMode\n        title\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        email\n        website\n        openingHours\n        offices\n        municipalityArea\n        municipalityPopulation\n        mayorName\n        contactFirstName\n        contactLastName\n        contactEmail\n        phones {\n          number\n          deviceType\n          country\n          internationalNumber\n          label\n        }\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        images {\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        socialLinks {\n          network\n          text\n          url\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        location {\n          address {\n            street\n            city\n            zip\n          }\n          longitude\n          latitude\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetDirectoryDocument,
    "\n  query GetEventListConfig($url: URL) {\n    route(url: $url) {\n      ... on EventList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetEventListConfigDocument,
    "\n  query GetEventList(\n    $filter: EventFilterInput\n    $period: EventPeriodFilterInput\n    $sort: EventSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    eventSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        endDate\n        id\n        leadText\n        startDate\n        title\n        url\n        location {\n          title\n          address {\n            city\n          }\n        }\n        categories {\n          title\n        }\n        periods(filter: $period) {\n          items {\n            startDate\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetEventListDocument,
    "\n  query GetEvent($url: URL!) {\n    route(url: $url) {\n      ... on Event {\n        id\n        title\n        status\n        leadText\n        structuredContent\n        endDate\n        startDate\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        recurrenceSummary\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        audience\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetEventDocument,
    "\n  query GetHomepage {\n    page(url: \"/\") {\n      structuredContent\n      metadata {\n        title\n        description\n      }\n    }\n  }\n": types.GetHomepageDocument,
    "\n  query GetNewsListConfig($url: URL) {\n    route(url: $url) {\n      ... on NewsList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetNewsListConfigDocument,
    "\n  query GetNewsList($filter: NewsFilterInput, $sort: NewsSortInput, $pageSize: Int, $currentPage: Int) {\n    newsSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        leadText\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        slug\n        status\n        title\n        url\n        categories {\n          description\n          relativeUrl\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetNewsListDocument,
    "\n  query GetNews($url: URL!) {\n    route(url: $url) {\n      ... on News {\n        structuredContent\n        title\n        leadText\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        url\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        __typename\n        categories {\n          description\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetNewsDocument,
    "\n  query GetPage($url: URL) {\n    route(url: $url) {\n      ... on Page {\n        title\n        status\n        structuredContent\n        leadText\n        surtitle\n        publicationDate\n        modifiedDate\n        images {\n          ratio_21x9 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetPageDocument,
    "\n  query GetPublicationListConfig($url: URL) {\n    route(url: $url) {\n      ... on PublicationList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetPublicationListConfigDocument,
    "\n  query GetPublicationList(\n    $filter: PublicationFilterInput\n    $sort: PublicationSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    publicationSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        id\n        images {\n          original {\n            alt\n            height\n            url\n            width\n          }\n        }\n        leadText\n        modifiedDate\n        publicationDate\n        slug\n        status\n        title\n        url\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          placeholder\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetPublicationListDocument,
    "\n  query GetPublication($url: URL!) {\n    route(url: $url) {\n      ... on Publication {\n        title\n        status\n        structuredContent\n        leadText\n        publicationDate\n        modifiedDate\n        files {\n          label\n          downloadUrl\n          extname\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_A4_portrait {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetPublicationDocument,
    "\n  query GetResolutionListConfig($url: URL) {\n    route(url: $url) {\n      ... on ResolutionList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n": types.GetResolutionListConfigDocument,
    "\n  query GetResolutionList(\n    $filter: ResolutionFilterInput\n    $sort: ResolutionSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    resolutionSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        title\n        fileCount\n        issueDate\n        publicationDate\n        url\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n": types.GetResolutionListDocument,
    "\n  query GetResolution($url: URL!) {\n    route(url: $url) {\n      ... on Resolution {\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        issueDate\n        leadText\n        modifiedDate\n        publicationDate\n        structuredContent\n        surtitle\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        title\n        metadata {\n          description\n          title\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n": types.GetResolutionDocument,
    "\n  query GetSearchConfig($url: URL) {\n    route(url: $url) {\n      ... on GlobalSearch {\n        __typename\n        defaultPageSize\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          attribute\n          label\n          ... on SelectFilter {\n            attribute\n            label\n            options {\n              label\n              value\n              count\n              children {\n                label\n                value\n                count\n                children {\n                  label\n                  value\n                  count\n                  children {\n                    label\n                    value\n                    count\n                  }\n                }\n              }\n            }\n            placeholder\n          }\n          ... on TextFilter {\n            label\n            attribute\n          }\n        }\n        searchFilter {\n          attribute\n        }\n        title\n        url\n      }\n    }\n  }\n": types.GetSearchConfigDocument,
    "\n  query GetSearchList(\n    $filter: GlobalSearchFilterInput\n    $sort: GlobalSearchSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    search(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      totalCount\n      items {\n        __typename\n        ... on Page {\n          title\n          surtitle\n          leadText\n          url\n          modifiedDate\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on News {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Event {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Publication {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Directory {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Album {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Resolution {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n      }\n    }\n  }\n": types.GetSearchListDocument,
    "\n  query GetDirectoriesLocation(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize) {\n      totalCount\n      items {\n        id\n        title\n        location {\n          latitude\n          longitude\n        }\n        categories {\n          title\n          relativeUrl\n          description\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n      }\n    }\n  }\n": types.GetDirectoriesLocationDocument,
    "\n  query GetRouteAndType($url: URL) {\n    route(url: $url) {\n      ... on DirectoryMap {\n        types\n        metadata {\n          title\n          description\n        }\n        filters {\n          __typename\n          attribute\n          ... on TextFilter {\n            attribute\n          }\n        }\n        url\n      }\n    }\n  }\n": types.GetRouteAndTypeDocument,
    "\n  query GetDirectoriesAndMapLinks($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        mapLinks {\n          url\n          text\n          icon {\n            src\n            type\n          }\n        }\n      }\n    }\n  }\n": types.GetDirectoriesAndMapLinksDocument,
    "\n  query GetSiteAndMapDetails($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        __typename\n        title\n        leadText\n        metadata {\n          title\n          description\n        }\n        mapLinks {\n          url\n          text\n          icon {\n            src\n          }\n        }\n      }\n    }\n  }\n": types.GetSiteAndMapDetailsDocument,
    "\n  query GetAdminBar($url: String) {\n    barConfig(url: $url) {\n      currentUser {\n        displayName\n        avatar\n      }\n      entries {\n        ...BarMenuEntryFragment\n        children {\n          ...BarMenuEntryFragment\n          children {\n            ...BarMenuEntryFragment\n          }\n        }\n      }\n    }\n  }\n\n  fragment BarMenuEntryFragment on BarMenuEntry {\n    icon {\n      type\n      src\n    }\n    title\n    url\n    screenReaderTitle\n  }\n": types.GetAdminBarDocument,
    "\n  query GetAlerts {\n    alertSearch {\n      totalCount\n      items {\n        id\n        variant\n        title\n        description\n        modifiedDate\n        action {\n          url\n          text\n        }\n      }\n    }\n  }\n": types.GetAlertsDocument,
    "\n  query GetDirectoryMarkersDetails($id: Int!) {\n    directory(id: $id) {\n      accessibility {\n        hearingImpairment\n        intellectualImpairment\n        mentalImpairment\n        reducedMobility\n        signLanguageReception\n        strollers\n        visualImpairment\n      }\n      categories {\n        description\n        relativeUrl\n        title\n      }\n      email\n      images {\n        ratio_3x2 {\n          alt\n          height\n          url\n          width\n        }\n      }\n      location {\n        title\n        address {\n          city\n          country\n          street\n          zip\n        }\n      }\n      phones {\n        country\n        deviceType\n        internationalNumber\n        label\n        number\n      }\n      title\n      website\n    }\n  }\n": types.GetDirectoryMarkersDetailsDocument,
    "\n  query GetEventPeriods($id: Int!, $pageSize: Int, $currentPage: Int) {\n    event(id: $id) {\n      recurrenceSummary\n      periods(pageSize: $pageSize, currentPage: $currentPage) {\n        items {\n          startDate\n          fullday\n          endDate\n        }\n        totalCount\n        pageInfo {\n          currentPage\n        }\n      }\n    }\n  }\n": types.GetEventPeriodsDocument,
    "\n  query GetFooter {\n    siteConfig {\n      footer {\n        top {\n          action {\n            text\n            url\n            class\n            icon {\n              src\n              type\n            }\n            rel\n            target\n          }\n          description\n          title\n        }\n        logoLight {\n          url\n          width\n          height\n        }\n        title\n        buttons {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        clientInfo {\n          address\n          openingHours\n          tel\n        }\n        quickAccess1 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        quickAccess2 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n    }\n  }\n": types.GetFooterDocument,
    "\n  query GetHeader {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoLight {\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n      searchPage\n    }\n    flashInfoSearch {\n      items {\n        id\n        url\n        description\n        title\n        modifiedDate\n      }\n    }\n  }\n\n  fragment MenuItemFragment on MenuItem {\n    url\n    title\n    target\n    linkTitle\n    level\n    description\n    className\n  }\n": types.GetHeaderDocument,
    "\n  query GetRoute($url: URL!) {\n    route(url: $url) {\n      __typename\n\n      ... on Redirect {\n        redirectCode\n        relativeUrl\n      }\n    }\n  }\n": types.GetRouteDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAlbumListConfig($url: URL) {\n    route(url: $url) {\n      ... on AlbumList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetAlbumListConfig($url: URL) {\n    route(url: $url) {\n      ... on AlbumList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAlbumList($filter: AlbumFilterInput, $sort: AlbumSortInput, $pageSize: Int, $currentPage: Int) {\n    albumSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        id\n        leadText\n        url\n        title\n        videoCount\n        photoCount\n        mediaCount\n        media {\n          ... on AlbumPhoto {\n            alt\n            caption\n          }\n          ... on AlbumVideo {\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n        }\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetAlbumList($filter: AlbumFilterInput, $sort: AlbumSortInput, $pageSize: Int, $currentPage: Int) {\n    albumSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        id\n        leadText\n        url\n        title\n        videoCount\n        photoCount\n        mediaCount\n        media {\n          ... on AlbumPhoto {\n            alt\n            caption\n          }\n          ... on AlbumVideo {\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n        }\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAlbum($url: URL!) {\n    route(url: $url) {\n      ... on Album {\n        id\n        mediaCount\n        photoCount\n        videoCount\n        media {\n          ... on AlbumVideo {\n            url\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n          ... on AlbumPhoto {\n            alt\n            caption\n            src\n            height\n            width\n          }\n        }\n        title\n        status\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetAlbum($url: URL!) {\n    route(url: $url) {\n      ... on Album {\n        id\n        mediaCount\n        photoCount\n        videoCount\n        media {\n          ... on AlbumVideo {\n            url\n            caption\n            provider\n            thumbnail {\n              url\n              width\n              height\n              alt\n            }\n          }\n          ... on AlbumPhoto {\n            alt\n            caption\n            src\n            height\n            width\n          }\n        }\n        title\n        status\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectoryDetails($url: URL) {\n    route(url: $url) {\n      ... on DirectoryList {\n        defaultPageSize\n        leadText\n        proposeUrl\n        rssUrl\n        title\n        url\n        viewMode\n        type\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          __typename\n          attribute\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDirectoryDetails($url: URL) {\n    route(url: $url) {\n      ... on DirectoryList {\n        defaultPageSize\n        leadText\n        proposeUrl\n        rssUrl\n        title\n        url\n        viewMode\n        type\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          __typename\n          attribute\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectoryList(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        categories {\n          relativeUrl\n          title\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        location {\n          address {\n            city\n            country\n            street\n            zip\n          }\n        }\n        openingHours\n        offices\n        phones {\n          deviceType\n          number\n        }\n        title\n        url\n        viewMode\n        website\n        email\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetDirectoryList(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        categories {\n          relativeUrl\n          title\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        location {\n          address {\n            city\n            country\n            street\n            zip\n          }\n        }\n        openingHours\n        offices\n        phones {\n          deviceType\n          number\n        }\n        title\n        url\n        viewMode\n        website\n        email\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectory($url: URL!) {\n    route(url: $url) {\n      ... on Directory {\n        viewMode\n        title\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        email\n        website\n        openingHours\n        offices\n        municipalityArea\n        municipalityPopulation\n        mayorName\n        contactFirstName\n        contactLastName\n        contactEmail\n        phones {\n          number\n          deviceType\n          country\n          internationalNumber\n          label\n        }\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        images {\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        socialLinks {\n          network\n          text\n          url\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        location {\n          address {\n            street\n            city\n            zip\n          }\n          longitude\n          latitude\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetDirectory($url: URL!) {\n    route(url: $url) {\n      ... on Directory {\n        viewMode\n        title\n        leadText\n        structuredContent\n        publicationDate\n        modifiedDate\n        email\n        website\n        openingHours\n        offices\n        municipalityArea\n        municipalityPopulation\n        mayorName\n        contactFirstName\n        contactLastName\n        contactEmail\n        phones {\n          number\n          deviceType\n          country\n          internationalNumber\n          label\n        }\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        images {\n          ratio_1x1 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        socialLinks {\n          network\n          text\n          url\n        }\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        location {\n          address {\n            street\n            city\n            zip\n          }\n          longitude\n          latitude\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEventListConfig($url: URL) {\n    route(url: $url) {\n      ... on EventList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetEventListConfig($url: URL) {\n    route(url: $url) {\n      ... on EventList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        proposeUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEventList(\n    $filter: EventFilterInput\n    $period: EventPeriodFilterInput\n    $sort: EventSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    eventSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        endDate\n        id\n        leadText\n        startDate\n        title\n        url\n        location {\n          title\n          address {\n            city\n          }\n        }\n        categories {\n          title\n        }\n        periods(filter: $period) {\n          items {\n            startDate\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetEventList(\n    $filter: EventFilterInput\n    $period: EventPeriodFilterInput\n    $sort: EventSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    eventSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        endDate\n        id\n        leadText\n        startDate\n        title\n        url\n        location {\n          title\n          address {\n            city\n          }\n        }\n        categories {\n          title\n        }\n        periods(filter: $period) {\n          items {\n            startDate\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEvent($url: URL!) {\n    route(url: $url) {\n      ... on Event {\n        id\n        title\n        status\n        leadText\n        structuredContent\n        endDate\n        startDate\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        recurrenceSummary\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        audience\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetEvent($url: URL!) {\n    route(url: $url) {\n      ... on Event {\n        id\n        title\n        status\n        leadText\n        structuredContent\n        endDate\n        startDate\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        recurrenceSummary\n        accessibility {\n          hearingImpairment\n          intellectualImpairment\n          mentalImpairment\n          reducedMobility\n          signLanguageReception\n          strollers\n          visualImpairment\n        }\n        audience\n        categories {\n          relativeUrl\n          title\n          description\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetHomepage {\n    page(url: \"/\") {\n      structuredContent\n      metadata {\n        title\n        description\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetHomepage {\n    page(url: \"/\") {\n      structuredContent\n      metadata {\n        title\n        description\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetNewsListConfig($url: URL) {\n    route(url: $url) {\n      ... on NewsList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetNewsListConfig($url: URL) {\n    route(url: $url) {\n      ... on NewsList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetNewsList($filter: NewsFilterInput, $sort: NewsSortInput, $pageSize: Int, $currentPage: Int) {\n    newsSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        leadText\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        slug\n        status\n        title\n        url\n        categories {\n          description\n          relativeUrl\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetNewsList($filter: NewsFilterInput, $sort: NewsSortInput, $pageSize: Int, $currentPage: Int) {\n    newsSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        leadText\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        slug\n        status\n        title\n        url\n        categories {\n          description\n          relativeUrl\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetNews($url: URL!) {\n    route(url: $url) {\n      ... on News {\n        structuredContent\n        title\n        leadText\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        url\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        __typename\n        categories {\n          description\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetNews($url: URL!) {\n    route(url: $url) {\n      ... on News {\n        structuredContent\n        title\n        leadText\n        publicationDate\n        modifiedDate\n        images {\n          ratio_3x2 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        url\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        __typename\n        categories {\n          description\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPage($url: URL) {\n    route(url: $url) {\n      ... on Page {\n        title\n        status\n        structuredContent\n        leadText\n        surtitle\n        publicationDate\n        modifiedDate\n        images {\n          ratio_21x9 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetPage($url: URL) {\n    route(url: $url) {\n      ... on Page {\n        title\n        status\n        structuredContent\n        leadText\n        surtitle\n        publicationDate\n        modifiedDate\n        images {\n          ratio_21x9 {\n            url\n            width\n            height\n            alt\n          }\n        }\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPublicationListConfig($url: URL) {\n    route(url: $url) {\n      ... on PublicationList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetPublicationListConfig($url: URL) {\n    route(url: $url) {\n      ... on PublicationList {\n        defaultPageSize\n        leadText\n        filters {\n          __typename\n          attribute\n        }\n        proposeUrl\n        rssUrl\n        title\n        url\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPublicationList(\n    $filter: PublicationFilterInput\n    $sort: PublicationSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    publicationSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        id\n        images {\n          original {\n            alt\n            height\n            url\n            width\n          }\n        }\n        leadText\n        modifiedDate\n        publicationDate\n        slug\n        status\n        title\n        url\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          placeholder\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetPublicationList(\n    $filter: PublicationFilterInput\n    $sort: PublicationSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    publicationSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        categories {\n          description\n          relativeUrl\n          title\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        id\n        images {\n          original {\n            alt\n            height\n            url\n            width\n          }\n        }\n        leadText\n        modifiedDate\n        publicationDate\n        slug\n        status\n        title\n        url\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          placeholder\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetPublication($url: URL!) {\n    route(url: $url) {\n      ... on Publication {\n        title\n        status\n        structuredContent\n        leadText\n        publicationDate\n        modifiedDate\n        files {\n          label\n          downloadUrl\n          extname\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_A4_portrait {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetPublication($url: URL!) {\n    route(url: $url) {\n      ... on Publication {\n        title\n        status\n        structuredContent\n        leadText\n        publicationDate\n        modifiedDate\n        files {\n          label\n          downloadUrl\n          extname\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_A4_portrait {\n            alt\n            height\n            url\n            width\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetResolutionListConfig($url: URL) {\n    route(url: $url) {\n      ... on ResolutionList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetResolutionListConfig($url: URL) {\n    route(url: $url) {\n      ... on ResolutionList {\n        defaultPageSize\n        leadText\n        url\n        title\n        rssUrl\n        filters {\n          __typename\n          attribute\n        }\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        metadata {\n          title\n          description\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetResolutionList(\n    $filter: ResolutionFilterInput\n    $sort: ResolutionSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    resolutionSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        title\n        fileCount\n        issueDate\n        publicationDate\n        url\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"): (typeof documents)["\n  query GetResolutionList(\n    $filter: ResolutionFilterInput\n    $sort: ResolutionSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    resolutionSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      totalCount\n      items {\n        id\n        title\n        fileCount\n        issueDate\n        publicationDate\n        url\n        categories {\n          title\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n        label\n        ... on SelectFilter {\n          options {\n            ...SelectFilterOptionFragment\n            children {\n              ...SelectFilterOptionFragment\n              children {\n                ...SelectFilterOptionFragment\n              }\n            }\n          }\n          placeholder\n        }\n      }\n    }\n  }\n  fragment SelectFilterOptionFragment on SelectFilterOption {\n    label\n    value\n    count\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetResolution($url: URL!) {\n    route(url: $url) {\n      ... on Resolution {\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        issueDate\n        leadText\n        modifiedDate\n        publicationDate\n        structuredContent\n        surtitle\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        title\n        metadata {\n          description\n          title\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"): (typeof documents)["\n  query GetResolution($url: URL!) {\n    route(url: $url) {\n      ... on Resolution {\n        breadcrumbs {\n          items {\n            title\n            url\n            siblings {\n              title\n              url\n            }\n          }\n        }\n        categories {\n          relativeUrl\n          title\n          parent {\n            __typename\n          }\n        }\n        files {\n          downloadUrl\n          extname\n          label\n          mime\n          size\n          viewUrl\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n        issueDate\n        leadText\n        modifiedDate\n        publicationDate\n        structuredContent\n        surtitle\n        pager {\n          list {\n            text\n            url\n          }\n          next {\n            text\n            url\n          }\n          prev {\n            text\n            url\n          }\n        }\n        title\n        metadata {\n          description\n          title\n        }\n      }\n    }\n    siteConfig {\n      socialShare\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSearchConfig($url: URL) {\n    route(url: $url) {\n      ... on GlobalSearch {\n        __typename\n        defaultPageSize\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          attribute\n          label\n          ... on SelectFilter {\n            attribute\n            label\n            options {\n              label\n              value\n              count\n              children {\n                label\n                value\n                count\n                children {\n                  label\n                  value\n                  count\n                  children {\n                    label\n                    value\n                    count\n                  }\n                }\n              }\n            }\n            placeholder\n          }\n          ... on TextFilter {\n            label\n            attribute\n          }\n        }\n        searchFilter {\n          attribute\n        }\n        title\n        url\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSearchConfig($url: URL) {\n    route(url: $url) {\n      ... on GlobalSearch {\n        __typename\n        defaultPageSize\n        breadcrumbs {\n          items {\n            title\n            url\n          }\n        }\n        filters {\n          attribute\n          label\n          ... on SelectFilter {\n            attribute\n            label\n            options {\n              label\n              value\n              count\n              children {\n                label\n                value\n                count\n                children {\n                  label\n                  value\n                  count\n                  children {\n                    label\n                    value\n                    count\n                  }\n                }\n              }\n            }\n            placeholder\n          }\n          ... on TextFilter {\n            label\n            attribute\n          }\n        }\n        searchFilter {\n          attribute\n        }\n        title\n        url\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSearchList(\n    $filter: GlobalSearchFilterInput\n    $sort: GlobalSearchSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    search(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      totalCount\n      items {\n        __typename\n        ... on Page {\n          title\n          surtitle\n          leadText\n          url\n          modifiedDate\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on News {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Event {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Publication {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Directory {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Album {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Resolution {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSearchList(\n    $filter: GlobalSearchFilterInput\n    $sort: GlobalSearchSortInput\n    $pageSize: Int\n    $currentPage: Int\n  ) {\n    search(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      totalCount\n      items {\n        __typename\n        ... on Page {\n          title\n          surtitle\n          leadText\n          url\n          modifiedDate\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on News {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Event {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Publication {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Directory {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Album {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n        ... on Resolution {\n          title\n          leadText\n          url\n          modifiedDate\n          categories {\n            title\n          }\n          images {\n            ratio_3x2 {\n              alt\n              height\n              url\n              width\n            }\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectoriesLocation(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize) {\n      totalCount\n      items {\n        id\n        title\n        location {\n          latitude\n          longitude\n        }\n        categories {\n          title\n          relativeUrl\n          description\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDirectoriesLocation(\n    $type: String!\n    $filter: DirectoryFilterInput\n    $sort: DirectorySortInput\n    $pageSize: Int\n  ) {\n    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize) {\n      totalCount\n      items {\n        id\n        title\n        location {\n          latitude\n          longitude\n        }\n        categories {\n          title\n          relativeUrl\n          description\n        }\n        images {\n          ratio_3x2 {\n            alt\n            height\n            url\n            width\n          }\n        }\n      }\n      pageInfo {\n        currentPage\n        pageSize\n        totalPages\n      }\n      filters {\n        __typename\n        attribute\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetRouteAndType($url: URL) {\n    route(url: $url) {\n      ... on DirectoryMap {\n        types\n        metadata {\n          title\n          description\n        }\n        filters {\n          __typename\n          attribute\n          ... on TextFilter {\n            attribute\n          }\n        }\n        url\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetRouteAndType($url: URL) {\n    route(url: $url) {\n      ... on DirectoryMap {\n        types\n        metadata {\n          title\n          description\n        }\n        filters {\n          __typename\n          attribute\n          ... on TextFilter {\n            attribute\n          }\n        }\n        url\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectoriesAndMapLinks($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        mapLinks {\n          url\n          text\n          icon {\n            src\n            type\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDirectoriesAndMapLinks($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        mapLinks {\n          url\n          text\n          icon {\n            src\n            type\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSiteAndMapDetails($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        __typename\n        title\n        leadText\n        metadata {\n          title\n          description\n        }\n        mapLinks {\n          url\n          text\n          icon {\n            src\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSiteAndMapDetails($url: URL) {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoDark {\n          alt\n          height\n          url\n          width\n        }\n      }\n      footer {\n        quickAccess2 {\n          rel\n          target\n          text\n          url\n        }\n      }\n    }\n    route(url: $url) {\n      ... on GlobalMap {\n        __typename\n        title\n        leadText\n        metadata {\n          title\n          description\n        }\n        mapLinks {\n          url\n          text\n          icon {\n            src\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAdminBar($url: String) {\n    barConfig(url: $url) {\n      currentUser {\n        displayName\n        avatar\n      }\n      entries {\n        ...BarMenuEntryFragment\n        children {\n          ...BarMenuEntryFragment\n          children {\n            ...BarMenuEntryFragment\n          }\n        }\n      }\n    }\n  }\n\n  fragment BarMenuEntryFragment on BarMenuEntry {\n    icon {\n      type\n      src\n    }\n    title\n    url\n    screenReaderTitle\n  }\n"): (typeof documents)["\n  query GetAdminBar($url: String) {\n    barConfig(url: $url) {\n      currentUser {\n        displayName\n        avatar\n      }\n      entries {\n        ...BarMenuEntryFragment\n        children {\n          ...BarMenuEntryFragment\n          children {\n            ...BarMenuEntryFragment\n          }\n        }\n      }\n    }\n  }\n\n  fragment BarMenuEntryFragment on BarMenuEntry {\n    icon {\n      type\n      src\n    }\n    title\n    url\n    screenReaderTitle\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetAlerts {\n    alertSearch {\n      totalCount\n      items {\n        id\n        variant\n        title\n        description\n        modifiedDate\n        action {\n          url\n          text\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetAlerts {\n    alertSearch {\n      totalCount\n      items {\n        id\n        variant\n        title\n        description\n        modifiedDate\n        action {\n          url\n          text\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDirectoryMarkersDetails($id: Int!) {\n    directory(id: $id) {\n      accessibility {\n        hearingImpairment\n        intellectualImpairment\n        mentalImpairment\n        reducedMobility\n        signLanguageReception\n        strollers\n        visualImpairment\n      }\n      categories {\n        description\n        relativeUrl\n        title\n      }\n      email\n      images {\n        ratio_3x2 {\n          alt\n          height\n          url\n          width\n        }\n      }\n      location {\n        title\n        address {\n          city\n          country\n          street\n          zip\n        }\n      }\n      phones {\n        country\n        deviceType\n        internationalNumber\n        label\n        number\n      }\n      title\n      website\n    }\n  }\n"): (typeof documents)["\n  query GetDirectoryMarkersDetails($id: Int!) {\n    directory(id: $id) {\n      accessibility {\n        hearingImpairment\n        intellectualImpairment\n        mentalImpairment\n        reducedMobility\n        signLanguageReception\n        strollers\n        visualImpairment\n      }\n      categories {\n        description\n        relativeUrl\n        title\n      }\n      email\n      images {\n        ratio_3x2 {\n          alt\n          height\n          url\n          width\n        }\n      }\n      location {\n        title\n        address {\n          city\n          country\n          street\n          zip\n        }\n      }\n      phones {\n        country\n        deviceType\n        internationalNumber\n        label\n        number\n      }\n      title\n      website\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetEventPeriods($id: Int!, $pageSize: Int, $currentPage: Int) {\n    event(id: $id) {\n      recurrenceSummary\n      periods(pageSize: $pageSize, currentPage: $currentPage) {\n        items {\n          startDate\n          fullday\n          endDate\n        }\n        totalCount\n        pageInfo {\n          currentPage\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetEventPeriods($id: Int!, $pageSize: Int, $currentPage: Int) {\n    event(id: $id) {\n      recurrenceSummary\n      periods(pageSize: $pageSize, currentPage: $currentPage) {\n        items {\n          startDate\n          fullday\n          endDate\n        }\n        totalCount\n        pageInfo {\n          currentPage\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetFooter {\n    siteConfig {\n      footer {\n        top {\n          action {\n            text\n            url\n            class\n            icon {\n              src\n              type\n            }\n            rel\n            target\n          }\n          description\n          title\n        }\n        logoLight {\n          url\n          width\n          height\n        }\n        title\n        buttons {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        clientInfo {\n          address\n          openingHours\n          tel\n        }\n        quickAccess1 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        quickAccess2 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetFooter {\n    siteConfig {\n      footer {\n        top {\n          action {\n            text\n            url\n            class\n            icon {\n              src\n              type\n            }\n            rel\n            target\n          }\n          description\n          title\n        }\n        logoLight {\n          url\n          width\n          height\n        }\n        title\n        buttons {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        clientInfo {\n          address\n          openingHours\n          tel\n        }\n        quickAccess1 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n        quickAccess2 {\n          class\n          icon {\n            src\n            type\n          }\n          rel\n          target\n          text\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetHeader {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoLight {\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n      searchPage\n    }\n    flashInfoSearch {\n      items {\n        id\n        url\n        description\n        title\n        modifiedDate\n      }\n    }\n  }\n\n  fragment MenuItemFragment on MenuItem {\n    url\n    title\n    target\n    linkTitle\n    level\n    description\n    className\n  }\n"): (typeof documents)["\n  query GetHeader {\n    menu(position: \"header\") {\n      items {\n        ...MenuItemFragment\n        children {\n          ...MenuItemFragment\n          children {\n            ...MenuItemFragment\n          }\n        }\n      }\n    }\n    siteConfig {\n      siteName\n      header {\n        logoLight {\n          url\n        }\n      }\n      socialLinks {\n        network\n        text\n        url\n      }\n      searchPage\n    }\n    flashInfoSearch {\n      items {\n        id\n        url\n        description\n        title\n        modifiedDate\n      }\n    }\n  }\n\n  fragment MenuItemFragment on MenuItem {\n    url\n    title\n    target\n    linkTitle\n    level\n    description\n    className\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetRoute($url: URL!) {\n    route(url: $url) {\n      __typename\n\n      ... on Redirect {\n        redirectCode\n        relativeUrl\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetRoute($url: URL!) {\n    route(url: $url) {\n      __typename\n\n      ... on Redirect {\n        redirectCode\n        relativeUrl\n      }\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;