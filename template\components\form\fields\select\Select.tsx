"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import SelectElement from "@/components/ui/select/Select";
import { SelectField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type SelectProps = Omit<SelectField, "__typename">;

export default function Select({
  name,
  autocomplete,
  defaultValue: userDefaultValue,
  label,
  description,
  placeholder,
  required,
  choices,
  condition,
  validationMessage,
  columnSpan,
}: SelectProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const selectId = useId();
  const errorId = useId();

  // Fallback to a default value if none is defined
  const defaultValue = userDefaultValue ?? choices.find((choice) => choice?.defaultSelected)?.value ?? "";

  if (!visible) return null;

  return (
    <FormControl columnSpan={columnSpan}>
      <Label htmlFor={selectId} description={description ?? undefined} required={required}>
        {label}
      </Label>

      <SelectElement
        id={selectId}
        autoComplete={autocomplete ?? undefined}
        defaultValue={defaultValue}
        placeholder={placeholder ?? undefined}
        error={!!error}
        aria-describedby={error ? errorId : undefined}
        aria-invalid={error ? true : undefined}
        {...register(name, { required })}
      >
        {choices.map(
          (choice, index) =>
            choice && (
              <option key={index} value={choice.value ?? ""}>
                {choice.label}
              </option>
            )
        )}
      </SelectElement>

      {error && (
        <FormHelper id={errorId} variant="error">
          {error.message?.toString()}
        </FormHelper>
      )}
    </FormControl>
  );
}
