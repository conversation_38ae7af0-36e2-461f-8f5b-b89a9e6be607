"use client";

import clsx from "clsx";
import { CSSProperties, useMemo, useState } from "react";
import { useMediaQuery } from "usehooks-ts";
import Lightbox, { GenericSlide, LightboxProps, SlideIframe, SlideImage, SlideVideo } from "yet-another-react-lightbox";
import Captions from "yet-another-react-lightbox/plugins/captions";
import "yet-another-react-lightbox/plugins/captions.css";
import Video from "yet-another-react-lightbox/plugins/video";
import "yet-another-react-lightbox/styles.css";
import styles from "./Gallery.module.scss";
import GalleryImage from "./GalleryImage";
import IframeSlide from "./IframeSlide";

interface GalleryProps {
  label?: string;
  className?: string;
  columns?: number;
  slides: (SlideImage | SlideVideo | SlideIframe)[];
  lightboxProps?: Partial<LightboxProps>;
}

interface GridCSSProperties extends CSSProperties {
  "--gallery-columns"?: number;
}

/**
 * A responsive media gallery displayed on 3 columns (desktop) and a single image (mobile).
 */
export default function Gallery({ className, slides, columns = 3, label, lightboxProps }: GalleryProps) {
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const isDesktopViewport = useMediaQuery("(min-width: 1216px)");
  const viewMode = useMemo(() => (isDesktopViewport ? "grid" : "single"), [isDesktopViewport]);

  if (slides.length === 0) {
    return;
  }

  const imageCount = slides.filter((slide) => isSlideImage(slide)).length;

  const videoCount = slides.filter((slide) => isSlideVideo(slide) || isSlideIframe(slide)).length;

  const grideStyle: GridCSSProperties = {
    "--gallery-columns": viewMode === "grid" ? columns || 1 : undefined,
  };

  return (
    <>
      <section className={clsx(styles.gallery, className)} aria-label={label || "Galerie photos"}>
        {viewMode === "grid" && (
          <ul className={styles.grid} style={grideStyle}>
            {slides.map((slide, index) => (
              <li key={index}>
                <GalleryImage
                  poster={getThumbnailPoster(slide)}
                  src={isSlideVideo(slide) ? slide.sources[0]?.src : slide.src}
                  caption={typeof slide.description === "string" ? slide.description : undefined}
                  playable={isSlideVideo(slide) || isSlideIframe(slide)}
                  onClick={() => {
                    setIsLightboxOpen(true);
                    setLightboxIndex(index);
                  }}
                />
              </li>
            ))}
          </ul>
        )}

        {viewMode === "single" && (
          <div className={styles.single}>
            <GalleryImage
              poster={getThumbnailPoster(slides[0])}
              src={isSlideVideo(slides[0]) ? slides[0].sources[0]?.src : slides[0].src}
              caption={typeof slides[0].description === "string" ? slides[0].description : undefined}
              playable={isSlideVideo(slides[0]) || isSlideIframe(slides[0])}
              onClick={() => setIsLightboxOpen(true)}
            />
            <AdditionalMediaIndicator firstSlide={slides[0]} imageCount={imageCount} videoCount={videoCount} />
          </div>
        )}
      </section>

      <Lightbox
        plugins={[Captions, Video]}
        labels={{
          Close: "Fermer le dialogue",
          Previous: "Image précédente",
          Next: "Image suivante",
          Carousel: "Carrousel",
          "Photo gallery": "Galerie photos",
          "{index} of {total}": "{index} sur {total}",
          Slide: "Image",
          Lightbox: "Visionneuse de médias",
          Caption: "Légende",
          ...lightboxProps?.labels,
        }}
        index={lightboxIndex}
        on={{ view: ({ index: currentIndex }) => setLightboxIndex(currentIndex) }}
        open={isLightboxOpen}
        close={() => setIsLightboxOpen(false)}
        slides={slides}
        render={{
          slide: ({ slide, offset }) => {
            return isSlideIframe(slide) ? <IframeSlide slide={slide} offset={offset} /> : null;
          },
        }}
        carousel={{
          finite: true,
        }}
      />
    </>
  );
}

function isSlideImage(slide: GenericSlide): slide is SlideImage {
  return slide.type === undefined || slide.type === "image";
}

function isSlideIframe(slide: GenericSlide): slide is SlideIframe {
  return slide.type === "iframe";
}

function isSlideVideo(slide: GenericSlide): slide is SlideVideo {
  return slide.type === "video";
}

/**
 * Returns the source of the image to be displayd in the grid.
 */
function getThumbnailPoster(slide: SlideImage | SlideVideo | SlideIframe) {
  return isSlideImage(slide) ? slide.src : slide.poster;
}

interface AdditionalMediaIndicatorProps {
  firstSlide: SlideImage | SlideVideo | SlideIframe;
  imageCount: number;
  videoCount: number;
}

/**
 * Tells the user how many additional media elements are present.
 */
function AdditionalMediaIndicator({ firstSlide, imageCount, videoCount }: AdditionalMediaIndicatorProps) {
  if (isSlideImage(firstSlide)) {
    imageCount -= 1;
  }

  if (isSlideVideo(firstSlide) || isSlideIframe(firstSlide)) {
    videoCount -= 1;
  }

  if (imageCount === 0 && videoCount === 0) {
    return;
  }

  const content = [
    imageCount > 0 && `${imageCount} photo${imageCount > 1 ? "s" : ""}`,
    videoCount > 0 && `${videoCount} vidéo${videoCount > 1 ? "s" : ""}`,
  ].filter(Boolean);

  let roleDescription = "Images et vidéos supplémentaires";

  if (videoCount === 0) {
    roleDescription = "Images supplémentaires";
  }

  if (imageCount === 0) {
    roleDescription = "Vidéos supplémentaires";
  }

  return (
    <p className={styles.mediaCount} aria-roledescription={roleDescription}>
      <span aria-hidden="true">+</span> {content.join(" et ")}
    </p>
  );
}
