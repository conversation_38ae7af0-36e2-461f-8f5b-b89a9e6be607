import type { Met<PERSON>, StoryObj } from "@storybook/nextjs";
import Paragraph from "./Paragraph";

const meta: Meta<typeof Paragraph> = {
  title: "Blocks/Paragraph",
  component: Paragraph,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Paragraph>;

export const Default: Story = {
  args: {
    html: "His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum.",
  },
};

export const RichContent: Story = {
  args: {
    html: "His cognitis Gallus ut <b>serpens</b> adpetitus telo <strong>vel</strong> saxo iamque spes extremas opperiens et succurrens saluti suae <a href='#'>quavis</a> ratione <i>colligi</i> omnes iussit armatos et cum starent attoniti, districta dentium <em>acie</em> stridens adeste inquit viri fortes mihi periclitanti vobiscum.",
  },
};
