@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.newsFocus {
  position: relative;
  display: flex;
  flex-direction: column-reverse;
  gap: 12px;
  margin-bottom: 32px;

  @include breakpoint(medium up) {
    flex-direction: row-reverse;
    gap: 24px;
    justify-content: left;
    margin-bottom: 40px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    margin-bottom: 48px;
  }
}

.image {
  flex-grow: 1;
  width: 100%;

  @container (min-width: 720px) {
    max-width: 348px;
  }

  @container (min-width: 1216px) {
    max-width: 592px;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  justify-content: center;

  @include breakpoint(large up) {
    gap: 16px;
    width: 50%;
    padding-right: 96px;
  }
}

.category {
  display: block;
  margin-bottom: 12px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 16px;
    font-size: 2.4rem;
  }
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 3.2rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}
