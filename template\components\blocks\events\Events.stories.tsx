import type { Event, Image } from "@/generated/graphql/graphql";
import { OneThirdColumnsDecorator, TwoThirdColumnsDecorator } from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Events from "./Events";

const meta: Meta<typeof Events> = {
  title: "Blocks/Events",
  component: Events,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Events>;

const imageData: Image = {
  url: "/assets/placeholder-720x480.png",
  width: 720,
  height: 480,
  alt: "Image d'exemple",
};

const eventData: Event = {
  title: "Titre de l’agenda lorem ipsum dolor sit amet, consetet elis",
  leadText: "Ex hoc numero nobis exempla sumenda sunt",
  // @ts-expect-error Incomplete image collection
  images: {
    ratio_3x2: imageData,
  },
  url: "#",
  startDate: "2025-01-01",
  endDate: "2025-12-31",
  categories: [
    // @ts-expect-error Incomplete category
    {
      title: "Catégorie",
      relativeUrl: "#",
    },
  ],
  location: {
    title: "Stratis",
    longitude: 6.044_76,
    latitude: 43.1541,
    address: {
      city: "La Farlède",
      zip: "83210",
      street: ["18 Rue Lavoisier"],
      country: "FR",
    },
  },
};

export const Default: Story = {
  args: {
    title: "Agenda",
    titleLevel: 1,
    listUrl: "#",
    proposeUrl: true,
    tags: [
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
    ],
    focusedEvent: eventData,
    events: [eventData, eventData, eventData, eventData, eventData, eventData],
  },
};

export const TwoColumns: Story = {
  args: {
    events: [eventData, eventData, eventData, eventData],
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    events: [eventData, eventData, eventData, eventData],
  },
  decorators: [OneThirdColumnsDecorator],
};
