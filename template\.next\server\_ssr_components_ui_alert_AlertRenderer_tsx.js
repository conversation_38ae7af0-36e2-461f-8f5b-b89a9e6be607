"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_ui_alert_AlertRenderer_tsx";
exports.ids = ["_ssr_components_ui_alert_AlertRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./components/ui/alert/AlertRenderer.tsx":
/*!***********************************************!*\
  !*** ./components/ui/alert/AlertRenderer.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AlertRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/api/app-dynamic.js\");\n\n\nconst PopUpAlert = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/usehooks-ts@3.1.1_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/lodash.debounce@4.0.8\"), __webpack_require__.e(\"vendor-chunks/react-remove-scroll@2.7.1_@types+react@19.1.9_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/@radix-ui+react-dialog@1.1._2b1577dcb6622a3f85460f7799e16ff1\"), __webpack_require__.e(\"vendor-chunks/@radix-ui+react-focus-scope_41fc3656be8a2a64df229075c55ef0d9\"), __webpack_require__.e(\"vendor-chunks/aria-hidden@1.2.6\"), __webpack_require__.e(\"vendor-chunks/react-remove-scroll-bar@2.3_c31249418407dd99e69021e4a3135376\"), __webpack_require__.e(\"vendor-chunks/use-callback-ref@1.3.3_@types+react@19.1.9_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/use-sidecar@1.1.3_@types+react@19.1.9_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/react-style-singleton@2.2.3_@types+react@19.1.9_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/@radix-ui+react-focus-guard_d989674be485e7e8223c93c43010205c\"), __webpack_require__.e(\"vendor-chunks/get-nonce@1.0.1\"), __webpack_require__.e(\"_ssr_components_ui_alert_PopUpAlert_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./PopUpAlert */ \"(ssr)/./components/ui/alert/PopUpAlert.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\AlertRenderer.tsx -> \" + \"./PopUpAlert\"\n        ]\n    }\n});\nconst StickyNote = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/usehooks-ts@3.1.1_react@19.1.1\"), __webpack_require__.e(\"vendor-chunks/lodash.debounce@4.0.8\"), __webpack_require__.e(\"_ssr_components_ui_alert_StickyNote_tsx\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./StickyNote */ \"(ssr)/./components/ui/alert/StickyNote.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\AlertRenderer.tsx -> \" + \"./StickyNote\"\n        ]\n    }\n});\nconst componentMap = {\n    POPUP: PopUpAlert,\n    STICKY: StickyNote\n};\n/**\r\n * Render the alert based on the variant type (POPUP or STICKY).\r\n */ function AlertRenderer({ entries }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: entries.map((alert)=>{\n            const Component = alert.variant && componentMap[alert.variant];\n            return Component ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                alert: alert\n            }, alert.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\AlertRenderer.tsx\",\n                lineNumber: 33,\n                columnNumber: 28\n            }, this) : null;\n        })\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert/AlertRenderer.tsx\n");

/***/ })

};
;