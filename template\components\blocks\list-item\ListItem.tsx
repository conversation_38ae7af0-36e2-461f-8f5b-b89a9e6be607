import { ListItemBlock } from "@/generated/graphql/graphql";
import React from "react";
import striptags from "striptags";

type ListItemProps = Partial<Omit<ListItemBlock, "__typename" | "innerBlocks">>;

export default function ListItem({ anchor, html, children }: React.PropsWithChildren<ListItemProps>) {
  const innerHTML = striptags(html ?? "", ["a", "b", "i", "strong", "em"]);

  // Output the html directly if the item has no children
  if (!children) {
    // eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- HTML is sanitized
    return <li id={anchor ?? undefined} className="block-list-item" dangerouslySetInnerHTML={{ __html: innerHTML }} />;
  }

  return (
    <li id={anchor ?? undefined} className="block-list-item">
      {innerHTML ? (
        <>
          {/* eslint-disable-next-line @eslint-react/dom/no-dangerously-set-innerhtml -- HTML is sanitized */}
          <span dangerouslySetInnerHTML={{ __html: innerHTML }} />
          {children}
        </>
      ) : (
        children
      )}
    </li>
  );
}
