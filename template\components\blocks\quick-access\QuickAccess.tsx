"use client";

import Icon from "@/components/ui/icon/Icon";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import { IconType, type QuickAccessBlock } from "@/generated/graphql/graphql";
import A11y from "@/lib/swiper/modules/a11y";
import clsx from "clsx";
import "css.escape";
import Link from "next/link";
import { useId, useState } from "react";
import "swiper/css";
import "swiper/css/navigation";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import { Swiper as SwiperInstance } from "swiper/types";
import styles from "./QuickAccess.module.scss";
import QuickAccessFocus from "./QuickAccessFocus";

type QuickAccessProps = Omit<QuickAccessBlock, "__typename" | "innerBlocks">;

export default function QuickAccess({ items = [], focus }: QuickAccessProps) {
  const titleId = useId();
  const previousId = useId();
  const nextId = useId();
  const [isCarousel, setIsCarousel] = useState(false);

  function handleBreakpoint(swiper: SwiperInstance) {
    setTimeout(() => setIsCarousel(swiper.width !== swiper.virtualSize));
  }

  if (items.length === 0) {
    return null;
  }

  return (
    <nav
      role="navigation"
      className={clsx("block-quick-access contained", styles.quickAccess)}
      aria-labelledby={titleId}
      aria-roledescription={isCarousel ? "Navigation de type carrousel" : undefined}
    >
      <h2 id={titleId} className="sr-only">
        Accès rapides
      </h2>
      <div className={styles.swiperContainer}>
        <Tooltip content="Afficher les éléments précédents">
          <div id={previousId} role="button" tabIndex={0} className={styles.prevButton}>
            <i className="fas fa-chevron-left" aria-hidden="true"></i>
            <span className="sr-only">Afficher les éléments précédents</span>
          </div>
        </Tooltip>
        <Tooltip content="Afficher les éléments suivants">
          <div id={nextId} role="button" tabIndex={0} className={styles.nextButton}>
            <i className="fas fa-chevron-right" aria-hidden="true"></i>
            <span className="sr-only">Afficher les éléments suivants</span>
          </div>
        </Tooltip>
        <Swiper
          modules={[Navigation, A11y]}
          slidesPerView={1}
          navigation={{
            prevEl: "#" + CSS.escape(previousId),
            nextEl: "#" + CSS.escape(nextId),
            disabledClass: styles.navigationDisabled,
            lockClass: styles.navigationHidden,
          }}
          breakpoints={{
            768: { slidesPerView: Math.min(4, items.length) },
            1600: { slidesPerView: Math.min(focus ? 4 : 6, items.length) },
          }}
          wrapperTag="ul"
          a11y={{
            nextSlideMessage: "Éléments suivants affichés",
            prevSlideMessage: "Éléments précédents affichés",
            firstSlideMessage: "Premiers éléments affichés",
            lastSlideMessage: "Derniers éléments affichés",
            slideLabelMessage: "{{index}} sur {{slidesLength}}",
            slideRole: undefined,
          }}
          onBreakpoint={handleBreakpoint}
        >
          {items.map((item, index) => (
            <SwiperSlide tag="li" key={index}>
              <Link className={styles.item} href={item.url}>
                {item.icon && <Icon type={IconType.URL} src={item.icon.src} className={styles.icon} />}
                <span className={styles.text}>{item.text}</span>
              </Link>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      {focus && <QuickAccessFocus {...focus} />}
    </nav>
  );
}
