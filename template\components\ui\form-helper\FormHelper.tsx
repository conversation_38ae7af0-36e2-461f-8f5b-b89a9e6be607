import clsx from "clsx";
import styles from "./FormHelper.module.scss";

interface FormHelperProps {
  variant: "info" | "error" | "warn" | "success";
}

const variantIcons: Record<FormHelperProps["variant"], string> = {
  info: "far fa-square-info",
  error: "far fa-octagon-xmark",
  warn: "far fa-triangle-exclamation",
  success: "far fa-circle-check",
};

const variantRoleDescription: Record<FormHelperProps["variant"], string> = {
  info: "Information",
  error: "Erreur",
  warn: "Attention",
  success: "Ok",
};

export default function FormHelper({
  variant,
  className,
  children,
  ...restProps
}: React.PropsWithChildren<FormHelperProps & React.HTMLAttributes<HTMLDivElement>>) {
  return (
    <p
      className={clsx(styles.formHelper, variant && styles[`variant-${variant}`], className)}
      {...restProps}
      {...(variant ? { "aria-roledescription": variantRoleDescription[variant] } : {})}
    >
      {variant && <i className={variantIcons[variant]} aria-hidden="true"></i>}
      {children}
    </p>
  );
}
