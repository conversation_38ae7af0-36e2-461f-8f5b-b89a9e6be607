@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.briefNews {
  position: relative;
}

.category {
  display: block;
  margin-bottom: 12px;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(large up) {
    margin-bottom: 16px;
  }
}

.title {
  font-size: 1.8rem;
  line-height: 120%;
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}
