@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.skipLink {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: $layer-skip-links;
  padding: 24px;
  font-size: 1.6rem;
  line-height: 110%;
  color: $color-primary-500;
  white-space: nowrap;
  text-decoration: underline;
  text-decoration-thickness: 8%;
  text-underline-position: from-font;
  text-underline-offset: 17.5%;
  background: white;
  border: 2px solid $color-primary-500;
  border-radius: 4px;

  @include breakpoint(large up) {
    font-size: 1.8rem;
  }

  &:not(:focus) {
    @include visually-hidden;
  }
}
