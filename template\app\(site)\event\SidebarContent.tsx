import { Event } from "@/generated/graphql/graphql";
import dynamic from "next/dynamic";

const PublicWidget = dynamic(() => import("@/components/ui/public-widget/PublicWidget"));
const DateWidgetContainer = dynamic(() => import("@/components/ui/date-widget/DateWidgetContainer"));

interface SidebarContentProps {
  eventData: Pick<Event, "audience" | "accessibility" | "startDate" | "endDate" | "recurrenceSummary">;
  eventId: number;
}

function SidebarContent({
  eventData: { audience, accessibility, startDate, recurrenceSummary },
  eventId,
}: SidebarContentProps) {
  return (
    <>
      <PublicWidget audience={audience} accessibility={accessibility ?? undefined} />
      {startDate && <DateWidgetContainer recurrenceSummary={recurrenceSummary} eventId={eventId} />}
    </>
  );
}

export default SidebarContent;
