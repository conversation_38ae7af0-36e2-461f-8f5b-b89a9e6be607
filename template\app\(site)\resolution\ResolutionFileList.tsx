import PublicationFile from "@/components/ui/publication-file/PublicationFile";
import { Resolution } from "@/generated/graphql/graphql";
import { useId } from "react";
import styles from "./ResolutionFileList.module.scss";

interface ResolutionFileList {
  files: Resolution["files"];
}

export default function ResolutionFileList({ files }: ResolutionFileList) {
  const fileCountId = useId();

  if (files.length === 0) {
    return;
  }

  return (
    <div className="container">
      <p id={fileCountId} className={styles.fileCount}>
        {files.length} document{files.length === 1 ? "" : "s"}
      </p>
      <ul className={styles.fileList} aria-describedby={fileCountId}>
        {files.map((file, index) => (
          <PublicationFile
            key={index}
            label={file.label ?? "Fichier"}
            extname={file.extname ?? undefined}
            size={file.size ?? undefined}
            downloadUrl={file.downloadUrl ?? undefined}
            viewUrl={file.viewUrl ?? undefined}
          />
        ))}
      </ul>
    </div>
  );
}
