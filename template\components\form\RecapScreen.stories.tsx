import { FormStep } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import { FormProvider, useForm } from "react-hook-form";
import RecapScreen from "./RecapScreen";

const meta: Meta<typeof RecapScreen> = {
  title: "Form/RecapScreen",
  component: RecapScreen,
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => {
      const methods = useForm({
        defaultValues: {
          firstName: "<PERSON>",
          lastName: "<PERSON><PERSON>",
          email: "<EMAIL>",
          phone: "01 23 45 67 89",
          address: {
            street1: "123 Rue de la Paix",
            city: "Paris",
            zip: "75001",
            country: "FR",
          },
          preferences: ["newsletter", "sms"],
          subscribe: true,
          comments:
            "Ceci est un commentaire de test pour montrer comment les données sont affichées dans le récapitulatif.",
        },
      });

      return (
        <FormProvider {...methods}>
          <div style={{ width: "600px", maxWidth: "100%" }}>
            <Story />
          </div>
        </FormProvider>
      );
    },
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof meta>;

const mockSteps: FormStep[] = [
  {
    title: "Informations personnelles",
    stepNumber: 0,
    fields: [
      {
        __typename: "TextField",
        name: "firstName",
        label: "Prénom",
        autocomplete: null,
        columnSpan: 6,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: true,
        showLabel: true,
        size: null,
        type: "text",
        validationMessage: null,
      },
      {
        __typename: "TextField",
        name: "lastName",
        label: "Nom",
        autocomplete: null,
        columnSpan: 6,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: true,
        showLabel: true,
        size: null,
        type: "text",
        validationMessage: null,
      },
      {
        __typename: "EmailField",
        name: "email",
        label: "Email",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: true,
        showLabel: true,
        size: null,
        type: "email",
        validationMessage: null,
        confirmation: null,
      },
      {
        __typename: "PhoneField",
        name: "phone",
        label: "Téléphone",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: false,
        showLabel: true,
        size: null,
        type: "phone",
        validationMessage: null,
      },
    ],
    prev: null,
    next: null,
    condition: null,
    description: null,
  },
  {
    title: "Adresse et contact",
    stepNumber: 1,
    fields: [
      {
        __typename: "TextAreaField",
        name: "address",
        label: "Adresse",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: true,
        showLabel: true,
        size: null,
        type: "textarea",
        validationMessage: null,
        wysiwyg: false,
      },
    ],
    prev: null,
    next: null,
    condition: null,
    description: null,
  },
  {
    title: "Préférences",
    stepNumber: 2,
    fields: [
      {
        __typename: "CheckboxField",
        name: "preferences",
        label: "Préférences de communication",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: false,
        showLabel: true,
        size: null,
        type: "checkbox",
        validationMessage: null,
        choices: [
          { value: "newsletter", label: "Newsletter", defaultSelected: false, icon: null },
          { value: "sms", label: "SMS", defaultSelected: false, icon: null },
        ],
      },
      {
        __typename: "CheckboxField",
        name: "subscribe",
        label: "S'abonner à la newsletter",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: false,
        showLabel: true,
        size: null,
        type: "checkbox",
        validationMessage: null,
        choices: [{ value: "yes", label: "Oui", defaultSelected: false, icon: null }],
      },
      {
        __typename: "TextAreaField",
        name: "comments",
        label: "Commentaires",
        autocomplete: null,
        columnSpan: 12,
        condition: null,
        cssClass: null,
        defaultValue: null,
        description: null,
        descriptionPlacement: null,
        hidden: false,
        pattern: null,
        placeholder: null,
        required: false,
        showLabel: true,
        size: null,
        type: "textarea",
        validationMessage: null,
        wysiwyg: false,
      },
    ],
    prev: null,
    next: null,
    condition: null,
    description: null,
  },
];

export const Default: Story = {
  args: {
    steps: mockSteps,
  },
};

export const Submitting: Story = {
  args: {
    steps: mockSteps,
  },
};

export const CustomSubmitLabel: Story = {
  args: {
    steps: mockSteps,
  },
};

export const EmptyForm: Story = {
  args: {
    steps: mockSteps,
  },
  decorators: [
    (Story) => {
      const methods = useForm({
        defaultValues: {}, // Empty form
      });

      return (
        <FormProvider {...methods}>
          <div style={{ width: "600px", maxWidth: "100%" }}>
            <Story />
          </div>
        </FormProvider>
      );
    },
  ],
};
