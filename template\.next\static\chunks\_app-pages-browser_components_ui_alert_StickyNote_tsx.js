/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_components_ui_alert_StickyNote_tsx"],{

/***/ "(app-pages-browser)/../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs":
/*!************************************************************************!*\
  !*** ../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vY2xzeEAyLjEuMS9ub2RlX21vZHVsZXMvY2xzeC9kaXN0L2Nsc3gubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsY0FBYyxhQUFhLCtDQUErQyxnREFBZ0QsZUFBZSxRQUFRLElBQUksMENBQTBDLHlDQUF5QyxTQUFnQixnQkFBZ0Isd0NBQXdDLElBQUksbURBQW1ELFNBQVMsaUVBQWUsSUFBSSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxjbHN4QDIuMS4xXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js":
/*!*****************************************************************************************!*\
  !*** ../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js ***!
  \*****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as the `TypeError` message for \"Functions\" methods. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/** Used to match leading and trailing whitespace. */\nvar reTrim = /^\\s+|\\s+$/g;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof __webpack_require__.g == 'object' && __webpack_require__.g && __webpack_require__.g.Object === Object && __webpack_require__.g;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root = freeGlobal || freeSelf || Function('return this')();\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now = function() {\n  return root.Date.now();\n};\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        result = wait - timeSinceLastCall;\n\n    return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && objectToString.call(value) == symbolTag);\n}\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = value.replace(reTrim, '');\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nmodule.exports = debounce;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/app-dir/link.js":
/*!****************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/app-dir/link.js ***!
  \****************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */ default: function() {\n        return LinkComponent;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/index.js\"));\nconst _formaturl = __webpack_require__(/*! ../../shared/lib/router/utils/format-url */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ../components/router-reducer/router-reducer-types */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _usemergedref = __webpack_require__(/*! ../use-merged-ref */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/use-merged-ref.js\");\nconst _utils = __webpack_require__(/*! ../../shared/lib/utils */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/add-base-path.js\");\nconst _warnonce = __webpack_require__(/*! ../../shared/lib/utils/warn-once */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _links = __webpack_require__(/*! ../components/links */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/components/links.js\");\nconst _islocalurl = __webpack_require__(/*! ../../shared/lib/router/utils/is-local-url */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ../components/app-router-instance */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/components/app-router-instance.js\");\nconst _erroronce = __webpack_require__(/*! ../../shared/lib/utils/error-once */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils/error-once.js\");\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        (0, _approuterinstance.dispatchNavigateAction)(as || href, replace ? 'replace' : 'push', scroll != null ? scroll : true, linkInstanceRef.current);\n    };\n    _react.default.startTransition(navigate);\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\nfunction LinkComponent(props) {\n    _s();\n    const [linkStatus, setOptimisticLinkStatus] = (0, _react.useOptimistic)(_links.IDLE_LINK_STATUS);\n    let children;\n    const linkInstanceRef = (0, _react.useRef)(null);\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, onNavigate, ref: forwardedRef, unstable_dynamicOnHover, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            unstable_dynamicOnHover: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior' || key === 'unstable_dynamicOnHover') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    if (true) {\n        if (props.locale) {\n            (0, _warnonce.warnOnce)('The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization');\n        }\n        if (!asProp) {\n            let href;\n            if (typeof hrefProp === 'string') {\n                href = hrefProp;\n            } else if (typeof hrefProp === 'object' && typeof hrefProp.pathname === 'string') {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split('/').some((segment)=>segment.startsWith('[') && segment.endsWith(']'));\n                if (hasDynamicSegment) {\n                    throw Object.defineProperty(new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E267\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo({\n        \"LinkComponent.useMemo\": ()=>{\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n    }[\"LinkComponent.useMemo\"], [\n        hrefProp,\n        asProp\n    ]);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n    // mount. In the future we will also use this to keep track of all the\n    // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n    // a revalidation or refresh.\n    const observeLinkVisibilityOnMount = _react.default.useCallback({\n        \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": (element)=>{\n            if (router !== null) {\n                linkInstanceRef.current = (0, _links.mountLinkInstance)(element, href, router, appPrefetchKind, prefetchEnabled, setOptimisticLinkStatus);\n            }\n            return ({\n                \"LinkComponent.useCallback[observeLinkVisibilityOnMount]\": ()=>{\n                    if (linkInstanceRef.current) {\n                        (0, _links.unmountLinkForCurrentNavigation)(linkInstanceRef.current);\n                        linkInstanceRef.current = null;\n                    }\n                    (0, _links.unmountPrefetchableInstance)(element);\n                }\n            })[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"];\n        }\n    }[\"LinkComponent.useCallback[observeLinkVisibilityOnMount]\"], [\n        prefetchEnabled,\n        href,\n        router,\n        appPrefetchKind,\n        setOptimisticLinkStatus\n    ]);\n    const mergedRef = (0, _usemergedref.useMergedRef)(observeLinkVisibilityOnMount, childRef);\n    const childProps = {\n        ref: mergedRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled || \"development\" === 'development') {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled) {\n                return;\n            }\n            const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true;\n            (0, _links.onNavigationIntent)(e.currentTarget, upgradeToDynamicPrefetch);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the basePath.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        childProps.href = (0, _addbasepath.addBasePath)(as);\n    }\n    let link;\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        link = /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    } else {\n        link = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            ...restProps,\n            ...childProps,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(LinkStatusContext.Provider, {\n        value: linkStatus,\n        children: link\n    });\n}\n_s(LinkComponent, \"MNV6IdWv8Lu3MKc7Fm4v59uGRY0=\");\n_c = LinkComponent;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)(_links.IDLE_LINK_STATUS);\nconst useLinkStatus = ()=>{\n    return (0, _react.useContext)(LinkStatusContext);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c;\n$RefreshReg$(_c, \"LinkComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/app-dir/link.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/use-merged-ref.js":
/*!******************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/use-merged-ref.js ***!
  \******************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/format-url.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/format-url.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    formatUrl: function() {\n        return formatUrl;\n    },\n    formatWithValidation: function() {\n        return formatWithValidation;\n    },\n    urlObjectKeys: function() {\n        return urlObjectKeys;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/../node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _querystring = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./querystring */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/querystring.js\"));\nconst slashedProtocols = /https?|ftp|gopher|file/;\nfunction formatUrl(urlObj) {\n    let { auth, hostname } = urlObj;\n    let protocol = urlObj.protocol || '';\n    let pathname = urlObj.pathname || '';\n    let hash = urlObj.hash || '';\n    let query = urlObj.query || '';\n    let host = false;\n    auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : '';\n    if (urlObj.host) {\n        host = auth + urlObj.host;\n    } else if (hostname) {\n        host = auth + (~hostname.indexOf(':') ? \"[\" + hostname + \"]\" : hostname);\n        if (urlObj.port) {\n            host += ':' + urlObj.port;\n        }\n    }\n    if (query && typeof query === 'object') {\n        query = String(_querystring.urlQueryToSearchParams(query));\n    }\n    let search = urlObj.search || query && \"?\" + query || '';\n    if (protocol && !protocol.endsWith(':')) protocol += ':';\n    if (urlObj.slashes || (!protocol || slashedProtocols.test(protocol)) && host !== false) {\n        host = '//' + (host || '');\n        if (pathname && pathname[0] !== '/') pathname = '/' + pathname;\n    } else if (!host) {\n        host = '';\n    }\n    if (hash && hash[0] !== '#') hash = '#' + hash;\n    if (search && search[0] !== '?') search = '?' + search;\n    pathname = pathname.replace(/[?#]/g, encodeURIComponent);\n    search = search.replace('#', '%23');\n    return \"\" + protocol + host + pathname + search + hash;\n}\nconst urlObjectKeys = [\n    'auth',\n    'hash',\n    'host',\n    'hostname',\n    'href',\n    'path',\n    'pathname',\n    'port',\n    'protocol',\n    'query',\n    'search',\n    'slashes'\n];\nfunction formatWithValidation(url) {\n    if (true) {\n        if (url !== null && typeof url === 'object') {\n            Object.keys(url).forEach((key)=>{\n                if (!urlObjectKeys.includes(key)) {\n                    console.warn(\"Unknown key passed via urlObject into url.format: \" + key);\n                }\n            });\n        }\n    }\n    return formatUrl(url);\n} //# sourceMappingURL=format-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/format-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/is-local-url.js":
/*!*********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/is-local-url.js ***!
  \*********************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"isLocalURL\", ({\n    enumerable: true,\n    get: function() {\n        return isLocalURL;\n    }\n}));\nconst _utils = __webpack_require__(/*! ../../utils */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils.js\");\nconst _hasbasepath = __webpack_require__(/*! ../../../../client/has-base-path */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/has-base-path.js\");\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils.isAbsoluteUrl)(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils.getLocationOrigin)();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasbasepath.hasBasePath)(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n} //# sourceMappingURL=is-local-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjRfQGJhYmVsK2NvcmVANy4yX2ZjZThkYWUyODgwODBlZDE2ZGZjNzM5NmY3YzhjMWRjL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9yb3V0ZXIvdXRpbHMvaXMtbG9jYWwtdXJsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OENBTWdCQTs7O2VBQUFBOzs7bUNBTmlDO3lDQUNyQjtBQUtyQixTQUFTQSxXQUFXQyxHQUFXO0lBQ3BDLGdFQUFnRTtJQUNoRSxJQUFJLENBQUNDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQWNELE1BQU0sT0FBTztJQUNoQyxJQUFJO1FBQ0YsNERBQTREO1FBQzVELE1BQU1FLGlCQUFpQkMsQ0FBQUEsR0FBQUEsT0FBQUEsaUJBQUFBO1FBQ3ZCLE1BQU1DLFdBQVcsSUFBSUMsSUFBSUwsS0FBS0U7UUFDOUIsT0FBT0UsU0FBU0UsTUFBTSxLQUFLSixrQkFBa0JLLENBQUFBLEdBQUFBLGFBQUFBLFdBQUFBLEVBQVlILFNBQVNJLFFBQVE7SUFDNUUsRUFBRSxPQUFPQyxHQUFHO1FBQ1YsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxzcmNcXHNoYXJlZFxcbGliXFxyb3V0ZXJcXHV0aWxzXFxpcy1sb2NhbC11cmwudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgaXNBYnNvbHV0ZVVybCwgZ2V0TG9jYXRpb25PcmlnaW4gfSBmcm9tICcuLi8uLi91dGlscydcbmltcG9ydCB7IGhhc0Jhc2VQYXRoIH0gZnJvbSAnLi4vLi4vLi4vLi4vY2xpZW50L2hhcy1iYXNlLXBhdGgnXG5cbi8qKlxuICogRGV0ZWN0cyB3aGV0aGVyIGEgZ2l2ZW4gdXJsIGlzIHJvdXRhYmxlIGJ5IHRoZSBOZXh0LmpzIHJvdXRlciAoYnJvd3NlciBvbmx5KS5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzTG9jYWxVUkwodXJsOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgLy8gcHJldmVudCBhIGh5ZHJhdGlvbiBtaXNtYXRjaCBvbiBocmVmIGZvciB1cmwgd2l0aCBhbmNob3IgcmVmc1xuICBpZiAoIWlzQWJzb2x1dGVVcmwodXJsKSkgcmV0dXJuIHRydWVcbiAgdHJ5IHtcbiAgICAvLyBhYnNvbHV0ZSB1cmxzIGNhbiBiZSBsb2NhbCBpZiB0aGV5IGFyZSBvbiB0aGUgc2FtZSBvcmlnaW5cbiAgICBjb25zdCBsb2NhdGlvbk9yaWdpbiA9IGdldExvY2F0aW9uT3JpZ2luKClcbiAgICBjb25zdCByZXNvbHZlZCA9IG5ldyBVUkwodXJsLCBsb2NhdGlvbk9yaWdpbilcbiAgICByZXR1cm4gcmVzb2x2ZWQub3JpZ2luID09PSBsb2NhdGlvbk9yaWdpbiAmJiBoYXNCYXNlUGF0aChyZXNvbHZlZC5wYXRobmFtZSlcbiAgfSBjYXRjaCAoXykge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG4iXSwibmFtZXMiOlsiaXNMb2NhbFVSTCIsInVybCIsImlzQWJzb2x1dGVVcmwiLCJsb2NhdGlvbk9yaWdpbiIsImdldExvY2F0aW9uT3JpZ2luIiwicmVzb2x2ZWQiLCJVUkwiLCJvcmlnaW4iLCJoYXNCYXNlUGF0aCIsInBhdGhuYW1lIiwiXyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/is-local-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/querystring.js":
/*!********************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/querystring.js ***!
  \********************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    assign: function() {\n        return assign;\n    },\n    searchParamsToUrlQuery: function() {\n        return searchParamsToUrlQuery;\n    },\n    urlQueryToSearchParams: function() {\n        return urlQueryToSearchParams;\n    }\n});\nfunction searchParamsToUrlQuery(searchParams) {\n    const query = {};\n    for (const [key, value] of searchParams.entries()){\n        const existing = query[key];\n        if (typeof existing === 'undefined') {\n            query[key] = value;\n        } else if (Array.isArray(existing)) {\n            existing.push(value);\n        } else {\n            query[key] = [\n                existing,\n                value\n            ];\n        }\n    }\n    return query;\n}\nfunction stringifyUrlQueryParam(param) {\n    if (typeof param === 'string') {\n        return param;\n    }\n    if (typeof param === 'number' && !isNaN(param) || typeof param === 'boolean') {\n        return String(param);\n    } else {\n        return '';\n    }\n}\nfunction urlQueryToSearchParams(query) {\n    const searchParams = new URLSearchParams();\n    for (const [key, value] of Object.entries(query)){\n        if (Array.isArray(value)) {\n            for (const item of value){\n                searchParams.append(key, stringifyUrlQueryParam(item));\n            }\n        } else {\n            searchParams.set(key, stringifyUrlQueryParam(value));\n        }\n    }\n    return searchParams;\n}\nfunction assign(target) {\n    for(var _len = arguments.length, searchParamsList = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){\n        searchParamsList[_key - 1] = arguments[_key];\n    }\n    for (const searchParams of searchParamsList){\n        for (const key of searchParams.keys()){\n            target.delete(key);\n        }\n        for (const [key, value] of searchParams.entries()){\n            target.append(key, value);\n        }\n    }\n    return target;\n} //# sourceMappingURL=querystring.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/router/utils/querystring.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils.js":
/*!*************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils.js ***!
  \*************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DecodeError: function() {\n        return DecodeError;\n    },\n    MiddlewareNotFoundError: function() {\n        return MiddlewareNotFoundError;\n    },\n    MissingStaticPage: function() {\n        return MissingStaticPage;\n    },\n    NormalizeError: function() {\n        return NormalizeError;\n    },\n    PageNotFoundError: function() {\n        return PageNotFoundError;\n    },\n    SP: function() {\n        return SP;\n    },\n    ST: function() {\n        return ST;\n    },\n    WEB_VITALS: function() {\n        return WEB_VITALS;\n    },\n    execOnce: function() {\n        return execOnce;\n    },\n    getDisplayName: function() {\n        return getDisplayName;\n    },\n    getLocationOrigin: function() {\n        return getLocationOrigin;\n    },\n    getURL: function() {\n        return getURL;\n    },\n    isAbsoluteUrl: function() {\n        return isAbsoluteUrl;\n    },\n    isResSent: function() {\n        return isResSent;\n    },\n    loadGetInitialProps: function() {\n        return loadGetInitialProps;\n    },\n    normalizeRepeatedSlashes: function() {\n        return normalizeRepeatedSlashes;\n    },\n    stringifyError: function() {\n        return stringifyError;\n    }\n});\nconst WEB_VITALS = [\n    'CLS',\n    'FCP',\n    'FID',\n    'INP',\n    'LCP',\n    'TTFB'\n];\nfunction execOnce(fn) {\n    let used = false;\n    let result;\n    return function() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        if (!used) {\n            used = true;\n            result = fn(...args);\n        }\n        return result;\n    };\n}\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/;\nconst isAbsoluteUrl = (url)=>ABSOLUTE_URL_REGEX.test(url);\nfunction getLocationOrigin() {\n    const { protocol, hostname, port } = window.location;\n    return protocol + \"//\" + hostname + (port ? ':' + port : '');\n}\nfunction getURL() {\n    const { href } = window.location;\n    const origin = getLocationOrigin();\n    return href.substring(origin.length);\n}\nfunction getDisplayName(Component) {\n    return typeof Component === 'string' ? Component : Component.displayName || Component.name || 'Unknown';\n}\nfunction isResSent(res) {\n    return res.finished || res.headersSent;\n}\nfunction normalizeRepeatedSlashes(url) {\n    const urlParts = url.split('?');\n    const urlNoQuery = urlParts[0];\n    return urlNoQuery // first we replace any non-encoded backslashes with forward\n    // then normalize repeated forward slashes\n    .replace(/\\\\/g, '/').replace(/\\/\\/+/g, '/') + (urlParts[1] ? \"?\" + urlParts.slice(1).join('?') : '');\n}\nasync function loadGetInitialProps(App, ctx) {\n    if (true) {\n        var _App_prototype;\n        if ((_App_prototype = App.prototype) == null ? void 0 : _App_prototype.getInitialProps) {\n            const message = '\"' + getDisplayName(App) + '.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.';\n            throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n                value: \"E394\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n    }\n    // when called from _app `ctx` is nested in `ctx`\n    const res = ctx.res || ctx.ctx && ctx.ctx.res;\n    if (!App.getInitialProps) {\n        if (ctx.ctx && ctx.Component) {\n            // @ts-ignore pageProps default\n            return {\n                pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx)\n            };\n        }\n        return {};\n    }\n    const props = await App.getInitialProps(ctx);\n    if (res && isResSent(res)) {\n        return props;\n    }\n    if (!props) {\n        const message = '\"' + getDisplayName(App) + '.getInitialProps()\" should resolve to an object. But found \"' + props + '\" instead.';\n        throw Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    if (true) {\n        if (Object.keys(props).length === 0 && !ctx.ctx) {\n            console.warn(\"\" + getDisplayName(App) + \" returned an empty object from `getInitialProps`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps\");\n        }\n    }\n    return props;\n}\nconst SP = typeof performance !== 'undefined';\nconst ST = SP && [\n    'mark',\n    'measure',\n    'getEntriesByName'\n].every((method)=>typeof performance[method] === 'function');\nclass DecodeError extends Error {\n}\nclass NormalizeError extends Error {\n}\nclass PageNotFoundError extends Error {\n    constructor(page){\n        super();\n        this.code = 'ENOENT';\n        this.name = 'PageNotFoundError';\n        this.message = \"Cannot find module for page: \" + page;\n    }\n}\nclass MissingStaticPage extends Error {\n    constructor(page, message){\n        super();\n        this.message = \"Failed to load static file for page: \" + page + \" \" + message;\n    }\n}\nclass MiddlewareNotFoundError extends Error {\n    constructor(){\n        super();\n        this.code = 'ENOENT';\n        this.message = \"Cannot find the middleware module\";\n    }\n}\nfunction stringifyError(error) {\n    return JSON.stringify({\n        message: error.message,\n        stack: error.stack\n    });\n} //# sourceMappingURL=utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils/error-once.js":
/*!************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \************************************************************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4zLjRfQGJhYmVsK2NvcmVANy4yX2ZjZThkYWUyODgwODBlZDE2ZGZjNzM5NmY3YzhjMWRjL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc3JjXFxzaGFyZWRcXGxpYlxcdXRpbHNcXGVycm9yLW9uY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVycm9yT25jZSA9IChfOiBzdHJpbmcpID0+IHt9XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBjb25zdCBlcnJvcnMgPSBuZXcgU2V0PHN0cmluZz4oKVxuICBlcnJvck9uY2UgPSAobXNnOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWVycm9ycy5oYXMobXNnKSkge1xuICAgICAgY29uc29sZS5lcnJvcihtc2cpXG4gICAgfVxuICAgIGVycm9ycy5hZGQobXNnKVxuICB9XG59XG5cbmV4cG9ydCB7IGVycm9yT25jZSB9XG4iXSwibmFtZXMiOlsiZXJyb3JPbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImVycm9ycyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJlcnJvciIsImFkZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js":
/*!***************************************************************************************************!*\
  !*** ../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBoolean: () => (/* binding */ useBoolean),\n/* harmony export */   useClickAnyWhere: () => (/* binding */ useClickAnyWhere),\n/* harmony export */   useCopyToClipboard: () => (/* binding */ useCopyToClipboard),\n/* harmony export */   useCountdown: () => (/* binding */ useCountdown),\n/* harmony export */   useCounter: () => (/* binding */ useCounter),\n/* harmony export */   useDarkMode: () => (/* binding */ useDarkMode),\n/* harmony export */   useDebounceCallback: () => (/* binding */ useDebounceCallback),\n/* harmony export */   useDebounceValue: () => (/* binding */ useDebounceValue),\n/* harmony export */   useDocumentTitle: () => (/* binding */ useDocumentTitle),\n/* harmony export */   useEventCallback: () => (/* binding */ useEventCallback),\n/* harmony export */   useEventListener: () => (/* binding */ useEventListener),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsClient: () => (/* binding */ useIsClient),\n/* harmony export */   useIsMounted: () => (/* binding */ useIsMounted),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useMap: () => (/* binding */ useMap),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery),\n/* harmony export */   useOnClickOutside: () => (/* binding */ useOnClickOutside),\n/* harmony export */   useReadLocalStorage: () => (/* binding */ useReadLocalStorage),\n/* harmony export */   useResizeObserver: () => (/* binding */ useResizeObserver),\n/* harmony export */   useScreen: () => (/* binding */ useScreen),\n/* harmony export */   useScript: () => (/* binding */ useScript),\n/* harmony export */   useScrollLock: () => (/* binding */ useScrollLock),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useStep: () => (/* binding */ useStep),\n/* harmony export */   useTernaryDarkMode: () => (/* binding */ useTernaryDarkMode),\n/* harmony export */   useTimeout: () => (/* binding */ useTimeout),\n/* harmony export */   useToggle: () => (/* binding */ useToggle),\n/* harmony export */   useUnmount: () => (/* binding */ useUnmount),\n/* harmony export */   useWindowSize: () => (/* binding */ useWindowSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash.debounce */ \"(app-pages-browser)/../node_modules/.pnpm/lodash.debounce@4.0.8/node_modules/lodash.debounce/index.js\");\n\n\n\n// src/useBoolean/useBoolean.ts\nfunction useBoolean(defaultValue = false) {\n  if (typeof defaultValue !== \"boolean\") {\n    throw new Error(\"defaultValue must be `true` or `false`\");\n  }\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultValue);\n  const setTrue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue(true);\n  }, []);\n  const setFalse = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue(false);\n  }, []);\n  const toggle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue((x) => !x);\n  }, []);\n  return { value, setValue, setTrue, setFalse, toggle };\n}\nvar useIsomorphicLayoutEffect = typeof window !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n// src/useEventListener/useEventListener.ts\nfunction useEventListener(eventName, handler, element, options) {\n  const savedHandler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    savedHandler.current = handler;\n  }, [handler]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const targetElement = (element == null ? void 0 : element.current) ?? window;\n    if (!(targetElement && targetElement.addEventListener))\n      return;\n    const listener = (event) => {\n      savedHandler.current(event);\n    };\n    targetElement.addEventListener(eventName, listener, options);\n    return () => {\n      targetElement.removeEventListener(eventName, listener, options);\n    };\n  }, [eventName, element, options]);\n}\n\n// src/useClickAnyWhere/useClickAnyWhere.ts\nfunction useClickAnyWhere(handler) {\n  useEventListener(\"click\", (event) => {\n    handler(event);\n  });\n}\nfunction useCopyToClipboard() {\n  const [copiedText, setCopiedText] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const copy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (text) => {\n    if (!(navigator == null ? void 0 : navigator.clipboard)) {\n      console.warn(\"Clipboard not supported\");\n      return false;\n    }\n    try {\n      await navigator.clipboard.writeText(text);\n      setCopiedText(text);\n      return true;\n    } catch (error) {\n      console.warn(\"Copy failed\", error);\n      setCopiedText(null);\n      return false;\n    }\n  }, []);\n  return [copiedText, copy];\n}\nfunction useCounter(initialValue) {\n  const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue ?? 0);\n  const increment = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount((x) => x + 1);\n  }, []);\n  const decrement = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount((x) => x - 1);\n  }, []);\n  const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCount(initialValue ?? 0);\n  }, [initialValue]);\n  return {\n    count,\n    increment,\n    decrement,\n    reset,\n    setCount\n  };\n}\nfunction useInterval(callback, delay) {\n  const savedCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (delay === null) {\n      return;\n    }\n    const id = setInterval(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearInterval(id);\n    };\n  }, [delay]);\n}\n\n// src/useCountdown/useCountdown.ts\nfunction useCountdown({\n  countStart,\n  countStop = 0,\n  intervalMs = 1e3,\n  isIncrement = false\n}) {\n  const {\n    count,\n    increment,\n    decrement,\n    reset: resetCounter\n  } = useCounter(countStart);\n  const {\n    value: isCountdownRunning,\n    setTrue: startCountdown,\n    setFalse: stopCountdown\n  } = useBoolean(false);\n  const resetCountdown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    stopCountdown();\n    resetCounter();\n  }, [stopCountdown, resetCounter]);\n  const countdownCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (count === countStop) {\n      stopCountdown();\n      return;\n    }\n    if (isIncrement) {\n      increment();\n    } else {\n      decrement();\n    }\n  }, [count, countStop, decrement, increment, isIncrement, stopCountdown]);\n  useInterval(countdownCallback, isCountdownRunning ? intervalMs : null);\n  return [count, { startCountdown, stopCountdown, resetCountdown }];\n}\nfunction useEventCallback(fn) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  useIsomorphicLayoutEffect(() => {\n    ref.current = fn;\n  }, [fn]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args) => {\n    var _a;\n    return (_a = ref.current) == null ? void 0 : _a.call(ref, ...args);\n  }, [ref]);\n}\n\n// src/useLocalStorage/useLocalStorage.ts\nvar IS_SERVER = typeof window === \"undefined\";\nfunction useLocalStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried setting localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.localStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting localStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER) {\n      console.warn(\n        `Tried removing localStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.localStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"local-storage\", { key }));\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nvar IS_SERVER2 = typeof window === \"undefined\";\nfunction useMediaQuery(query, {\n  defaultValue = false,\n  initializeWithValue = true\n} = {}) {\n  const getMatches = (query2) => {\n    if (IS_SERVER2) {\n      return defaultValue;\n    }\n    return window.matchMedia(query2).matches;\n  };\n  const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return getMatches(query);\n    }\n    return defaultValue;\n  });\n  function handleChange() {\n    setMatches(getMatches(query));\n  }\n  useIsomorphicLayoutEffect(() => {\n    const matchMedia = window.matchMedia(query);\n    handleChange();\n    if (matchMedia.addListener) {\n      matchMedia.addListener(handleChange);\n    } else {\n      matchMedia.addEventListener(\"change\", handleChange);\n    }\n    return () => {\n      if (matchMedia.removeListener) {\n        matchMedia.removeListener(handleChange);\n      } else {\n        matchMedia.removeEventListener(\"change\", handleChange);\n      }\n    };\n  }, [query]);\n  return matches;\n}\n\n// src/useDarkMode/useDarkMode.ts\nvar COLOR_SCHEME_QUERY = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY = \"usehooks-ts-dark-mode\";\nfunction useDarkMode(options = {}) {\n  const {\n    defaultValue,\n    localStorageKey = LOCAL_STORAGE_KEY,\n    initializeWithValue = true\n  } = options;\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY, {\n    initializeWithValue,\n    defaultValue\n  });\n  const [isDarkMode, setDarkMode] = useLocalStorage(\n    localStorageKey,\n    defaultValue ?? isDarkOS ?? false,\n    { initializeWithValue }\n  );\n  useIsomorphicLayoutEffect(() => {\n    if (isDarkOS !== isDarkMode) {\n      setDarkMode(isDarkOS);\n    }\n  }, [isDarkOS]);\n  return {\n    isDarkMode,\n    toggle: () => {\n      setDarkMode((prev) => !prev);\n    },\n    enable: () => {\n      setDarkMode(true);\n    },\n    disable: () => {\n      setDarkMode(false);\n    },\n    set: (value) => {\n      setDarkMode(value);\n    }\n  };\n}\nfunction useUnmount(func) {\n  const funcRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(func);\n  funcRef.current = func;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(\n    () => () => {\n      funcRef.current();\n    },\n    []\n  );\n}\n\n// src/useDebounceCallback/useDebounceCallback.ts\nfunction useDebounceCallback(func, delay = 500, options) {\n  const debouncedFunc = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  useUnmount(() => {\n    if (debouncedFunc.current) {\n      debouncedFunc.current.cancel();\n    }\n  });\n  const debounced = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const debouncedFuncInstance = lodash_debounce__WEBPACK_IMPORTED_MODULE_1__(func, delay, options);\n    const wrappedFunc = (...args) => {\n      return debouncedFuncInstance(...args);\n    };\n    wrappedFunc.cancel = () => {\n      debouncedFuncInstance.cancel();\n    };\n    wrappedFunc.isPending = () => {\n      return !!debouncedFunc.current;\n    };\n    wrappedFunc.flush = () => {\n      return debouncedFuncInstance.flush();\n    };\n    return wrappedFunc;\n  }, [func, delay, options]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    debouncedFunc.current = lodash_debounce__WEBPACK_IMPORTED_MODULE_1__(func, delay, options);\n  }, [func, delay, options]);\n  return debounced;\n}\nfunction useDebounceValue(initialValue, delay, options) {\n  const eq = (options == null ? void 0 : options.equalityFn) ?? ((left, right) => left === right);\n  const unwrappedInitialValue = initialValue instanceof Function ? initialValue() : initialValue;\n  const [debouncedValue, setDebouncedValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(unwrappedInitialValue);\n  const previousValueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(unwrappedInitialValue);\n  const updateDebouncedValue = useDebounceCallback(\n    setDebouncedValue,\n    delay,\n    options\n  );\n  if (!eq(previousValueRef.current, unwrappedInitialValue)) {\n    updateDebouncedValue(unwrappedInitialValue);\n    previousValueRef.current = unwrappedInitialValue;\n  }\n  return [debouncedValue, updateDebouncedValue];\n}\nfunction useDocumentTitle(title, options = {}) {\n  const { preserveTitleOnUnmount = true } = options;\n  const defaultTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  useIsomorphicLayoutEffect(() => {\n    defaultTitle.current = window.document.title;\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    window.document.title = title;\n  }, [title]);\n  useUnmount(() => {\n    if (!preserveTitleOnUnmount && defaultTitle.current) {\n      window.document.title = defaultTitle.current;\n    }\n  });\n}\nfunction useHover(elementRef) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const handleMouseEnter = () => {\n    setValue(true);\n  };\n  const handleMouseLeave = () => {\n    setValue(false);\n  };\n  useEventListener(\"mouseenter\", handleMouseEnter, elementRef);\n  useEventListener(\"mouseleave\", handleMouseLeave, elementRef);\n  return value;\n}\nfunction useIntersectionObserver({\n  threshold = 0,\n  root = null,\n  rootMargin = \"0%\",\n  freezeOnceVisible = false,\n  initialIsIntersecting = false,\n  onChange\n} = {}) {\n  var _a;\n  const [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ({\n    isIntersecting: initialIsIntersecting,\n    entry: void 0\n  }));\n  const callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  callbackRef.current = onChange;\n  const frozen = ((_a = state.entry) == null ? void 0 : _a.isIntersecting) && freezeOnceVisible;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!ref)\n      return;\n    if (!(\"IntersectionObserver\" in window))\n      return;\n    if (frozen)\n      return;\n    let unobserve;\n    const observer = new IntersectionObserver(\n      (entries) => {\n        const thresholds = Array.isArray(observer.thresholds) ? observer.thresholds : [observer.thresholds];\n        entries.forEach((entry) => {\n          const isIntersecting = entry.isIntersecting && thresholds.some((threshold2) => entry.intersectionRatio >= threshold2);\n          setState({ isIntersecting, entry });\n          if (callbackRef.current) {\n            callbackRef.current(isIntersecting, entry);\n          }\n          if (isIntersecting && freezeOnceVisible && unobserve) {\n            unobserve();\n            unobserve = void 0;\n          }\n        });\n      },\n      { threshold, root, rootMargin }\n    );\n    observer.observe(ref);\n    return () => {\n      observer.disconnect();\n    };\n  }, [\n    ref,\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    JSON.stringify(threshold),\n    root,\n    rootMargin,\n    frozen,\n    freezeOnceVisible\n  ]);\n  const prevRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    var _a2;\n    if (!ref && ((_a2 = state.entry) == null ? void 0 : _a2.target) && !freezeOnceVisible && !frozen && prevRef.current !== state.entry.target) {\n      prevRef.current = state.entry.target;\n      setState({ isIntersecting: initialIsIntersecting, entry: void 0 });\n    }\n  }, [ref, state.entry, freezeOnceVisible, frozen, initialIsIntersecting]);\n  const result = [\n    setRef,\n    !!state.isIntersecting,\n    state.entry\n  ];\n  result.ref = result[0];\n  result.isIntersecting = result[1];\n  result.entry = result[2];\n  return result;\n}\nfunction useIsClient() {\n  const [isClient, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setClient(true);\n  }, []);\n  return isClient;\n}\nfunction useIsMounted() {\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    isMounted.current = true;\n    return () => {\n      isMounted.current = false;\n    };\n  }, []);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => isMounted.current, []);\n}\nfunction useMap(initialState = /* @__PURE__ */ new Map()) {\n  const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Map(initialState));\n  const actions = {\n    set: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key, value) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.set(key, value);\n        return copy;\n      });\n    }, []),\n    setAll: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((entries) => {\n      setMap(() => new Map(entries));\n    }, []),\n    remove: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key) => {\n      setMap((prev) => {\n        const copy = new Map(prev);\n        copy.delete(key);\n        return copy;\n      });\n    }, []),\n    reset: (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n      setMap(() => /* @__PURE__ */ new Map());\n    }, [])\n  };\n  return [map, actions];\n}\n\n// src/useOnClickOutside/useOnClickOutside.ts\nfunction useOnClickOutside(ref, handler, eventType = \"mousedown\", eventListenerOptions = {}) {\n  useEventListener(\n    eventType,\n    (event) => {\n      const target = event.target;\n      if (!target || !target.isConnected) {\n        return;\n      }\n      const isOutside = Array.isArray(ref) ? ref.filter((r) => Boolean(r.current)).every((r) => r.current && !r.current.contains(target)) : ref.current && !ref.current.contains(target);\n      if (isOutside) {\n        handler(event);\n      }\n    },\n    void 0,\n    eventListenerOptions\n  );\n}\nvar IS_SERVER3 = typeof window === \"undefined\";\nfunction useReadLocalStorage(key, options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER3) {\n    initializeWithValue = false;\n  }\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return null;\n      }\n      return parsed;\n    },\n    [options]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (IS_SERVER3) {\n      return null;\n    }\n    try {\n      const raw = window.localStorage.getItem(key);\n      return raw ? deserializer(raw) : null;\n    } catch (error) {\n      console.warn(`Error reading localStorage key \\u201C${key}\\u201D:`, error);\n      return null;\n    }\n  }, [key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return void 0;\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"local-storage\", handleStorageChange);\n  return storedValue;\n}\nvar initialSize = {\n  width: void 0,\n  height: void 0\n};\nfunction useResizeObserver(options) {\n  const { ref, box = \"content-box\" } = options;\n  const [{ width, height }, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialSize);\n  const isMounted = useIsMounted();\n  const previousSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ ...initialSize });\n  const onResize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(void 0);\n  onResize.current = options.onResize;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!ref.current)\n      return;\n    if (typeof window === \"undefined\" || !(\"ResizeObserver\" in window))\n      return;\n    const observer = new ResizeObserver(([entry]) => {\n      const boxProp = box === \"border-box\" ? \"borderBoxSize\" : box === \"device-pixel-content-box\" ? \"devicePixelContentBoxSize\" : \"contentBoxSize\";\n      const newWidth = extractSize(entry, boxProp, \"inlineSize\");\n      const newHeight = extractSize(entry, boxProp, \"blockSize\");\n      const hasChanged = previousSize.current.width !== newWidth || previousSize.current.height !== newHeight;\n      if (hasChanged) {\n        const newSize = { width: newWidth, height: newHeight };\n        previousSize.current.width = newWidth;\n        previousSize.current.height = newHeight;\n        if (onResize.current) {\n          onResize.current(newSize);\n        } else {\n          if (isMounted()) {\n            setSize(newSize);\n          }\n        }\n      }\n    });\n    observer.observe(ref.current, { box });\n    return () => {\n      observer.disconnect();\n    };\n  }, [box, ref, isMounted]);\n  return { width, height };\n}\nfunction extractSize(entry, box, sizeType) {\n  if (!entry[box]) {\n    if (box === \"contentBoxSize\") {\n      return entry.contentRect[sizeType === \"inlineSize\" ? \"width\" : \"height\"];\n    }\n    return void 0;\n  }\n  return Array.isArray(entry[box]) ? entry[box][0][sizeType] : (\n    // @ts-ignore Support Firefox's non-standard behavior\n    entry[box][sizeType]\n  );\n}\nvar IS_SERVER4 = typeof window === \"undefined\";\nfunction useScreen(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER4) {\n    initializeWithValue = false;\n  }\n  const readScreen = () => {\n    if (IS_SERVER4) {\n      return void 0;\n    }\n    return window.screen;\n  };\n  const [screen, setScreen] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readScreen();\n    }\n    return void 0;\n  });\n  const debouncedSetScreen = useDebounceCallback(\n    setScreen,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const newScreen = readScreen();\n    const setSize = options.debounceDelay ? debouncedSetScreen : setScreen;\n    if (newScreen) {\n      const {\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      } = newScreen;\n      setSize({\n        width,\n        height,\n        availHeight,\n        availWidth,\n        colorDepth,\n        orientation,\n        pixelDepth\n      });\n    }\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return screen;\n}\nvar cachedScriptStatuses = /* @__PURE__ */ new Map();\nfunction getScriptNode(src) {\n  const node = document.querySelector(\n    `script[src=\"${src}\"]`\n  );\n  const status = node == null ? void 0 : node.getAttribute(\"data-status\");\n  return {\n    node,\n    status\n  };\n}\nfunction useScript(src, options) {\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return \"idle\";\n    }\n    if (typeof window === \"undefined\") {\n      return \"loading\";\n    }\n    return cachedScriptStatuses.get(src) ?? \"loading\";\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!src || (options == null ? void 0 : options.shouldPreventLoad)) {\n      return;\n    }\n    const cachedScriptStatus = cachedScriptStatuses.get(src);\n    if (cachedScriptStatus === \"ready\" || cachedScriptStatus === \"error\") {\n      setStatus(cachedScriptStatus);\n      return;\n    }\n    const script = getScriptNode(src);\n    let scriptNode = script.node;\n    if (!scriptNode) {\n      scriptNode = document.createElement(\"script\");\n      scriptNode.src = src;\n      scriptNode.async = true;\n      if (options == null ? void 0 : options.id) {\n        scriptNode.id = options.id;\n      }\n      scriptNode.setAttribute(\"data-status\", \"loading\");\n      document.body.appendChild(scriptNode);\n      const setAttributeFromEvent = (event) => {\n        const scriptStatus = event.type === \"load\" ? \"ready\" : \"error\";\n        scriptNode == null ? void 0 : scriptNode.setAttribute(\"data-status\", scriptStatus);\n      };\n      scriptNode.addEventListener(\"load\", setAttributeFromEvent);\n      scriptNode.addEventListener(\"error\", setAttributeFromEvent);\n    } else {\n      setStatus(script.status ?? cachedScriptStatus ?? \"loading\");\n    }\n    const setStateFromEvent = (event) => {\n      const newStatus = event.type === \"load\" ? \"ready\" : \"error\";\n      setStatus(newStatus);\n      cachedScriptStatuses.set(src, newStatus);\n    };\n    scriptNode.addEventListener(\"load\", setStateFromEvent);\n    scriptNode.addEventListener(\"error\", setStateFromEvent);\n    return () => {\n      if (scriptNode) {\n        scriptNode.removeEventListener(\"load\", setStateFromEvent);\n        scriptNode.removeEventListener(\"error\", setStateFromEvent);\n      }\n      if (scriptNode && (options == null ? void 0 : options.removeOnUnmount)) {\n        scriptNode.remove();\n        cachedScriptStatuses.delete(src);\n      }\n    };\n  }, [src, options == null ? void 0 : options.shouldPreventLoad, options == null ? void 0 : options.removeOnUnmount, options == null ? void 0 : options.id]);\n  return status;\n}\nvar IS_SERVER5 = typeof window === \"undefined\";\nfunction useScrollLock(options = {}) {\n  const { autoLock = true, lockTarget, widthReflow = true } = options;\n  const [isLocked, setIsLocked] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const target = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const originalStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const lock = () => {\n    if (target.current) {\n      const { overflow, paddingRight } = target.current.style;\n      originalStyle.current = { overflow, paddingRight };\n      if (widthReflow) {\n        const offsetWidth = target.current === document.body ? window.innerWidth : target.current.offsetWidth;\n        const currentPaddingRight = parseInt(window.getComputedStyle(target.current).paddingRight, 10) || 0;\n        const scrollbarWidth = offsetWidth - target.current.scrollWidth;\n        target.current.style.paddingRight = `${scrollbarWidth + currentPaddingRight}px`;\n      }\n      target.current.style.overflow = \"hidden\";\n      setIsLocked(true);\n    }\n  };\n  const unlock = () => {\n    if (target.current && originalStyle.current) {\n      target.current.style.overflow = originalStyle.current.overflow;\n      if (widthReflow) {\n        target.current.style.paddingRight = originalStyle.current.paddingRight;\n      }\n    }\n    setIsLocked(false);\n  };\n  useIsomorphicLayoutEffect(() => {\n    if (IS_SERVER5)\n      return;\n    if (lockTarget) {\n      target.current = typeof lockTarget === \"string\" ? document.querySelector(lockTarget) : lockTarget;\n    }\n    if (!target.current) {\n      target.current = document.body;\n    }\n    if (autoLock) {\n      lock();\n    }\n    return () => {\n      unlock();\n    };\n  }, [autoLock, lockTarget, widthReflow]);\n  return { isLocked, lock, unlock };\n}\nvar IS_SERVER6 = typeof window === \"undefined\";\nfunction useSessionStorage(key, initialValue, options = {}) {\n  const { initializeWithValue = true } = options;\n  const serializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    },\n    [options]\n  );\n  const deserializer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (value) => {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      if (value === \"undefined\") {\n        return void 0;\n      }\n      const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n      let parsed;\n      try {\n        parsed = JSON.parse(value);\n      } catch (error) {\n        console.error(\"Error parsing JSON:\", error);\n        return defaultValue;\n      }\n      return parsed;\n    },\n    [options, initialValue]\n  );\n  const readValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    const initialValueToUse = initialValue instanceof Function ? initialValue() : initialValue;\n    if (IS_SERVER6) {\n      return initialValueToUse;\n    }\n    try {\n      const raw = window.sessionStorage.getItem(key);\n      return raw ? deserializer(raw) : initialValueToUse;\n    } catch (error) {\n      console.warn(`Error reading sessionStorage key \\u201C${key}\\u201D:`, error);\n      return initialValueToUse;\n    }\n  }, [initialValue, key, deserializer]);\n  const [storedValue, setStoredValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return readValue();\n    }\n    return initialValue instanceof Function ? initialValue() : initialValue;\n  });\n  const setValue = useEventCallback((value) => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried setting sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    try {\n      const newValue = value instanceof Function ? value(readValue()) : value;\n      window.sessionStorage.setItem(key, serializer(newValue));\n      setStoredValue(newValue);\n      window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n    } catch (error) {\n      console.warn(`Error setting sessionStorage key \\u201C${key}\\u201D:`, error);\n    }\n  });\n  const removeValue = useEventCallback(() => {\n    if (IS_SERVER6) {\n      console.warn(\n        `Tried removing sessionStorage key \\u201C${key}\\u201D even though environment is not a client`\n      );\n    }\n    const defaultValue = initialValue instanceof Function ? initialValue() : initialValue;\n    window.sessionStorage.removeItem(key);\n    setStoredValue(defaultValue);\n    window.dispatchEvent(new StorageEvent(\"session-storage\", { key }));\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    setStoredValue(readValue());\n  }, [key]);\n  const handleStorageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      if (event.key && event.key !== key) {\n        return;\n      }\n      setStoredValue(readValue());\n    },\n    [key, readValue]\n  );\n  useEventListener(\"storage\", handleStorageChange);\n  useEventListener(\"session-storage\", handleStorageChange);\n  return [storedValue, setValue, removeValue];\n}\nfunction useStep(maxStep) {\n  const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const canGoToNextStep = currentStep + 1 <= maxStep;\n  const canGoToPrevStep = currentStep - 1 > 0;\n  const setStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (step) => {\n      const newStep = step instanceof Function ? step(currentStep) : step;\n      if (newStep >= 1 && newStep <= maxStep) {\n        setCurrentStep(newStep);\n        return;\n      }\n      throw new Error(\"Step not valid\");\n    },\n    [maxStep, currentStep]\n  );\n  const goToNextStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (canGoToNextStep) {\n      setCurrentStep((step) => step + 1);\n    }\n  }, [canGoToNextStep]);\n  const goToPrevStep = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (canGoToPrevStep) {\n      setCurrentStep((step) => step - 1);\n    }\n  }, [canGoToPrevStep]);\n  const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setCurrentStep(1);\n  }, []);\n  return [\n    currentStep,\n    {\n      goToNextStep,\n      goToPrevStep,\n      canGoToNextStep,\n      canGoToPrevStep,\n      setStep,\n      reset\n    }\n  ];\n}\n\n// src/useTernaryDarkMode/useTernaryDarkMode.ts\nvar COLOR_SCHEME_QUERY2 = \"(prefers-color-scheme: dark)\";\nvar LOCAL_STORAGE_KEY2 = \"usehooks-ts-ternary-dark-mode\";\nfunction useTernaryDarkMode({\n  defaultValue = \"system\",\n  localStorageKey = LOCAL_STORAGE_KEY2,\n  initializeWithValue = true\n} = {}) {\n  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY2, { initializeWithValue });\n  const [mode, setMode] = useLocalStorage(localStorageKey, defaultValue, {\n    initializeWithValue\n  });\n  const isDarkMode = mode === \"dark\" || mode === \"system\" && isDarkOS;\n  const toggleTernaryDarkMode = () => {\n    const modes = [\"light\", \"system\", \"dark\"];\n    setMode((prevMode) => {\n      const nextIndex = (modes.indexOf(prevMode) + 1) % modes.length;\n      return modes[nextIndex];\n    });\n  };\n  return {\n    isDarkMode,\n    ternaryDarkMode: mode,\n    setTernaryDarkMode: setMode,\n    toggleTernaryDarkMode\n  };\n}\nfunction useTimeout(callback, delay) {\n  const savedCallback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(callback);\n  useIsomorphicLayoutEffect(() => {\n    savedCallback.current = callback;\n  }, [callback]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!delay && delay !== 0) {\n      return;\n    }\n    const id = setTimeout(() => {\n      savedCallback.current();\n    }, delay);\n    return () => {\n      clearTimeout(id);\n    };\n  }, [delay]);\n}\nfunction useToggle(defaultValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!!defaultValue);\n  const toggle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setValue((x) => !x);\n  }, []);\n  return [value, toggle, setValue];\n}\nvar IS_SERVER7 = typeof window === \"undefined\";\nfunction useWindowSize(options = {}) {\n  let { initializeWithValue = true } = options;\n  if (IS_SERVER7) {\n    initializeWithValue = false;\n  }\n  const [windowSize, setWindowSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => {\n    if (initializeWithValue) {\n      return {\n        width: window.innerWidth,\n        height: window.innerHeight\n      };\n    }\n    return {\n      width: void 0,\n      height: void 0\n    };\n  });\n  const debouncedSetWindowSize = useDebounceCallback(\n    setWindowSize,\n    options.debounceDelay\n  );\n  function handleSize() {\n    const setSize = options.debounceDelay ? debouncedSetWindowSize : setWindowSize;\n    setSize({\n      width: window.innerWidth,\n      height: window.innerHeight\n    });\n  }\n  useEventListener(\"resize\", handleSize);\n  useIsomorphicLayoutEffect(() => {\n    handleSize();\n  }, []);\n  return windowSize;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/alert/StickyNote.module.scss":
/*!****************************************************!*\
  !*** ./components/ui/alert/StickyNote.module.scss ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"stickyNote\":\"StickyNote_stickyNote__nJ0xY\",\"container\":\"StickyNote_container__xgoqR\",\"color-primary\":\"StickyNote_color-primary__tUPMI\",\"color-secondary\":\"StickyNote_color-secondary__uAPNv\",\"color-tertiary\":\"StickyNote_color-tertiary__eYvxg\",\"color-danger\":\"StickyNote_color-danger__P6MDR\",\"topIcon\":\"StickyNote_topIcon__piP5B\",\"wrapper\":\"StickyNote_wrapper__6n7M8\",\"content\":\"StickyNote_content__EzB7Q\",\"action\":\"StickyNote_action__IvO1F\",\"header\":\"StickyNote_header__bE37W\",\"title\":\"StickyNote_title__SDREt\",\"description\":\"StickyNote_description__J800i\",\"close\":\"StickyNote_close__x_3S3\"};\n    if(true) {\n      // 1755607374676\n      var cssReload = __webpack_require__(/*! ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  \nmodule.exports.__checksum = \"c42412c88d12\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/alert/StickyNote.module.scss\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/alert/StickyNote.tsx":
/*!********************************************!*\
  !*** ./components/ui/alert/StickyNote.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STICKY_NOTE_CLOSED_KEY: () => (/* binding */ STICKY_NOTE_CLOSED_KEY),\n/* harmony export */   \"default\": () => (/* binding */ StickyNote)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var usehooks_ts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! usehooks-ts */ \"(app-pages-browser)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js\");\n/* harmony import */ var _StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StickyNote.module.scss */ \"(app-pages-browser)/./components/ui/alert/StickyNote.module.scss\");\n/* harmony import */ var _StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ STICKY_NOTE_CLOSED_KEY,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst Button = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_components_ui_button_Button_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/ui/button/Button */ \"(app-pages-browser)/./components/ui/button/Button.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\StickyNote.tsx -> \" + \"@/components/ui/button/Button\"\n        ]\n    }\n});\n_c = Button;\nconst STICKY_NOTE_CLOSED_KEY = \"citeopolis:sticky-note:closed\";\nfunction getStickyNoteKey(param) {\n    let { id, modifiedDate } = param;\n    return \"sticky-note|\".concat(id, \"|\").concat(modifiedDate);\n}\nfunction StickyNote(param) {\n    let { alert: { id, title, description, action, modifiedDate } } = param;\n    _s();\n    const [isClosed, setIsClosed] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [closedStickyNoteKey, setClosedStickyNoteKey] = (0,usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useLocalStorage)(STICKY_NOTE_CLOSED_KEY, \"\");\n    const titleId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const alertKey = getStickyNoteKey({\n        id,\n        modifiedDate\n    });\n    const isClient = (0,usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useIsClient)();\n    if (!isClient || closedStickyNoteKey === alertKey || isClosed) {\n        return null;\n    }\n    const handleCloseAlert = ()=>{\n        setClosedStickyNoteKey(alertKey);\n        setIsClosed(true);\n        const siteWrapper = document.querySelector(\".site-wrapper\");\n        siteWrapper === null || siteWrapper === void 0 ? void 0 : siteWrapper.focus();\n    };\n    const activeColorVariant = \"primary\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().stickyNote),\n        role: \"dialog\",\n        \"aria-labelledby\": titleId,\n        \"aria-live\": \"assertive\",\n        tabIndex: -1,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().container), (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[\"color-\".concat(activeColorVariant)]),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"far fa-triangle-exclamation\", (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().topIcon)),\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().content),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                id: titleId,\n                                className: \"sr-only\",\n                                children: \"Alerte\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().header),\n                                children: [\n                                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 25\n                                    }, this),\n                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().description),\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 31\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            (action === null || action === void 0 ? void 0 : action.url) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().action),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    asChild: true,\n                                    variant: \"outlined\",\n                                    color: \"\".concat(activeColorVariant, \"-inverted\"),\n                                    startIcon: \"far fa-check\",\n                                    size: \"xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: action === null || action === void 0 ? void 0 : action.url,\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().itemLink),\n                                        children: action.text || \"En savoir plus\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().close), (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[\"color-\".concat(activeColorVariant)]),\n                        onClick: handleCloseAlert,\n                        \"aria-label\": \"Fermer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-xmark\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(StickyNote, \"emTB3IHYPZjC5l6usrfciKXLTdA=\", false, function() {\n    return [\n        usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useLocalStorage,\n        react__WEBPACK_IMPORTED_MODULE_4__.useId,\n        usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useIsClient\n    ];\n});\n_c1 = StickyNote;\nvar _c, _c1;\n$RefreshReg$(_c, \"Button\");\n$RefreshReg$(_c1, \"StickyNote\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/alert/StickyNote.tsx\n"));

/***/ })

}]);