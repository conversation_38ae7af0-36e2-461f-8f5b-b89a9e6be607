import citeopolisConfig from "@citeopolis/eslint-config";
import next from "@next/eslint-plugin-next";
import storybook from "eslint-plugin-storybook";

// Extend the citeopolis config with next one
export default [
  ...citeopolisConfig,
  {
    plugins: {
      "@next/next": next,
    },
    rules: {
      ...next.configs.recommended.rules,
      ...next.configs["core-web-vitals"].rules,
    },
  },
  ...storybook.configs["flat/recommended"],
];
