@use "sass:math";
@use "@/styles/lib/variables.scss" as *;

.input {
  width: 24px;
  height: 24px;
  font-size: 12px;
  line-height: 24px;
  color: $color-white;
  appearance: none;
  cursor: pointer;
  border: 1px solid $color-neutral-500;
  border-radius: 50%;
  transition:
    background-color 200ms ease-in-out,
    border-color 200ms ease-in-out;

  &::before {
    position: relative;
    top: 50%;
    left: 50%;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: top;
    content: "";
    border: 1px solid transparent;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition:
      background-color 200ms ease-in-out,
      border-color 200ms ease-in-out;
  }

  &:checked {
    border-color: $color-primary-400;

    &::before {
      background-color: $color-primary-500;
      border-color: $color-primary-400;
    }
  }

  &.size-small {
    width: 16px;
    height: 16px;
    line-height: 16px;

    &::before {
      width: 10px;
      height: 10px;
    }
  }

  &.error {
    border-color: $color-danger;
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.3;
  }
}
