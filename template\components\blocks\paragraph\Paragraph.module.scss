@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.paragraph {
  margin-block: 12px;

  @include breakpoint(large up) {
    margin-block: 16px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }

  a {
    color: $color-primary-500;
    text-decoration: underline;
    text-underline-offset: 17.5%;

    &:hover,
    &:focus {
      color: white;
      text-decoration: none;
      outline: none;
      background-color: $color-primary-500;
    }
  }

  b,
  strong {
    font-weight: 700;
  }

  i,
  em {
    font-style: italic;
  }
}
