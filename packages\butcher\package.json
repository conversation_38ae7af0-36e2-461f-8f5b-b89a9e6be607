{"name": "@citeopolis/butcher", "version": "0.0.1", "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/butcher"}, "type": "module", "exports": {".": {"import": "./dist/butcher.js", "types": "./dist/butcher.d.ts"}}, "module": "./dist/butcher.js", "types": "./dist/butcher.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "format": "prettier -w .", "prepare": "pnpm run build", "prepublish": "pnpm run build", "test": "vitest run"}, "lint-staged": {"*.{json,md}": "prettier --write", "*.ts": ["eslint --fix", "prettier --write", "vitest related --run"]}, "prettier": "@citeopolis/prettier-config", "dependencies": {"colorette": "^2.0.20", "globby": "^14.0.0", "zod": "^4.0.17"}, "devDependencies": {"@citeopolis/eslint-config": "workspace:^", "@citeopolis/prettier-config": "workspace:^", "@types/node": "^20.0.0", "eslint": "^9.31.0", "prettier": "^3", "tsup": "^8.3.5", "typescript": "^5.5.0", "vite": "^6.1.0", "vitest": "^3.0.5"}}