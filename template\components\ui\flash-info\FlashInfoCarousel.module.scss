@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.swiperControls {
  display: flex;
  grid-area: controls;
  align-items: center;
  align-self: center;
  min-height: 32px;
}

.swiper {
  grid-area: content;
  width: 100%;

  :global(.swiper-wrapper) {
    align-items: center;
  }

  :global(.swiper-slide) {
    // Add some padding to make the focus outline visible
    padding: 4px;
  }
}

.paginationInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 1.2rem;
  line-height: 110%;

  @include breakpoint(large up) {
    margin-right: 16px;
  }
}

.nextButton,
.prevButton,
.playButton {
  @include size(24px);

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition:
    background 0.2s ease-in-out,
    color 0.2s ease-in-out;

  i {
    font-size: 1.4rem;
  }

  &:hover,
  &:focus-visible {
    color: var(--button-hover-color);
    background-color: var(--button-hover-background);
  }
}
