"use client";

import Tooltip from "@/components/ui/tooltip/Tooltip";
import clsx from "clsx";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { focusable } from "tabbable";
import { useMediaQuery } from "usehooks-ts";
import styles from "./Breadcrumbs.module.scss";

interface Crumb {
  title: string;
  url: string;
  siblings?: Crumb[];
}

interface BreadcrumbsProps {
  items: Crumb[];
}

const DOTS = Symbol("DOTS");

export default function Breadcrumbs({ items }: BreadcrumbsProps) {
  const breadcrumbsRef = useRef<HTMLElement>(null);
  const [expanded, setExpanded] = useState<boolean>(false);
  const isDesktopViewport = useMediaQuery("(min-width: 1600px)");
  const [crumbs, setCrumbs] = useState<(Crumb | typeof DOTS)[]>([]);

  // Add a collapsible section to avoid too much overflow on mobile and tablet
  useEffect(() => {
    setCrumbs(!expanded && !isDesktopViewport && items.length > 2 ? [items[0], DOTS, items.at(-1)!] : items);
  }, [expanded, isDesktopViewport, items]);

  if (items.length === 0) {
    return null;
  }

  /**
   * Remove the expand button, and insert the collapsed elements.
   * Move the focus on the first focusable element of the breadcrumbs.
   */
  const expand = function () {
    setExpanded(true);

    if (breadcrumbsRef.current) {
      focusable(breadcrumbsRef.current, { includeContainer: false })[0]?.focus();
    }
  };

  return (
    <nav role="navigation" className={styles.breadcrumbs} aria-label="Vous êtes ici" ref={breadcrumbsRef}>
      <ol className={styles.list}>
        {crumbs.map((crumb, index) => {
          return (
            <li key={index} className={styles.crumb}>
              {index !== 0 && <i className={clsx("far fa-chevron-right", styles.separator)} aria-hidden="true"></i>}
              {crumb === DOTS ? (
                <Tooltip content="Déployer toute l'arborescence">
                  <button
                    className={styles.expandButton}
                    type="button"
                    onClick={() => expand()}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        e.preventDefault();
                        expand();
                      }
                    }}
                    aria-expanded={expanded}
                    aria-label="Déployer toute l'arborescence"
                  >
                    <span aria-hidden="true">...</span>
                  </button>
                </Tooltip>
              ) : (
                <Crumb
                  index={index}
                  title={crumb.title || "Sans titre"}
                  url={crumb.url}
                  isLast={index === crumbs.length - 1}
                />
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

interface CrumbProps {
  index: number;
  title: string;
  url: string;
  isLast: boolean;
}

function Crumb({ index, title, url, isLast }: CrumbProps) {
  return index === 0 ? (
    <Tooltip content="Page d'accueil">
      <Link href={url} className={styles.home}>
        {url === "/" && <i className="far fa-house" aria-hidden="true"></i>}
        <span className="sr-only">{title}</span>
      </Link>
    </Tooltip>
  ) : (isLast ? (
    <span className={styles.current}>{title}</span>
  ) : (
    <Link href={url} className={styles.link}>
      {title}
    </Link>
  ));
}
