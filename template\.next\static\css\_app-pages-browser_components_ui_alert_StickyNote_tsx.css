/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/alert/StickyNote.module.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.StickyNote_stickyNote__nJ0xY {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 950;
  padding: 0 16px;
}
@media screen and (min-width: 1302px) {
  .StickyNote_stickyNote__nJ0xY {
    flex-direction: row;
    gap: 32px;
  }
}

.StickyNote_container__xgoqR {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  max-width: 592px;
  padding: 24px 12px;
  margin: auto;
  border-radius: 4px 4px 0 0;
}
@media screen and (min-width: 768px) {
  .StickyNote_container__xgoqR {
    padding: 24px 16px;
  }
}
@media screen and (min-width: 1302px) {
  .StickyNote_container__xgoqR {
    gap: 16px;
    padding: 32px 16px;
  }
}
.StickyNote_container__xgoqR.StickyNote_color-primary__tUPMI {
  color: #fff;
  background-color: #214fab;
}
.StickyNote_container__xgoqR.StickyNote_color-secondary__uAPNv {
  background-color: #3ec8ad;
}
.StickyNote_container__xgoqR.StickyNote_color-tertiary__eYvxg {
  background-color: #eec478;
}
.StickyNote_container__xgoqR.StickyNote_color-danger__P6MDR {
  color: #fff;
  background-color: #d61200;
}
.StickyNote_container__xgoqR .StickyNote_topIcon__piP5B {
  font-size: 2.4rem;
}

.StickyNote_wrapper__6n7M8 {
  display: flex;
  flex-wrap: nowrap;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
  min-width: 0;
}
@media screen and (min-width: 1302px) {
  .StickyNote_wrapper__6n7M8 {
    gap: 16px;
  }
}

.StickyNote_content__EzB7Q {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
@media screen and (min-width: 1302px) {
  .StickyNote_content__EzB7Q {
    gap: 16px;
  }
}
.StickyNote_content__EzB7Q .StickyNote_action__IvO1F {
  display: flex;
  justify-content: center;
  width: 100%;
}

.StickyNote_header__bE37W {
  display: flex;
  flex-direction: column;
  gap: 6px;
}
@media screen and (min-width: 1302px) {
  .StickyNote_header__bE37W {
    gap: 8px;
  }
}
.StickyNote_header__bE37W .StickyNote_title__SDREt {
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 110%;
}
@media screen and (min-width: 768px) {
  .StickyNote_header__bE37W .StickyNote_title__SDREt {
    font-size: 1.4rem;
  }
}
.StickyNote_header__bE37W .StickyNote_description__J800i {
  font-size: 1.2rem;
  line-height: 110%;
}
@media screen and (min-width: 768px) {
  .StickyNote_header__bE37W .StickyNote_description__J800i {
    font-size: 1.4rem;
  }
}

.StickyNote_close__x_3S3 {
  padding: 0 8px;
  margin-left: auto;
  font-size: 1.2rem;
  cursor: pointer;
  border-radius: 4px;
  transition: background 0.5s ease-in-out, color 0.5s ease-in-out;
}
.StickyNote_close__x_3S3 i {
  font-size: 1.4rem;
}
.StickyNote_close__x_3S3.StickyNote_color-primary__tUPMI:hover, .StickyNote_close__x_3S3.StickyNote_color-primary__tUPMI:focus {
  color: currentcolor;
  background-color: white;
}
.StickyNote_close__x_3S3.StickyNote_color-primary__tUPMI:hover > *, .StickyNote_close__x_3S3.StickyNote_color-primary__tUPMI:focus > * {
  color: #214fab;
}
.StickyNote_close__x_3S3.StickyNote_color-danger__P6MDR:hover, .StickyNote_close__x_3S3.StickyNote_color-danger__P6MDR:focus {
  color: currentcolor;
  background-color: white;
}
.StickyNote_close__x_3S3.StickyNote_color-danger__P6MDR:hover > *, .StickyNote_close__x_3S3.StickyNote_color-danger__P6MDR:focus > * {
  color: #d61200;
}
.StickyNote_close__x_3S3.StickyNote_color-secondary__uAPNv:hover, .StickyNote_close__x_3S3.StickyNote_color-secondary__uAPNv:focus, .StickyNote_close__x_3S3.StickyNote_color-tertiary__eYvxg:hover, .StickyNote_close__x_3S3.StickyNote_color-tertiary__eYvxg:focus {
  color: currentcolor;
  background-color: white;
}
.StickyNote_close__x_3S3.StickyNote_color-secondary__uAPNv:hover > *, .StickyNote_close__x_3S3.StickyNote_color-secondary__uAPNv:focus > *, .StickyNote_close__x_3S3.StickyNote_color-tertiary__eYvxg:hover > *, .StickyNote_close__x_3S3.StickyNote_color-tertiary__eYvxg:focus > * {
  color: #000;
}
