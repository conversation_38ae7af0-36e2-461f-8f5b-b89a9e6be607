@use "sass:color";
@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.container {
  position: relative;
  display: flex;
  width: 100%;
}

$icon-width: 20px;
$icon-gap: 8px;

// TODO: Apply additional padding for other variants
.input {
  width: 100%;
  font-size: 1.6rem;
  line-height: 1.3;
  background-color: $color-white;

  &::placeholder {
    color: $color-neutral-500;
  }

  &.variant-outlined {
    border: 1px solid $color-neutral-500;
    border-radius: 4px;

    &.error {
      background-color: color.adjust($color-danger, $lightness: 57%);
    }
  }

  &.variant-underline {
    border-bottom: 1px solid $color-neutral-500;

    &.error {
      color: $color-danger;
    }
  }

  &.variant-filled {
    background-color: $color-neutral-100;
    border-bottom: 1px solid $color-neutral-500;
    border-radius: 4px 4px 0 0;

    &.error {
      background-color: color.adjust($color-danger, $lightness: 57%);
    }
  }

  &.variant-text {
    border-radius: 4px;
  }

  &.size-small {
    padding: 8px 16px;
  }

  &.size-large,
  &.size-medium {
    padding: 12px 16px;

    .container:has(.startIcon) & {
      padding-left: $icon-width + $icon-gap + 16px;
    }

    .container:has(.endIcon) & {
      padding-right: $icon-width + $icon-gap + 16px;
    }
  }

  &.size-large {
    @include breakpoint(large up) {
      padding: 21px 24px;

      .container:has(.startIcon) & {
        padding-left: $icon-width + $icon-gap + 24px;
      }

      .container:has(.endIcon) & {
        padding-right: $icon-width + $icon-gap + 24px;
      }
    }
  }

  &.error {
    border-color: $color-danger;

    &::placeholder {
      color: $color-danger;
    }
  }

  &:disabled {
    opacity: 0.3;
  }
}

.innerLabel {
  position: absolute;
  top: 50%;
  left: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.6rem;
  line-height: 130%;
  color: $color-neutral-500;
  white-space: nowrap;
  pointer-events: none;
  transform: translateY(-50%);
  transform-origin: top left;
  transition:
    font-size 0.2s ease-out,
    line-height 0.2s ease-out,
    left 0.2s ease-out,
    top 0.2s ease-out;

  .input.error ~ & {
    color: $color-danger;
  }

  .container:has(.startIcon) & {
    left: $icon-width + $icon-gap + 16px;
  }

  &:has(+ .input.size-large) {
    @include breakpoint(large up) {
      left: 24px;

      .container:has(.startIcon) & {
        left: $icon-width + $icon-gap + 24px;
      }
    }
  }

  &:has(+ .input:focus),
  &:has(+ .input:not(:placeholder-shown)) {
    top: 0;
    left: 16px;
    font-size: 1.2rem;
    line-height: 110%;
    background-color: $color-white;
    box-shadow: 0 0 0 4px $color-white;
  }

  // TODO: Use CSS variable
  &:has(+ .input.size-large:focus),
  &:has(+ .input.size-large:not(:placeholder-shown)) {
    font-size: 1.4rem;
  }

  .container &:has(+ .input.size-large:focus),
  .container &:has(+ .input.size-large:not(:placeholder-shown)) {
    @include breakpoint(large up) {
      left: 24px;
    }
  }
}

.startIcon,
.endIcon {
  position: absolute;
  top: 50%;
  margin-inline: 16px;
  vertical-align: middle;
  pointer-events: none;
  transform: translateY(-50%);

  .container:has(input:placeholder-shown) & {
    color: $color-neutral-500;
  }

  .container:has(input.error) & {
    color: $color-danger;
  }

  // FIXME: Not reusable, put the size on the container
  @include breakpoint(large up) {
    .container:has(.input.size-large) & {
      margin-inline: 24px;
    }
  }
}

.startIcon {
  left: 0;
}

.endIcon {
  right: 0;
}
