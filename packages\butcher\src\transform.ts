/**
 * Strip a feature from the input.
 */
export default function transform(input: string, featureName: string): string {
  const startTag = `feature-start ${featureName}`;
  const endTag = `feature-end ${featureName}`;

  let inside = false;

  return input
    .split("\n")
    .filter((line) => {
      if (line.includes(startTag)) {
        inside = true;
        return false;
      }

      if (line.includes(endTag)) {
        inside = false;
        return false;
      }

      return !inside;
    })
    .join("\n");
}
