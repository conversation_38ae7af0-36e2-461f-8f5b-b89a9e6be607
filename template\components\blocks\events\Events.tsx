"use client";

import Hx from "@/components/ui/title/Hx";
import type { EventsBlock } from "@/generated/graphql/graphql";
import { SubtitleLevelProvider } from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import styles from "./Events.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));
const EventsFocus = dynamic(() => import("./EventsFocus"));
const EventsList = dynamic(() => import("./EventsList"));
const Tags = dynamic(() => import("@/components/ui/tag-bar/TagBar"));

export type EventsProps = Partial<Omit<EventsBlock, "__typename" | "innerBlocks">> & {
  // TODO: Remove when implemented in BE
  proposeUrl?: boolean;
};

/**
 * A block that displays a selection of events.
 */
export default function Events({
  anchor,
  events,
  focusedEvent,
  listUrl,
  proposeUrl,
  tags,
  title,
  titleLevel,
}: EventsProps) {
  return (
    <section id={anchor ?? undefined} className={clsx("block-event contained", styles.events)}>
      {(title || (tags ?? []).length > 0) && (
        <div className={styles.titleWrapper}>
          {title && (
            <Hx level={titleLevel} className={styles.title}>
              {title}
            </Hx>
          )}
          {tags && <Tags className={styles.tags} items={tags} aria-roledescription="Liste des thématiques" />}
        </div>
      )}
      <SubtitleLevelProvider level={titleLevel}>
        {focusedEvent && <EventsFocus event={focusedEvent} />}
        {events && events.length > 0 && <EventsList items={events} />}
        {(listUrl || proposeUrl) && (
          <div className={styles.actions}>
            {listUrl && (
              <Button asChild variant="contained" size="lg" startIcon="fas fa-plus">
                <Link href={listUrl}>Tous les événements</Link>
              </Button>
            )}
            {proposeUrl && (
              <Button asChild variant="outlined" size="lg" startIcon="far fa-lightbulb-on">
                <Link href="#">Proposer un événement</Link>
              </Button>
            )}
          </div>
        )}
      </SubtitleLevelProvider>
    </section>
  );
}
