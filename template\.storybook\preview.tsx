import "@citeopolis-fontawesome/optimized/css/all.min.css";
import type { Preview } from "@storybook/nextjs";
import "../styles/global.scss";
import { rethinkSans } from "../utils/typography";

const preview: Preview = {
  parameters: {
    viewport: {
      options: {
        desktop: {
          name: "Desktop",
          styles: { width: "1600px", height: "1024px" },
          type: "desktop",
        },
        tablet: {
          name: "Tablet",
          styles: { width: "768px", height: "1024px" },
          type: "tablet",
        },
        mobile: {
          name: "Mobile",
          styles: { width: "376px", height: "668px" },
          type: "mobile",
        },
      },
    },

    // Configures controls for props
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /date$/i,
      },
    },

    // Enables documentation autodocs tagging
    docs: {
      autodocs: true,
    },

    // Adds backgrounds for light and dark themes
    backgrounds: {
      default: "light",
      values: [
        { name: "light", value: "#FFFFFF" },
        { name: "dark", value: "#0D2044" },
      ],
    },

    // Enhances accessibility checks
    a11y: {
      context: {
        include: ["#root"],
        exclude: ["[data-storybook-a11y=ignore]", ".underline"],
      },
      config: {},
      options: {
        checks: { "color-contrast": { options: { noScroll: true } } },
        restoreScroll: true,
      },
    },

    // https://www.chromatic.com/docs/modes/
    chromatic: {
      modes: {
        "376px": {
          viewport: 376,
        },
        "768px": {
          viewport: 768,
        },
        "1600px": {
          viewport: 1600,
        },
      },
    },
  },

  tags: ["autodocs"],

  // Inject local font in preview
  decorators: [
    (Story) => (
      <div id="root" className={rethinkSans.className} lang="fr">
        <Story />
      </div>
    ),
  ],
};

export default preview;
