@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.sidebar {
  @include breakpoint(large up) {
    width: 344px;
    padding-right: $space-9;
  }
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: $space-5;

  @include breakpoint(medium up) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }

  @include breakpoint(large up) {
    gap: $space-6;
  }
}

.list {
  display: flex;
  flex-direction: column;
  gap: $space-11;
  margin-block: $space-7;
  container: directory-list / inline-size;

  @include breakpoint(medium up) {
    grid-template-columns: repeat(3, 1fr);
    gap: $space-11 $space-7;
  }

  @include breakpoint(large up) {
    gap: $space-13 $space-8;
    margin-block: $space-8;
  }
}
