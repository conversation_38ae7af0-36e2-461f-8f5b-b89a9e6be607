---
title: "Getting Started"
weight: 1
---

# Getting Started

## Setup your environment

You'll need the following:

- Node.js : **22.x**
- NPM : **11.x**

## Setup the access token

Go to https://code.stratis.fr/-/user_settings/personal_access_tokens and create a new access token for your account. Set the permissions: **api**, **api_read** and **read_registry**.

Create a `.npmrc` file in your home directory with the following content:

```
@citeopolis:registry=https://code.stratis.fr/api/v4/projects/5490/packages/npm/
//code.stratis.fr/:_authToken=$GITLAB_NPM_TOKEN
```

Replace the `$GITLAB_NPM_TOKEN` with yours.

## Create an empty project

Run the following command to create a new project.

```sh
npm create @citeopolis/app
```

Once the directory is created, you can move into it and install the node dependencies.

Copy the `.env.example` file to `.env` and replace the URLs with your backend.

Then run the development server:

```sh
npm run dev
```

You can learn more about the available commands [here](/docs/packages/template).
