import type { Marker } from "./types";

export const markers = [
  // Centre de Paris (shared coordinates)
  {
    id: "notre-dame",
    coordinates: [2.3499, 48.8529],
    data: {
      title: "Cathédrale Notre-Dame de Paris",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "sainte-chapelle",
    coordinates: [2.3499, 48.8529],
    data: { title: "Sainte-Chapelle", type: "landmark", category: "historic" },
  },
  {
    id: "point-zero",
    coordinates: [2.3499, 48.8529],
    data: {
      title: "Point Zéro des Routes de France",
      type: "point_of_interest",
      category: "historic",
    },
  },
  {
    id: "flower-market",
    coordinates: [2.3499, 48.8529],
    data: {
      title: "Marché aux Fleurs et aux Oiseaux",
      type: "shop",
      category: "market",
    },
  },

  // Louvre area (shared coordinates)
  {
    id: "louvre-museum",
    coordinates: [2.3375, 48.8606],
    data: { title: "Musée du Louvre", type: "museum", category: "art" },
  },
  {
    id: "pyramide-louvre",
    coordinates: [2.3375, 48.8606],
    data: {
      title: "Pyramide du Louvre",
      type: "landmark",
      category: "architecture",
    },
  },
  {
    id: "tuileries-garden",
    coordinates: [2.3375, 48.8606],
    data: { title: "Jardin des Tuileries", type: "park", category: "nature" },
  },

  // Champs-Élysées / Arc de Triomphe (shared coordinates)
  {
    id: "arc-de-triomphe",
    coordinates: [2.295, 48.8738],
    data: { title: "Arc de Triomphe", type: "landmark", category: "historic" },
  },
  {
    id: "champs-elysees-shopping",
    coordinates: [2.295, 48.8738],
    data: {
      title: "Champs-Élysées Shopping Area",
      type: "shop",
      category: "shopping",
    },
  },

  // Eiffel Tower (individual)
  {
    id: "eiffel-tower",
    coordinates: [2.2945, 48.8584],
    data: { title: "Tour Eiffel", type: "landmark", category: "architecture" },
  },

  // Montmartre / Sacré-Cœur (shared coordinates)
  {
    id: "sacre-coeur",
    coordinates: [2.3431, 48.8867],
    data: {
      title: "Basilique du Sacré-Cœur de Montmartre",
      type: "landmark",
      category: "religious",
    },
  },
  {
    id: "place-du-tertre",
    coordinates: [2.3431, 48.8867],
    data: {
      title: "Place du Tertre",
      type: "point_of_interest",
      category: "art",
    },
  },
  {
    id: "montmartre-vines",
    coordinates: [2.3431, 48.8867],
    data: {
      title: "Vignes du Clos Montmartre",
      type: "point_of_interest",
      category: "nature",
    },
  },

  // Latin Quarter
  {
    id: "pantheon",
    coordinates: [2.3462, 48.8462],
    data: { title: "Panthéon", type: "landmark", category: "historic" },
  },
  {
    id: "sorbonne",
    coordinates: [2.3421, 48.8471],
    data: {
      title: "Université Paris-Sorbonne",
      type: "education",
      category: "university",
    },
  },
  {
    id: "luxembourg-garden",
    coordinates: [2.3371, 48.8462],
    data: { title: "Jardin du Luxembourg", type: "park", category: "nature" },
  },

  // Le Marais
  {
    id: "place-des-vosges",
    coordinates: [2.3659, 48.8559],
    data: { title: "Place des Vosges", type: "landmark", category: "historic" },
  },
  {
    id: "picasso-museum",
    coordinates: [2.3616, 48.8596],
    data: { title: "Musée Picasso", type: "museum", category: "art" },
  },

  // Opéra Garnier
  {
    id: "opera-garnier",
    coordinates: [2.3317, 48.8719],
    data: { title: "Opéra Garnier", type: "landmark", category: "architecture" },
  },
  {
    id: "galeries-lafayette",
    coordinates: [2.3323, 48.8741],
    data: { title: "Galeries Lafayette", type: "shop", category: "shopping" },
  },

  // Invalides
  {
    id: "les-invalides",
    coordinates: [2.3129, 48.8564],
    data: {
      title: "Hôtel des Invalides",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "rodin-museum",
    coordinates: [2.316, 48.8552],
    data: { title: "Musée Rodin", type: "museum", category: "art" },
  },

  // Canal Saint-Martin (shared coordinates)
  {
    id: "canal-saint-martin",
    coordinates: [2.362, 48.872],
    data: {
      title: "Canal Saint-Martin",
      type: "point_of_interest",
      category: "urban",
    },
  },
  {
    id: "hotel-du-nord",
    coordinates: [2.362, 48.872],
    data: { title: "Hôtel du Nord", type: "restaurant", category: "food" },
  },

  // Random points around Paris to fill up
  {
    id: "park-buttes",
    coordinates: [2.391, 48.879],
    data: {
      title: "Parc des Buttes-Chaumont",
      type: "park",
      category: "nature",
    },
  },
  {
    id: "perc-lachaise",
    coordinates: [2.393, 48.861],
    data: {
      title: "Cimetière du Père Lachaise",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "bastille",
    coordinates: [2.369, 48.853],
    data: {
      title: "Place de la Bastille",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "pompidou",
    coordinates: [2.3522, 48.8607],
    data: { title: "Centre Pompidou", type: "museum", category: "art" },
  },
  {
    id: "musee-dorsay",
    coordinates: [2.3265, 48.86],
    data: { title: "Musée d'Orsay", type: "museum", category: "art" },
  },
  {
    id: "seine-river",
    coordinates: [2.3, 48.85],
    data: {
      title: "Seine River",
      type: "point_of_interest",
      category: "nature",
    },
  },
  {
    id: "moulin-rouge",
    coordinates: [2.3323, 48.8841],
    data: {
      title: "Moulin Rouge",
      type: "entertainment",
      category: "nightlife",
    },
  },
  {
    id: "gare-du-nord",
    coordinates: [2.3553, 48.879],
    data: {
      title: "Gare du Nord",
      type: "transport",
      category: "train_station",
    },
  },
  {
    id: "gare-de-lest",
    coordinates: [2.3588, 48.8767],
    data: {
      title: "Gare de l'Est",
      type: "transport",
      category: "train_station",
    },
  },
  {
    id: "denfert-rochereau",
    coordinates: [2.332, 48.835],
    data: {
      title: "Catacombes de Paris",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "parc-monceau",
    coordinates: [2.311, 48.88],
    data: { title: "Parc Monceau", type: "park", category: "nature" },
  },
  {
    id: "place-vendome",
    coordinates: [2.3298, 48.8687],
    data: { title: "Place Vendôme", type: "shop", category: "luxury" },
  },
  {
    id: "place-concorde",
    coordinates: [2.321, 48.8656],
    data: {
      title: "Place de la Concorde",
      type: "landmark",
      category: "historic",
    },
  },
  {
    id: "grand-palais",
    coordinates: [2.313, 48.866],
    data: { title: "Grand Palais", type: "landmark", category: "architecture" },
  },
  {
    id: "petit-palais",
    coordinates: [2.316, 48.866],
    data: { title: "Petit Palais", type: "museum", category: "art" },
  },
  {
    id: "trocadero",
    coordinates: [2.288, 48.862],
    data: { title: "Jardins du Trocadéro", type: "park", category: "nature" },
  },
  {
    id: "musee-quai-branly",
    coordinates: [2.298, 48.859],
    data: {
      title: "Musée du Quai Branly - Jacques Chirac",
      type: "museum",
      category: "art",
    },
  },
  {
    id: "palais-royal",
    coordinates: [2.337, 48.863],
    data: { title: "Palais Royal", type: "landmark", category: "historic" },
  },
  {
    id: "chatelet",
    coordinates: [2.347, 48.859],
    data: {
      title: "Théâtre du Châtelet",
      type: "entertainment",
      category: "theater",
    },
  },
  {
    id: "forum-les-halles",
    coordinates: [2.347, 48.862],
    data: { title: "Forum des Halles", type: "shop", category: "shopping" },
  },
  {
    id: "hotel-de-ville",
    coordinates: [2.3506, 48.8566],
    data: { title: "Hôtel de Ville", type: "landmark", category: "historic" },
  },
  {
    id: "musee-cluny",
    coordinates: [2.3438, 48.8504],
    data: {
      title: "Musée de Cluny - Musée National du Moyen Âge",
      type: "museum",
      category: "history",
    },
  },
  {
    id: "jardin-des-plantes",
    coordinates: [2.3589, 48.8447],
    data: { title: "Jardin des Plantes", type: "park", category: "nature" },
  },
  {
    id: "grande-mosquee",
    coordinates: [2.3562, 48.841],
    data: {
      title: "Grande Mosquée de Paris",
      type: "religious",
      category: "islam",
    },
  },
  {
    id: "butte-aux-cailles",
    coordinates: [2.352, 48.827],
    data: {
      title: "Butte-aux-Cailles",
      type: "point_of_interest",
      category: "neighborhood",
    },
  },
  {
    id: "cité-universitaire",
    coordinates: [2.336, 48.818],
    data: {
      title: "Cité Internationale Universitaire de Paris",
      type: "education",
      category: "university",
    },
  },
  {
    id: "parc-de-la-villette",
    coordinates: [2.391, 48.891],
    data: { title: "Parc de la Villette", type: "park", category: "urban" },
  },
  {
    id: "cite-sciences",
    coordinates: [2.3888, 48.8938],
    data: {
      title: "Cité des Sciences et de l'Industrie",
      type: "museum",
      category: "science",
    },
  },

  // A few outside Paris
  {
    id: "london-bridge",
    coordinates: [-0.087, 51.507],
    data: { title: "Tower Bridge", type: "landmark", category: "architecture" },
  },
  {
    id: "buckingham-palace",
    coordinates: [-0.1419, 51.5014],
    data: { title: "Buckingham Palace", type: "landmark", category: "royal" },
  },
  {
    id: "big-ben",
    coordinates: [-0.1246, 51.5007],
    data: { title: "Big Ben", type: "landmark", category: "architecture" },
  },
  {
    id: "rome-colosseum",
    coordinates: [12.4922, 41.8902],
    data: { title: "Colosseum", type: "landmark", category: "historic" },
  },
  {
    id: "berlin-reichstag",
    coordinates: [13.3777, 52.5186],
    data: {
      title: "Reichstag Building",
      type: "landmark",
      category: "historic",
    },
  },
] satisfies Marker[];
