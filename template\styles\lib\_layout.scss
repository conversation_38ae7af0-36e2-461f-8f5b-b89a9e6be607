@use "mixins.scss" as *;
@use "variables.scss" as *;

// Fluid layout that can contain:
// - column main
// - column sidebar

.layout-1column,
.layout-2columns-left,
.layout-2columns-right,
.layout-3columns {
  width: 100%;
  max-width: 1216px;
  padding-inline: 16px;
  margin-block: 64px;
  margin-inline: auto;

  .column {
    margin-bottom: 64px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Tablet +
  @include breakpoint(medium up) {
    padding-inline: 24px;
    margin-block: 72px;

    .column {
      margin-bottom: 72px;
    }
  }

  // Desktop +
  @include breakpoint(large up) {
    display: flex;
    gap: 32px;
    padding-inline: 0;
    margin-block: 96px;

    .column {
      margin-bottom: 0;
    }

    .column.main {
      flex: 1;
    }
  }
}

.layout-1column-fullwidth {
  display: flex;
  width: 100%;
  margin-block: 64px;

  .column.main {
    width: 100%;
  }

  // Tablet +
  @include breakpoint(medium up) {
    margin-block: 72px;
  }

  // Desktop +
  @include breakpoint(large up) {
    margin-block: 96px;
  }
}
