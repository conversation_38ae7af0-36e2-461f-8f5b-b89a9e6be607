import { Image, News as NewsItem } from "@/generated/graphql/graphql";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import News from "./News";

const meta: Meta<typeof News> = {
  title: "Blocks/News",
  component: News,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof News>;

const imageData: Image = {
  url: "/assets/placeholder-720x480.png",
  width: 720,
  height: 480,
  alt: "Image d'exemple",
};

const newsData: NewsItem = {
  title: "Titre de l’actualité lorem ipsum dolor sit amet, consetet elis passam filis",
  // @ts-expect-error Incomplete image collection
  images: {
    ratio_3x2: imageData,
  },
  url: "#",
  categories: [
    // @ts-expect-error Incomplete category
    {
      title: "Catégorie",
      relativeUrl: "#",
    },
  ],
};

export const Default: Story = {
  args: {
    title: "Actualités",
    titleLevel: 1,
    listUrl: "#",
    proposeUrl: true,
    tags: [
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
      // @ts-expect-error Incomplete link
      { text: "Thématique", url: "#" },
    ],
    focusedNews: newsData,
    news: [newsData, newsData, newsData, newsData],
    briefNews: [newsData, newsData, newsData, newsData],
  },
};

export const ThreeNews: Story = {
  args: {
    title: "Actualités",
    titleLevel: 1,
    news: [newsData, newsData, newsData],
  },
};

export const TwoNews: Story = {
  args: {
    title: "Actualités",
    titleLevel: 1,
    news: [newsData, newsData],
  },
};
