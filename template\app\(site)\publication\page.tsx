import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import HeadingPublication from "@/components/ui/heading/HeadingPublication";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";
import PublicationFileList from "./PublicationFileList";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const PUBLICATION_QUERY = graphql(`
  query GetPublication($url: URL!) {
    route(url: $url) {
      ... on Publication {
        title
        status
        structuredContent
        leadText
        publicationDate
        modifiedDate
        files {
          label
          downloadUrl
          extname
          mime
          size
          viewUrl
        }
        images {
          ratio_A4_portrait {
            alt
            height
            url
            width
          }
        }
        categories {
          relativeUrl
          title
          parent {
            __typename
          }
        }
        breadcrumbs {
          items {
            title
            url
            siblings {
              title
              url
            }
          }
        }
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: PUBLICATION_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "Publication");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: PUBLICATION_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "all",
  });

  assert.ok(data.route?.__typename === "Publication");

  const {
    title,
    leadText,
    categories,
    pager,
    breadcrumbs,
    images,
    files,
    publicationDate,
    modifiedDate,
    structuredContent,
  } = data.route;
  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");

  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <HeadingPublication
        surtitle={surtitle}
        title={title ?? "Sans titre"}
        leadText={leadText}
        tags={tags}
        imageSrc={images?.ratio_A4_portrait?.url}
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />
      <div className="layout-1column-fullwidth">
        <div className="column main">
          <PublicationFileList files={files} />
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
      </div>
      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
