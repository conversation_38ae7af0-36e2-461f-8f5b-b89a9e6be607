import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { AlbumFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput, FilterInputParams } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import assert from "node:assert";
import Item from "./Item";
import styles from "./page.module.scss";

const ALBUM_LIST_QUERY = graphql(`
  query GetAlbumListConfig($url: URL) {
    route(url: $url) {
      ... on AlbumList {
        defaultPageSize
        leadText
        url
        title
        rssUrl
        proposeUrl
        filters {
          __typename
          attribute
        }
        breadcrumbs {
          items {
            title
            url
          }
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const ALBUM_SEARCH_QUERY = graphql(`
  query GetAlbumList($filter: AlbumFilterInput, $sort: AlbumSortInput, $pageSize: Int, $currentPage: Int) {
    albumSearch(filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        id
        leadText
        url
        title
        videoCount
        photoCount
        mediaCount
        media {
          ... on AlbumPhoto {
            alt
            caption
          }
          ... on AlbumVideo {
            caption
            provider
            thumbnail {
              url
              width
              height
              alt
            }
          }
        }
        categories {
          title
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
          placeholder
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: ALBUM_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "AlbumList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<FilterInputParams<AlbumFilterInput> & { p?: string }>;
}) {
  const { data: albumList } = await query({
    query: ALBUM_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(albumList.route?.__typename === "AlbumList");

  const { title, leadText, breadcrumbs, filters: defaultFilters, rssUrl, url, defaultPageSize } = albumList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<AlbumFilterInput>({
    params,
    filters: defaultFilters,
  });

  const { data: albumSearch } = await query({
    query: ALBUM_SEARCH_QUERY,
    errorPolicy: "all",
    variables: {
      pageSize,
      currentPage,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = albumSearch?.albumSearch || {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Albums" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar title="Filtrer les albums" filters={filters} url={url} />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />
          </div>

          {items.length > 0 && (
            <ol className={styles.grid}>
              {items.map((album) => (
                <li key={album.id}>
                  <Item album={album} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
