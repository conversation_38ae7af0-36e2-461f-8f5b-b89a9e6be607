@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.directory {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  padding: 24px 16px;
  background-color: $color-neutral-100;

  @include breakpoint(medium up) {
    padding: 24px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    padding: 32px;
  }

  @container (min-width: 700px) {
    display: grid;
    grid-template: "image title" auto "access access" auto "infos infos" auto "actions actions" auto / 247px 1fr;
  }
}

.imageWrapper {
  order: -1;

  @container (min-width: 700px) {
    grid-area: image;
  }

  img {
    max-width: 247px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }

  @container (min-width: 700px) {
    grid-area: title;
    align-self: center;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.accessibility {
  z-index: $layer-link-block + 1;
  grid-area: access;
}

.infos {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @container (min-width: 700px) {
    flex-direction: row;
    grid-area: infos;
    gap: 24px;
  }
}

.info {
  padding-left: 12px;
  font-size: 1.6rem;
  line-height: 150%;
  border-left: 1px solid $color-neutral-300;

  .label {
    display: block;
    margin-bottom: 8px;
    font-weight: 700;
    line-height: 110%;
  }

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.actions {
  position: relative;
  z-index: $layer-link-block + 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;

  @container (min-width: 700px) {
    display: grid;
    grid-area: actions;
    grid-template-columns: repeat(auto-fit, minmax(142px, 1fr));
  }

  @container (min-width: 700px) and (max-width: 820px) {
    // Expand the website button to full width if present
    a:nth-child(4) {
      grid-column: span 3;
    }
  }

  @include breakpoint(large up) {
    gap: 8px;
  }
}
