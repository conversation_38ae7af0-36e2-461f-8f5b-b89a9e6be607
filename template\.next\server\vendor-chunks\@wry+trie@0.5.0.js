"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry+trie@0.5.0";
exports.ids = ["vendor-chunks/@wry+trie@0.5.0"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trie: () => (/* binding */ Trie)\n/* harmony export */ });\n// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\nclass Trie {\n    constructor(weakness = true, makeData = defaultMakeData) {\n        this.weakness = weakness;\n        this.makeData = makeData;\n    }\n    lookup() {\n        return this.lookupArray(arguments);\n    }\n    lookupArray(array) {\n        let node = this;\n        forEach.call(array, key => node = node.getChildTrie(key));\n        return hasOwnProperty.call(node, \"data\")\n            ? node.data\n            : node.data = this.makeData(slice.call(array));\n    }\n    peek() {\n        return this.peekArray(arguments);\n    }\n    peekArray(array) {\n        let node = this;\n        for (let i = 0, len = array.length; node && i < len; ++i) {\n            const map = node.mapFor(array[i], false);\n            node = map && map.get(array[i]);\n        }\n        return node && node.data;\n    }\n    remove() {\n        return this.removeArray(arguments);\n    }\n    removeArray(array) {\n        let data;\n        if (array.length) {\n            const head = array[0];\n            const map = this.mapFor(head, false);\n            const child = map && map.get(head);\n            if (child) {\n                data = child.removeArray(slice.call(array, 1));\n                if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n                    map.delete(head);\n                }\n            }\n        }\n        else {\n            data = this.data;\n            delete this.data;\n        }\n        return data;\n    }\n    getChildTrie(key) {\n        const map = this.mapFor(key, true);\n        let child = map.get(key);\n        if (!child)\n            map.set(key, child = new Trie(this.weakness, this.makeData));\n        return child;\n    }\n    mapFor(key, create) {\n        return this.weakness && isObjRef(key)\n            ? this.weak || (create ? this.weak = new WeakMap : void 0)\n            : this.strong || (create ? this.strong = new Map : void 0);\n    }\n}\nfunction isObjRef(value) {\n    switch (typeof value) {\n        case \"object\":\n            if (value === null)\n                break;\n        // Fall through to return true...\n        case \"function\":\n            return true;\n    }\n    return false;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0B3cnkrdHJpZUAwLjUuMC9ub2RlX21vZHVsZXMvQHdyeS90cmllL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlCQUFpQjtBQUN6QixRQUFRLGlCQUFpQjtBQUNsQjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLGlCQUFpQjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxjaXRlb3BvbGlzLWZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAd3J5K3RyaWVAMC41LjBcXG5vZGVfbW9kdWxlc1xcQHdyeVxcdHJpZVxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBBIFt0cmllXShodHRwczovL2VuLndpa2lwZWRpYS5vcmcvd2lraS9UcmllKSBkYXRhIHN0cnVjdHVyZSB0aGF0IGhvbGRzXG4vLyBvYmplY3Qga2V5cyB3ZWFrbHksIHlldCBjYW4gYWxzbyBob2xkIG5vbi1vYmplY3Qga2V5cywgdW5saWtlIHRoZVxuLy8gbmF0aXZlIGBXZWFrTWFwYC5cbi8vIElmIG5vIG1ha2VEYXRhIGZ1bmN0aW9uIGlzIHN1cHBsaWVkLCB0aGUgbG9va2VkLXVwIGRhdGEgd2lsbCBiZSBhbiBlbXB0eSxcbi8vIG51bGwtcHJvdG90eXBlIE9iamVjdC5cbmNvbnN0IGRlZmF1bHRNYWtlRGF0YSA9ICgpID0+IE9iamVjdC5jcmVhdGUobnVsbCk7XG4vLyBVc2VmdWwgZm9yIHByb2Nlc3NpbmcgYXJndW1lbnRzIG9iamVjdHMgYXMgd2VsbCBhcyBhcnJheXMuXG5jb25zdCB7IGZvckVhY2gsIHNsaWNlIH0gPSBBcnJheS5wcm90b3R5cGU7XG5jb25zdCB7IGhhc093blByb3BlcnR5IH0gPSBPYmplY3QucHJvdG90eXBlO1xuZXhwb3J0IGNsYXNzIFRyaWUge1xuICAgIGNvbnN0cnVjdG9yKHdlYWtuZXNzID0gdHJ1ZSwgbWFrZURhdGEgPSBkZWZhdWx0TWFrZURhdGEpIHtcbiAgICAgICAgdGhpcy53ZWFrbmVzcyA9IHdlYWtuZXNzO1xuICAgICAgICB0aGlzLm1ha2VEYXRhID0gbWFrZURhdGE7XG4gICAgfVxuICAgIGxvb2t1cCgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMubG9va3VwQXJyYXkoYXJndW1lbnRzKTtcbiAgICB9XG4gICAgbG9va3VwQXJyYXkoYXJyYXkpIHtcbiAgICAgICAgbGV0IG5vZGUgPSB0aGlzO1xuICAgICAgICBmb3JFYWNoLmNhbGwoYXJyYXksIGtleSA9PiBub2RlID0gbm9kZS5nZXRDaGlsZFRyaWUoa2V5KSk7XG4gICAgICAgIHJldHVybiBoYXNPd25Qcm9wZXJ0eS5jYWxsKG5vZGUsIFwiZGF0YVwiKVxuICAgICAgICAgICAgPyBub2RlLmRhdGFcbiAgICAgICAgICAgIDogbm9kZS5kYXRhID0gdGhpcy5tYWtlRGF0YShzbGljZS5jYWxsKGFycmF5KSk7XG4gICAgfVxuICAgIHBlZWsoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnBlZWtBcnJheShhcmd1bWVudHMpO1xuICAgIH1cbiAgICBwZWVrQXJyYXkoYXJyYXkpIHtcbiAgICAgICAgbGV0IG5vZGUgPSB0aGlzO1xuICAgICAgICBmb3IgKGxldCBpID0gMCwgbGVuID0gYXJyYXkubGVuZ3RoOyBub2RlICYmIGkgPCBsZW47ICsraSkge1xuICAgICAgICAgICAgY29uc3QgbWFwID0gbm9kZS5tYXBGb3IoYXJyYXlbaV0sIGZhbHNlKTtcbiAgICAgICAgICAgIG5vZGUgPSBtYXAgJiYgbWFwLmdldChhcnJheVtpXSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5vZGUgJiYgbm9kZS5kYXRhO1xuICAgIH1cbiAgICByZW1vdmUoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnJlbW92ZUFycmF5KGFyZ3VtZW50cyk7XG4gICAgfVxuICAgIHJlbW92ZUFycmF5KGFycmF5KSB7XG4gICAgICAgIGxldCBkYXRhO1xuICAgICAgICBpZiAoYXJyYXkubGVuZ3RoKSB7XG4gICAgICAgICAgICBjb25zdCBoZWFkID0gYXJyYXlbMF07XG4gICAgICAgICAgICBjb25zdCBtYXAgPSB0aGlzLm1hcEZvcihoZWFkLCBmYWxzZSk7XG4gICAgICAgICAgICBjb25zdCBjaGlsZCA9IG1hcCAmJiBtYXAuZ2V0KGhlYWQpO1xuICAgICAgICAgICAgaWYgKGNoaWxkKSB7XG4gICAgICAgICAgICAgICAgZGF0YSA9IGNoaWxkLnJlbW92ZUFycmF5KHNsaWNlLmNhbGwoYXJyYXksIDEpKTtcbiAgICAgICAgICAgICAgICBpZiAoIWNoaWxkLmRhdGEgJiYgIWNoaWxkLndlYWsgJiYgIShjaGlsZC5zdHJvbmcgJiYgY2hpbGQuc3Ryb25nLnNpemUpKSB7XG4gICAgICAgICAgICAgICAgICAgIG1hcC5kZWxldGUoaGVhZCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgZGF0YSA9IHRoaXMuZGF0YTtcbiAgICAgICAgICAgIGRlbGV0ZSB0aGlzLmRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfVxuICAgIGdldENoaWxkVHJpZShrZXkpIHtcbiAgICAgICAgY29uc3QgbWFwID0gdGhpcy5tYXBGb3Ioa2V5LCB0cnVlKTtcbiAgICAgICAgbGV0IGNoaWxkID0gbWFwLmdldChrZXkpO1xuICAgICAgICBpZiAoIWNoaWxkKVxuICAgICAgICAgICAgbWFwLnNldChrZXksIGNoaWxkID0gbmV3IFRyaWUodGhpcy53ZWFrbmVzcywgdGhpcy5tYWtlRGF0YSkpO1xuICAgICAgICByZXR1cm4gY2hpbGQ7XG4gICAgfVxuICAgIG1hcEZvcihrZXksIGNyZWF0ZSkge1xuICAgICAgICByZXR1cm4gdGhpcy53ZWFrbmVzcyAmJiBpc09ialJlZihrZXkpXG4gICAgICAgICAgICA/IHRoaXMud2VhayB8fCAoY3JlYXRlID8gdGhpcy53ZWFrID0gbmV3IFdlYWtNYXAgOiB2b2lkIDApXG4gICAgICAgICAgICA6IHRoaXMuc3Ryb25nIHx8IChjcmVhdGUgPyB0aGlzLnN0cm9uZyA9IG5ldyBNYXAgOiB2b2lkIDApO1xuICAgIH1cbn1cbmZ1bmN0aW9uIGlzT2JqUmVmKHZhbHVlKSB7XG4gICAgc3dpdGNoICh0eXBlb2YgdmFsdWUpIHtcbiAgICAgICAgY2FzZSBcIm9iamVjdFwiOlxuICAgICAgICAgICAgaWYgKHZhbHVlID09PSBudWxsKVxuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAvLyBGYWxsIHRocm91Z2ggdG8gcmV0dXJuIHRydWUuLi5cbiAgICAgICAgY2FzZSBcImZ1bmN0aW9uXCI6XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js":
/*!*********************************************************************************!*\
  !*** ../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trie: () => (/* binding */ Trie)\n/* harmony export */ });\n// A [trie](https://en.wikipedia.org/wiki/Trie) data structure that holds\n// object keys weakly, yet can also hold non-object keys, unlike the\n// native `WeakMap`.\n// If no makeData function is supplied, the looked-up data will be an empty,\n// null-prototype Object.\nconst defaultMakeData = () => Object.create(null);\n// Useful for processing arguments objects as well as arrays.\nconst { forEach, slice } = Array.prototype;\nconst { hasOwnProperty } = Object.prototype;\nclass Trie {\n    constructor(weakness = true, makeData = defaultMakeData) {\n        this.weakness = weakness;\n        this.makeData = makeData;\n    }\n    lookup() {\n        return this.lookupArray(arguments);\n    }\n    lookupArray(array) {\n        let node = this;\n        forEach.call(array, key => node = node.getChildTrie(key));\n        return hasOwnProperty.call(node, \"data\")\n            ? node.data\n            : node.data = this.makeData(slice.call(array));\n    }\n    peek() {\n        return this.peekArray(arguments);\n    }\n    peekArray(array) {\n        let node = this;\n        for (let i = 0, len = array.length; node && i < len; ++i) {\n            const map = node.mapFor(array[i], false);\n            node = map && map.get(array[i]);\n        }\n        return node && node.data;\n    }\n    remove() {\n        return this.removeArray(arguments);\n    }\n    removeArray(array) {\n        let data;\n        if (array.length) {\n            const head = array[0];\n            const map = this.mapFor(head, false);\n            const child = map && map.get(head);\n            if (child) {\n                data = child.removeArray(slice.call(array, 1));\n                if (!child.data && !child.weak && !(child.strong && child.strong.size)) {\n                    map.delete(head);\n                }\n            }\n        }\n        else {\n            data = this.data;\n            delete this.data;\n        }\n        return data;\n    }\n    getChildTrie(key) {\n        const map = this.mapFor(key, true);\n        let child = map.get(key);\n        if (!child)\n            map.set(key, child = new Trie(this.weakness, this.makeData));\n        return child;\n    }\n    mapFor(key, create) {\n        return this.weakness && isObjRef(key)\n            ? this.weak || (create ? this.weak = new WeakMap : void 0)\n            : this.strong || (create ? this.strong = new Map : void 0);\n    }\n}\nfunction isObjRef(value) {\n    switch (typeof value) {\n        case \"object\":\n            if (value === null)\n                break;\n        // Fall through to return true...\n        case \"function\":\n            return true;\n    }\n    return false;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\n");

/***/ })

};
;