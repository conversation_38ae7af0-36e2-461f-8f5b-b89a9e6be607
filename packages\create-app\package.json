{"name": "@citeopolis/create-app", "version": "1.0.1", "description": "Create a new Citeopolis application", "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/create-app"}, "type": "module", "bin": {"create-app": "./dist/bin.js"}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "format": "prettier -w .", "prepare": "pnpm run build", "prepublish": "pnpm run build", "test": "vitest run"}, "lint-staged": {"*.{json,md}": "prettier --write", "*.ts": ["eslint --fix", "prettier --write", "vitest related --run"]}, "prettier": "@citeopolis/prettier-config", "dependencies": {"@citeopolis/butcher": "workspace:^", "@clack/prompts": "^0.9.1", "colorette": "^2.0.20", "commander": "^13.0.0", "execa": "^9.5.2", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0"}, "devDependencies": {"@citeopolis/eslint-config": "workspace:^", "@citeopolis/prettier-config": "workspace:^", "@types/fs-extra": "^11.0.4", "@types/node": "^20", "eslint": "^9.31.0", "globby": "^14.1.0", "prettier": "^3", "tsup": "^8.3.5", "typescript": "^5", "vitest": "^3.0.2"}}