@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.imageLink {
  position: relative;
  display: block;
  width: 100%;
  aspect-ratio: 3 / 2;
  text-align: left;
  cursor: pointer;
  background-color: $color-neutral-300;

  // Remove the misused outline (goes under sibling nodes).
  // The overlay serves as a focus indicator here.
  &:focus-visible {
    outline: none;
  }
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  padding: 24px;
  font-size: 1.6rem;
  line-height: 130%;
  color: $color-white;
  background: #0000008c;
  opacity: 0;
  transition: opacity 300ms ease-in-out;

  .imageLink:hover &,
  .imageLink:focus & {
    opacity: 1;
  }

  @include breakpoint(medium up) {
    span {
      display: -webkit-box;
      overflow: hidden;

      // stylelint-disable-next-line -- The caption is fully available in the ligthbox context
      -webkit-line-clamp: 5;
      -webkit-box-orient: vertical;
    }
  }
}

.expandIcon {
  margin-left: auto;
  font-size: 2.4rem;
}

.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 5.6rem;
  color: $color-white;
  transform: translate(-50%, -50%);
}
