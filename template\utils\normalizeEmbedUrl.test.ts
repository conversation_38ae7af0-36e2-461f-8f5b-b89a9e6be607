import { describe, expect, it } from "vitest";
import normalizeEmbedUrl from "./normalizeEmbedUrl";

describe("normalizeEmbedUrl", () => {
  it("converts YouTube long URL to embed", () => {
    const url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ";

    expect(normalizeEmbedUrl(url)).toBe("https://www.youtube.com/embed/dQw4w9WgXcQ");
  });

  it("converts YouTube short URL to embed", () => {
    const url = "https://youtu.be/dQw4w9WgXcQ";

    expect(normalizeEmbedUrl(url)).toBe("https://www.youtube.com/embed/dQw4w9WgXcQ");
  });

  it("converts Vimeo URL to embed", () => {
    const url = "https://vimeo.com/12345678";

    expect(normalizeEmbedUrl(url)).toBe("https://player.vimeo.com/video/12345678");
  });

  it("converts Dailymotion long URL to embed", () => {
    const url = "https://www.dailymotion.com/video/x7tgcz4";

    expect(normalizeEmbedUrl(url)).toBe("https://www.dailymotion.com/embed/video/x7tgcz4");
  });

  it("converts Dailymotion short URL to embed", () => {
    const url = "https://dai.ly/x7tgcz4";

    expect(normalizeEmbedUrl(url)).toBe("https://www.dailymotion.com/embed/video/x7tgcz4");
  });

  it("returns original URL if not recognized", () => {
    const url = "https://example.com/video/12345";

    expect(normalizeEmbedUrl(url)).toBe(url);
  });
});
