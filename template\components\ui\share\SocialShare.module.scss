@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.socialShare {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px;
  align-items: center;
  padding-block: 14px;
  border-top: 1px solid $color-neutral-300;
}

.title {
  font-size: 1.4rem;
  color: $color-neutral-500;

  i {
    margin-right: 8px;
  }
}

.linksContainer {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 10px;
  color: $color-neutral-500;
}

.socialLink {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  aspect-ratio: 1;
  font-size: 1.2rem;
  background-color: $color-neutral-100;
  border: 1px solid $color-neutral-100;
  border-radius: 100%;
  transition:
    color 0.2s ease,
    background-color 0.2s ease,
    border-color 0.2s ease;

  @include on-event {
    border-color: $color-neutral-500;
  }
}
