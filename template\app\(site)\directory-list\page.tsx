import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Item from "@/components/ui/directory-card/DirectoryCard";
import FilterSelection from "@/components/ui/filters/FilterSelection";
import FilterSidebar from "@/components/ui/filters/FilterSidebar";
import Heading from "@/components/ui/heading/Heading";
import Pagination from "@/components/ui/pagination/Pagination";
import PaginationInfo from "@/components/ui/pagination/PaginationInfo";
import { graphql } from "@/generated/graphql";
import { DirectoryFilterInput, SortDirection } from "@/generated/graphql/graphql";
import { createFilterInput } from "@/lib/filters";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import clsx from "clsx";
import { Metadata } from "next";
import assert from "node:assert";
import styles from "./page.module.scss";

const DIRECTORY_LIST_QUERY = graphql(`
  query GetDirectoryDetails($url: URL) {
    route(url: $url) {
      ... on DirectoryList {
        defaultPageSize
        leadText
        proposeUrl
        rssUrl
        title
        url
        viewMode
        type
        breadcrumbs {
          items {
            title
            url
          }
        }
        filters {
          __typename
          attribute
        }
        metadata {
          title
          description
        }
      }
    }
  }
`);

const DIRECTORY_SEARCH_QUERY = graphql(`
  query GetDirectoryList(
    $type: String!
    $filter: DirectoryFilterInput
    $sort: DirectorySortInput
    $pageSize: Int
    $currentPage: Int
  ) {
    directorySearch(type: $type, filter: $filter, sort: $sort, pageSize: $pageSize, currentPage: $currentPage) {
      totalCount
      items {
        id
        categories {
          relativeUrl
          title
        }
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
          ratio_1x1 {
            alt
            height
            url
            width
          }
        }
        location {
          address {
            city
            country
            street
            zip
          }
        }
        openingHours
        offices
        phones {
          deviceType
          number
        }
        title
        url
        viewMode
        website
        email
        accessibility {
          hearingImpairment
          intellectualImpairment
          mentalImpairment
          reducedMobility
          signLanguageReception
          strollers
          visualImpairment
        }
      }
      pageInfo {
        currentPage
        pageSize
        totalPages
      }
      filters {
        __typename
        attribute
        label
        ... on SelectFilter {
          options {
            ...SelectFilterOptionFragment
            children {
              ...SelectFilterOptionFragment
              children {
                ...SelectFilterOptionFragment
              }
            }
          }
          placeholder
        }
      }
    }
  }
  fragment SelectFilterOptionFragment on SelectFilterOption {
    label
    value
    count
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: DIRECTORY_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(data.route?.__typename === "DirectoryList");

  const { metadata } = data.route;

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | null> & { p?: string }>;
}) {
  const { data: directoryList } = await query({
    query: DIRECTORY_LIST_QUERY,
    variables: {
      url: await getCurrentServerUrl(),
    },
  });

  assert.ok(directoryList.route?.__typename === "DirectoryList");

  const {
    title,
    leadText,
    breadcrumbs,
    filters: defaultFilters,
    rssUrl,
    url,
    defaultPageSize,
    type,
    viewMode,
  } = directoryList.route;

  const { p = "", ...params } = await searchParams;
  const currentPage = Number.parseInt(p) || 1;
  const pageSize = defaultPageSize || 10;

  const filterInput = createFilterInput<DirectoryFilterInput>({
    params,
    filters: defaultFilters,
  });

  const { data: directorySearch } = await query({
    query: DIRECTORY_SEARCH_QUERY,
    variables: {
      type,
      pageSize,
      currentPage,
      filter: filterInput,
      sort: {
        publicationDate: SortDirection.DESC,
        title: SortDirection.ASC,
      },
    },
  });

  const {
    items = [],
    pageInfo = { currentPage: 1, pageSize: 1, totalPages: 1 },
    totalCount = 0,
    filters = [],
  } = directorySearch?.directorySearch ?? {};

  return (
    <>
      <Breadcrumbs items={breadcrumbs?.items ?? []} />

      <Heading surtitle="Annuaire" title={title} leadText={leadText} rssUrl={rssUrl} />

      <div className="layout-2columns-left">
        <aside className={clsx("column sidebar", styles.sidebar)}>
          <FilterSidebar
            title={`Filtrer les ${viewMode === "PERSON" ? "personnes" : "structures"}`}
            filters={filters}
            url={url}
          />
        </aside>

        <div className="column main">
          <FilterSelection filters={filters} url={url} />

          <div className={styles.toolbar}>
            <PaginationInfo currentPage={currentPage} pageSize={pageSize} totalCount={totalCount} />
          </div>

          {items.length > 0 && (
            <ol className={styles.list}>
              {items.map((directory) => (
                <li key={directory.id}>
                  <Item directory={directory} />
                </li>
              ))}
            </ol>
          )}

          <Pagination currentPage={pageInfo.currentPage} totalPages={pageInfo.totalPages} />
        </div>
      </div>
    </>
  );
}
