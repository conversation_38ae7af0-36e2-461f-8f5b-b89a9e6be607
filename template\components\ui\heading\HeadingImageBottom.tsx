import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import striptags from "striptags";
import styles from "./HeadingImageBottom.module.scss";

const Tag = dynamic(() => import("@/components/ui/tag/Tag"));

interface HeadingImageBottomProps {
  imageSrc?: string | null;
  leadText?: string | null;
  modifiedDate?: string;
  publicationDate?: string;
  surtitle?: string | null;
  tagsRoleDescription?: string;
  tags?: { text: string; url?: string }[];
  title: string;
}

/**
 * The heading with an image below.
 */
export default function HeadingImageBottom({
  imageSrc,
  leadText,
  modifiedDate,
  publicationDate,
  surtitle,
  tags,
  tagsRoleDescription,
  title,
}: HeadingImageBottomProps) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.content}>
          <h1 className={styles.title}>
            {surtitle && (
              <span className={styles.surtitle}>
                {surtitle}
                <span className="sr-only">:</span>
              </span>
            )}
            {title}
          </h1>
          {leadText && <p className={styles.teaser}>{striptags(leadText)}</p>}
          {tags && tags.length > 0 && (
            <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
              {tags.map((tag, index) => (
                <li key={index}>
                  {tag.url ? (
                    <Tag variant="primary" asChild>
                      <Link href={tag.url}>{tag.text}</Link>
                    </Tag>
                  ) : (
                    <Tag variant="primary">{tag.text}</Tag>
                  )}
                </li>
              ))}
            </ul>
          )}
          {(publicationDate || modifiedDate) && (
            <p className={styles.dates}>
              {publicationDate && (
                <>
                  <span>Publié le </span>
                  <time dateTime={publicationDate}>
                    {new Date(publicationDate).toLocaleDateString("fr-FR", {
                      dateStyle: "long",
                    })}
                  </time>
                </>
              )}
              {modifiedDate && (
                <>
                  <span>Mis à jour le </span>
                  <time dateTime={modifiedDate}>
                    {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                      dateStyle: "long",
                    })}
                  </time>
                </>
              )}
            </p>
          )}
        </div>
        {imageSrc && (
          <div className={styles.image}>
            <Image src={imageSrc} width={1216} height={521} alt="" sizes="(max-width: 767px) 100vw, 1216px" />
          </div>
        )}
      </div>
    </header>
  );
}
