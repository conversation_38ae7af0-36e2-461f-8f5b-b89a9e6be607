@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.list[data-orientation="horizontal"] {
  position: relative;
  display: flex;
  border-bottom: 1px solid $color-neutral-500;

  &::after {
    position: absolute;
    bottom: -1px;
    left: 0;
    width: var(--tab-width, 0);
    height: 4px;
    content: "";
    background-color: $color-primary-500;
    transform: translateX(var(--tab-left, 0));
    transform-origin: left;
    transition:
      width 300ms ease,
      transform 300ms ease;
  }
}

.tabs[data-orientation="vertical"] {
  position: relative;
  display: grid;
  grid-template-columns: 280px 1fr;
}

.list[data-orientation="vertical"] {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 280px;
  border-right: 1px solid $color-neutral-500;

  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    width: 4px;
    height: var(--tab-height, 0);
    content: "";
    background-color: $color-primary-500;
    transform: translateY(var(--tab-top, 0));
    transform-origin: top;
    transition:
      height 300ms ease,
      transform 300ms ease;
  }
}
