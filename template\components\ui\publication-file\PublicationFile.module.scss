@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.publicationFile {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  padding: 24px 16px;
  background: $color-neutral-100;
  border-radius: 4px;

  @container (min-width: 700px) {
    flex-direction: row;
    align-items: center;
    padding: 32px;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.fileIcon {
  font-size: 4rem;
  vertical-align: middle;
  color: $color-primary-500;
}

.fileInfo {
  flex-grow: 1;
  line-height: 110%;
  word-break: break-word;
}

.fileMeta {
  display: flex;
  align-items: center;
  min-height: 32px;
  font-size: 1.2rem;
  line-height: 110%;
  color: $color-neutral-500;
  text-transform: capitalize;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.actions {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  gap: 12px;

  @container (min-width: 1216px) {
    flex-direction: row;
    gap: 16px;
    align-items: center;
  }

  @include breakpoint(large up) {
    justify-content: flex-end;
  }
}

.buttons {
  display: flex;
  gap: 6px;

  @include breakpoint(large up) {
    gap: 8px;
  }

  a {
    flex-grow: 1;
  }
}

.actionButton {
  @include breakpoint(medium up) {
    min-width: 153px;
  }

  @include breakpoint(large up) {
    min-width: 125px;
  }
}
