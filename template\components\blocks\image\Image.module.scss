@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.imageContainer {
  padding-block: 24px;
  margin-block: 12px;

  @include breakpoint(medium up) {
    padding-block: 32px;
    margin-block: 16px;
  }

  figure {
    width: 100%;
    margin: 0;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.imageLink {
  display: block;
  width: 100%;
  cursor: pointer;
}

.imageWrapper {
  max-width: 100%;
}

.image {
  position: relative;
  width: 100%;
  height: 100%;
}

.caption {
  padding-inline: 16px;
  margin-top: 10px;
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;
  word-break: break-word;
  word-wrap: break-word;

  @include breakpoint(medium up) {
    padding-inline: 24px;
    font-size: 1.4rem;
  }

  @include breakpoint(large up) {
    padding-inline: 32px;
  }
}
