import Icon from "@/components/ui/icon/Icon";
import { IconType, QuickAccessBlockFocus } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Link from "next/link";
import styles from "./QuickAccessFocus.module.scss";

type QuickAccessFocusProps = Omit<QuickAccessBlockFocus, "__typename">;

export default function QuickAccessFocus({ title, icon, url }: QuickAccessFocusProps) {
  return (
    <Link href={url} className={styles.quickAccessFocus}>
      {icon && <Icon type={IconType.URL} src={icon.src} className={styles.icon} />}
      <span className={styles.title}>{title}</span>
      <i className={clsx("far fa-arrow-right", styles.arrowIcon)} aria-hidden="true"></i>
    </Link>
  );
}
