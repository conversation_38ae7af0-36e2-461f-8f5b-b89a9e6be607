import ButtonBase from "@/components/ui/button/Button";
import { ButtonBlock } from "@/generated/graphql/graphql";
import Link from "next/link";

type ButtonProps = Partial<Omit<ButtonBlock, "__typename" | "innerBlocks">>;

const buttonVariants = {
  PRIMARY: "contained",
  SECONDARY: "outlined",
  TERTIARY: "text",
} as const;

export default function Button({ html, url, target, width, variant }: ButtonProps) {
  const style = { width: width ? `calc(${width}% - var(--buttons-gap) * ${(100 - width) / 100})` : undefined };

  return (
    <ButtonBase asChild className="block-button" color="primary" variant={buttonVariants[variant ?? "PRIMARY"]}>
      {url ? (
        <Link href={url} target={target ?? undefined} style={style}>
          <span dangerouslySetInnerHTML={{ __html: html ?? "" }} />
        </Link>
      ) : (
        <div style={style}>
          <span dangerouslySetInnerHTML={{ __html: html ?? "" }} />
        </div>
      )}
    </ButtonBase>
  );
}
