@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.errorPage {
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  margin-top: $space-7;
  text-align: start;

  @include breakpoint(medium up) {
    max-width: 80%;
    margin: $space-7 auto;
  }

  @include breakpoint(large up) {
    flex-direction: row-reverse;
    gap: 82px;
    justify-content: space-between;
    max-width: none;
    text-align: left;
  }
}

.content {
  order: -1;

  h1 {
    font-size: 1.6rem;
    line-height: 130%;
    color: $color-primary-500;

    @include breakpoint(medium up) {
      font-size: 1.8rem;
    }

    @include breakpoint(large up) {
      font-size: 2.4rem;
    }
  }

  h2 {
    margin-block: 12px;
    font-size: 4rem;
    font-weight: 700;
    line-height: 100%;
    letter-spacing: -0.8px;

    @include breakpoint(medium up) {
      font-size: 4.8rem;
      letter-spacing: -0.96px;
    }

    @include breakpoint(large up) {
      margin-block: 16px;
      font-size: 6.4rem;
      letter-spacing: -1.28px;
    }

    em {
      font-style: normal;
      color: $color-primary-500;
    }
  }

  p {
    max-width: 505px;
    margin-block: 12px;
    font-size: 1.6rem;
    line-height: 150%;

    @include breakpoint(medium up) {
      margin-block: 16px;
      font-size: 1.8rem;
    }
  }
}

.returnLink {
  display: inline-block;
  font-size: 1.6rem;
  line-height: 120%;
  color: $color-primary-500;
  text-decoration: underline;
  cursor: pointer;

  i {
    margin-right: 3px;
    font-size: 1.4rem;
  }
}

.illustration {
  flex-shrink: 0;
  width: 100%;
  max-width: 396px;

  @include breakpoint(medium up) {
    max-width: 538px;
  }
}

.searchForm {
  position: relative;
  display: flex;
  padding: 16px 24px;
  margin-top: 32px;
  background-color: $color-white;
  border-radius: 4px;
  box-shadow: 0 4px 20px 0 rgb(0 0 0 / 15%);

  @include breakpoint(medium up) {
    margin-top: 40px;
  }

  @include breakpoint(large up) {
    margin-top: 48px;
  }

  // Transfer the focus indicator to the search form
  .searchInput input:focus-visible {
    outline: none;
  }

  &:has(.searchInput input:focus-visible) {
    outline: $focus-outline;
    outline-offset: $focus-outline-offset;
  }

  .searchButton {
    pointer-events: all;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      color: $color-primary-400;
    }
  }
}

.contact {
  margin-block: $space-7;
  font-size: 1.2rem;
  line-height: 110%;
  color: $color-neutral-500;
  text-align: center;

  a {
    text-decoration-line: underline;
    text-decoration-thickness: 8%;
    text-decoration-style: solid;
    text-underline-offset: 13.5%;
    text-decoration-skip-ink: auto;
    cursor: pointer;
  }

  @include breakpoint(medium up) {
    display: block;
    max-width: 80%;
    margin: 50px auto;
    font-size: 1.4rem;
  }
}
