"use client";

import Collapsible from "@/components/ui/collapsible/Collapsible";
import { AudioBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./Audio.module.scss";

type AudioProps = Partial<Omit<AudioBlock, "__typename" | "innerBlocks">>;

export default function Audio({ anchor, src, caption, transcription, preload }: AudioProps) {
  return (
    <figure id={anchor ?? undefined} className={clsx("block-audio contained", styles.audio)}>
      {caption && <figcaption className={styles.caption}>{caption}</figcaption>}
      <audio src={src} controls className={styles.element} preload={preload ?? "none"} />
      {transcription && (
        <Collapsible
          className={styles.transcription}
          icon={<i className="far fa-subtitles" aria-hidden="true"></i>}
          label="Transcription"
          noHeading={true}
        >
          {transcription.split("\n").map((line, index) => (
            <p key={index}>{line}</p>
          ))}
        </Collapsible>
      )}
    </figure>
  );
}
