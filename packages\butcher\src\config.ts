import { readFile } from "node:fs/promises";
import path from "node:path";
import butcher, { <PERSON> } from "./butcher";
import { FeaturesListSchema } from "./schema";
import { logger } from "./utils/logger";

export async function loadConfigFile(configFile: string): Promise<Butcher> {
  const projectRoot = path.resolve(path.dirname(configFile));

  const jsonString = await readFile(path.resolve(configFile), "utf8");

  const featuresData = JSON.parse(jsonString);

  const { data: features, error, success } = FeaturesListSchema.safeParse(featuresData);

  if (!success) {
    logger.break();

    logger.error("Feature validation failed for 'butcher.json':");

    for (const issue of error.issues) {
      const path = issue.path.join(".") || "[root]";

      logger.error(`  - Path: ${path}, Message: ${issue.message}`);
    }

    // eslint-disable-next-line unicorn/no-process-exit -- TODO: Throw an error
    process.exit(1);
  }

  return butcher(projectRoot, features);
}
