"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { TextAreaField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type TextAreaProps = Omit<TextAreaField, "__typename">;

//TODO if wysiwyg => tinymce-react
export default function TextArea({
  autocomplete,
  condition,
  defaultValue,
  description,
  label,
  name,
  placeholder,
  required,
  columnSpan,
}: TextAreaProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name);
  const inputId = useId();
  const errorId = useId();

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>
        <Textfield
          id={inputId}
          startIcon="far fa-envelope"
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          placeholder={placeholder ?? undefined}
          required={required}
          multiline
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
          {...register(name, { required })}
        />
        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}{" "}
      </FormControl>
    )
  );
}
