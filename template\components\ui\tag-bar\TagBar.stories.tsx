import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import TagBar from "./TagBar";

const meta: Meta<typeof TagBar> = {
  title: "Components/TagBar",
  component: TagBar,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof TagBar>;

export const Default: Story = {
  args: {
    items: [
      { text: "Quae dum ita struuntur", url: "#" },
      { text: "Indicatum est apud", url: "#" },
      { text: "Tyrum indumentum regale", url: "#" },
      { text: "Incertum quo locante", url: "#" },
      { text: "Vel cuius usibus", url: "#" },
      { text: "Apparatum", url: "#" },
    ],
  },
};
