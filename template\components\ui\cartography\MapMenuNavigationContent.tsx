import { Image as ImageType, Link as LinkType, MenuItem } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./MapMenuNavigationContent.module.scss";

interface MapMenuProps {
  logoDark: ImageType | null;
  quickAccess2: PartialDeep<LinkType, { recurseIntoArrays: true }>[];
  mapLinks: PartialDeep<LinkType, { recurseIntoArrays: true }>[];
  menuItems?: PartialDeep<MenuItem, { recurseIntoArrays: true }>[];
}

export default function MapMenuNavigationContent({ logoDark, quickAccess2, mapLinks, menuItems }: MapMenuProps) {
  return (
    <div className={styles.menuContent}>
      <div className={styles.top}>
        <header className={styles.header}>
          <div className={styles.clientLogo}>
            {logoDark?.url && (
              <Image
                src={logoDark?.url}
                width={logoDark?.width}
                height={logoDark?.height}
                alt={logoDark?.alt ?? "Logo"}
                className={styles.logoImage}
              />
            )}
          </div>
        </header>

        <nav role="navigation" aria-label="Cartographies disponibles" className={styles.section}>
          <h3 className={styles.sectionTitle}>Toutes les cartographies :</h3>
          <ul className={styles.mapTypeList}>
            {mapLinks?.map(
              ({ icon, text, url }, index) =>
                url && (
                  <li className={styles.mapTypeItem} key={index}>
                    <Link className={styles.mapTypeLink} href={url} aria-label={text}>
                      {icon?.src && <i className={clsx(icon.src, styles.mapTypeIcon)} aria-hidden="true" />}
                      {text}
                    </Link>
                  </li>
                )
            )}
          </ul>
        </nav>
      </div>

      <div className={styles.bottom}>
        <nav role="navigation" aria-label="Menu principal du site" className={styles.wrapper}>
          <details className={styles.menu}>
            <summary className={styles.trigger}>
              <i className="far fa-caret-right" aria-hidden="true"></i>
              <h3 className={styles.title}>Menu</h3>
            </summary>
            <div className={styles.content}>
              <ul>
                {menuItems?.map(
                  ({ title, url }, index) =>
                    url && (
                      <li key={index}>
                        <Link href={url} className={styles.navLink} aria-label={title}>
                          {title}
                        </Link>
                      </li>
                    )
                )}
              </ul>
            </div>
          </details>

          {quickAccess2 && quickAccess2.length > 0 && (
            <ul className={styles.navList}>
              {quickAccess2.map(
                (link, index) =>
                  link?.url && (
                    <li key={index}>
                      <Link
                        href={link.url}
                        className={styles.navLink}
                        target={link.target ?? undefined}
                        rel={link.rel ?? undefined}
                        aria-label={link.text}
                      >
                        {link.text}
                      </Link>
                    </li>
                  )
              )}
            </ul>
          )}
        </nav>

        <footer className={styles.footer}>
          <Link href="/" className={styles.backToSiteLink}>
            <i className="far fa-arrow-left" aria-hidden="true"></i>
            <span>Retour au site</span>
          </Link>
        </footer>
      </div>
    </div>
  );
}
