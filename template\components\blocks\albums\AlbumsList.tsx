import type { Album } from "@/generated/graphql/graphql";
import AlbumsItem from "./AlbumsItem";
import styles from "./AlbumsList.module.scss";

interface AlbumsListProps {
  items: Album[];
}

export default function AlbumsList({ items }: AlbumsListProps) {
  return (
    <ul className={styles.albumList}>
      {items.map((album, index) => (
        <li key={index}>
          <AlbumsItem album={album} />
        </li>
      ))}
    </ul>
  );
}
