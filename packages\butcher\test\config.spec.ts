import { mkdtemp, rm, writeFile } from "node:fs/promises";
import { tmpdir } from "node:os";
import path from "node:path";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { loadConfigFile } from "../src/config";
import { logger } from "../src/utils/logger";

describe("loadConfigFile", () => {
  let tmpDir: string;

  beforeEach(async () => {
    tmpDir = await mkdtemp(path.join(tmpdir(), "butcher-test-"));
  });

  afterEach(async () => {
    rm(tmpDir, { recursive: true, force: true });
    vi.clearAllMocks();
  });

  it("loads a valid butcher.json successfully", async () => {
    const validConfig = [{ name: "featureA", files: ["fileA.ts"] }, { name: "featureB" }];

    const configPath = path.join(tmpDir, "butcher.json");

    await writeFile(configPath, JSON.stringify(validConfig), "utf8");

    const butcherInstance = await loadConfigFile(configPath);

    expect(typeof butcherInstance.execute).toBe("function");
  });

  it("throws and logs errors for invalid butcher.json", async () => {
    const invalidConfig = [{ wrongProp: "noName" }];
    const configPath = path.join(tmpDir, "butcher.json");

    await writeFile(configPath, JSON.stringify(invalidConfig), "utf8");

    const exitSpy = vi.spyOn(process, "exit").mockImplementation(() => {
      throw new Error("process.exit called");
    });

    await expect(loadConfigFile(configPath)).rejects.toThrow("process.exit called");

    expect(logger.error).toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith(expect.stringContaining("Feature validation failed"));

    exitSpy.mockRestore();
  });

  it("throws if file does not exist", async () => {
    const missingPath = path.join(tmpDir, "missing.json");

    await expect(loadConfigFile(missingPath)).rejects.toThrow();
  });
});
