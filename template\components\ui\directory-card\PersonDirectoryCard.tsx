"use client";

import { formatAddress } from "@/components/ui/address/Address";
import Button from "@/components/ui/button/Button";
import Hx from "@/components/ui/title/Hx";
import type { Directory, PhoneDeviceType } from "@/generated/graphql/graphql";
import useTitleLevel from "@/lib/hooks/useTitleLevel";
import formatPhone from "@/utils/formatPhone";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import { PartialDeep } from "type-fest";
import styles from "./PersonDirectoryCard.module.scss";

interface PersonDirectoryCardProps {
  directory: PartialDeep<Directory, { recurseIntoArrays: true }> & Pick<Directory, "viewMode">;
}

const deviceIcons: Partial<Record<PhoneDeviceType, string>> = {
  LANDLINE: "fa-phone",
  MOBILE: "fa-mobile",
};

export default function PersonDirectoryCard({
  directory: { title, images, phones, email, categories, url, website, offices, location },
}: PersonDirectoryCardProps) {
  const image = images?.ratio_1x1 ?? null;
  const [category] = categories ?? [];
  const titleLevel = useTitleLevel();
  const formattedAddress = formatAddress(location?.address);

  return (
    <article className={styles.directory}>
      <div className={styles.contentWrapper}>
        <div className={styles.content}>
          <Hx level={titleLevel} className={styles.title}>
            {category && (
              <span className={styles.category}>
                {category.title}
                <span className="sr-only">:</span>
              </span>
            )}
            {url ? (
              <Link href={url} className={styles.titleLink}>
                {title}
              </Link>
            ) : (
              title
            )}
          </Hx>
          {offices && offices.length > 0 && (
            <ul className={styles.roles} aria-roledescription="Fonctions">
              {offices.map((office, index) => (
                <li key={index}>{office}</li>
              ))}
            </ul>
          )}
          {formattedAddress && (
            <p className={styles.info}>
              <strong className={styles.label}>Adresse :</strong>
              {formattedAddress}
            </p>
          )}
        </div>
        {image && image.url && (
          <Image className={styles.image} src={image.url} alt={image.alt ?? ""} width={176} height={176} />
        )}
      </div>
      {((phones && phones.some((phone) => phone?.number)) || email || website) && (
        <div className={styles.actions}>
          {phones?.map((phone, index) => {
            return (
              phone?.number && (
                <Button
                  key={index}
                  asChild
                  variant="outlined"
                  startIcon={
                    phone.deviceType && deviceIcons[phone.deviceType] && clsx("far", deviceIcons[phone.deviceType])
                  }
                >
                  <a href={`tel:${phone.number}`}>{formatPhone(phone.number)}</a>
                </Button>
              )
            );
          })}
          {email && (
            <Button asChild variant="outlined" startIcon="far fa-at">
              <a href={`mailto:${email}`}>Courriel</a>
            </Button>
          )}
          {website && (
            <Button
              className={styles.websiteButton}
              asChild
              variant="contained"
              startIcon="fas fa-arrow-up-right-from-square"
            >
              <a href={website} target="_blank" rel="noreferrer">
                Site internet
              </a>
            </Button>
          )}
        </div>
      )}
    </article>
  );
}
