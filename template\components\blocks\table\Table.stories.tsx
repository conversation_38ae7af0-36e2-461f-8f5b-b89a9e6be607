import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Table from "./Table";

const meta: Meta<typeof Table> = {
  title: "Blocks/Table",
  component: Table,
  tags: ["autodocs"],
};

// Generates a type safe table cell
function _(html: string, options?: { rowspan?: number; colspan?: number }) {
  const { rowspan = null, colspan = null } = options ?? {};
  return { html, rowspan, colspan };
}

export default meta;

type Story = StoryObj<typeof Table>;

export const Default: Story = {
  args: {
    caption: "Lorem ipsum",
    header: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
    footer: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
  },
};

export const FirstColumnHeader: Story = {
  args: {
    caption: "Lorem ipsum",
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
    footer: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    firstColumnHeader: true,
  },
};

export const BothHeaders: Story = {
  args: {
    caption: "Lorem ipsum",
    header: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
    footer: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    firstColumnHeader: true,
  },
};

export const BothHeaders2: Story = {
  args: {
    caption: "Lorem ipsum",
    header: [_(""), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
    footer: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    firstColumnHeader: true,
  },
};

export const Fixed: Story = {
  args: {
    caption: "Lorem ipsum",
    header: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Proin gravita lorem ipsum dolor sit amet")],
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
    footer: [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    fixedLayout: true,
  },
};

export const VeryLarge: Story = {
  args: {
    caption: "Lorem ipsum",
    cells: [
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
      [_("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum"), _("Lorem ipsum")],
    ],
  },
};

export const RichContent: Story = {
  args: {
    caption: "Lorem ipsum",
    header: [_("<strong>Lorem</strong> ipsum"), _("Lorem <em>ipsum</em>"), _('<a href="#">Lorem ipsum</a>')],
    cells: [
      [_("<strong>Lorem</strong> ipsum"), _("Lorem <em>ipsum</em>"), _('<a href="#">Lorem ipsum</a>')],
      [_("<strong>Lorem</strong> ipsum"), _("Lorem <em>ipsum</em>"), _('<a href="#">Lorem ipsum</a>')],
    ],
    footer: [_("<strong>Lorem</strong> ipsum"), _("Lorem <em>ipsum</em>"), _('<a href="#">Lorem ipsum</a>')],
  },
};

export const MergeColumns: Story = {
  args: {
    caption: "Fusion de deux colonnes (colspan)",
    header: [_("Nom"), _("Âge"), _("Ville")],
    cells: [
      [_("Jean Dupont"), _("35"), _("Paris")],
      [_("Fusionné : Email + Téléphone", { colspan: 2 }), _("Lyon")],
      [_("Sophie Martin"), _("28"), _("Marseille")],
    ],
  },
};

export const MergeRows: Story = {
  args: {
    caption: "Fusion de deux lignes (rowspan)",
    header: [_("Jour"), _("Heure"), _("Activité")],
    cells: [
      [_("Lundi", { rowspan: 2 }), _("09:00"), _("Réunion d'équipe")],
      [_("14:00"), _("Développement")],
      [_("Mardi"), _("10:00"), _("Formation")],
    ],
  },
};

// New story to demonstrate header/footer with colspan/rowspan
export const HeaderFooterSpanning: Story = {
  args: {
    caption: "Header and footer with colspan/rowspan support",
    header: [_("Name"), _("Contact Info", { colspan: 2 }), _("Location")],
    cells: [
      [_("Jean Dupont"), _("<EMAIL>"), _("555-1234"), _("Paris")],
      [_("Sophie Martin"), _("<EMAIL>"), _("555-5678"), _("Lyon")],
      [_("Pierre Durand"), _("<EMAIL>"), _("555-9012"), _("Marseille")],
    ],
    footer: [_("Total: 3 people", { colspan: 2 }), _("Average age: 31"), _("3 cities")],
  },
};
