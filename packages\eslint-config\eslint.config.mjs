import comments from "@eslint-community/eslint-plugin-eslint-comments";
import eslintReact from "@eslint-react/eslint-plugin";
import { FlatCompat } from "@eslint/eslintrc";
import stylistic from "@stylistic/eslint-plugin";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import unicorn from "eslint-plugin-unicorn";
import globals from "globals";
import tseslint from "typescript-eslint";

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
});

export default tseslint.config(
  // Common files
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    extends: [
      ...tseslint.configs.recommended,
      unicorn.configs.all,
      ...compat.extends("plugin:@creedengo/recommended"),
      ...compat.extends("plugin:clsx/recommended"),
    ],
    plugins: {
      "@eslint-community/eslint-comments": comments,
      "@stylistic": stylistic,
    },
    rules: {
      // Force developers to document why they disabled a certain rule
      "@eslint-community/eslint-comments/require-description": ["error", { ignore: ["eslint-enable"] }],
    },
  },
  // React (JSX) files
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    extends: [
      react.configs.flat["recommended"],
      eslintReact.configs["recommended-typescript"],
      reactHooks.configs["recommended-latest"],
      ...compat.extends("plugin:jsx-a11y/strict"),
    ],
    plugins: {
      react,
      reactHooks,
    },
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
      globals: {
        ...globals.browser,
      },
    },
    settings: {
      react: {
        version: "detect",
      },
    },
    rules: {
      // Enforce component definition as function
      "react/function-component-definition": "error",
      // Error only on certain entities
      "react/no-unescaped-entities": ["error", { forbid: [">", "}"] }],
      // Throw error if component name is malformed
      "@eslint-react/naming-convention/component-name": "error",
      // Disable `<track>` element requirement on the `<audio>` and `<video>` elements.
      "jsx-a11y/media-has-caption": "off",
      // Allow some redundant roles for compatibility with old screen readers.
      "jsx-a11y/no-redundant-roles": [
        "error",
        {
          nav: ["navigation"],
          header: ["banner"],
          main: ["main"],
          footer: ["contentinfo"],
          figure: ["figure"],
          aside: ["complementary"],
        },
      ],
      // React is already imported in Next.js context
      "react/react-in-jsx-scope": "off",
      // Duplicate with `unicorn/filename-case`
      "@eslint-react/naming-convention/filename": "off",
      // Disabled because defined specifically for each types
      "unicorn/filename-case": "off",
      // Removed `class` from the list since `className` is a React keyword
      "unicorn/no-keyword-prefix": ["error", { checkProperties: true, disallowedPrefixes: ["new", "custom"] }],
      // Allow `null` because it is used in GraphQL types
      "unicorn/no-null": "off",
      // Allow some abbreviations (used in react/next).
      "unicorn/prevent-abbreviations": [
        "warn",
        {
          ignore: [/.*Props$/, /^props$/, /.*Params$/, /^params$/, /^useRef$/, /^args$/, /.*Ref$/, /^src$/, /^prev$/],
        },
      ],
      // Disable forced destructuring because of graphql object depth
      "unicorn/consistent-destructuring": "off",
      // Disabled because buggy with react. It triggers on each querySelector.
      "@creedengo/no-multiple-access-dom-element": "off",
      // Code styles
      "@stylistic/padding-line-between-statements": [
        "error",
        // JSCS: requirePaddingNewLineAfterVariableDeclaration
        { blankLine: "always", prev: ["const", "let", "var"], next: "*" },
        { blankLine: "any", prev: ["const", "let", "var"], next: ["const", "let", "var", "return", "continue"] },
        // JSCS: requirePaddingNewLinesAfterBlocks
        { blankLine: "always", prev: "block-like", next: "*" },
        { blankLine: "never", prev: "case", next: ["case", "default"] },
        // JSCS: requirePaddingNewLinesAfterUseStrict
        { blankLine: "always", prev: "directive", next: "*" },
        // JSCS: disallowPaddingNewLinesAfterUseStrict
        { blankLine: "never", prev: "directive", next: "directive" },
        // JSCS: requirePaddingNewLinesBeforeExport
        { blankLine: "always", prev: "*", next: "export" },
        // JSCS: requirePaddingNewlinesBeforeKeywords
        { blankLine: "always", prev: "*", next: ["if", "for", "while", "do", "switch", "try"] },
      ],
    },
  },
  // React components
  {
    files: ["**/*.{jsx,tsx}"],
    rules: {
      // Enforce filename convention
      "unicorn/filename-case": ["error", { case: "pascalCase" }],
    },
  },
  // React App pages
  {
    files: ["app/**/{page,layout,route,not-found,error}.{ts,tsx}"],
    rules: {
      // Enforce filename convention
      "unicorn/filename-case": ["error", { case: "kebabCase" }],
    },
  },
  // React hooks
  {
    files: ["**/use*.{ts,tsx}"],
    rules: {
      // Enforce filename convention
      "unicorn/filename-case": ["error", { case: "camelCase" }],
    },
  }
);
