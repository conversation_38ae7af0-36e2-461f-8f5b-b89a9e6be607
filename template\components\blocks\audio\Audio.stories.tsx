import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Audio from "./Audio";

const meta: Meta<typeof Audio> = {
  title: "Blocks/Audio",
  component: Audio,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Audio>;

export const Default: Story = {
  args: {
    caption: "Never Gonna Give You Up - Rick <PERSON>",
    src: "/assets/never-gonna-give-you-up.ogg",
  },
};

export const Transcription: Story = {
  args: {
    caption: "Never Gonna Give You Up - Rick <PERSON>tley",
    transcription:
      "Never gonna give you up, never gonna let you down\nNever gonna run around and desert you\nNever gonna make you cry, never gonna say goodbye\nNever gonna tell a lie and hurt you",
    src: "/assets/never-gonna-give-you-up.ogg",
  },
};
