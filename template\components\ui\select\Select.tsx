import clsx from "clsx";
import styles from "./Select.module.scss";

interface SelectProps {
  variant?: "outlined" | "underline" | "filled";
  error?: boolean;
  placeholder?: string;
}

export default function Select({
  variant = "outlined",
  error,
  placeholder,
  children,
  ...restProps
}: React.PropsWithChildren<SelectProps> & React.SelectHTMLAttributes<HTMLSelectElement>) {
  return (
    <select
      className={clsx(styles.select, variant && styles[`variant-${variant}`], error && styles.error)}
      {...restProps}
    >
      {typeof placeholder === "string" && <option value="">{placeholder}</option>}
      {children}
    </select>
  );
}
