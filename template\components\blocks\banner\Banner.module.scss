@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.bannerContainer {
  padding-block: 24px;
  margin-block: 12px;

  @include breakpoint(medium up) {
    padding-block: 32px;
    margin-block: 16px;
  }

  &:global(.contained) {
    max-width: 1008px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.banner {
  display: flex;
  flex-flow: column wrap;
  align-items: stretch;
  justify-content: flex-start;
  overflow: hidden;
  border-radius: 4px;

  @include breakpoint(medium up) {
    flex-direction: row;
  }

  @include breakpoint(large up) {
    margin-bottom: 160px;
  }

  &:last-child {
    margin-bottom: 0;
  }

  &.variant-image_left,
  &.variant-image_right {
    align-items: stretch;
    justify-content: space-between;
  }

  &.variant-image_left {
    flex-direction: column-reverse;

    @include breakpoint(medium up) {
      flex-direction: row-reverse;
    }
  }

  .content {
    display: flex;
    flex: 1 1 50%;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    min-height: 148px;
    padding: 12px 24px;
    color: $color-white;
    background-color: $color-primary-500;

    @include breakpoint(medium up) {
      min-height: 155px;
      padding: 16px 32px;
    }

    @include breakpoint(large up) {
      min-height: 100%;
      padding: 16px 56px;
    }

    .title {
      font-size: 1.6rem;
      font-weight: $fw-bold;
      line-height: 110%;

      @include breakpoint(medium up) {
        font-size: 1.8rem;
      }

      @include breakpoint(large up) {
        font-size: 2.4rem;
      }
    }

    .description {
      margin-top: 6px;
      font-size: 1.2rem;
      line-height: 110%;

      @include breakpoint(large up) {
        margin-top: 8px;
        font-size: 1.4rem;
      }
    }

    .actionLink {
      gap: 4px;
      margin-top: 12px;
      font-size: 1.2rem;
      text-decoration: underline currentcolor;
      text-underline-offset: 4px;

      @include breakpoint(medium up) {
        font-size: 1.4rem;
      }

      @include breakpoint(large up) {
        margin-top: 16px;
      }

      i {
        margin-left: 1ch;
        font-size: 1.2rem;
      }
    }
  }

  &.variant-text_only {
    .content {
      min-height: 155px;
      padding: 12px 24px;

      @include breakpoint(medium up) {
        padding: 16px 40px;
      }

      @include breakpoint(large up) {
        min-height: 245px;
        padding: 16px 72px;
      }
    }
  }

  .imageWrapper {
    position: relative;
    flex: 1 1 50%;
    overflow: hidden;

    img {
      @include object-fit;

      width: 100%;
      min-height: 148px;

      @include breakpoint(medium up) {
        min-height: 155px;
      }

      @include breakpoint(large up) {
        min-height: 216px;
      }
    }
  }
}
