@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.newsItem {
  position: relative;
  display: flex;
  flex-direction: row-reverse;
  gap: 12px 16px;

  @include breakpoint(medium up) {
    flex-direction: column-reverse;
  }
}

.content {
  flex-grow: 1;
  margin-top: 0;

  @include breakpoint(medium up) {
    margin-top: 6px;
  }

  @include breakpoint(large up) {
    margin-top: 8px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.image {
  @include max-size(74px, 50px);

  flex-shrink: 0;
  width: 100%;
  margin-bottom: 0;

  @include breakpoint(medium up) {
    @include max-size(100%);
  }
}
