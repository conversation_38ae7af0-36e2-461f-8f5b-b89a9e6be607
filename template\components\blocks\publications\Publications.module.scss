@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.publications {
  margin-block: 96px;
  container: block-publications / inline-size;

  @include breakpoint(large up) {
    margin-block: 160px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.title {
  margin-bottom: 12px;
  font-size: 4rem;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;

  @include breakpoint(medium up) {
    font-size: 4.8rem;
    letter-spacing: -0.96px;
  }

  @include breakpoint(large up) {
    margin-bottom: 16px;
    font-size: 6.4rem;
    letter-spacing: -1.28px;
  }
}

.actions {
  margin-top: 12px;

  @include breakpoint(large up) {
    margin-top: 16px;
  }

  a {
    width: 100%;

    @include breakpoint(large up) {
      width: auto;
    }
  }
}
