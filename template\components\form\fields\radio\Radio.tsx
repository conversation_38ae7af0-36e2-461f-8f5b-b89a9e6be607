"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import RadioInput from "@/components/ui/radio/Radio";
import { RadioField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";
import styles from "./Radio.module.scss";

type RadioProps = Omit<RadioField, "__typename">;

export default function Radio({ choices, label, description, name, condition, columnSpan, required }: RadioProps) {
  const { register } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name);
  const legendId = useId();
  const errorId = useId();

  if (!visible) return null;

  return (
    <FormControl spacing="dense" aria-labelledby={legendId} columnSpan={columnSpan}>
      <Label asChild id={legendId} description={description ?? undefined} required={required}>
        {label}
      </Label>

      <div>
        {choices?.map((choice, index) => {
          if (choice) {
            return (
              <label className={styles.labelledRadio} key={choice.value ?? index}>
                <RadioInput
                  value={choice.value ?? ""}
                  defaultChecked={choice.defaultSelected ?? false}
                  error={!!error}
                  aria-describedby={error ? errorId : undefined}
                  aria-invalid={error ? true : undefined}
                  {...register(name)}
                />
                <span>{choice.label}</span>
              </label>
            );
          }
        })}
      </div>

      {error && (
        <FormHelper id={errorId} variant="error">
          {error.message?.toString()}
        </FormHelper>
      )}
    </FormControl>
  );
}
