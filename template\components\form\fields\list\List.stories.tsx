import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import List from "./List";

const meta: Meta<typeof List> = {
  title: "Form/List",
  component: List,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof List>;

export const Default: Story = {
  args: {
    name: "list_1",
    label: "Liste de tâches",
    description: "Ajoutez des éléments à votre liste",
  },
};

export const WithMaxRows: Story = {
  args: {
    name: "list_2",
    label: "Top 3 des priorités",
    description: "Maximum 3 éléments autorisés",
    maxRows: 3,
    required: true,
  },
};

export const WithColumns: Story = {
  args: {
    name: "list_3",
    label: "Expériences professionnelles",
    description: "Listez vos expériences (poste, entreprise, durée)",
    enableColumns: true,
    choices: [
      {
        label: "Poste",
        value: "position",
      },
      {
        label: "Entreprise",
        value: "company",
      },
      {
        label: "Durée",
        value: "duration",
      },
    ],
  },
};

export const ContactList: Story = {
  args: {
    name: "contacts",
    label: "Liste de contacts",
    description: "Ajoutez des contacts d'urgence",
    enableColumns: true,
    maxRows: 5,
    choices: [
      {
        label: "Nom",
        value: "name",
      },
      {
        label: "Téléphone",
        value: "phone",
      },
      {
        label: "Relation",
        value: "relation",
      },
    ],
  },
};

export const SimpleList: Story = {
  args: {
    name: "shopping_list",
    label: "Liste de courses",
    description: "Ajoutez des articles à votre liste",
    maxRows: 10,
  },
};
