/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_ui_alert_StickyNote_tsx";
exports.ids = ["_ssr_components_ui_alert_StickyNote_tsx"];
exports.modules = {

/***/ "(ssr)/./components/ui/alert/StickyNote.module.scss":
/*!****************************************************!*\
  !*** ./components/ui/alert/StickyNote.module.scss ***!
  \****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"stickyNote\": \"StickyNote_stickyNote__nJ0xY\",\n\t\"container\": \"StickyNote_container__xgoqR\",\n\t\"color-primary\": \"StickyNote_color-primary__tUPMI\",\n\t\"color-secondary\": \"StickyNote_color-secondary__uAPNv\",\n\t\"color-tertiary\": \"StickyNote_color-tertiary__eYvxg\",\n\t\"color-danger\": \"StickyNote_color-danger__P6MDR\",\n\t\"topIcon\": \"StickyNote_topIcon__piP5B\",\n\t\"wrapper\": \"StickyNote_wrapper__6n7M8\",\n\t\"content\": \"StickyNote_content__EzB7Q\",\n\t\"action\": \"StickyNote_action__IvO1F\",\n\t\"header\": \"StickyNote_header__bE37W\",\n\t\"title\": \"StickyNote_title__SDREt\",\n\t\"description\": \"StickyNote_description__J800i\",\n\t\"close\": \"StickyNote_close__x_3S3\"\n};\n\nmodule.exports.__checksum = \"ecb710714787\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2FsZXJ0L1N0aWNreU5vdGUubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcdGVtcGxhdGVcXGNvbXBvbmVudHNcXHVpXFxhbGVydFxcU3RpY2t5Tm90ZS5tb2R1bGUuc2NzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJzdGlja3lOb3RlXCI6IFwiU3RpY2t5Tm90ZV9zdGlja3lOb3RlX19uSjB4WVwiLFxuXHRcImNvbnRhaW5lclwiOiBcIlN0aWNreU5vdGVfY29udGFpbmVyX194Z29xUlwiLFxuXHRcImNvbG9yLXByaW1hcnlcIjogXCJTdGlja3lOb3RlX2NvbG9yLXByaW1hcnlfX3RVUE1JXCIsXG5cdFwiY29sb3Itc2Vjb25kYXJ5XCI6IFwiU3RpY2t5Tm90ZV9jb2xvci1zZWNvbmRhcnlfX3VBUE52XCIsXG5cdFwiY29sb3ItdGVydGlhcnlcIjogXCJTdGlja3lOb3RlX2NvbG9yLXRlcnRpYXJ5X19lWXZ4Z1wiLFxuXHRcImNvbG9yLWRhbmdlclwiOiBcIlN0aWNreU5vdGVfY29sb3ItZGFuZ2VyX19QNk1EUlwiLFxuXHRcInRvcEljb25cIjogXCJTdGlja3lOb3RlX3RvcEljb25fX3BpUDVCXCIsXG5cdFwid3JhcHBlclwiOiBcIlN0aWNreU5vdGVfd3JhcHBlcl9fNm43TThcIixcblx0XCJjb250ZW50XCI6IFwiU3RpY2t5Tm90ZV9jb250ZW50X19FekI3UVwiLFxuXHRcImFjdGlvblwiOiBcIlN0aWNreU5vdGVfYWN0aW9uX19Jdk8xRlwiLFxuXHRcImhlYWRlclwiOiBcIlN0aWNreU5vdGVfaGVhZGVyX19iRTM3V1wiLFxuXHRcInRpdGxlXCI6IFwiU3RpY2t5Tm90ZV90aXRsZV9fU0RSRXRcIixcblx0XCJkZXNjcmlwdGlvblwiOiBcIlN0aWNreU5vdGVfZGVzY3JpcHRpb25fX0o4MDBpXCIsXG5cdFwiY2xvc2VcIjogXCJTdGlja3lOb3RlX2Nsb3NlX194XzNTM1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJlY2I3MTA3MTQ3ODdcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert/StickyNote.module.scss\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert/StickyNote.tsx":
/*!********************************************!*\
  !*** ./components/ui/alert/StickyNote.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   STICKY_NOTE_CLOSED_KEY: () => (/* binding */ STICKY_NOTE_CLOSED_KEY),\n/* harmony export */   \"default\": () => (/* binding */ StickyNote)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var usehooks_ts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! usehooks-ts */ \"(ssr)/../node_modules/.pnpm/usehooks-ts@3.1.1_react@19.1.1/node_modules/usehooks-ts/dist/index.js\");\n/* harmony import */ var _StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StickyNote.module.scss */ \"(ssr)/./components/ui/alert/StickyNote.module.scss\");\n/* harmony import */ var _StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ STICKY_NOTE_CLOSED_KEY,default auto */ \n\n\n\n\n\n\nconst Button = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_ssr_components_ui_button_Button_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/ui/button/Button */ \"(ssr)/./components/ui/button/Button.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ui\\\\alert\\\\StickyNote.tsx -> \" + \"@/components/ui/button/Button\"\n        ]\n    }\n});\nconst STICKY_NOTE_CLOSED_KEY = \"citeopolis:sticky-note:closed\";\nfunction getStickyNoteKey({ id, modifiedDate }) {\n    return `sticky-note|${id}|${modifiedDate}`;\n}\nfunction StickyNote({ alert: { id, title, description, action, modifiedDate } }) {\n    const [isClosed, setIsClosed] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const [closedStickyNoteKey, setClosedStickyNoteKey] = (0,usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useLocalStorage)(STICKY_NOTE_CLOSED_KEY, \"\");\n    const titleId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const alertKey = getStickyNoteKey({\n        id,\n        modifiedDate\n    });\n    const isClient = (0,usehooks_ts__WEBPACK_IMPORTED_MODULE_6__.useIsClient)();\n    if (!isClient || closedStickyNoteKey === alertKey || isClosed) {\n        return null;\n    }\n    const handleCloseAlert = ()=>{\n        setClosedStickyNoteKey(alertKey);\n        setIsClosed(true);\n        const siteWrapper = document.querySelector(\".site-wrapper\");\n        siteWrapper?.focus();\n    };\n    const activeColorVariant = \"primary\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().stickyNote),\n        role: \"dialog\",\n        \"aria-labelledby\": titleId,\n        \"aria-live\": \"assertive\",\n        tabIndex: -1,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().container), (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[`color-${activeColorVariant}`]),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(\"far fa-triangle-exclamation\", (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().topIcon)),\n                        \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().content),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                id: titleId,\n                                className: \"sr-only\",\n                                children: \"Alerte\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().header),\n                                children: [\n                                    title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 25\n                                    }, this),\n                                    description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().description),\n                                        children: description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 31\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, this),\n                            action?.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().action),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Button, {\n                                    asChild: true,\n                                    variant: \"outlined\",\n                                    color: `${activeColorVariant}-inverted`,\n                                    startIcon: \"far fa-check\",\n                                    size: \"xs\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        href: action?.url,\n                                        className: (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().itemLink),\n                                        children: action.text || \"En savoir plus\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default().close), (_StickyNote_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[`color-${activeColorVariant}`]),\n                        onClick: handleCloseAlert,\n                        \"aria-label\": \"Fermer\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"fas fa-xmark\",\n                            \"aria-hidden\": \"true\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\alert\\\\StickyNote.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert/StickyNote.tsx\n");

/***/ })

};
;