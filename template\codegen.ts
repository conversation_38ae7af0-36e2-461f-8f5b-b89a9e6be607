import type { CodegenConfig } from "@graphql-codegen/cli";
import assert from "node:assert";
import process from "node:process";

assert.ok(
  process.env.GRAPHQL_URL,
  "The GRAPHQL_URL environment variable is not set. Maybe you forgot to copy the `.env.example` file?"
);

const config: CodegenConfig = {
  schema: [
    {
      [process.env.GRAPHQL_URL!]: {
        headers: {
          "User-Agent": "graphql-codegen/5",
        },
      },
    },
  ],
  documents: ["./app/**/*.tsx", "./components/**/*.tsx", "./middleware.ts"],
  generates: {
    "./generated/graphql/": {
      preset: "client",
      config: {
        avoidOptionals: {
          field: true,
          object: true,
          defaultValue: true,
        },
        namingConvention: {
          enumValues: "change-case-all#upperCase",
        },
        scalars: {
          URL: "string",
          HTML: "string",
        },
      },
    },
  },
  overwrite: true,
  ignoreNoDocuments: true,
};

export default config;
