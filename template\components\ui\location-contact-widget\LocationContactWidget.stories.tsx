import { PhoneDeviceType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import LocationContactWidget from "./LocationContactWidget";

const meta: Meta<typeof LocationContactWidget> = {
  title: "Components/Widget/LocationContact",
  component: LocationContactWidget,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof LocationContactWidget>;

export const Default: Story = {
  args: {
    email: "<EMAIL>",
    website: "https://www.stratis.fr",
    phones: [
      // @ts-expect-error -- Incomplete phone
      {
        deviceType: PhoneDeviceType.MOBILE,
        number: "0465715233",
      },
      // @ts-expect-error -- Incomplete phone
      {
        deviceType: PhoneDeviceType.MOBILE,
        number: "06399845",
      },
      // @ts-expect-error -- Incomplete phone
      {
        deviceType: PhoneDeviceType.FAX,
        number: "0465715234",
      },
    ],
    location: {
      title: "STRATIS",
      longitude: 6.045,
      latitude: 43.1541,
      address: {
        street: ["18 Rue Lavoisier"],
        zip: "83210",
        city: "La Farlède",
        country: "FR",
      },
    },
  },
};
