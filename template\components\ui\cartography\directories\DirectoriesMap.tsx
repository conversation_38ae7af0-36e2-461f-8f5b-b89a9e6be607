"use client";

import MapComponent from "@/components/ui/map/Map";
import { Marker } from "@/components/ui/map/types";
import { Directory } from "@/generated/graphql/graphql";
import maplibregl from "maplibre-gl";
import { useCallback, useMemo, useRef, useState } from "react";
import { PartialDeep } from "type-fest";
import { useStateWithDeps } from "use-state-with-deps";
import { useMediaQuery } from "usehooks-ts";
import styles from "./DirectoriesMap.module.scss";
import PopupGroup from "./PopupGroup";
import PopupSingle from "./PopupSingle";

interface directoriesProps {
  directories: PartialDeep<Directory, { recurseIntoArrays: true }>[];
}

export type LocationMarker = Marker<{
  directory: string;
}> & {
  id: string | number;
  coordinates: [number, number];
  data: {
    directory: string;
  };
};

export default function DirectoriesMap({ directories }: directoriesProps) {
  const mapRef = useRef<maplibregl.Map>(null);
  const isMobileViewport = useMediaQuery("(max-width: 1302px)");

  const [selectedMarkers, setSelectedMarkers] = useState<LocationMarker[]>([]);
  const [selectedId, setSelectedId] = useStateWithDeps(
    selectedMarkers.length === 1 ? Number(selectedMarkers[0].id) : null,
    [selectedMarkers]
  );

  const handleClose = useCallback(() => {
    setSelectedMarkers([]);
  }, []);

  const markers: LocationMarker[] = useMemo(
    () =>
      directories
        .filter((item) => item.location?.longitude && item.location?.latitude && item.id)
        .map((item) => {
          return {
            id: item.id!,
            coordinates: [item.location?.longitude ?? 0, item.location?.latitude ?? 0],
            data: {
              directory: JSON.stringify(item),
            },
          };
        }),
    [directories]
  );

  const handleSelectMarker = useCallback(
    (markers: LocationMarker[]) => {
      if (markers.length === 0) return;

      setSelectedMarkers(markers);

      if (!mapRef.current) return;

      const point = mapRef.current.project(markers[0].coordinates);

      if (point.x < 420 && !isMobileViewport) {
        mapRef.current.panTo(markers[0].coordinates, { offset: [200, 0] });
      }

      if (point.y > 350 && isMobileViewport) {
        mapRef.current.panTo(markers[0].coordinates, { offset: [0, -200] });
      }
    },
    [mapRef, isMobileViewport]
  );

  console.log("selectedId", selectedId);
  return (
    <section className={styles.directories}>
      <MapComponent
        className={styles.map}
        markers={markers}
        selectedMarkerIds={selectedMarkers.map((m) => m.id)}
        onSelectionChange={handleSelectMarker}
        controls={{ fullscreen: false, position: isMobileViewport ? "top-right" : "bottom-right" }}
        onMapLoad={(m) => (mapRef.current = m)}
      />
      {selectedId ? (
        <PopupGroup setSelectedId={setSelectedId} onClose={handleClose} selectedMarkers={selectedMarkers} />
      ) : (
        <PopupSingle setSelectedId={setSelectedId} onClose={handleClose} selectedMarkers={selectedMarkers} />
      )}
    </section>
  );
}
