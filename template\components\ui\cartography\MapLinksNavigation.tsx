import { Link as LinkType } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Link from "next/link";
import styles from "./MapLinksNavigation.module.scss";

interface MapLinksNavigationProps {
  mapLinks: LinkType[];
  pathname: string;
}

export function MapLinksNavigation({ mapLinks, pathname }: MapLinksNavigationProps) {
  return (
    <nav className={styles.nav} role="navigation" aria-label="Toutes les cartographies">
      <ul className={styles.navList}>
        {mapLinks?.map(({ icon, text, url }) => {
          const isActive = pathname.includes(url);

          return (
            <li key={url} className={styles.navItem}>
              <Link
                href={url}
                className={clsx(styles.navLink, { [styles.navLinkActive]: isActive })}
                aria-current={isActive ? "page" : undefined}
              >
                {icon?.src && <i className={clsx(styles.navIcon, icon.src)} aria-hidden="true" />}
                {text}
              </Link>
            </li>
          );
        })}
      </ul>
    </nav>
  );
}
