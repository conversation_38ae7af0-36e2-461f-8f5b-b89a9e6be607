import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Hero from "./Hero";

const meta: Meta<typeof Hero> = {
  title: "Blocks/Hero",
  component: Hero,
  tags: ["autodocs"],
  argTypes: {
    slides: {
      control: "object",
      description: "Array of slide objects with title, leadText, imageSrc/videoSrc, link",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Hero>;

const imageSource = "/assets/placeholder-720x480.png";
const videoSource = "/assets/clipultralight-short.webm";

export const Default: Story = {
  args: {
    slides: [
      {
        leadText: "Événements et Festivités",
        title: "Participez à la vie de notre commune avec un programme riche toute l'année",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
      {
        leadText: "Nature et Détente",
        title: "Profitez de nos paysages verdoyants et de nos sentiers pour vous ressourcer",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
      {
        leadText: "Culture et Patrimoine",
        title: "Plongez dans notre histoire à travers nos monuments et événements locaux",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
      {
        leadText: "Marchés et Saveurs Locales",
        title: "Goûtez aux produits frais de nos producteurs sur nos marchés animés",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
    ],
  },
};

export const SingleImage: Story = {
  args: {
    slides: [
      {
        leadText: "Bienvenue à Citéopolis",
        title: "Découvrez une ville connectée, durable et inclusive",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
    ],
  },
};

export const SingleVideo: Story = {
  args: {
    slides: [
      {
        leadText: "Culture et Patrimoine",
        title: "Découvrez le charme de notre village, entre traditions et modernité",
        imageSrc: null,
        videoSrc: videoSource,
        link: "#",
      },
    ],
  },
};

export const VeryLongText: Story = {
  args: {
    slides: [
      {
        leadText: "Culture et Patrimoine",
        title:
          "Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source. Lorem Ipsum comes from sections 1.10.32 and 1.10.33 of de Finibus Bonorum et Malorum (The Extremes of Good and Evil) by Cicero, written in 45 BC. This book is a treatise on the theory of ethics, very popular during the Renaissance. The first line of Lorem Ipsum, Lorem ipsum dolor sit amet, comes from a line in section 1.10.32. The standard chunk of Lorem Ipsum used since the 1500s is reproduced below for those interested. Sections 1.10.32 and 1.10.33 from de Finibus Bonorum et Malorum by Cicero are also reproduced in their exact original form, accompanied by English versions from the 1914 translation by H. Rackham.",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
      {
        leadText: "Culture et Patrimoine",
        title: "Découvrez le charme de notre village, entre traditions et modernité",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
      {
        leadText: "Culture et Patrimoine",
        title: "Découvrez le charme de notre village, entre traditions et modernité",
        imageSrc: imageSource,
        videoSrc: null,
        link: "#",
      },
    ],
  },
};
