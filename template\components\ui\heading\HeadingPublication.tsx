import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import striptags from "striptags";
import styles from "./HeadingPublication.module.scss";

const Tag = dynamic(() => import("@/components/ui/tag/Tag"));

interface HeadingPublicationProps {
  imageSrc?: string | null;
  leadText?: string | null;
  modifiedDate?: string;
  publicationDate?: string;
  subtitle?: string | null;
  subtitleRoleDescription?: string;
  surtitle?: string | null;
  tagsRoleDescription?: string;
  tags?: { text: string; url?: string }[];
  title: string;
}

/**
 * The heading of a publication.
 */
export default function HeadingPublication({
  imageSrc,
  leadText,
  modifiedDate,
  publicationDate,
  subtitle,
  subtitleRoleDescription,
  surtitle,
  tags,
  tagsRoleDescription,
  title,
}: HeadingPublicationProps) {
  return (
    <header className={styles.heading}>
      <div className={styles.wrapper}>
        <div className={styles.content}>
          <div className={styles.titleWrapper}>
            <h1 className={styles.title}>
              {surtitle && (
                <span className={styles.surtitle}>
                  {surtitle}
                  <span className="sr-only">:</span>
                </span>
              )}
              {title}
            </h1>
            {subtitle && (
              <p className={styles.subtitle} aria-roledescription={subtitleRoleDescription}>
                {subtitle}
              </p>
            )}
          </div>
          {imageSrc && (
            <div className={styles.imageWrapper}>
              <Image
                className={styles.image}
                src={imageSrc}
                width={268}
                height={380}
                alt=""
                sizes="(max-width: 768px) 146px, (max-width: 1301px) 223px, 268px"
              />
            </div>
          )}
        </div>
        {leadText && <p className={styles.teaser}>{striptags(leadText)}</p>}
        {tags && tags.length > 0 && (
          <ul className={styles.tags} aria-roledescription={tagsRoleDescription}>
            {tags.map((tag, index) => (
              <li key={index}>
                {tag.url ? (
                  <Tag variant="primary" asChild>
                    <Link href={tag.url}>{tag.text}</Link>
                  </Tag>
                ) : (
                  <Tag variant="primary">{tag.text}</Tag>
                )}
              </li>
            ))}
          </ul>
        )}
        {(publicationDate || modifiedDate) && (
          <p className={styles.dates}>
            {publicationDate && (
              <>
                <span>Publié le </span>
                <time dateTime={publicationDate}>
                  {new Date(publicationDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
            {modifiedDate && (
              <>
                <span>Mis à jour le </span>
                <time dateTime={modifiedDate}>
                  {new Date(modifiedDate).toLocaleDateString("fr-FR", {
                    dateStyle: "long",
                  })}
                </time>
              </>
            )}
          </p>
        )}
      </div>
    </header>
  );
}
