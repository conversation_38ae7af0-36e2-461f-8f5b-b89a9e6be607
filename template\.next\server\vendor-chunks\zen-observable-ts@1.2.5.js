"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/zen-observable-ts@1.2.5";
exports.ids = ["vendor-chunks/zen-observable-ts@1.2.5"];
exports.modules = {

/***/ "(rsc)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js":
/*!**********************************************************************************************!*\
  !*** ../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Observable: () => (/* binding */ Observable)\n/* harmony export */ });\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// === Symbol Support ===\nvar hasSymbols = function () {\n  return typeof Symbol === 'function';\n};\n\nvar hasSymbol = function (name) {\n  return hasSymbols() && Boolean(Symbol[name]);\n};\n\nvar getSymbol = function (name) {\n  return hasSymbol(name) ? Symbol[name] : '@@' + name;\n};\n\nif (hasSymbols() && !hasSymbol('observable')) {\n  Symbol.observable = Symbol('observable');\n}\n\nvar SymbolIterator = getSymbol('iterator');\nvar SymbolObservable = getSymbol('observable');\nvar SymbolSpecies = getSymbol('species'); // === Abstract Operations ===\n\nfunction getMethod(obj, key) {\n  var value = obj[key];\n  if (value == null) return undefined;\n  if (typeof value !== 'function') throw new TypeError(value + ' is not a function');\n  return value;\n}\n\nfunction getSpecies(obj) {\n  var ctor = obj.constructor;\n\n  if (ctor !== undefined) {\n    ctor = ctor[SymbolSpecies];\n\n    if (ctor === null) {\n      ctor = undefined;\n    }\n  }\n\n  return ctor !== undefined ? ctor : Observable;\n}\n\nfunction isObservable(x) {\n  return x instanceof Observable; // SPEC: Brand check\n}\n\nfunction hostReportError(e) {\n  if (hostReportError.log) {\n    hostReportError.log(e);\n  } else {\n    setTimeout(function () {\n      throw e;\n    });\n  }\n}\n\nfunction enqueue(fn) {\n  Promise.resolve().then(function () {\n    try {\n      fn();\n    } catch (e) {\n      hostReportError(e);\n    }\n  });\n}\n\nfunction cleanupSubscription(subscription) {\n  var cleanup = subscription._cleanup;\n  if (cleanup === undefined) return;\n  subscription._cleanup = undefined;\n\n  if (!cleanup) {\n    return;\n  }\n\n  try {\n    if (typeof cleanup === 'function') {\n      cleanup();\n    } else {\n      var unsubscribe = getMethod(cleanup, 'unsubscribe');\n\n      if (unsubscribe) {\n        unsubscribe.call(cleanup);\n      }\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n}\n\nfunction closeSubscription(subscription) {\n  subscription._observer = undefined;\n  subscription._queue = undefined;\n  subscription._state = 'closed';\n}\n\nfunction flushSubscription(subscription) {\n  var queue = subscription._queue;\n\n  if (!queue) {\n    return;\n  }\n\n  subscription._queue = undefined;\n  subscription._state = 'ready';\n\n  for (var i = 0; i < queue.length; ++i) {\n    notifySubscription(subscription, queue[i].type, queue[i].value);\n    if (subscription._state === 'closed') break;\n  }\n}\n\nfunction notifySubscription(subscription, type, value) {\n  subscription._state = 'running';\n  var observer = subscription._observer;\n\n  try {\n    var m = getMethod(observer, type);\n\n    switch (type) {\n      case 'next':\n        if (m) m.call(observer, value);\n        break;\n\n      case 'error':\n        closeSubscription(subscription);\n        if (m) m.call(observer, value);else throw value;\n        break;\n\n      case 'complete':\n        closeSubscription(subscription);\n        if (m) m.call(observer);\n        break;\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n\n  if (subscription._state === 'closed') cleanupSubscription(subscription);else if (subscription._state === 'running') subscription._state = 'ready';\n}\n\nfunction onNotify(subscription, type, value) {\n  if (subscription._state === 'closed') return;\n\n  if (subscription._state === 'buffering') {\n    subscription._queue.push({\n      type: type,\n      value: value\n    });\n\n    return;\n  }\n\n  if (subscription._state !== 'ready') {\n    subscription._state = 'buffering';\n    subscription._queue = [{\n      type: type,\n      value: value\n    }];\n    enqueue(function () {\n      return flushSubscription(subscription);\n    });\n    return;\n  }\n\n  notifySubscription(subscription, type, value);\n}\n\nvar Subscription = /*#__PURE__*/function () {\n  function Subscription(observer, subscriber) {\n    // ASSERT: observer is an object\n    // ASSERT: subscriber is callable\n    this._cleanup = undefined;\n    this._observer = observer;\n    this._queue = undefined;\n    this._state = 'initializing';\n    var subscriptionObserver = new SubscriptionObserver(this);\n\n    try {\n      this._cleanup = subscriber.call(undefined, subscriptionObserver);\n    } catch (e) {\n      subscriptionObserver.error(e);\n    }\n\n    if (this._state === 'initializing') this._state = 'ready';\n  }\n\n  var _proto = Subscription.prototype;\n\n  _proto.unsubscribe = function unsubscribe() {\n    if (this._state !== 'closed') {\n      closeSubscription(this);\n      cleanupSubscription(this);\n    }\n  };\n\n  _createClass(Subscription, [{\n    key: \"closed\",\n    get: function () {\n      return this._state === 'closed';\n    }\n  }]);\n\n  return Subscription;\n}();\n\nvar SubscriptionObserver = /*#__PURE__*/function () {\n  function SubscriptionObserver(subscription) {\n    this._subscription = subscription;\n  }\n\n  var _proto2 = SubscriptionObserver.prototype;\n\n  _proto2.next = function next(value) {\n    onNotify(this._subscription, 'next', value);\n  };\n\n  _proto2.error = function error(value) {\n    onNotify(this._subscription, 'error', value);\n  };\n\n  _proto2.complete = function complete() {\n    onNotify(this._subscription, 'complete');\n  };\n\n  _createClass(SubscriptionObserver, [{\n    key: \"closed\",\n    get: function () {\n      return this._subscription._state === 'closed';\n    }\n  }]);\n\n  return SubscriptionObserver;\n}();\n\nvar Observable = /*#__PURE__*/function () {\n  function Observable(subscriber) {\n    if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');\n    if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');\n    this._subscriber = subscriber;\n  }\n\n  var _proto3 = Observable.prototype;\n\n  _proto3.subscribe = function subscribe(observer) {\n    if (typeof observer !== 'object' || observer === null) {\n      observer = {\n        next: observer,\n        error: arguments[1],\n        complete: arguments[2]\n      };\n    }\n\n    return new Subscription(observer, this._subscriber);\n  };\n\n  _proto3.forEach = function forEach(fn) {\n    var _this = this;\n\n    return new Promise(function (resolve, reject) {\n      if (typeof fn !== 'function') {\n        reject(new TypeError(fn + ' is not a function'));\n        return;\n      }\n\n      function done() {\n        subscription.unsubscribe();\n        resolve();\n      }\n\n      var subscription = _this.subscribe({\n        next: function (value) {\n          try {\n            fn(value, done);\n          } catch (e) {\n            reject(e);\n            subscription.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n    });\n  };\n\n  _proto3.map = function map(fn) {\n    var _this2 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this2.subscribe({\n        next: function (value) {\n          try {\n            value = fn(value);\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.filter = function filter(fn) {\n    var _this3 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this3.subscribe({\n        next: function (value) {\n          try {\n            if (!fn(value)) return;\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.reduce = function reduce(fn) {\n    var _this4 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    var hasSeed = arguments.length > 1;\n    var hasValue = false;\n    var seed = arguments[1];\n    var acc = seed;\n    return new C(function (observer) {\n      return _this4.subscribe({\n        next: function (value) {\n          var first = !hasValue;\n          hasValue = true;\n\n          if (!first || hasSeed) {\n            try {\n              acc = fn(acc, value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          } else {\n            acc = value;\n          }\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));\n          observer.next(acc);\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.concat = function concat() {\n    var _this5 = this;\n\n    for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n      sources[_key] = arguments[_key];\n    }\n\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscription;\n      var index = 0;\n\n      function startNext(next) {\n        subscription = next.subscribe({\n          next: function (v) {\n            observer.next(v);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            if (index === sources.length) {\n              subscription = undefined;\n              observer.complete();\n            } else {\n              startNext(C.from(sources[index++]));\n            }\n          }\n        });\n      }\n\n      startNext(_this5);\n      return function () {\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = undefined;\n        }\n      };\n    });\n  };\n\n  _proto3.flatMap = function flatMap(fn) {\n    var _this6 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscriptions = [];\n\n      var outer = _this6.subscribe({\n        next: function (value) {\n          if (fn) {\n            try {\n              value = fn(value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          }\n\n          var inner = C.from(value).subscribe({\n            next: function (value) {\n              observer.next(value);\n            },\n            error: function (e) {\n              observer.error(e);\n            },\n            complete: function () {\n              var i = subscriptions.indexOf(inner);\n              if (i >= 0) subscriptions.splice(i, 1);\n              completeIfDone();\n            }\n          });\n          subscriptions.push(inner);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          completeIfDone();\n        }\n      });\n\n      function completeIfDone() {\n        if (outer.closed && subscriptions.length === 0) observer.complete();\n      }\n\n      return function () {\n        subscriptions.forEach(function (s) {\n          return s.unsubscribe();\n        });\n        outer.unsubscribe();\n      };\n    });\n  };\n\n  _proto3[SymbolObservable] = function () {\n    return this;\n  };\n\n  Observable.from = function from(x) {\n    var C = typeof this === 'function' ? this : Observable;\n    if (x == null) throw new TypeError(x + ' is not an object');\n    var method = getMethod(x, SymbolObservable);\n\n    if (method) {\n      var observable = method.call(x);\n      if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');\n      if (isObservable(observable) && observable.constructor === C) return observable;\n      return new C(function (observer) {\n        return observable.subscribe(observer);\n      });\n    }\n\n    if (hasSymbol('iterator')) {\n      method = getMethod(x, SymbolIterator);\n\n      if (method) {\n        return new C(function (observer) {\n          enqueue(function () {\n            if (observer.closed) return;\n\n            for (var _iterator = _createForOfIteratorHelperLoose(method.call(x)), _step; !(_step = _iterator()).done;) {\n              var item = _step.value;\n              observer.next(item);\n              if (observer.closed) return;\n            }\n\n            observer.complete();\n          });\n        });\n      }\n    }\n\n    if (Array.isArray(x)) {\n      return new C(function (observer) {\n        enqueue(function () {\n          if (observer.closed) return;\n\n          for (var i = 0; i < x.length; ++i) {\n            observer.next(x[i]);\n            if (observer.closed) return;\n          }\n\n          observer.complete();\n        });\n      });\n    }\n\n    throw new TypeError(x + ' is not observable');\n  };\n\n  Observable.of = function of() {\n    for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      items[_key2] = arguments[_key2];\n    }\n\n    var C = typeof this === 'function' ? this : Observable;\n    return new C(function (observer) {\n      enqueue(function () {\n        if (observer.closed) return;\n\n        for (var i = 0; i < items.length; ++i) {\n          observer.next(items[i]);\n          if (observer.closed) return;\n        }\n\n        observer.complete();\n      });\n    });\n  };\n\n  _createClass(Observable, null, [{\n    key: SymbolSpecies,\n    get: function () {\n      return this;\n    }\n  }]);\n\n  return Observable;\n}();\n\nif (hasSymbols()) {\n  Object.defineProperty(Observable, Symbol('extensions'), {\n    value: {\n      symbol: SymbolObservable,\n      hostReportError: hostReportError\n    },\n    configurable: true\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js":
/*!**********************************************************************************************!*\
  !*** ../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Observable: () => (/* binding */ Observable)\n/* harmony export */ });\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// === Symbol Support ===\nvar hasSymbols = function () {\n  return typeof Symbol === 'function';\n};\n\nvar hasSymbol = function (name) {\n  return hasSymbols() && Boolean(Symbol[name]);\n};\n\nvar getSymbol = function (name) {\n  return hasSymbol(name) ? Symbol[name] : '@@' + name;\n};\n\nif (hasSymbols() && !hasSymbol('observable')) {\n  Symbol.observable = Symbol('observable');\n}\n\nvar SymbolIterator = getSymbol('iterator');\nvar SymbolObservable = getSymbol('observable');\nvar SymbolSpecies = getSymbol('species'); // === Abstract Operations ===\n\nfunction getMethod(obj, key) {\n  var value = obj[key];\n  if (value == null) return undefined;\n  if (typeof value !== 'function') throw new TypeError(value + ' is not a function');\n  return value;\n}\n\nfunction getSpecies(obj) {\n  var ctor = obj.constructor;\n\n  if (ctor !== undefined) {\n    ctor = ctor[SymbolSpecies];\n\n    if (ctor === null) {\n      ctor = undefined;\n    }\n  }\n\n  return ctor !== undefined ? ctor : Observable;\n}\n\nfunction isObservable(x) {\n  return x instanceof Observable; // SPEC: Brand check\n}\n\nfunction hostReportError(e) {\n  if (hostReportError.log) {\n    hostReportError.log(e);\n  } else {\n    setTimeout(function () {\n      throw e;\n    });\n  }\n}\n\nfunction enqueue(fn) {\n  Promise.resolve().then(function () {\n    try {\n      fn();\n    } catch (e) {\n      hostReportError(e);\n    }\n  });\n}\n\nfunction cleanupSubscription(subscription) {\n  var cleanup = subscription._cleanup;\n  if (cleanup === undefined) return;\n  subscription._cleanup = undefined;\n\n  if (!cleanup) {\n    return;\n  }\n\n  try {\n    if (typeof cleanup === 'function') {\n      cleanup();\n    } else {\n      var unsubscribe = getMethod(cleanup, 'unsubscribe');\n\n      if (unsubscribe) {\n        unsubscribe.call(cleanup);\n      }\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n}\n\nfunction closeSubscription(subscription) {\n  subscription._observer = undefined;\n  subscription._queue = undefined;\n  subscription._state = 'closed';\n}\n\nfunction flushSubscription(subscription) {\n  var queue = subscription._queue;\n\n  if (!queue) {\n    return;\n  }\n\n  subscription._queue = undefined;\n  subscription._state = 'ready';\n\n  for (var i = 0; i < queue.length; ++i) {\n    notifySubscription(subscription, queue[i].type, queue[i].value);\n    if (subscription._state === 'closed') break;\n  }\n}\n\nfunction notifySubscription(subscription, type, value) {\n  subscription._state = 'running';\n  var observer = subscription._observer;\n\n  try {\n    var m = getMethod(observer, type);\n\n    switch (type) {\n      case 'next':\n        if (m) m.call(observer, value);\n        break;\n\n      case 'error':\n        closeSubscription(subscription);\n        if (m) m.call(observer, value);else throw value;\n        break;\n\n      case 'complete':\n        closeSubscription(subscription);\n        if (m) m.call(observer);\n        break;\n    }\n  } catch (e) {\n    hostReportError(e);\n  }\n\n  if (subscription._state === 'closed') cleanupSubscription(subscription);else if (subscription._state === 'running') subscription._state = 'ready';\n}\n\nfunction onNotify(subscription, type, value) {\n  if (subscription._state === 'closed') return;\n\n  if (subscription._state === 'buffering') {\n    subscription._queue.push({\n      type: type,\n      value: value\n    });\n\n    return;\n  }\n\n  if (subscription._state !== 'ready') {\n    subscription._state = 'buffering';\n    subscription._queue = [{\n      type: type,\n      value: value\n    }];\n    enqueue(function () {\n      return flushSubscription(subscription);\n    });\n    return;\n  }\n\n  notifySubscription(subscription, type, value);\n}\n\nvar Subscription = /*#__PURE__*/function () {\n  function Subscription(observer, subscriber) {\n    // ASSERT: observer is an object\n    // ASSERT: subscriber is callable\n    this._cleanup = undefined;\n    this._observer = observer;\n    this._queue = undefined;\n    this._state = 'initializing';\n    var subscriptionObserver = new SubscriptionObserver(this);\n\n    try {\n      this._cleanup = subscriber.call(undefined, subscriptionObserver);\n    } catch (e) {\n      subscriptionObserver.error(e);\n    }\n\n    if (this._state === 'initializing') this._state = 'ready';\n  }\n\n  var _proto = Subscription.prototype;\n\n  _proto.unsubscribe = function unsubscribe() {\n    if (this._state !== 'closed') {\n      closeSubscription(this);\n      cleanupSubscription(this);\n    }\n  };\n\n  _createClass(Subscription, [{\n    key: \"closed\",\n    get: function () {\n      return this._state === 'closed';\n    }\n  }]);\n\n  return Subscription;\n}();\n\nvar SubscriptionObserver = /*#__PURE__*/function () {\n  function SubscriptionObserver(subscription) {\n    this._subscription = subscription;\n  }\n\n  var _proto2 = SubscriptionObserver.prototype;\n\n  _proto2.next = function next(value) {\n    onNotify(this._subscription, 'next', value);\n  };\n\n  _proto2.error = function error(value) {\n    onNotify(this._subscription, 'error', value);\n  };\n\n  _proto2.complete = function complete() {\n    onNotify(this._subscription, 'complete');\n  };\n\n  _createClass(SubscriptionObserver, [{\n    key: \"closed\",\n    get: function () {\n      return this._subscription._state === 'closed';\n    }\n  }]);\n\n  return SubscriptionObserver;\n}();\n\nvar Observable = /*#__PURE__*/function () {\n  function Observable(subscriber) {\n    if (!(this instanceof Observable)) throw new TypeError('Observable cannot be called as a function');\n    if (typeof subscriber !== 'function') throw new TypeError('Observable initializer must be a function');\n    this._subscriber = subscriber;\n  }\n\n  var _proto3 = Observable.prototype;\n\n  _proto3.subscribe = function subscribe(observer) {\n    if (typeof observer !== 'object' || observer === null) {\n      observer = {\n        next: observer,\n        error: arguments[1],\n        complete: arguments[2]\n      };\n    }\n\n    return new Subscription(observer, this._subscriber);\n  };\n\n  _proto3.forEach = function forEach(fn) {\n    var _this = this;\n\n    return new Promise(function (resolve, reject) {\n      if (typeof fn !== 'function') {\n        reject(new TypeError(fn + ' is not a function'));\n        return;\n      }\n\n      function done() {\n        subscription.unsubscribe();\n        resolve();\n      }\n\n      var subscription = _this.subscribe({\n        next: function (value) {\n          try {\n            fn(value, done);\n          } catch (e) {\n            reject(e);\n            subscription.unsubscribe();\n          }\n        },\n        error: reject,\n        complete: resolve\n      });\n    });\n  };\n\n  _proto3.map = function map(fn) {\n    var _this2 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this2.subscribe({\n        next: function (value) {\n          try {\n            value = fn(value);\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.filter = function filter(fn) {\n    var _this3 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      return _this3.subscribe({\n        next: function (value) {\n          try {\n            if (!fn(value)) return;\n          } catch (e) {\n            return observer.error(e);\n          }\n\n          observer.next(value);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.reduce = function reduce(fn) {\n    var _this4 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    var hasSeed = arguments.length > 1;\n    var hasValue = false;\n    var seed = arguments[1];\n    var acc = seed;\n    return new C(function (observer) {\n      return _this4.subscribe({\n        next: function (value) {\n          var first = !hasValue;\n          hasValue = true;\n\n          if (!first || hasSeed) {\n            try {\n              acc = fn(acc, value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          } else {\n            acc = value;\n          }\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          if (!hasValue && !hasSeed) return observer.error(new TypeError('Cannot reduce an empty sequence'));\n          observer.next(acc);\n          observer.complete();\n        }\n      });\n    });\n  };\n\n  _proto3.concat = function concat() {\n    var _this5 = this;\n\n    for (var _len = arguments.length, sources = new Array(_len), _key = 0; _key < _len; _key++) {\n      sources[_key] = arguments[_key];\n    }\n\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscription;\n      var index = 0;\n\n      function startNext(next) {\n        subscription = next.subscribe({\n          next: function (v) {\n            observer.next(v);\n          },\n          error: function (e) {\n            observer.error(e);\n          },\n          complete: function () {\n            if (index === sources.length) {\n              subscription = undefined;\n              observer.complete();\n            } else {\n              startNext(C.from(sources[index++]));\n            }\n          }\n        });\n      }\n\n      startNext(_this5);\n      return function () {\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = undefined;\n        }\n      };\n    });\n  };\n\n  _proto3.flatMap = function flatMap(fn) {\n    var _this6 = this;\n\n    if (typeof fn !== 'function') throw new TypeError(fn + ' is not a function');\n    var C = getSpecies(this);\n    return new C(function (observer) {\n      var subscriptions = [];\n\n      var outer = _this6.subscribe({\n        next: function (value) {\n          if (fn) {\n            try {\n              value = fn(value);\n            } catch (e) {\n              return observer.error(e);\n            }\n          }\n\n          var inner = C.from(value).subscribe({\n            next: function (value) {\n              observer.next(value);\n            },\n            error: function (e) {\n              observer.error(e);\n            },\n            complete: function () {\n              var i = subscriptions.indexOf(inner);\n              if (i >= 0) subscriptions.splice(i, 1);\n              completeIfDone();\n            }\n          });\n          subscriptions.push(inner);\n        },\n        error: function (e) {\n          observer.error(e);\n        },\n        complete: function () {\n          completeIfDone();\n        }\n      });\n\n      function completeIfDone() {\n        if (outer.closed && subscriptions.length === 0) observer.complete();\n      }\n\n      return function () {\n        subscriptions.forEach(function (s) {\n          return s.unsubscribe();\n        });\n        outer.unsubscribe();\n      };\n    });\n  };\n\n  _proto3[SymbolObservable] = function () {\n    return this;\n  };\n\n  Observable.from = function from(x) {\n    var C = typeof this === 'function' ? this : Observable;\n    if (x == null) throw new TypeError(x + ' is not an object');\n    var method = getMethod(x, SymbolObservable);\n\n    if (method) {\n      var observable = method.call(x);\n      if (Object(observable) !== observable) throw new TypeError(observable + ' is not an object');\n      if (isObservable(observable) && observable.constructor === C) return observable;\n      return new C(function (observer) {\n        return observable.subscribe(observer);\n      });\n    }\n\n    if (hasSymbol('iterator')) {\n      method = getMethod(x, SymbolIterator);\n\n      if (method) {\n        return new C(function (observer) {\n          enqueue(function () {\n            if (observer.closed) return;\n\n            for (var _iterator = _createForOfIteratorHelperLoose(method.call(x)), _step; !(_step = _iterator()).done;) {\n              var item = _step.value;\n              observer.next(item);\n              if (observer.closed) return;\n            }\n\n            observer.complete();\n          });\n        });\n      }\n    }\n\n    if (Array.isArray(x)) {\n      return new C(function (observer) {\n        enqueue(function () {\n          if (observer.closed) return;\n\n          for (var i = 0; i < x.length; ++i) {\n            observer.next(x[i]);\n            if (observer.closed) return;\n          }\n\n          observer.complete();\n        });\n      });\n    }\n\n    throw new TypeError(x + ' is not observable');\n  };\n\n  Observable.of = function of() {\n    for (var _len2 = arguments.length, items = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      items[_key2] = arguments[_key2];\n    }\n\n    var C = typeof this === 'function' ? this : Observable;\n    return new C(function (observer) {\n      enqueue(function () {\n        if (observer.closed) return;\n\n        for (var i = 0; i < items.length; ++i) {\n          observer.next(items[i]);\n          if (observer.closed) return;\n        }\n\n        observer.complete();\n      });\n    });\n  };\n\n  _createClass(Observable, null, [{\n    key: SymbolSpecies,\n    get: function () {\n      return this;\n    }\n  }]);\n\n  return Observable;\n}();\n\nif (hasSymbols()) {\n  Object.defineProperty(Observable, Symbol('extensions'), {\n    value: {\n      symbol: SymbolObservable,\n      hostReportError: hostReportError\n    },\n    configurable: true\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/zen-observable-ts@1.2.5/node_modules/zen-observable-ts/module.js\n");

/***/ })

};
;