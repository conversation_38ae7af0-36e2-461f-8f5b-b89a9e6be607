@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.menuContent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  min-width: 320px;
  height: 100%;
}

.top {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.bottom {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding-bottom: 17px;
  margin-bottom: 16px;
  border-bottom: 1px solid $color-neutral-300;
}

.clientLogo {
  width: 107px;
  height: 43px;
  margin-bottom: 5px;
}

.clientLogo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  object-position: left;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.sectionTitle {
  font-size: 1.8rem;
  line-height: 130%;
}

.mapTypeList {
  display: flex;
  flex-direction: column;
  color: $color-primary-500;
}

.mapTypeItem {
  display: flex;
  gap: 5px;
  align-items: center;
  height: 48px;
  font-size: 1.8rem;
  font-weight: 700;
  cursor: pointer;
  transition:
    border-color 0.3s ease,
    color 0.3s ease;
}

.mapTypeLink {
  border-bottom: 1px solid transparent;
}

.mapTypeItem:hover .mapTypeLink,
.mapTypeItem:focus-visible .mapTypeLink {
  color: $color-black;
  border-bottom: 1px solid $color-black;
}

.mapTypeIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
}

.wrapper::after {
  display: flex;
  width: 100%;
  height: 12px;
  margin-bottom: 11px;
  content: "";
  border-bottom: $color-neutral-300 1px solid;
}

.menu {
  position: sticky;
  transition:
    transform 0.3s ease,
    max-height 0.3s ease;
}

.menu::after {
  display: flex;
  width: 46px;
  height: 12px;
  margin-bottom: 11px;
  content: "";
  border-bottom: $color-neutral-300 1px solid;
}

.trigger {
  display: flex;
  gap: 10px;
  align-items: center;
  cursor: pointer;
}

.trigger:hover h3,
.trigger:focus-visible h3,
.trigger:hover i,
.trigger:focus-visible i {
  color: $color-primary-500;
}

.trigger i {
  font-size: 1.8rem;
  color: $color-neutral-500;
  transition: transform 0.3s ease-in-out;
}

.trigger h3 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.trigger h3 p {
  margin: 0;
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;
}

.menu[open] i {
  transform: rotate(90deg);
}

.content {
  width: 100%;
  max-height: 140px;
  margin-top: 12px;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 120%;
  color: $color-neutral-700;
  text-transform: uppercase;
}

.content li {
  padding: 4px 0 4px 6px;
}

.content a:hover,
.content a:focus-visible {
  border-bottom: 1px solid currentcolor;
}

.menu[open] .content {
  animation: fade-down 0.3s ease-out;
}

.menu:not([open]) .content {
  max-height: 0;
  overflow: hidden;
  animation: fade-up 0.3s ease-out;
}

.navList {
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-700;
  text-transform: uppercase;
}

.navList li {
  padding: 4px 0 4px 6px;
}

.navList a:hover,
.navList a:focus-visible {
  border-bottom: 1px solid currentcolor;
}

.footer {
  display: flex;
  gap: 6px;
  align-items: flex-start;
  height: 32px;
  color: $color-neutral-700;
}

.backToSiteLink {
  display: flex;
  gap: 6px;
  align-items: center;
  font-size: 1.4rem;
  color: $color-neutral-700;
}

.backToSiteLink:hover,
.backToSiteLink:focus-visible {
  color: $color-primary-500;
}

.backToSiteLink:hover span {
  border-bottom: 1px solid currentcolor;
}

.backToSiteLink i {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.6rem;

  @include size(24px);
}

@keyframes fade-down {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-up {
  from {
    opacity: 1;
    transform: translateY(0);
  }

  to {
    opacity: 0;
    transform: translateY(20px);
  }
}
