import SocialLink from "@/components/ui/social-link/SocialLink";
import { SocialLink as GQSocialLink } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./SocialMediaWidget.module.scss";

interface SocialMediaWidgetProps {
  socialLinks?: GQSocialLink[] | null;
}

export default function SocialMediaWidget({ socialLinks }: SocialMediaWidgetProps) {
  if (!socialLinks || socialLinks.length === 0) {
    return null;
  }

  return (
    <div className={clsx("widget widget-social-media", styles.socialMediaWidget)}>
      <h3 className={styles.title}>
        <i className="far fa-thumbs-up" aria-hidden="true"></i> Sur les réseaux
      </h3>
      <ul className={styles.socials}>
        {socialLinks?.map((link, index) => (
          <li key={index}>
            <SocialLink {...link} size="lg" />
          </li>
        ))}
      </ul>
    </div>
  );
}
