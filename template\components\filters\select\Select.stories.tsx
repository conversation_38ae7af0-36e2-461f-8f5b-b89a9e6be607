import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import SelectFilter from "./Select";

const meta: Meta<typeof SelectFilter> = {
  title: "Filters/Select",
  component: SelectFilter,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof SelectFilter>;

export const Default: Story = {
  args: {
    filter: {
      attribute: "cat",
      label: "Catégories",
      multiple: false,
      placeholder: "Toutes les catégories",
      options: [
        // @ts-expect-error -- Incomplete option
        {
          value: "numerique",
          label: "Numérique",
          count: 3,
        },
        // @ts-expect-error -- Incomplete option
        {
          value: "urbanisme",
          label: "Urbanisme",
          count: 10,
        },
        // @ts-expect-error -- Incomplete option
        {
          value: "economie_locale",
          label: "Économie Locale",
          count: 2,
        },
        // @ts-expect-error -- Incomplete option
        {
          value: "communication",
          label: "Communication",
          count: 4,
        },
      ],
    },
  },
};
