import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import FormHelper from "../form-helper/FormHelper";
import Label from "../label/Label";
import Textfield from "../textfield/Textfield";
import FormControl from "./FormControl";

const meta: Meta<typeof FormControl> = {
  title: "Components/FormControl",
  component: FormControl,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof FormControl>;

export const Default: Story = {
  args: {
    children: (
      <>
        <Label htmlFor="form-input-1" description="Texte de description additionel">
          Libellé
        </Label>
        <Textfield id="form-input-1" aria-describedby="form-helper-1" />
        <FormHelper id="form-helper-1" variant="info">
          Texte du message
        </FormHelper>
      </>
    ),
  },
};

/**
 * The `dense` spacing option brings more space.
 *
 * Useful if the input controls are stacked items like checkboxes or radio buttons.
 */
export const Dense: Story = {
  args: {
    spacing: "dense",
    children: (
      <>
        <Label htmlFor="form-input-2" description="Texte de description additionel">
          Libellé
        </Label>
        <Textfield id="form-input-2" aria-describedby="form-helper-1" />
        <FormHelper id="form-helper-2" variant="info">
          Texte du message
        </FormHelper>
      </>
    ),
  },
};
