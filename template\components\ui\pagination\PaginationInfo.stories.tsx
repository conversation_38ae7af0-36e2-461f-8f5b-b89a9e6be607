import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import PaginationInfo from "./PaginationInfo";

const meta: Meta<typeof PaginationInfo> = {
  title: "Components/PaginationInfo",
  component: PaginationInfo,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof PaginationInfo>;

export const Default: Story = {
  args: {
    currentPage: 1,
    pageSize: 10,
    totalCount: 200,
  },
};
