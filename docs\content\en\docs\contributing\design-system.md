---
title: "Design System"
weight: 1
---

The following document is about good practices when implementing a design system.

# Colors

Colors are named in a [Tailwind CSS](https://tailwindcss.com/docs/colors) fashion.

It means each color has a finite palette.

Example:

```scss
$color-primary-50: #e5ecfa;
$color-primary-100: #ccdaf5;
// ...

$color-secondary-50: #f3fcfa;
$color-secondary-100: #fdf9f1;
// ...
```

# Component variations

A component can have multiple varations, usually one or any of the following:

- Shape
- Size
- Color

{{% hint info %}}
This convention is based on [MUI](https://mui.com/material-ui/) patterns.
{{% /hint %}}

## Shape

The shape of a component is usually defined by the `variant` property.

Example:

```tsx
interface ButtonProps {
  variant: "contained" | "outlined" | "text";
}
```

## Size

The size of a component is defined by the `size` property.

Example:

```tsx
interface ButtonProps {
  size: "xs" | "sm" | "md" | "lg";
}
```

## Color

The color of a component is defined by the `color` property.

Example:

```tsx
interface ButtonProps {
  color: "primary" | "secondary" | "tertiary";
}
```

{{% hint info %}}
The colors described here should match the variable names described earlier.
{{% /hint %}}

# Naming

Naming things is hard, but it's the role of a maintainer to ensure a smooth usage for the production developers.

### Be consistent with what you describe

Stay close to the `GraphQL` types, a lot of thinking has been done already upfront for you.

```tsx
// ❌
const taxo = category.title;
<span className={styles.thematic}>{taxo}</span>;
```

```tsx
// ✅
<span className={styles.category}>{category.title}</span>
```

### Do NOT use custom

Custom has absolutely no meaning.

```tsx
// ❌
interface ButtonProps {
  size: "sm" | "md" | "custom";
}

export default function Button({ size }: ButtonProps) {}
```

```tsx
// ✅
interface ButtonProps {
  size: "sm" | "md" | "lg";
}

export default function Button({ size }: ButtonProps) {}
```
