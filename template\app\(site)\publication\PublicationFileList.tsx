import PublicationFile from "@/components/ui/publication-file/PublicationFile";
import { Publication } from "@/generated/graphql/graphql";
import { useId } from "react";
import styles from "./PublicationFileList.module.scss";

interface PublicationFileListProps {
  files: Publication["files"];
}

export default function PublicationFileList({ files }: PublicationFileListProps) {
  const fileCountId = useId();

  if (files.length === 0) {
    return;
  }

  return (
    <div className="container">
      <p id={fileCountId} className={styles.fileCount}>
        {files.length} document{files.length === 1 ? "" : "s"}
      </p>
      <ul className={styles.fileList} aria-describedby={fileCountId}>
        {files.map((file, index) => (
          <PublicationFile
            key={index}
            label={file.label ?? "Fichier"}
            extname={file.extname ?? undefined}
            size={file.size ?? undefined}
            downloadUrl={file.downloadUrl ?? undefined}
            viewUrl={file.viewUrl ?? undefined}
          />
        ))}
      </ul>
    </div>
  );
}
