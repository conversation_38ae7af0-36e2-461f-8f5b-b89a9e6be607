import { ButtonBlockVariant, Orientation } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Button from "../button/Button";
import Buttons from "./Buttons";

const meta: Meta<typeof Buttons> = {
  title: "Blocks/Buttons",
  component: Buttons,
  tags: ["autodocs"],
  argTypes: {
    justifyContent: {
      control: "select",
      options: ["flex-start", "center", "flex-end", "space-between"],
      description: "Horizontal alignment of buttons",
    },
    orientation: {
      control: "select",
      options: [Orientation.HORIZONTAL, Orientation.VERTICAL],
      description: "Layout direction of buttons",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Buttons>;

export const Horizontal: Story = {
  args: {
    orientation: Orientation.HORIZONTAL,
    children: (
      <>
        <Button html="Primaire" url="#" variant={ButtonBlockVariant.PRIMARY} />
        <Button html="Secondaire" url="#" variant={ButtonBlockVariant.SECONDARY} />
        <Button html="Tertiaire" url="#" variant={ButtonBlockVariant.TERTIARY} />
      </>
    ),
  },
};

export const AlignRight: Story = {
  args: {
    justifyContent: "flex-end",
    orientation: Orientation.HORIZONTAL,
    children: (
      <>
        <Button html="Primaire" url="#" variant={ButtonBlockVariant.PRIMARY} />
        <Button html="Secondaire" url="#" variant={ButtonBlockVariant.SECONDARY} />
        <Button html="Tertiaire" url="#" variant={ButtonBlockVariant.TERTIARY} />
      </>
    ),
  },
};

export const Centered: Story = {
  args: {
    justifyContent: "center",
    orientation: Orientation.HORIZONTAL,
    children: (
      <>
        <Button html="Primaire" url="#" variant={ButtonBlockVariant.PRIMARY} />
        <Button html="Secondaire" url="#" variant={ButtonBlockVariant.SECONDARY} />
        <Button html="Tertiaire" url="#" variant={ButtonBlockVariant.TERTIARY} />
      </>
    ),
  },
};

export const SpaceBetween: Story = {
  args: {
    justifyContent: "space-between",
    orientation: Orientation.HORIZONTAL,
    children: (
      <>
        <Button html="Primaire" url="#" variant={ButtonBlockVariant.PRIMARY} />
        <Button html="Secondaire" url="#" variant={ButtonBlockVariant.SECONDARY} />
        <Button html="Tertiaire" url="#" variant={ButtonBlockVariant.TERTIARY} />
      </>
    ),
  },
};

export const Vertical: Story = {
  args: {
    orientation: Orientation.VERTICAL,
    children: (
      <>
        <Button html="Primaire" url="#" variant={ButtonBlockVariant.PRIMARY} />
        <Button html="Secondaire" url="#" variant={ButtonBlockVariant.SECONDARY} />
        <Button html="Tertiaire" url="#" variant={ButtonBlockVariant.TERTIARY} />
      </>
    ),
  },
};

/**
 * Two buttons with the width set to 50%
 */
export const FullWidth: Story = {
  args: {
    orientation: Orientation.HORIZONTAL,
    children: (
      <>
        <Button html="S'inscrire" url="#" variant={ButtonBlockVariant.PRIMARY} width={50} />
        <Button html="Continuer en tant qu'invité" url="#" variant={ButtonBlockVariant.SECONDARY} width={50} />
      </>
    ),
  },
};
