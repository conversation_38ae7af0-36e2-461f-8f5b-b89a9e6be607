@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.news {
  @extend %text-wrap;

  width: 100%;
  margin-bottom: 96px;
  container: block-news / inline-size;

  @include breakpoint(large up) {
    margin-bottom: 160px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.titleWrapper {
  margin-bottom: 32px;

  @include breakpoint(medium up) {
    margin-bottom: 40px;
  }

  @include breakpoint(large up) {
    margin-bottom: 48px;
  }
}

.title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;

  @include breakpoint(medium up) {
    font-size: 4.8rem;
    letter-spacing: -0.96px;
  }

  @include breakpoint(large up) {
    font-size: 6.4rem;
    letter-spacing: -1.28px;
  }
}

.tags {
  margin-top: 12px;

  @include breakpoint(large up) {
    margin-top: 16px;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 32px;

  @include breakpoint(medium up) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    margin-top: 40px;
  }

  @include breakpoint(large up) {
    display: flex;
    flex-direction: row;
    gap: 8px;
    margin-top: 48px;
  }
}
