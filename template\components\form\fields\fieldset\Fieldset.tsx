"use client";

import { Fieldset as BaseFieldsetProps } from "@/generated/graphql/graphql";
import { useId } from "react";
import useFormCondition from "../../useFormCondition";
import styles from "./Fieldset.module.scss";

type FieldsetProps = Omit<BaseFieldsetProps, "__typename">;

export default function Fieldset({ title, description, children, condition }: React.PropsWithChildren<FieldsetProps>) {
  const { visible } = useFormCondition(null, condition);
  const legendId = useId();
  const descriptionId = useId();

  return (
    visible && (
      <div
        role="group"
        className={styles.fieldset}
        aria-labelledby={title ? legendId : undefined}
        aria-describedby={description ? descriptionId : undefined}
      >
        {title && (
          <p id={legendId} className={styles.legend}>
            {title}
          </p>
        )}

        {description && (
          <p id={descriptionId} className={styles.description}>
            {description}
          </p>
        )}

        {children}
      </div>
    )
  );
}
