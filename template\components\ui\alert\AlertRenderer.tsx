import { <PERSON><PERSON>, Al<PERSON>Variant } from "@/generated/graphql/graphql";
import dynamic from "next/dynamic";
import { ComponentType } from "react";
import { PartialDeep } from "type-fest";

const PopUpAlert = dynamic(() => import("./PopUpAlert"));
const StickyNote = dynamic(() => import("./StickyNote"));

interface AlertProps {
  alert: PartialDeep<Alert, { recurseIntoArrays: true }>;
}

interface AlertRendererProps {
  entries: PartialDeep<
    Pick<Alert, "id" | "title" | "description" | "modifiedDate" | "action" | "variant">,
    { recurseIntoArrays: true }
  >[];
}

const componentMap: Partial<Record<AlertVariant, ComponentType<AlertProps>>> = {
  POPUP: PopUpAlert,
  STICKY: StickyNote,
};

/**
 * Render the alert based on the variant type (POPUP or STICKY).
 */
export default function AlertRenderer({ entries }: AlertRendererProps) {
  return (
    <>
      {entries.map((alert) => {
        const Component = alert.variant && componentMap[alert.variant];
        return Component ? <Component key={alert.id} alert={alert} /> : null;
      })}
    </>
  );
}
