@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.fileCount {
  font-size: 2.4rem;
  font-weight: $fw-bold;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 3.2rem;
  }

  @include breakpoint(large up) {
    font-size: 4.8rem;
  }
}

.fileList {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-block: 24px;
  margin-top: 12px;
  margin-bottom: 32px;
  container-type: inline-size;

  @include breakpoint(medium up) {
    padding-block: 32px;
    margin-bottom: 40px;
  }

  @include breakpoint(large up) {
    gap: 8px;
    margin-bottom: 48px;
  }
}
