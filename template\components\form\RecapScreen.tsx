"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import Label from "@/components/ui/label/Label";
import { Field, FormStep } from "@/generated/graphql/graphql";
import { useCallback, useMemo } from "react";
import { useFormContext } from "react-hook-form";
import styles from "./RecapScreen.module.scss";

interface RecapScreenProps {
  steps: FormStep[];
}

const fieldNameOrder = [
  "prefix",
  "firstName",
  "lastName",
  "additionalName",
  "suffix",
  "street1",
  "street2",
  "city",
  "state",
  "zip",
  "country",
];

export default function RecapScreen({ steps }: RecapScreenProps) {
  const { getValues } = useFormContext();

  const formData = getValues();

  const formatValue = useCallback((value: unknown): string => {
    if (value === null || value === "") return "Non renseigné";

    if (value instanceof FileList) {
      return value.length > 0 ? [...value].map((f) => f.name).join(", ") : "Aucun fichier sélectionné";
    }

    if (Array.isArray(value)) {
      return value.length > 0 ? value.join(", ") : "Aucune sélection";
    }

    if (typeof value === "boolean") {
      return value ? "Oui" : "Non";
    }

    return String(value);
  }, []);

  const fieldsObject = useMemo(() => {
    const map: Record<string, { label: string; typename?: string; name: string; value: string }> = {};

    const processField = (field: Field) => {
      if ("name" in field && "__typename" in field) {
        if (field.__typename === "NameField") {
          const nameField = formData[field.name];

          const nameFieldKeys: string[] = Object.keys(nameField).sort((a, b) => {
            return fieldNameOrder.indexOf(a) - fieldNameOrder.indexOf(b);
          });

          for (const nameFieldKey of nameFieldKeys) {
            map[nameFieldKey] = {
              name: nameFieldKey,
              label: field.label,
              typename: field.__typename,
              value: formatValue(nameField[nameFieldKey]),
            };
          }
        } else {
          map[field.name] = {
            name: field.name,
            label: field.label,
            typename: field.__typename,
            value: formatValue(formData[field.name]),
          };
        }
      }

      if ("fields" in field) {
        const { fields } = field;

        for (const field of fields) {
          processField(field);
        }
      }
    };

    for (const step of steps) {
      if (step.fields) {
        const { fields } = step;

        for (const field of fields) {
          processField(field);
        }
      }
    }

    return map;
  }, [steps, formData, formatValue]);

  const fieldsMap = Object.values(fieldsObject);

  return (
    <div className={styles.recapScreen}>
      <div className={styles.header}>
        <h2 className={styles.title}>Récapitulatif de votre demande</h2>
        <p className={styles.description}>
          Veuillez vérifier les informations saisies avant de soumettre votre formulaire.
        </p>
      </div>

      <div className={styles.content}>
        <div className={styles.fieldsList}>
          {fieldsMap.map((v) => {
            return (
              <FormControl key={v.name} className={styles.field}>
                <Label className={styles.fieldLabel}>{v.label}</Label>

                <p className={styles.fieldValue}>{v.value}</p>
              </FormControl>
            );
          })}
        </div>
      </div>
    </div>
  );
}
