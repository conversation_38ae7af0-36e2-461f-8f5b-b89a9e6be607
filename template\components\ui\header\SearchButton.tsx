"use client";

import Button from "@/components/ui/button/Button";
import useModal from "@/components/ui/modal/useModal";
import SearchPopup from "@/components/ui/search/SearchPopup";
import Tooltip from "@/components/ui/tooltip/Tooltip";
import styles from "./Header.module.scss";

export default function SearchButton({ searchUrl }: { searchUrl: string }) {
  const { Trigger } = useModal(<SearchPopup searchUrl={searchUrl} />);

  return (
    <Trigger>
      <Tooltip content="Ouvrir la recherche">
        <Button className={styles.searchButton} startIcon="far fa-magnifying-glass">
          <span className="sr-only">Ouvrir la recherche</span>
        </Button>
      </Tooltip>
    </Trigger>
  );
}
