import type { StorybookConfig } from "@storybook/nextjs";
import path from "node:path";
import { fileURLToPath } from "node:url";

const config: StorybookConfig = {
  stories: ["../stories/**/*.mdx", "../(stories|components)/**/*.stories.@(js|jsx|mjs|ts|tsx)"],

  addons: ["@storybook/addon-docs", "@storybook/addon-a11y"],

  framework: {
    name: "@storybook/nextjs",
    options: {},
  },

  staticDirs: ["../public", { from: "../fonts", to: "/fonts" }, { from: "../stories/assets", to: "/assets" }],

  async webpackFinal(config) {
    // Fixes an issue on windows not transpiling to CJS?
    // It has to be here so it keeps working on nix systems
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    if (config.resolve) {
      config.resolve.alias = {
        ...config.resolve.alias,
        "@": path.resolve(__dirname, "../src"),
      };
    }

    // https://github.com/storybookjs/storybook/issues/18557
    const imageRule = config.module?.rules?.find((rule) => {
      return (rule as { test: RegExp })?.test?.test(".svg") ?? false;
    }) as Record<string, any>;

    imageRule.exclude = /\.svg$/;

    config.module?.rules?.push({
      test: /\.svg$/,
      issuer: /\.[jt]sx?$/,
      use: ["@svgr/webpack"],
    });

    const scssRule = config.module?.rules?.find((rule) => {
      return (rule as { test: RegExp })?.test?.test(".scss") ?? false;
    }) as Record<string, any>;

    // Set the api param to "modern-compiler" to promote modern SCSS syntax
    // and silence JS API Legacy warnings.
    // See https://github.com/storybookjs/storybook/blob/0de5316c80be67621017b9e00e69e8352124be87/code/frameworks/nextjs/src/css/webpack.ts#L58-L66
    if (scssRule?.use?.[4]?.options) {
      scssRule.use[4].options["api"] = "modern-compiler";
    }

    return config;
  },
};

export default config;
