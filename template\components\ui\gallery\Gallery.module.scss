@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.gallery {
  width: 100%;
  container: gallery / inline-size;
}

.grid {
  display: grid;
  grid-template-columns: repeat(var(--gallery-columns, 1), 1fr);

  // In limited space (on desktop only), use flexible columns
  @include breakpoint(large up) {
    @container (max-width: 1200px) {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }
}

.single {
  position: relative;
  width: 100%;
}

.mediaCount {
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 1;
  padding: 16px;
  font-size: 1.2rem;
  line-height: 110%;
  color: $color-white;
  background-color: rgb(0 0 0 / 50%);
}
