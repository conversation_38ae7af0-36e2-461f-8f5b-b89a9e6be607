import "server-only";

import { from, HttpLink } from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { ApolloClient, InMemoryCache, registerApolloClient } from "@apollo/experimental-nextjs-app-support";
import { headers } from "next/headers";

// Log proper errors due to GraphQL endpoint
const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    for (const error of graphQLErrors) console.error(error);
  }

  if (networkError) {
    console.error("Network Error:", networkError);
  }
});

// FIXME: CORS is not working during SSR, so we forward the cookies.
export const {
  getClient: getApolloClient,
  query,
  PreloadQuery,
} = registerApolloClient(async () => {
  const headerList = await headers();

  const httpLink = new HttpLink({
    uri: process.env.GRAPHQL_URL,
    credentials: "include",
    headers: {
      // Forward cookies from initial request to the API.
      cookie: headerList.get("cookie") ?? "",
      // Forward the preview token to the API.
      ["x-preview-token"]: headerList.get("x-preview-token") ?? "",
    },
  });

  return new ApolloClient({
    link: from([errorLink, httpLink]),
    cache: new InMemoryCache({
      typePolicies: {
        SiteConfig: {
          merge: true,
        },
      },
    }),
  });
});
