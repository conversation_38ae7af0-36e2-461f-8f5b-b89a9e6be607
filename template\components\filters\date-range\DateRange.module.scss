@use "@/styles/lib/variables.scss" as *;

.root {
  display: block;
}

.summary {
  display: flex;
  gap: 0.8rem;
  align-items: baseline;
  width: 100%;
  padding: 0 0 1.6rem;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;
  color: $color-black;
  text-align: left;
  cursor: pointer;

  &::marker {
    display: none !important;
  }

  i {
    color: $color-neutral-500;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;

    .root[open] & {
      transform: rotate(0);
    }
  }
}

.form {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  width: 100%;
}

.control {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.submit {
  margin: 0.8rem 0 0;
}
