"use client";

import Tag from "@/components/ui/tag/Tag";
import { FilterInterface, SelectFilterOption } from "@/generated/graphql/graphql";
import { FilterValues, isDateRangeFilter, isSelectFilter } from "@/lib/filters";
import { format as formatDate } from "date-fns";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useMemo } from "react";
import styles from "./FilterSelection.module.scss";

interface FilterSelectionProps {
  filters: FilterInterface[];
  url: string;
}

export default function FilterSelection({ filters = [], url }: FilterSelectionProps) {
  const searchParams = useSearchParams();
  const router = useRouter();

  const filterValues = useMemo(() => {
    const map: FilterValues = new Map();

    for (const filter of filters) {
      if (searchParams.has(filter.attribute) && searchParams.getAll(filter.attribute).some(Boolean)) {
        map.set(filter.attribute, searchParams.getAll(filter.attribute));
      }
    }

    return map;
  }, [filters, searchParams]);

  const flattenOptions = useCallback((options: SelectFilterOption[] = []): SelectFilterOption[] => {
    return options.flatMap((option) => {
      return [option, ...(option.children ? flattenOptions(option.children) : [])];
    });
  }, []);

  const flattenedOptionsMap = useMemo(() => {
    const map = new Map<string, SelectFilterOption[]>();

    for (const filter of filters) {
      if (isSelectFilter(filter)) {
        map.set(filter.attribute, flattenOptions(filter.options || []));
      }
    }

    return map;
  }, [flattenOptions, filters]);

  const hasActiveFilters = useMemo(() => {
    return [...filterValues.values()].some((values) => Array.isArray(values) && values.some(Boolean));
  }, [filterValues]);

  const handleRemoveFilter = useCallback(
    (attribute: string, value?: string, empty: boolean = false) => {
      const params = new URLSearchParams(searchParams.toString());

      if (empty) {
        // Keep some options empty to preserve the order (e.g. DateRangeFilter)
        for (const v of params.getAll(attribute)) {
          params.delete(attribute, v);
          params.append(attribute, v == value ? "" : v);
        }

        // Remove the attribute fully, since it has only empty values
        if (!params.getAll(attribute).some(Boolean)) {
          params.delete(attribute);
        }
      } else {
        params.delete(attribute, value);
      }

      router.push(url + "?" + params.toString(), { scroll: false });

      if (empty) {
        // FIXME: This does not work if the 2 dates has the same value
        const input = document.querySelector<HTMLInputElement>(`form[data-filter='${attribute}'][value="${value}"]`);

        if (input) {
          input.value = "";
        }
      } else {
        document.querySelector<HTMLFormElement>(`form[data-filter='${attribute}']`)?.reset();
      }
    },
    [router, searchParams, url]
  );

  const handleNavigate = useCallback((): void => {
    const forms = document.querySelectorAll<HTMLFormElement>("form[data-filter]");

    for (const form of forms) {
      form.reset();
    }
  }, []);

  return (
    hasActiveFilters && (
      <div className={styles.root} aria-label="Filtres sélectionnés">
        <ul className={styles.list}>
          {filters.map((filter) => {
            const values = filterValues.get(filter.attribute);

            if (!values || values.length === 0 || !values.some(Boolean)) return null;

            const options = isSelectFilter(filter) ? flattenedOptionsMap.get(filter.attribute) || [] : [];

            return values.map((value, index) => {
              if (!value) {
                return;
              }

              let label = isSelectFilter(filter)
                ? options.find((option) => option.value === value)?.label || value
                : value;

              // Preserve the order of params for this attribute
              const empty = isDateRangeFilter(filter) && index === 0;

              if (isDateRangeFilter(filter)) {
                label = `${index === 0 ? "À partir du" : "Jusqu'au"} ${formatDate(value, "dd/MM/yyyy")}`;
              }

              return (
                <li key={`${filter.attribute}-${index}`}>
                  <Tag
                    invert
                    dismissable
                    size="lg"
                    variant="primary"
                    onDismiss={() => handleRemoveFilter(filter.attribute, value, empty)}
                  >
                    {label}
                  </Tag>
                </li>
              );
            });
          })}
        </ul>

        <Link href={url} className={styles.reset} scroll={false} onNavigate={handleNavigate}>
          <i className="far fa-arrows-rotate" aria-hidden="true" />

          <span>Réinitialiser les filtres</span>
        </Link>
      </div>
    )
  );
}
