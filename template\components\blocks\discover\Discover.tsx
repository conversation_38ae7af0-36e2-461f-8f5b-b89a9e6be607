"use client";

import Button from "@/components/ui/button/Button";
import { BlockLayout, DiscoverBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import Image from "next/image";
import Link from "next/link";
import { useId } from "react";
import styles from "./Discover.module.scss";

type DiscoverProps = Omit<DiscoverBlock, "__typename" | "innerBlocks">;

export default function Discover({ anchor, items, layout }: DiscoverProps) {
  const id = useId();

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <ul id={anchor ?? undefined} className={clsx("block-discover", layout === BlockLayout.CONTAINED && "contained")}>
      {items.map((item, index) => {
        const titleId = id + index.toString();
        return (
          <li
            key={index}
            className={clsx(
              styles.discoverItem,
              item.imagePosition?.toLowerCase() === "left" ? styles.imageLeft : styles.imageRight
            )}
          >
            <div className={styles.content}>
              <h2 id={titleId} className={styles.title}>
                {item.title}
              </h2>
              <p className={styles.surtitle} aria-roledescription="Thématique">
                {item.leadText}
              </p>
              {item.action?.url && (
                <Button size="sm" startIcon="far fa-plus" asChild>
                  <Link aria-describedby={titleId} href={item.action.url}>
                    {item.action.text || "Découvrir"}
                  </Link>
                </Button>
              )}
            </div>
            {item.image?.url && (
              <div className={styles.image}>
                <Image
                  src={item.image.url}
                  width={item.image.width}
                  height={item.image.height}
                  alt={item.image.alt ?? ""}
                />
              </div>
            )}
          </li>
        );
      })}
    </ul>
  );
}
