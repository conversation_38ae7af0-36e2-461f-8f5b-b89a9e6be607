@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

%title-after {
  position: absolute;
  top: 10%;
  right: 0;
  width: 1px;
  height: 80%;
  content: "";
}

.trigger {
  display: flex;
  gap: 16px;
  align-items: baseline;
  width: 100%;
  line-height: 120%;
  text-align: left;
  transition:
    color 100ms linear,
    border-color 100ms linear,
    background-color 100ms linear;

  &[data-state="closed"] .closedIcon {
    display: none;
  }

  &[data-state="open"] .openIcon {
    display: none;
  }

  .closedIcon,
  .openIcon {
    cursor: pointer;
  }

  .title {
    position: relative;
    width: 100%;

    &::after {
      @extend %title-after;

      background-color: $color-neutral-300;
    }
  }

  &.level-0 {
    padding: 24px 8px 24px 12px;
    font-size: 2.4rem;
    color: $color-neutral-700;
    text-transform: uppercase;
    border-bottom: 2px solid $color-neutral-500;

    &:hover {
      padding: 24px 8px 22px 12px;
      background-color: $color-neutral-200;
      border-bottom: 4px solid $color-black;
    }

    &[data-state="open"] {
      padding: 24px 8px 20px 12px;
      color: $color-primary-500;
      background-color: $color-primary-50;
      border-bottom: 4px solid transparent;

      .title::after {
        background-color: $color-primary-200;
      }

      &:hover {
        color: $color-neutral-500;
        background-color: $color-neutral-200;
        border-bottom: 4px solid $color-black;

        .title::after {
          background-color: $color-neutral-300;
        }
      }
    }

    i {
      font-size: 2rem;
    }
  }

  &.level-1 {
    padding: 8px 16px 12px;
    font-size: 1.8rem;
    color: $color-neutral-700;
    border-bottom: 1px solid $color-neutral-500;

    &:hover {
      padding: 8px 16px 9px;
      background-color: $color-neutral-200;
      border-bottom: 4px solid $color-neutral-500;
    }

    &[data-state="open"] {
      padding: 8px 16px;
      color: $color-primary-500;
      background-color: $color-primary-50;
      border-bottom: 4px solid transparent;

      .title::after {
        background-color: $color-primary-200;
      }

      &:hover {
        padding: 8px 16px;
        color: $color-neutral-500;
        background-color: $color-neutral-200;
        border-bottom: 4px solid $color-neutral-500;
      }
    }
  }

  &.level-2 {
    padding: 6px 8px 6px 32px;
    font-size: 1.4rem;
    color: $color-neutral-500;

    &:hover {
      background-color: $color-neutral-200;
    }

    .title::after {
      background-color: $color-primary-200;
    }
  }
}

.content {
  &[data-state="closed"] {
    display: none;
  }

  &.level-0 {
    padding: 16px 0 40px;
    border-bottom: 4px solid $color-primary-400;
  }

  &.level-1 {
    padding: 16px 0 24px;
    border-bottom: 2px solid $color-primary-500;
  }
}
