"use client";

import Highlighter from "@/components/utils/Highlighter";
import type { Searchable } from "@/generated/graphql/graphql";
import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import type { PartialDeep } from "type-fest";
import styles from "./Item.module.scss";

interface ItemProps {
  detail: PartialDeep<Searchable, { recurseIntoArrays: true }>;
  searchWords?: string[];
}

export default function Item({
  detail: { images, url, title, categories, leadText, surtitle, modifiedDate },
  searchWords = [],
}: ItemProps) {
  const [category] = categories ?? [];
  const image = images?.ratio_3x2 ?? null;
  const formattedDate = modifiedDate && format(new Date(modifiedDate), "dd/MM/yyyy");

  title ??= "Sans titre";

  // Highlight searched text
  function highlight(text?: string) {
    return <Highlighter highlightClassName={styles.highlight} searchWords={searchWords} textToHighlight={text ?? ""} />;
  }

  return (
    <article className={styles.searchItem}>
      <div className={styles.details}>
        <h3 className={styles.title}>
          {surtitle ? (
            <span className={styles.category}>{highlight(surtitle)}</span>
          ) : (
            category && (
              <span className={styles.category}>
                {highlight(category.title)}
                <span className="sr-only">:</span>
              </span>
            )
          )}
          {url ? (
            <Link href={url} className={styles.titleLink}>
              {highlight(title)}
            </Link>
          ) : (
            highlight(title)
          )}
        </h3>
        <div className={styles.infos}>
          {formattedDate && <div className={styles.date}>Mis à jour le {formattedDate}</div>}
          {leadText && <p className={styles.description}>{highlight(leadText)}</p>}
        </div>
      </div>
      {image?.url && (
        <div className={styles.imageWrapper}>
          <Image src={image.url} width={image.width} height={image.height} alt={image.alt ?? ""} sizes="176px" />
        </div>
      )}
    </article>
  );
}
