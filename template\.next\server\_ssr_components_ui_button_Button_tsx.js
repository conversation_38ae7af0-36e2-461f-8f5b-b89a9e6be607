/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_ui_button_Button_tsx";
exports.ids = ["_ssr_components_ui_button_Button_tsx"];
exports.modules = {

/***/ "(ssr)/./components/ui/button/Button.module.scss":
/*!*************************************************!*\
  !*** ./components/ui/button/Button.module.scss ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"button\": \"Button_button__qOu8O\",\n\t\"variant-contained\": \"Button_variant-contained__zkX_O\",\n\t\"color-primary\": \"Button_color-primary__s9PDR\",\n\t\"color-secondary\": \"Button_color-secondary__PUGgD\",\n\t\"color-tertiary\": \"Button_color-tertiary__UiubZ\",\n\t\"color-primary-inverted\": \"Button_color-primary-inverted__wtAtY\",\n\t\"color-secondary-inverted\": \"Button_color-secondary-inverted__6dhHp\",\n\t\"color-tertiary-inverted\": \"Button_color-tertiary-inverted__HBWl5\",\n\t\"color-danger-inverted\": \"Button_color-danger-inverted__p6aqN\",\n\t\"variant-outlined\": \"Button_variant-outlined__hqGws\",\n\t\"variant-text\": \"Button_variant-text__zAK9M\",\n\t\"size-xs\": \"Button_size-xs__riZtg\",\n\t\"size-sm\": \"Button_size-sm__wJOqU\",\n\t\"size-md\": \"Button_size-md__k78ss\",\n\t\"size-lg\": \"Button_size-lg__jW51I\",\n\t\"startIcon\": \"Button_startIcon__gXO6b\",\n\t\"endIcon\": \"Button_endIcon__HyjGg\"\n};\n\nmodule.exports.__checksum = \"c49dcf786733\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2J1dHRvbi9CdXR0b24ubW9kdWxlLnNjc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcY2l0ZW9wb2xpcy1mcm9udGVuZFxcdGVtcGxhdGVcXGNvbXBvbmVudHNcXHVpXFxidXR0b25cXEJ1dHRvbi5tb2R1bGUuc2NzcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJidXR0b25cIjogXCJCdXR0b25fYnV0dG9uX19xT3U4T1wiLFxuXHRcInZhcmlhbnQtY29udGFpbmVkXCI6IFwiQnV0dG9uX3ZhcmlhbnQtY29udGFpbmVkX196a1hfT1wiLFxuXHRcImNvbG9yLXByaW1hcnlcIjogXCJCdXR0b25fY29sb3ItcHJpbWFyeV9fczlQRFJcIixcblx0XCJjb2xvci1zZWNvbmRhcnlcIjogXCJCdXR0b25fY29sb3Itc2Vjb25kYXJ5X19QVUdnRFwiLFxuXHRcImNvbG9yLXRlcnRpYXJ5XCI6IFwiQnV0dG9uX2NvbG9yLXRlcnRpYXJ5X19VaXViWlwiLFxuXHRcImNvbG9yLXByaW1hcnktaW52ZXJ0ZWRcIjogXCJCdXR0b25fY29sb3ItcHJpbWFyeS1pbnZlcnRlZF9fd3RBdFlcIixcblx0XCJjb2xvci1zZWNvbmRhcnktaW52ZXJ0ZWRcIjogXCJCdXR0b25fY29sb3Itc2Vjb25kYXJ5LWludmVydGVkX182ZGhIcFwiLFxuXHRcImNvbG9yLXRlcnRpYXJ5LWludmVydGVkXCI6IFwiQnV0dG9uX2NvbG9yLXRlcnRpYXJ5LWludmVydGVkX19IQldsNVwiLFxuXHRcImNvbG9yLWRhbmdlci1pbnZlcnRlZFwiOiBcIkJ1dHRvbl9jb2xvci1kYW5nZXItaW52ZXJ0ZWRfX3A2YXFOXCIsXG5cdFwidmFyaWFudC1vdXRsaW5lZFwiOiBcIkJ1dHRvbl92YXJpYW50LW91dGxpbmVkX19ocUd3c1wiLFxuXHRcInZhcmlhbnQtdGV4dFwiOiBcIkJ1dHRvbl92YXJpYW50LXRleHRfX3pBSzlNXCIsXG5cdFwic2l6ZS14c1wiOiBcIkJ1dHRvbl9zaXplLXhzX19yaVp0Z1wiLFxuXHRcInNpemUtc21cIjogXCJCdXR0b25fc2l6ZS1zbV9fd0pPcVVcIixcblx0XCJzaXplLW1kXCI6IFwiQnV0dG9uX3NpemUtbWRfX2s3OHNzXCIsXG5cdFwic2l6ZS1sZ1wiOiBcIkJ1dHRvbl9zaXplLWxnX19qVzUxSVwiLFxuXHRcInN0YXJ0SWNvblwiOiBcIkJ1dHRvbl9zdGFydEljb25fX2dYTzZiXCIsXG5cdFwiZW5kSWNvblwiOiBcIkJ1dHRvbl9lbmRJY29uX19IeWpHZ1wiXG59O1xuXG5tb2R1bGUuZXhwb3J0cy5fX2NoZWNrc3VtID0gXCJjNDlkY2Y3ODY3MzNcIlxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button/Button.module.scss\n");

/***/ }),

/***/ "(ssr)/./components/ui/button/Button.tsx":
/*!*****************************************!*\
  !*** ./components/ui/button/Button.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@19.1.9_react@19.1.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(ssr)/../node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Button_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button.module.scss */ \"(ssr)/./components/ui/button/Button.module.scss\");\n/* harmony import */ var _Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_Button_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n// TODO: Add \"only icon\" option\n// TODO: Add \"inverted colors\" option, for dark bg (use a specific global class? e.g. dark)\n// TODO: Add \"neutral\" color\nfunction Button({ ref, variant = \"contained\", size = \"md\", color = \"primary\", className, startIcon, endIcon, children, asChild, disabled = false, onClick, onKeyDown, ...restProps }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    function handleClick(event) {\n        if (event.currentTarget instanceof HTMLButtonElement) {\n            if (disabled) {\n                event.preventDefault();\n                event.stopPropagation();\n            } else {\n                onClick?.(event);\n            }\n        }\n    }\n    function handleKeyDown(event) {\n        if (event.key !== \" \" && event.key !== \"Enter\") {\n            return;\n        }\n        if (event.currentTarget instanceof HTMLButtonElement) {\n            if (disabled) {\n                event.preventDefault();\n                event.stopPropagation();\n            } else {\n                onKeyDown?.(event);\n            }\n        }\n    }\n    const { type, tabIndex } = restProps;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        ref: ref,\n        \"aria-disabled\": disabled ? true : undefined,\n        tabIndex: disabled && type !== \"submit\" && type !== \"reset\" ? -1 : tabIndex,\n        className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().button), (_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[`variant-${variant}`], (_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[`color-${color}`], (_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[`size-${size}`], className),\n        ...restProps,\n        onClick: handleClick,\n        onKeyDown: handleKeyDown,\n        children: [\n            startIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonIconSlot, {\n                icon: startIcon,\n                className: (_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().startIcon)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n                lineNumber: 92,\n                columnNumber: 21\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slottable, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            endIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ButtonIconSlot, {\n                icon: endIcon,\n                className: (_Button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().endIcon)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n                lineNumber: 94,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\nfunction ButtonIconSlot({ icon, className }) {\n    if (typeof icon === \"string\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n            className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(icon, className),\n            \"aria-hidden\": \"true\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n            lineNumber: 101,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: className,\n        children: icon\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\citeopolis-frontend\\\\template\\\\components\\\\ui\\\\button\\\\Button.tsx\",\n        lineNumber: 104,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button/Button.tsx\n");

/***/ })

};
;