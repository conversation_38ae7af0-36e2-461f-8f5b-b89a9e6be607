@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.accordionItem {
  margin-bottom: 6px;

  &:last-child {
    margin-bottom: 0;
  }

  .trigger {
    display: flex;
    padding: 16px 12px;
    font-size: 1.6rem;
    line-height: 110%;
    cursor: pointer;
    border-bottom: 1px solid $color-neutral-500;

    @include breakpoint(medium up) {
      padding: 24px 12px;
      font-size: 2rem;
    }
  }

  &[open] {
    .trigger {
      font-weight: 700;
      color: $color-primary-500;
      border-bottom: 4px solid currentcolor;
    }
  }

  .content {
    padding-bottom: 24px;
    margin-top: 12px;

    @include breakpoint(medium up) {
      padding-bottom: 32px;
      margin-top: 16px;
    }
  }
}

.tab {
  position: relative;
  flex: 1;
  padding: 24px 12px;
  font-size: 2rem;
  cursor: pointer;
  transition: color 200ms ease;

  &:hover {
    color: $color-primary-500;
  }

  &[data-state="active"] {
    font-weight: $fw-bold;
    color: $color-primary-500;
  }
}

.tabPanel {
  padding-block: 16px;
  animation: fade-in 300ms ease;

  &[data-state="inactive"] {
    display: none;
  }
}

.tab[data-orientation="vertical"] {
  padding: 16px 12px;
  font-size: 2rem;
  text-align: left;
  cursor: pointer;
  transition: color 200ms ease;

  &:hover {
    color: $color-primary-500;
  }

  &[data-state="active"] {
    font-weight: $fw-bold;
    color: $color-primary-500;
  }
}

.tabPanel[data-orientation="vertical"] {
  flex: 1;
  padding: 12px 0 12px 32px;
  animation: fade-in 0.3s ease;

  &[data-state="inactive"] {
    display: none;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(5px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
