---
title: "Navigation Indicator"
weight: 1
---

# Navigation Indicator

When [client side navigation](https://nextjs.org/docs/app/getting-started/linking-and-navigating) is enabled, only part of the DOM is updated.

To tell the user something happend wo have a progress bar at the top of the website to indicate the change of navigation state.

You can read the documentation of **@bprogress/next** here: https://bprogress.vercel.app/docs/next/quick-start

{{% hint info %}}
Sometimes a link has additional capabilities, for example, open a lightbox.<br>
To disable the progress bar on click, use the `data-disable-progress={true}` attribute.
{{% /hint %}}
