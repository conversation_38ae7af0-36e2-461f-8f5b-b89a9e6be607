import { MenuItem } from "@/generated/graphql/graphql";
import { AccordionContent, Root } from "@radix-ui/react-accordion";
import clsx from "clsx";
import Link from "next/link";
import ToggleMenuItem from "./ToggleMenuItem";
import itemStyles from "./ToggleMenuItem.module.scss";

interface ToggleMenuProps {
  menuItems?: MenuItem[];
}

export default function ToggleMenu({ menuItems = [] }: ToggleMenuProps) {
  if (menuItems.length === 0) return null;

  return (
    <Root type="multiple" style={{ width: "100%" }}>
      <ul>
        {menuItems.map(({ children, level, title, url }, index) => {
          return children && children.length > 0 ? (
            <ToggleMenuItem key={index} title={title} level={level} url={url}>
              {children && children.length > 0 && (
                <AccordionContent>
                  <ToggleMenu menuItems={children} />
                </AccordionContent>
              )}
            </ToggleMenuItem>
          ) : (
            <li key={index}>
              <Link className={clsx(itemStyles.trigger, itemStyles[`level-${level}`])} href={url}>
                {title}
              </Link>
            </li>
          );
        })}
      </ul>
    </Root>
  );
}
