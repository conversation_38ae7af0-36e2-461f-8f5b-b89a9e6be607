@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.container {
  @extend %text-wrap;

  padding-block: 24px;
  margin-block: 12px;
  container: block-frame / inline-size;

  @include breakpoint(large up) {
    padding-block: 32px;
    margin-block: 16px;
  }

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.frame {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 24px 16px;
  border: 4px solid currentcolor;
  border-radius: 8px;

  @include breakpoint(large up) {
    @container (min-width: 400px) {
      flex-direction: row;
      gap: 10px;
      padding: 32px;
    }
  }

  &.variant-primary {
    border-color: $color-primary-500;

    .icon {
      color: $color-primary-500;
    }
  }

  &.variant-secondary {
    border-color: $color-tertiary-500;

    .icon {
      color: $color-tertiary-800;

      &::after {
        background-color: $color-tertiary-500;
      }
    }
  }

  &.variant-tertiary {
    border-color: $color-secondary-500;

    .icon {
      color: $color-secondary-800;

      &::after {
        background-color: $color-secondary-500;
      }
    }
  }
}

.icon {
  display: flex;
  flex-shrink: 0;
  gap: 15px;
  align-items: center;
  width: 100%;
  height: auto;
  font-size: 3.2rem;

  @include breakpoint(large up) {
    @container (min-width: 400px) {
      flex-direction: column;
      width: auto;
      min-width: 56px;
      font-size: 4.8rem;
    }
  }

  &::after {
    flex: 1;
    width: 100%;
    height: 1px;
    content: "";
    background-color: currentcolor;

    @include breakpoint(large up) {
      @container (min-width: 400px) {
        width: 1px;
        height: 100%;
      }
    }
  }
}

.content {
  flex-grow: 1;
}
