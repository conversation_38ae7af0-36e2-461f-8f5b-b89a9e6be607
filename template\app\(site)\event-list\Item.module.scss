@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.eventItem {
  position: relative;
  display: flex;
  flex-direction: column-reverse;
  gap: 12px;

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.imageWrapper {
  img {
    @include object-fit;

    width: 100%;
  }
}

.top {
  display: flex;
  flex-direction: row-reverse;
  gap: 16px;
  justify-content: left;

  @include breakpoint(small down) {
    .imageWrapper {
      max-width: 74px;
    }
  }

  @container (min-width: 800px) {
    flex-direction: column-reverse;
  }

  @include breakpoint(medium up) {
    flex-direction: column-reverse;
    gap: 12px;
  }

  @include breakpoint(large up) {
    gap: 16px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.infos {
  margin-top: 24px;
  font-size: 1.2rem;
  line-height: 110%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }

  @include breakpoint(large up) {
    margin-top: 32px;
  }

  p {
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .city {
    font-size: 1.4rem;
    font-weight: 700;
    line-height: 130%;

    @include breakpoint(medium up) {
      font-size: 1.6rem;
    }
  }

  .place {
    font-weight: 700;
  }
}
