"use client";

import Toast from "@/components/ui/toast/Toast";
import { useCallback, useEffect, useRef } from "react";

type SubmissionState = {
  isSuccess?: boolean;
  successMessage?: string | null;
  error?: string | null;
  isSubmitting?: boolean;
};

export default function FormFeedback({
  submissionState,
  onReset,
  onStepReset,
  onNavigateToStep,
}: {
  submissionState: SubmissionState;
  onReset: () => void;
  onStepReset: () => void;
  onNavigateToStep: (step: number) => void;
}) {
  const hasHandledSuccess = useRef(false);
  const hasHandledError = useRef(false);

  const handleStartOver = useCallback(() => {
    onReset();
    onStepReset();
  }, [onReset, onStepReset]);

  const handleGoToError = useCallback(() => {
    onNavigateToStep(0);
  }, [onNavigateToStep]);

  useEffect(() => {
    if (!submissionState) {
      hasHandledSuccess.current = false;
      hasHandledError.current = false;
      return;
    }

    if (submissionState.isSuccess && !hasHandledSuccess.current) {
      hasHandledSuccess.current = true;

      // addToast({
      //   variant: "success",
      //   duration: 15000,
      //   children: submissionState.successMessage ?? "Formulaire envoyé avec succès.",
      //   actionText: "Recommencer",
      //   actionProps: {
      //     onClick: handleStartOver,
      //   },
      // });
    } else if (submissionState.error && !hasHandledError.current) {
      hasHandledError.current = true;

      // addToast(
      //   <Toast
      //     variant="error"
      //     duration={15000}
      //     actionText="Corriger les erreurs"
      //     actionProps={{
      //       altText: "Aller à l'étape incomplète",
      //       onClick: handleGoToError,
      //     }}
      //   >
      //     {submissionState.error ?? "Une erreur est survenue."}
      //   </Toast>
      // );
    }
  }, [submissionState, handleStartOver, handleGoToError]);

  return (
    <>
      {submissionState?.isSuccess && (
        <Toast
          variant="success"
          duration={15_000}
          actionText="Recommencer"
          actionProps={{
            altText: "Revenir au formulaire",
            onClick: handleStartOver,
          }}
        >
          {submissionState.successMessage ?? "Formulaire envoyé avec succès."}
        </Toast>
      )}
      {submissionState?.error && (
        <Toast
          variant="error"
          duration={15_000}
          actionText="Corriger les erreurs"
          actionProps={{
            altText: "Aller à l'étape incomplète",
            onClick: handleGoToError,
          }}
        >
          {submissionState.error ?? "Une erreur est survenue."}
        </Toast>
      )}
    </>
  );
}
