@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/variables.scss" as *;

.nav {
  width: 100%;
  height: 72px;

  @include breakpoint(large up) {
    gap: 8px;
    padding: 0 16px;
    margin-left: auto;
  }
}

.navList {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-bottom: 1px;
  padding-left: 0;
  margin: 0;
  list-style: none;
}

.navItem {
  display: flex;
  height: 100%;
}

.navLink {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-width: 90px;
  padding: 8px 0 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 1.2rem;
  line-height: 120%;
  color: $color-neutral-500;
  text-align: center;
  text-decoration: none;

  @include breakpoint(medium up) {
    flex-direction: row;
    min-width: 145px;
  }
}

.navLink:hover {
  color: $color-primary-500;
}

.navLinkActive {
  color: $color-primary-500;
  border-bottom: 4px solid $color-primary-500;
}

.navIcon {
  display: flex;
  flex-shrink: 0;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 32px;
  padding: 4px 20px;
  font-size: 1.8rem;

  @include breakpoint(medium up) {
    width: auto;
    padding: 0 5px;
  }
}
