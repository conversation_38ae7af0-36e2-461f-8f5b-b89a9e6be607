# Modal Component Documentation

The `Modal` component is a flexible and accessible overlay for displaying content with configurable sizes and focus management.

## Key Features:

- **Focus Management**:  
  Control which element receives focus when the modal opens and closes.

- **Size Control**:  
  Choose from predefined sizes (`"fullscreen"`, `"auto"`) or apply custom classes for different modal sizes.

- **Customizable Header Action**:  
  Add buttons or interactive elements in the modal's header.

## Props:

- **`size`**: Defines the modal's size.  
  Options:

  - `"fullscreen"`: The modal takes up the entire viewport.
  - `"auto"`: The modal size adjusts based on its content.

- **`focusStrategy`**: Controls focus behavior when the modal opens:

  - `"close-button"` (default): Focuses on the close button.
  - `"content"`: Focuses on the first focusable element inside the modal.

- **`headerAction`**: Optional content for the modal’s header (e.g., buttons or other interactive elements).

## Technical Notes:

- **Focusable Elements**:  
  Uses `tabbable` to ensure proper focus management within the modal. This ensures the focus is placed on appropriate elements inside the modal.

- **Custom Sizing**:  
  In addition to the predefined sizes, custom modal sizes can be applied using `size-{size}` classes for more tailored size options.

---

## Example Usage:

```tsx
import { useModal } from "./ModalProvider";
import Button from "@/components/ui/button/Button";

const MyComponent = () => {
  const { openModal } = useModal();

  return (
    <Button
      onClick={() =>
        openModal({
          title: "My Modal",
          content: <div>This is the content.</div>,
          size: "auto",
          focusStrategy: "content",
        })
      }
    >
      Open Modal
    </Button>
  );
};
```
