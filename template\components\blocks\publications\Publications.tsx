"use client";

import Hx from "@/components/ui/title/Hx";
import type { Publication, PublicationsBlock } from "@/generated/graphql/graphql";
import { SubtitleLevelProvider } from "@/lib/hooks/useTitleLevel";
import clsx from "clsx";
import dynamic from "next/dynamic";
import Link from "next/link";
import styles from "./Publications.module.scss";

const Button = dynamic(() => import("@/components/ui/button/Button"));
const PublicationsList = dynamic(() => import("./PublicationsList"));

type PublicationsProps = Partial<Omit<PublicationsBlock, "__typename" | "innerBlocks">>;

/**
 * A block that displays a selection of publications.
 */
export default function Publications({ anchor, listUrl, publications, title, titleLevel }: PublicationsProps) {
  const entries = publications?.filter((publication: Publication) => {
    return publication.files?.some((file) => file.viewUrl?.trim());
  });

  if (!entries || entries.length === 0) {
    return null;
  }

  return (
    <section id={anchor ?? undefined} className={clsx("block-publications contained", styles.publications)}>
      {title && (
        <Hx level={titleLevel} className={styles.title}>
          {title}
        </Hx>
      )}
      <SubtitleLevelProvider level={titleLevel}>
        <PublicationsList items={entries} />
      </SubtitleLevelProvider>
      {listUrl && (
        <div className={styles.actions}>
          <Button asChild variant="contained" size="lg" className={styles.button} startIcon="fas fa-plus">
            <Link href={listUrl}>Toutes les publications</Link>
          </Button>
        </div>
      )}
    </section>
  );
}
