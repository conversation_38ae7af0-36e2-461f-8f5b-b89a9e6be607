@use "@/styles/lib/variables.scss" as *;

// Check if the Hero block is the first of element of the content,
// then put the main header over it, so the hero fills the full height.
.site-wrapper:has(.main > .block-hero:first-child) {
  .layout-1column-fullwidth {
    margin-top: 0;
  }

  // feature-start google-translate
  // Push the admin bar further down since it's fixed
  // if the `GoogletTranslate` iframe is enabled.
  div.skiptranslate[style=""] ~ & {
    #admin-bar {
      top: 40px;
    }
  }

  // feature-end google-translate
}
