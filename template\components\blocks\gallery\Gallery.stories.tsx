import { OneThirdColumnsDecorator, TwoThirdColumnsDecorator } from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Image from "../image/Image";
import Gallery from "./Gallery";

const meta: Meta<typeof Gallery> = {
  title: "Blocks/Gallery",
  component: Gallery,
  tags: ["autodocs"],
  argTypes: {
    columns: {
      control: "number",
      description: "Number of columns in the gallery grid",
      defaultValue: 3,
    },
  },
};

export default meta;

type Story = StoryObj<typeof Gallery>;

const imageData = (n: number) => ({
  src: `https://placehold.co/720x480?text=${n}`,
  width: 720,
  height: 480,
  caption:
    "Texte au survol, vertical-align bottom, max 5 lignes ipsum dolor sit amet, consectetur adipiscing euismod mi mi, malesuada loborti sce euismod mi mi, malesuada lobortis mauris venenatis a. Nam nulla dui, eu cursus in, porta sed lectus lorem ipsum.",
});

export const Default: Story = {
  args: {
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
        <Image key="2" {...imageData(2)} alt="" />
        <Image key="3" {...imageData(3)} alt="" />
        <Image key="4" {...imageData(4)} alt="" />
        <Image key="5" {...imageData(5)} alt="" />
        <Image key="6" {...imageData(6)} alt="" />
      </>
    ),
  },
};

export const TwoColumns: Story = {
  args: {
    columns: 2,
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
        <Image key="2" {...imageData(2)} alt="" />
        <Image key="3" {...imageData(3)} alt="" />
        <Image key="4" {...imageData(4)} alt="" />
      </>
    ),
  },
};

export const OneColumn: Story = {
  args: {
    columns: 1,
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
        <Image key="2" {...imageData(2)} alt="" />
        <Image key="3" {...imageData(3)} alt="" />
      </>
    ),
  },
};

export const OneImage: Story = {
  args: {
    columns: 1,
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
      </>
    ),
  },
};

export const ContentTwoColumns: Story = {
  args: {
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
        <Image key="2" {...imageData(2)} alt="" />
        <Image key="3" {...imageData(3)} alt="" />
        <Image key="4" {...imageData(4)} alt="" />
        <Image key="5" {...imageData(5)} alt="" />
        <Image key="6" {...imageData(6)} alt="" />
      </>
    ),
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const ContentOneColumn: Story = {
  args: {
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" />
        <Image key="2" {...imageData(2)} alt="" />
        <Image key="3" {...imageData(3)} alt="" />
        <Image key="4" {...imageData(4)} alt="" />
        <Image key="5" {...imageData(5)} alt="" />
        <Image key="6" {...imageData(6)} alt="" />
      </>
    ),
  },
  decorators: [OneThirdColumnsDecorator],
};

/**
 * Should not render images with a `<figure>` element if it has no caption.
 */
export const WithoutCaption: Story = {
  args: {
    children: (
      <>
        <Image key="1" {...imageData(1)} alt="" caption={undefined} />
        <Image key="2" {...imageData(2)} alt="" caption={undefined} />
        <Image key="3" {...imageData(3)} alt="" caption={undefined} />
      </>
    ),
  },
};
