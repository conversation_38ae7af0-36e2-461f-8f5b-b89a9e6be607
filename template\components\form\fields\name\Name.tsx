"use client";

import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Select from "@/components/ui/select/Select";
import Textfield from "@/components/ui/textfield/Textfield";
import { NameField } from "@/generated/graphql/graphql";
import { useId } from "react";
import { Controller, useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";
import styles from "./Name.module.scss";

type NameProps = Omit<NameField, "__typename">;

const prefixData = {
  required: false,
  placeholder: "Civilité",
  choices: [
    { value: "Mr", label: "Monsieur", defaultSelected: false, icon: null },
    { value: "Mme", label: "Madame", defaultSelected: false, icon: null },
    { value: "Other", label: "Other", defaultSelected: false, icon: null },
  ],
};

export default function Name({
  name,
  autocomplete,
  defaultValue,
  label,
  description,
  placeholder,
  required,
  condition,
  validationMessage,
  firstName,
  lastName,
  additionalName,
  prefix,
  suffix,
  columnSpan,
}: NameProps) {
  const { register, control } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const inputId = useId();
  const errorId = useId();

  if (firstName || lastName || additionalName || prefix || suffix) {
    return (
      visible && (
        <FormControl columnSpan={columnSpan}>
          <Label description={description ?? undefined} required={required}>
            {label}
          </Label>

          <div className={styles.wrapper}>
            {prefix && (
              <Controller
                name={prefix.name}
                control={control}
                rules={{ required: prefix.required ? "Ce champ est obligatoire" : false }}
                defaultValue={prefix.defaultValue ?? ""}
                render={({ field: { onChange, value } }) => (
                  <Select
                    value={value}
                    onChange={(event) => onChange(event.target.value)}
                    placeholder={prefixData.placeholder}
                    required={prefixData.required}
                    error={!!error}
                  >
                    {prefixData.choices
                      ?.filter((choice) => choice !== null)
                      .map((choice) => (
                        <option key={choice!.value} value={choice!.value ?? ""}>
                          {choice!.label}
                        </option>
                      ))}
                  </Select>
                )}
              />
            )}

            {firstName && (
              <Textfield
                placeholder={firstName.placeholder ?? undefined}
                {...register(firstName.name, { required: firstName.required })}
              />
            )}

            {additionalName && (
              <Textfield
                placeholder={additionalName.placeholder ?? undefined}
                {...register(additionalName.name, { required: additionalName.required })}
              />
            )}

            {lastName && (
              <Textfield
                placeholder={lastName.placeholder ?? undefined}
                {...register(lastName.name, { required: lastName.required })}
              />
            )}

            {suffix && (
              <Textfield
                placeholder={suffix.placeholder ?? undefined}
                {...register(suffix.name, { required: suffix.required })}
              />
            )}
          </div>

          {error && (
            <FormHelper id={errorId} variant="error">
              {error.message?.toString()}
            </FormHelper>
          )}
        </FormControl>
      )
    );
  }

  return (
    visible && (
      <FormControl>
        <Label htmlFor={inputId} description={description ?? undefined} required={required}>
          {label}
        </Label>

        <Textfield
          id={inputId}
          autoComplete={autocomplete ?? undefined}
          defaultValue={defaultValue ?? undefined}
          placeholder={placeholder ?? undefined}
          error={!!error}
          aria-describedby={error ? errorId : undefined}
          aria-invalid={error ? true : undefined}
          {...register(name, { required })}
        />

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
