{"name": "@citeopolis/eslint-config", "version": "0.3.1", "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend", "directory": "packages/eslint-config"}, "type": "module", "exports": {".": {"import": "./eslint.config.mjs"}}, "files": ["./eslint.config.mjs"], "scripts": {"format": "prettier -w .", "test": "vitest run"}, "lint-staged": {"*.{json,md}": "prettier --write", "*.ts": ["prettier --write", "vitest related --run"]}, "prettier": "@citeopolis/prettier-config", "dependencies": {"@creedengo/eslint-plugin": "^2.1.0", "@eslint-community/eslint-plugin-eslint-comments": "^4.5.0", "@eslint-react/eslint-plugin": "^1.52.3", "@eslint/eslintrc": "^3.3.1", "@stylistic/eslint-plugin": "^5.2.3", "eslint-plugin-clsx": "^0.0.10", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unicorn": "^60.0.0", "globals": "^16.3.0", "typescript-eslint": "^8.38.0"}, "devDependencies": {"@citeopolis/prettier-config": "workspace:^", "eslint": "^9.31.0", "prettier": "^3.6.2", "vitest": "^3.2.4"}, "peerDependencies": {"eslint": "^9.31.0"}}