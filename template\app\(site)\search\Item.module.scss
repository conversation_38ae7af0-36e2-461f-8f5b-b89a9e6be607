@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.searchItem {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: $space-6;

  @include breakpoint(medium up) {
    flex-direction: row;
    gap: $space-7;
  }

  @include breakpoint(large up) {
    gap: $space-8;
  }
}

.details {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: $space-5;

  @include breakpoint(large up) {
    gap: $space-6;
  }
}

.imageWrapper {
  order: -1;
  width: 176px;
  height: 117px;

  img {
    width: 176px;
    height: 117px;
  }
}

.title {
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.infos {
  display: flex;
  flex-direction: column;
  gap: $space-5;

  @include breakpoint(large up) {
    gap: $space-6;
  }
}

.date {
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 120%;
  color: $color-neutral-500;
}

.description {
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
}

.highlight {
  background-color: $color-chart-7;
}
