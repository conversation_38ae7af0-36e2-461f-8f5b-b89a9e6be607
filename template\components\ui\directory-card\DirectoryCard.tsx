import type { Directory, DirectoryViewMode } from "@/generated/graphql/graphql";
import dynamic from "next/dynamic";
import { ComponentType } from "react";
import { PartialDeep } from "type-fest";

const Location = dynamic(() => import("./LocationDirectoryCard"));
const Person = dynamic(() => import("./PersonDirectoryCard"));

interface DirectoryCardProps {
  directory: PartialDeep<Directory, { recurseIntoArrays: true }> & Pick<Directory, "viewMode">;
}

const componentMap: Partial<Record<DirectoryViewMode, ComponentType<DirectoryCardProps>>> = {
  PERSON: Person,
  LOCATION: Location,
};

/**
 * Render the card of a directory according to the defined `viewMode`.
 */
export default function DirectoryCard({ directory }: DirectoryCardProps) {
  const Component = componentMap[directory.viewMode];
  return Component ? <Component directory={directory} /> : null;
}
