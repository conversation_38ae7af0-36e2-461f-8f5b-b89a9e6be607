@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.resolutionItem {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 6px;

  @include breakpoint(large up) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

.details {
  display: flex;
  flex-grow: 1;
  flex-direction: column;
  gap: 6px;

  @include breakpoint(large up) {
    gap: 8px;
  }
}

.title {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    font-size: 2.4rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.infos {
  flex-shrink: 0;
}

.issueDate {
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 150%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}

.fileCount {
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}

.publicationDate {
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 120%;
  color: $color-neutral-500;

  @include breakpoint(medium up) {
    font-size: 1.4rem;
  }
}
