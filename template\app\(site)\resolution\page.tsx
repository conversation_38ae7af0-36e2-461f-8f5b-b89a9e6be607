import BlockRenderer from "@/components/blocks/BlockRenderer";
import Breadcrumbs from "@/components/ui/breadcrumbs/Breadcrumbs";
import Heading from "@/components/ui/heading/HeadingPublication";
import SinglePagination from "@/components/ui/pagination/SinglePagination";
import { graphql } from "@/generated/graphql";
import { query } from "@/lib/graphql";
import { getCurrentServerUrl } from "@/utils/url";
import { format as formatDate } from "date-fns";
import { fr } from "date-fns/locale/fr";
import { Metadata } from "next";
import dynamic from "next/dynamic";
import assert from "node:assert";
import ResolutionFileList from "./ResolutionFileList";

const SocialShare = dynamic(() => import("@/components/ui/share/SocialShare"));

const RESOLUTION_QUERY = graphql(`
  query GetResolution($url: URL!) {
    route(url: $url) {
      ... on Resolution {
        breadcrumbs {
          items {
            title
            url
            siblings {
              title
              url
            }
          }
        }
        categories {
          relativeUrl
          title
          parent {
            __typename
          }
        }
        files {
          downloadUrl
          extname
          label
          mime
          size
          viewUrl
        }
        images {
          ratio_3x2 {
            alt
            height
            url
            width
          }
        }
        issueDate
        leadText
        modifiedDate
        publicationDate
        structuredContent
        surtitle
        pager {
          list {
            text
            url
          }
          next {
            text
            url
          }
          prev {
            text
            url
          }
        }
        title
        metadata {
          description
          title
        }
      }
    }
    siteConfig {
      socialShare
    }
  }
`);

export async function generateMetadata(): Promise<Metadata> {
  const { data } = await query({
    query: RESOLUTION_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "ignore",
  });

  assert.ok(data.route?.__typename === "Resolution");

  const { metadata } = data.route ?? {};

  return {
    title: metadata?.title,
    description: metadata?.description,
  };
}

export default async function Page() {
  const { data } = await query({
    query: RESOLUTION_QUERY,
    variables: { url: await getCurrentServerUrl() },
    errorPolicy: "all",
  });

  assert.ok(data.route?.__typename === "Resolution");

  const {
    title,
    leadText,
    categories,
    breadcrumbs,
    images,
    modifiedDate,
    publicationDate,
    issueDate,
    structuredContent,
    pager,
    files,
  } = data.route;

  const { socialShare } = data?.siteConfig ?? {};

  const surtitle = categories
    .filter((category) => !category.parent)
    .slice(0, 4)
    .map((category) => category.title)
    .filter(Boolean)
    .join(", ");

  const tags = categories.filter((category) => category.parent).map((category) => ({ text: category.title }));

  const formattedIssueDate = issueDate ? formatDate(new Date(issueDate), "eeee dd MMMM yyyy", { locale: fr }) : null;

  return (
    <>
      <Breadcrumbs items={breadcrumbs.items} />
      <Heading
        surtitle={surtitle}
        title={title ?? "Sans titre"}
        subtitle={issueDate ? `Date de parution : le ${formattedIssueDate}` : undefined}
        leadText={leadText}
        tags={tags}
        tagsRoleDescription="Thématiques"
        imageSrc={images?.ratio_3x2?.url}
        publicationDate={publicationDate}
        modifiedDate={modifiedDate}
      />
      <div className="layout-1column-fullwidth">
        <div className="column main">
          <ResolutionFileList files={files} />
          <BlockRenderer structuredContent={structuredContent} />
          {socialShare && <SocialShare />}
        </div>
      </div>
      {pager && <SinglePagination className="container" pager={pager} />}
    </>
  );
}
