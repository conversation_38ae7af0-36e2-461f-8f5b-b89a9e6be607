import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import { SlideIframe, SlideImage } from "yet-another-react-lightbox";
import Gallery from "./Gallery";

const meta: Meta<typeof Gallery> = {
  title: "Components/Gallery",
  component: Gallery,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Gallery>;

const imageSlide = (n: number): SlideImage => ({
  type: "image",
  src: `https://placehold.co/720x480?text=${n}`,
  width: 720,
  height: 480,
  description:
    "Texte au survol, vertical-align bottom, max 5 lignes ipsum dolor sit amet, consectetur adipiscing euismod mi mi, malesuada loborti sce euismod mi mi, malesuada lobortis mauris venenatis a. Nam nulla dui, eu cursus in, porta sed lectus lorem ipsum.",
});

const iframeSlide = (): SlideIframe => ({
  type: "iframe",
  src: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  titleAttr: "Vidéo YouTube",
  description:
    "Texte au survol, vertical-align bottom, max 5 lignes ipsum dolor sit amet, consectetur adipiscing euismod mi mi, malesuada loborti sce euismod mi mi, malesuada lobortis mauris venenatis a. Nam nulla dui, eu cursus in, porta sed lectus lorem ipsum.",
});

export const Default: Story = {
  args: {
    slides: [imageSlide(1), imageSlide(2), iframeSlide(), iframeSlide(), imageSlide(5), imageSlide(6)],
  },
};

export const Images: Story = {
  args: {
    slides: [imageSlide(1), imageSlide(2), imageSlide(3)],
  },
};

export const Videos: Story = {
  args: {
    slides: [iframeSlide(), iframeSlide(), iframeSlide()],
  },
};

export const BrokenVideo: Story = {
  args: {
    slides: [
      imageSlide(1),
      {
        type: "iframe",
        src: "ABCD",
        titleAttr: "Vidéo avec un lien cassé",
      },
      imageSlide(3),
    ],
  },
};
