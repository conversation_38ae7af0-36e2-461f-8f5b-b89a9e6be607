@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.albums {
  @extend %text-wrap;

  width: 100%;
  margin-bottom: 96px;
  container: block-albums / inline-size;

  @include breakpoint(large up) {
    margin-bottom: 160px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.title {
  margin-bottom: 32px;
  font-size: 4rem;
  font-weight: 700;
  line-height: 100%;
  letter-spacing: -0.8px;

  @include breakpoint(medium up) {
    margin-bottom: 40px;
    font-size: 4.8rem;
    letter-spacing: -0.96px;
  }

  @include breakpoint(large up) {
    margin-bottom: 48px;
    font-size: 6.4rem;
    letter-spacing: -1.28px;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 32px;

  @container (min-width: 800px) {
    flex-direction: row;
  }

  @include breakpoint(medium up) {
    margin-top: 40px;
  }

  @include breakpoint(large up) {
    gap: 8px;
    margin-top: 48px;
  }
}
