import { IconType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import AdminBar from "./AdminBar";

const meta: Meta<typeof AdminBar> = {
  title: "Components/AdminBar",
  component: AdminBar,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof AdminBar>;

export const Default: Story = {
  args: {
    entries: [
      {
        icon: {
          type: IconType.FONT,
          src: "far fa-sitemap",
        },
        title: "Mes sites",
      },
      {
        icon: {
          type: IconType.FONT,
          src: "far fa-house-blank",
        },
        title: "Nomdusite.fr",
      },
      {
        icon: {
          type: IconType.FONT,
          src: "far fa-arrows-rotate",
        },
        title: "2",
        screenReaderTitle: "2 mises à jour en attente",
      },
      {
        icon: {
          type: IconType.FONT,
          src: "far fa-message",
        },
        title: "0",
        screenReaderTitle: "0 commentaires en attente de validation",
      },
      {
        icon: {
          type: IconType.FONT,
          src: "far fa-plus",
        },
        title: "<PERSON><PERSON><PERSON>",
      },
    ],
  },
};
