import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Tree from "./Tree";

const meta: Meta<typeof Tree> = {
  title: "Filters/Tree",
  tags: ["autodocs"],
  component: Tree,
};

export default meta;

type Story = StoryObj<typeof Tree>;

export const Default: Story = {
  args: {
    filter: {
      label: "Thématiques",
      attribute: "cat",
      options: [
        {
          value: "arts",
          label: "Arts",
          count: 7,
          children: [
            {
              value: "literature",
              label: "Literature",
              count: 4,
              children: [
                {
                  value: "poetry",
                  label: "Poetry",
                  count: 2,
                  children: [],
                },
                {
                  value: "novels",
                  label: "Novels",
                  count: 2,
                  children: [],
                },
              ],
            },
            {
              value: "painting",
              label: "Painting",
              count: 3,
              children: [],
            },
          ],
        },
        {
          value: "science",
          label: "Science",
          count: 5,
          children: [
            {
              value: "physics",
              label: "Physics",
              count: 2,
              children: [
                {
                  value: "quantum",
                  label: "Quantum",
                  count: 1,
                  children: [
                    {
                      value: "math",
                      label: "Math",
                      count: 1,
                      children: [],
                    },
                    {
                      value: "particles",
                      label: "Particles",
                      count: 4,
                      children: [],
                    },
                  ],
                },
                {
                  value: "relativity",
                  label: "Relativity",
                  count: 1,
                  children: [],
                },
              ],
            },
            {
              value: "biology",
              label: "Biology",
              count: 3,
              children: [],
            },
          ],
        },
        {
          value: "sports",
          label: "Sports",
          count: 8,
          children: [
            {
              value: "football",
              label: "Football",
              count: 5,
              children: [],
            },
            {
              value: "basketball",
              label: "Basketball",
              count: 3,
              children: [],
            },
          ],
        },
      ],
    },
    selectedOptions: ["literature", "science", "basketball"],
  },
};

export const ExpandAll: Story = {
  args: {
    ...Default.args,
    defaultExpanded: true,
  },
};
