@use "@/styles/lib/mixins.scss" as *;

// NOTE: This spacing is specific to the home page
.main {
  padding-bottom: 96px;

  @include breakpoint(large up) {
    padding-bottom: 160px;
  }

  // TODO: Target a common class like ".block" or data-attribute
  :global(> *) {
    margin-block: 96px;

    @include breakpoint(large up) {
      margin-block: 160px;
    }

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
