/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/button/Button.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.Button_button__qOu8O {
  display: inline-flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  line-height: normal;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 200ms ease-in-out, color 200ms ease-in-out, border-color 200ms ease-in-out;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O {
  border: 2px solid transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary__s9PDR {
  color: #fff;
  background-color: #214fab;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary__s9PDR:hover {
  background-color: #3269d7;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary__PUGgD {
  color: #000;
  background-color: #3ec8ad;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary__PUGgD:hover {
  background-color: #9ce3d5;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary__UiubZ {
  color: #000;
  background-color: #eec478;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary__UiubZ:hover {
  background-color: #f5dcad;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary-inverted__wtAtY {
  color: #fff;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-primary-inverted__wtAtY:hover {
  color: #214fab;
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary-inverted__6dhHp, .Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary-inverted__HBWl5 {
  color: #000;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-secondary-inverted__6dhHp:hover, .Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-tertiary-inverted__HBWl5:hover {
  background-color: #fff;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-danger-inverted__p6aqN {
  color: #fff;
  background-color: transparent;
}
.Button_button__qOu8O.Button_variant-contained__zkX_O.Button_color-danger-inverted__p6aqN:hover {
  color: #d61200;
  background-color: #fff;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary__s9PDR {
  color: #214fab;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary__s9PDR:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary-inverted__wtAtY {
  color: #fff;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-primary-inverted__wtAtY:hover {
  color: #214fab;
  background-color: #e5ecfa;
  border-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-secondary-inverted__6dhHp, .Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-tertiary-inverted__HBWl5 {
  color: #000;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-secondary-inverted__6dhHp:hover, .Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-tertiary-inverted__HBWl5:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-danger-inverted__p6aqN {
  color: #fff;
  background-color: transparent;
  border: 2px solid currentcolor;
}
.Button_button__qOu8O.Button_variant-outlined__hqGws.Button_color-danger-inverted__p6aqN:hover {
  color: #d61200;
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_variant-text__zAK9M {
  border: 2px solid transparent;
}
.Button_button__qOu8O.Button_variant-text__zAK9M.Button_color-primary__s9PDR {
  color: #214fab;
}
.Button_button__qOu8O.Button_variant-text__zAK9M.Button_color-primary__s9PDR:hover {
  background-color: #e5ecfa;
}
.Button_button__qOu8O.Button_size-xs__riZtg {
  padding: 6px 12px;
  font-size: 1.2rem;
}
.Button_button__qOu8O.Button_size-xs__riZtg i {
  font-size: 1.4rem;
}
.Button_button__qOu8O.Button_size-sm__wJOqU {
  padding: 8px 16px;
  font-size: 1.4rem;
}
.Button_button__qOu8O.Button_size-sm__wJOqU i {
  font-size: 1.6rem;
}
.Button_button__qOu8O.Button_size-md__k78ss, .Button_button__qOu8O.Button_size-lg__jW51I {
  padding: 11px 24px;
  font-size: 1.6rem;
  font-weight: 700;
}
.Button_button__qOu8O.Button_size-md__k78ss i, .Button_button__qOu8O.Button_size-lg__jW51I i {
  font-size: 2rem;
  font-weight: 400;
}
@media screen and (min-width: 1302px) {
  .Button_button__qOu8O.Button_size-lg__jW51I {
    gap: 16px;
    padding: 18px 32px;
    font-size: 1.8rem;
  }
  .Button_button__qOu8O.Button_size-lg__jW51I i {
    font-size: 2.4rem;
  }
}
.Button_button__qOu8O .Button_startIcon__gXO6b,
.Button_button__qOu8O .Button_endIcon__HyjGg {
  line-height: 1;
}
.Button_button__qOu8O i {
  vertical-align: middle;
}
.Button_button__qOu8O[aria-disabled=true] {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.3;
}
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/tooltip/Tooltip.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.Tooltip_tooltipContent__rhWD0 {
  position: relative;
  z-index: 1300;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 344px;
  height: auto;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  box-shadow: 0 3px 10px 0 rgba(0, 0, 0, 0.1);
}

.Tooltip_tooltipArrow__nUriN {
  visibility: visible;
}

.Tooltip_variant-neutral__iRMy9 {
  color: #363636;
  background: #f7f7f7;
  background-color: #fff;
  border: 1px solid #707070;
}
.Tooltip_variant-neutral__iRMy9 .Tooltip_tooltipArrow__nUriN {
  fill: #fff;
}

.Tooltip_variant-primary__6wuts {
  color: #fff;
  background-color: #214fab;
}
.Tooltip_variant-primary__6wuts .Tooltip_tooltipArrow__nUriN {
  fill: #214fab;
}

.Tooltip_variant-secondary__DLVem {
  background-color: #9ce3d5;
}
.Tooltip_variant-secondary__DLVem .Tooltip_tooltipArrow__nUriN {
  fill: #9ce3d5;
}

.Tooltip_variant-tertiary__lWkUI {
  background-color: #f5dcad;
}
.Tooltip_variant-tertiary__lWkUI .Tooltip_tooltipArrow__nUriN {
  fill: #f5dcad;
}

.Tooltip_size-md__ce_Ie {
  padding: 12px 16px;
  font-size: 1.4rem;
  line-height: 110%;
  border-radius: 4px;
}
.Tooltip_size-md__ce_Ie[data-side=top][data-align=start] {
  left: 0;
  margin-bottom: 8px;
  transform: none;
}
.Tooltip_size-md__ce_Ie[data-side=top][data-align=start] > span {
  left: 12px !important;
}

.Tooltip_size-lg__63tyZ {
  padding: 16px 32px;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 130%;
  border-radius: 4px;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=start] {
  left: 0;
  border-radius: 4px 4px 4px 0;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=start] > span {
  left: -1px !important;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=end] {
  border-radius: 4px 4px 0;
}
.Tooltip_size-lg__63tyZ[data-side=top][data-align=end] > span {
  right: -1px !important;
  left: auto !important;
  transform: translateY(100%) rotateY(180deg) !important;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=start] {
  left: 0;
  border-radius: 0 4px 4px;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=start] > span {
  left: -1px !important;
  transform: rotateX(180deg) !important;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=end] {
  right: 0;
  border-radius: 4px 0 4px 4px;
  transform: none;
}
.Tooltip_size-lg__63tyZ[data-side=bottom][data-align=end] > span {
  right: -1px !important;
  left: auto !important;
}
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[2]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[7].use[3]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[13].oneOf[7].use[4]!../node_modules/.pnpm/next@15.3.4_@babel+core@7.2_fce8dae288080ed16dfc7396f7c8c1dc/node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[13].oneOf[7].use[5]!./components/ui/alert/PopUpAlert.module.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* Organizes the depth of absolute elements */
/* Border radius values */
/* Box shadows */
/* Blur filters */
.PopUpAlert_overlay__MjnQO {
  position: fixed;
  inset: 0;
  z-index: 1099;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
}

.PopUpAlert_modalContainer__fxiML {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  padding: 16px;
}
@media screen and (min-width: 768px) {
  .PopUpAlert_modalContainer__fxiML {
    padding: 24px;
  }
}

.PopUpAlert_popupAlert__38nLn {
  display: flex;
  flex-direction: column;
  max-width: 720px;
  color: #fff;
  background-color: #214fab;
  border-radius: 4px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

.PopUpAlert_header__JUA6P {
  display: flex;
  justify-content: space-between;
}

.PopUpAlert_closeButton__MLGmg {
  width: 48px;
  aspect-ratio: 1;
  margin-left: auto;
}

.PopUpAlert_content__0aCkE {
  padding: 0 16px 48px;
}
@media screen and (min-width: 768px) {
  .PopUpAlert_content__0aCkE {
    padding: 0 48px 48px;
  }
}
@media screen and (min-width: 1302px) {
  .PopUpAlert_content__0aCkE {
    padding: 0 56px 56px;
  }
}

.PopUpAlert_details__DzhTE {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
@media screen and (min-width: 1302px) {
  .PopUpAlert_details__DzhTE {
    gap: 16px;
  }
}

.PopUpAlert_topIcon__D6Kd4 {
  font-size: 3.2rem;
}

.PopUpAlert_title__NEbI5 {
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 110%;
}
@media screen and (min-width: 768px) {
  .PopUpAlert_title__NEbI5 {
    font-size: 1.8rem;
  }
}
@media screen and (min-width: 1302px) {
  .PopUpAlert_title__NEbI5 {
    font-size: 2.4rem;
  }
}

.PopUpAlert_description__sx9pn {
  font-size: 1.6rem;
  line-height: 110%;
}
@media screen and (min-width: 768px) {
  .PopUpAlert_description__sx9pn {
    font-size: 1.8rem;
  }
}

.PopUpAlert_actions__nNzYv {
  margin-top: 24px;
}
@media screen and (min-width: 1302px) {
  .PopUpAlert_actions__nNzYv {
    margin-top: 32px;
  }
}
