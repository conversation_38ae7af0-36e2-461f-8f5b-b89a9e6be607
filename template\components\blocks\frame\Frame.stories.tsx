import {
  OneHalfColumnsDecorator,
  OneThirdColumnsDecorator,
  TwoThirdColumnsDecorator,
} from "@/stories/ColumnsDecorator";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Heading from "../heading/Heading";
import Paragraph from "../paragraph/Paragraph";
import Frame from "./Frame";

const meta: Meta<typeof Frame> = {
  title: "Blocks/Frame",
  component: Frame,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Frame>;

export const Default: Story = {
  args: {
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
};

export const Primary: Story = {
  args: {
    variant: "primary",
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
};

export const Secondary: Story = {
  args: {
    variant: "secondary",
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
};

export const Tertiary: Story = {
  args: {
    variant: "tertiary",
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
};

export const RichContent: Story = {
  args: {
    children: (
      <>
        <Heading level={4} html="Lorem ipsum dolor sit amet, consectetur adipiscing elit" />
        <Paragraph html="His cognitis Gallus ut <b>serpens</b> adpetitus telo <strong>vel</strong> saxo iamque spes extremas opperiens et succurrens saluti suae <a href='#'>quavis</a> ratione <i>colligi</i> omnes iussit armatos et cum starent attoniti, districta dentium <em>acie</em> stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
      </>
    ),
  },
};

export const TwoColumns: Story = {
  args: {
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
  decorators: [TwoThirdColumnsDecorator],
};

export const OneColumn: Story = {
  args: {
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
  decorators: [OneThirdColumnsDecorator],
};

export const HalfColumn: Story = {
  args: {
    children: (
      <Paragraph html="His cognitis Gallus ut serpens adpetitus telo vel saxo iamque spes extremas opperiens et succurrens saluti suae quavis ratione colligi omnes iussit armatos et cum starent attoniti, districta dentium acie stridens adeste inquit viri fortes mihi periclitanti vobiscum." />
    ),
  },
  decorators: [OneHalfColumnsDecorator],
};
