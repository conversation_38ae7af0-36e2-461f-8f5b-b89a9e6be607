import Modal from "@/components/ui/modal/Modal";
import SearchForm from "./SearchForm";
import styles from "./SearchPopup.module.scss";

interface SearchPopupProps {
  searchUrl: string;
}

export default function SearchPopup({ searchUrl }: SearchPopupProps) {
  return (
    <Modal
      title="Rechercher"
      hideTitle={true}
      className={styles.searchPopup}
      focusStrategy="content"
      size="lg"
      closeButtonText="Fermer la recherche"
    >
      <SearchForm searchUrl={searchUrl} />
    </Modal>
  );
}
