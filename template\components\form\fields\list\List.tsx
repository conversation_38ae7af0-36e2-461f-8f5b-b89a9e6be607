"use client";

import Button from "@/components/ui/button/Button";
import FormControl from "@/components/ui/form-control/FormControl";
import FormHelper from "@/components/ui/form-helper/FormHelper";
import Label from "@/components/ui/label/Label";
import Textfield from "@/components/ui/textfield/Textfield";
import { ListField } from "@/generated/graphql/graphql";
import { useId, useState } from "react";
import { useFormContext } from "react-hook-form";
import useFormCondition from "../../useFormCondition";
import useFormError from "../../useFormError";

type ListProps = Omit<ListField, "__typename"> & {
  enableColumns?: boolean;
  choices?: {
    label?: string;
    value?: string;
  }[];
};

export default function List({
  name,
  label,
  description,
  required,
  condition,
  validationMessage,
  maxRows,
  enableColumns,
  choices,
  columnSpan,
}: ListProps) {
  const { register, watch, setValue } = useFormContext();
  const { visible } = useFormCondition(name, condition);
  const error = useFormError(name, { validationMessage });
  const errorId = useId();
  const [rows, setRows] = useState<number[]>([0]);

  const currentValue = watch(name) || [];

  const addRow = () => {
    if (!maxRows || rows.length < maxRows) {
      setRows([...rows, rows.length]);
    }
  };

  const removeRow = (index: number) => {
    setRows(rows.filter((_, index_) => index_ !== index));
    setValue(
      name,
      currentValue.filter((_: unknown, index_: number) => index_ !== index)
    );
  };

  return (
    visible && (
      <FormControl columnSpan={columnSpan}>
        <Label description={description ?? undefined} required={required}>
          {label}
        </Label>

        <div style={{ display: "grid", gap: "1rem" }}>
          {rows.map((rowIndex, index) => (
            <div key={rowIndex} style={{ display: "flex", gap: "0.5rem", alignItems: "center" }}>
              {enableColumns && choices ? (
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: `repeat(${choices.length}, 1fr)`,
                    gap: "0.5rem",
                    flex: 1,
                  }}
                >
                  {choices.map((choice, colIndex) => (
                    <Textfield
                      key={colIndex}
                      placeholder={choice?.label ?? `Colonne ${colIndex + 1}`}
                      {...register(`${name}.${index}.${colIndex}`, { required: required && colIndex === 0 })}
                    />
                  ))}
                </div>
              ) : (
                <Textfield
                  placeholder={`Élément ${index + 1}`}
                  {...register(`${name}.${index}`, { required: required && index === 0 })}
                  style={{ flex: 1 }}
                />
              )}

              {rows.length > 1 && (
                <Button
                  type="button"
                  variant="outlined"
                  size="sm"
                  onClick={() => removeRow(index)}
                  startIcon={<i className="far fa-trash" aria-hidden="true"></i>}
                >
                  Supprimer
                </Button>
              )}
            </div>
          ))}

          {(!maxRows || rows.length < maxRows) && (
            <Button
              type="button"
              variant="outlined"
              onClick={addRow}
              startIcon={<i className="far fa-plus" aria-hidden="true"></i>}
            >
              Ajouter un élément
            </Button>
          )}
        </div>

        {error && (
          <FormHelper id={errorId} variant="error">
            {error.message?.toString()}
          </FormHelper>
        )}
      </FormControl>
    )
  );
}
