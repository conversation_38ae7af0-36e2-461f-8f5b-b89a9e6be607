import { globby } from "globby";
import { readdir, readFile, rm, rmdir, writeFile } from "node:fs/promises";
import path from "node:path";
import { FeaturesList } from "./schema";
import transform from "./transform";
import { logger } from "./utils/logger";

export interface Butcher {
  execute: (exclude: string[]) => Promise<void>;
}

export default async function butcher(projectRoot: string, features: FeaturesList): Promise<Butcher> {
  return {
    execute: async (exclude: string[]) => {
      logger.info("Butcher starting");

      for (const feature of features) {
        if (exclude.includes(feature.name)) {
          logger.info(`🧩 ${feature.name}`);
          continue;
        }

        const entries = await globby(feature.files ?? [], { cwd: projectRoot });

        for (const file of entries) {
          const filePath = path.resolve(projectRoot, file);

          await rm(filePath);

          const parentDir = path.dirname(filePath);
          const parentDirFiles = await readdir(parentDir);

          if (parentDirFiles.length === 0) {
            await rmdir(parentDir);
          }
        }

        const directories = ["./app", "./components", "./styles"];
        const files = await globby(directories, { cwd: projectRoot });

        for (const file of files) {
          const filePath = path.join(projectRoot, file);
          const input = await readFile(filePath, "utf8");
          const output = transform(input, feature.name);

          if (output !== input) {
            await writeFile(filePath, output, "utf8");
          }
        }
      }
    },
  };
}

export { butcher };

export { loadConfigFile } from "./config";
