@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.title {
  margin-bottom: 24px;
  font-size: 1.8rem;
  line-height: 130%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 2.4rem;
  }
}

.content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;

  > details {
    padding-top: 24px;
    margin-top: 24px;
    border-top: 1px solid $color-neutral-300;
  }
}
