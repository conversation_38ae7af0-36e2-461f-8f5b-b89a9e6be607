@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.heroCarousel {
  position: relative;
  width: 100%;
  min-height: calc(100vh - var(--header-height, 0));

  :global(.swiper) {
    width: 100%;
  }

  :global(.swiper),
  :global(.swiper-wrapper),
  :global(.swiper-slide) {
    min-height: inherit;
  }

  :global(.swiper-wrapper) {
    height: auto !important;
  }
}

.imageWrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  &::after {
    @include gradient-overlay;
  }

  img {
    position: relative;
    z-index: 0;
    display: block;
    width: 100%;
    height: auto;
    aspect-ratio: 16 / 9;
    object-fit: cover;
    object-position: center;
  }
}

.controls {
  position: absolute;
  bottom: 16px;
  left: 32px;
  z-index: 2;
  display: flex;
  align-items: center;
  padding-inline: 6px;
  background-color: rgb(0 0 0 / 50%);
  border-radius: $rounded-full;

  @include breakpoint(medium up) {
    bottom: 56px;
    left: 72px;
  }

  @include breakpoint(large up) {
    bottom: 72px;
    left: 120px;
  }
}

.playButton {
  width: 24px;
  height: 24px;
  padding: 6px;
  font-size: 0.8rem;
  color: $color-white;
  cursor: pointer;

  i {
    display: block;
    pointer-events: none;
  }
}

.pagination {
  display: flex;
  align-items: center;
}

.bullet {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: 6px;
  cursor: pointer;
  transition: width 200ms ease;

  &::after {
    flex-shrink: 0;
    width: 8px;
    height: 8px;
    content: "";
    border: 1px solid $color-white;
    border-radius: $rounded-full;
    transition:
      width 200ms ease,
      height 200ms ease,
      background-color 200ms ease,
      border-color 200ms ease;
  }

  &:hover {
    &::after {
      width: 12px;
      height: 12px;
      background-color: $color-white;
      border-color: $color-white;
    }
  }

  &.active {
    width: 48px;

    &::after {
      width: 36px;
      height: 12px;
      background-color: $color-white;
      border-color: $color-white;
    }
  }
}
