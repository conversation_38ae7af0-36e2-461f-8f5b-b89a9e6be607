import { IconType, Link } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import QuickAccess from "./QuickAccess";

const meta: Meta<typeof QuickAccess> = {
  title: "Blocks/QuickAccess",
  component: QuickAccess,
  tags: ["autodocs"],
  // Give some space to the tooltip
  decorators: (Story) => (
    <div style={{ padding: "3rem 0" }}>
      <Story />
    </div>
  ),
};

export default meta;

type Story = StoryObj<typeof QuickAccess>;

const linkData: Link = {
  text: "Lorem Ipsum",
  url: "#",
  icon: {
    type: IconType.URL,
    src: "/assets/family.svg",
  },
  class: null,
  rel: null,
  target: null,
};

export const Default: Story = {
  args: {
    items: [linkData, linkData, linkData, linkData, linkData, linkData],
  },
};

/**
 * Should not render as a slider if there are not enough elements.
 */
export const Few: Story = {
  args: {
    items: [linkData, linkData],
  },
};

/**
 * Displays an additional section, next to the links.
 */
export const Focus: Story = {
  args: {
    focus: {
      title: "Mes démarches",
      url: "#",
      icon: {
        type: IconType.URL,
        src: "/assets/family-invert.svg",
      },
    },
    items: [linkData, linkData, linkData, linkData, linkData, linkData],
  },
};
