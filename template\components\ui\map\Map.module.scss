@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;

.map {
  position: relative;
  width: 100%;
  background-color: #040606;
}

.overlay {
  position: absolute;
  inset: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 16px;
  font-size: 2.2rem;
  line-height: 130%;
  color: $color-white;
  pointer-events: none;
  background-color: rgb(0 0 0 / 60%);
  transition:
    opacity 500ms ease,
    visibility 500ms ease;

  &:not([data-visible="true"]) {
    visibility: hidden;
    opacity: 0;
  }

  @include breakpoint(medium up) {
    padding: 18px;
    font-size: 2.4rem;
  }

  @include breakpoint(large up) {
    padding: 24px;
    font-size: 2.8rem;
  }
}
