import Button from "@/components/ui/button/Button";
import Textfield from "@/components/ui/textfield/Textfield";
import styles from "./SearchForm.module.scss";

interface SearchFormProps {
  searchUrl: string;
  searchValue?: string;
}

export default function SearchForm({ searchUrl, searchValue }: SearchFormProps) {
  return (
    <form className={styles.searchForm} action={searchUrl}>
      <Textfield
        // TODO: Get the attribute name from siteConfig
        name="s"
        type="search"
        label="Saisir un ou plusieurs mots-clés…"
        startIcon="far fa-search"
        size="large"
        defaultValue={searchValue}
        // eslint-disable-next-line jsx-a11y/no-autofocus -- Allow autofocus on the search input on search results page
        autoFocus
      />
      <Button size="lg" color="primary" type="submit">
        Rechercher
      </Button>
    </form>
  );
}
