import { fr } from "date-fns/locale";
import { describe, expect, it } from "vitest";
import formatMonth from "./formatMonth";

describe("formatMonth", () => {
  it("should add a trailing dot if the month has been cut off", () => {
    expect(formatMonth("2000-01-01", { locale: fr })).toBe("Janv.");
    expect(formatMonth("2000-02-01", { locale: fr })).toBe("Févr.");
    expect(formatMonth("2000-03-01", { locale: fr })).toBe("Mars");
    expect(formatMonth("2000-04-01", { locale: fr })).toBe("Avr.");
    expect(formatMonth("2000-05-01", { locale: fr })).toBe("Mai");
    expect(formatMonth("2000-06-01", { locale: fr })).toBe("Juin");
    expect(formatMonth("2000-07-01", { locale: fr })).toBe("Juil.");
    expect(formatMonth("2000-08-01", { locale: fr })).toBe("Août");
    expect(formatMonth("2000-09-01", { locale: fr })).toBe("Sept.");
    expect(formatMonth("2000-10-01", { locale: fr })).toBe("Oct.");
    expect(formatMonth("2000-11-01", { locale: fr })).toBe("Nov.");
    expect(formatMonth("2000-12-01", { locale: fr })).toBe("Déc.");
  });
});
