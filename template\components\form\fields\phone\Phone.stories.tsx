import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Phone from "./Phone";

const meta: Meta<typeof Phone> = {
  title: "Form/Phone",
  component: Phone,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Phone>;

export const Default: Story = {
  args: {
    name: "phone_1",
    label: "Numéro de téléphone",
    description: "Votre numéro de téléphone mobile ou fixe",
    placeholder: "06 12 34 56 78",
  },
};

export const Required: Story = {
  args: {
    name: "phone_2",
    label: "Téléphone obligatoire",
    description: "Ce champ est obligatoire",
    placeholder: "01 23 45 67 89",
    required: true,
  },
};

export const International: Story = {
  args: {
    name: "phone_4",
    label: "Téléphone international",
    description: "Format international accepté",
    placeholder: "+33 6 12 34 56 78",
  },
};
