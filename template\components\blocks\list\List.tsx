import { ListBlock } from "@/generated/graphql/graphql";
import clsx from "clsx";
import styles from "./List.module.scss";

type OrderedListType = "1" | "A" | "a" | "I" | "i";

type ListProps = Partial<Omit<ListBlock, "__typename" | "innerBlocks">>;

export default function List({
  anchor,
  ordered,
  reversed,
  startAt,
  type,
  children,
}: React.PropsWithChildren<ListProps>) {
  const ListTag = ordered ? "ol" : "ul";

  return (
    <ListTag
      className={clsx("block-list contained", styles.list)}
      id={anchor ?? undefined}
      {...(ordered && {
        start: startAt ?? undefined,
        type: (type as OrderedListType | null) ?? undefined,
        reversed,
      })}
    >
      {children}
    </ListTag>
  );
}
