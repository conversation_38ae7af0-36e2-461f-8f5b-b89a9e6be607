import Icon from "@/components/ui/icon/Icon";
import type { BarMenuEntry } from "@/generated/graphql/graphql";
import type { PartialDeep } from "type-fest";
import styles from "./AdminBar.module.scss";
import CiteopolisLogo from "./citeopolis-white.svg";

interface AdminBarProps {
  entries: PartialDeep<BarMenuEntry, { recurseIntoArrays: true }>[];
}

// FIXME: Hide the bar in an iframe context
export default function AdminBar({ entries }: AdminBarProps) {
  return (
    <nav id="admin-bar" role="navigation" className={styles.adminBar} aria-roledescription="Barre d'administration">
      <CiteopolisLogo className={styles.logo} />
      <ul className={styles.menu}>
        {entries.map((entry, index) => (
          <li key={index}>
            <a className={styles.menuitem} href={entry.url} title={entry.screenReaderTitle ?? undefined}>
              <Icon {...entry.icon} />
              <span className={styles.text}>{entry.title}</span>
            </a>
          </li>
        ))}
      </ul>
    </nav>
  );
}
