@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.personContactWidget {
  @extend %text-wrap;
}

.title {
  margin-bottom: 24px;
  font-size: 1.8rem;
  font-weight: $fw-bold;
  line-height: 110%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 3.2rem;
  }

  i {
    margin-right: 6px;
    font-size: 2.4rem;
    color: $color-secondary-500;
  }
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-top: 16px;

  @include breakpoint(large up) {
    gap: 8px;
  }
}

.contactName {
  font-weight: $fw-bold;
  line-height: 110%;
  color: $color-black;
}
