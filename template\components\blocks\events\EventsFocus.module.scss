@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.eventsFocus {
  position: relative;
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: 32px;

  @container (min-width: 720px) {
    flex-direction: row-reverse;
    align-items: flex-start;
    justify-content: left;
  }

  @include breakpoint(medium up) {
    gap: 24px;
    margin-bottom: 40px;
  }

  @include breakpoint(large up) {
    gap: 32px;
    margin-bottom: 48px;
  }
}

.image {
  flex-grow: 1;
  width: 100%;

  @container (min-width: 720px) {
    max-width: 348px;
  }

  @container (min-width: 1216px) {
    max-width: 592px;
  }
}

.details {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include breakpoint(small down) {
    padding-block: 24px;
  }

  @include breakpoint(large up) {
    gap: 32px;
  }
}

.date {
  order: -1;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    font-size: 3.2rem;
  }
}

.category {
  display: block;
  margin-bottom: 6px;
  font-size: 1.4rem;
  font-weight: 400;
  line-height: 130%;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 1.6rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 8px;
    font-size: 1.8rem;
  }
}

.titleLink {
  @extend %link-block;
  @extend %underline;
}

.location {
  font-weight: 700;
  color: $color-neutral-500;

  p {
    margin-bottom: 6px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .city {
    font-size: 1.4rem;
    line-height: 130%;

    @include breakpoint(medium up) {
      font-size: 1.6rem;
    }
  }

  .place {
    font-size: 1.2rem;
    line-height: 110%;

    @include breakpoint(medium up) {
      font-size: 1.4rem;
    }
  }
}
