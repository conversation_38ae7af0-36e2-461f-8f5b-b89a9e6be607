import StoryFormProvider from "@/stories/StoryFormProvider";
import type { Meta, StoryObj } from "@storybook/nextjs";
import FileUpload from "./FileUpload";

const meta: Meta<typeof FileUpload> = {
  title: "Form/FileUpload",
  component: FileUpload,
  decorators: [
    (Story) => (
      <StoryFormProvider>
        <Story />
      </StoryFormProvider>
    ),
  ],
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof FileUpload>;

export const Default: Story = {
  args: {
    name: "file_1",
    label: "Télécharger un fichier",
    description: "Sélectionnez un fichier depuis votre ordinateur",
  },
};

export const MultipleFiles: Story = {
  args: {
    name: "files_1",
    label: "Télécharger plusieurs fichiers",
    description: "Vous pouvez sélectionner plusieurs fichiers",
    multiple: true,
    maxFiles: 5,
  },
};

export const DocumentsOnly: Story = {
  args: {
    name: "documents_1",
    label: "Documents PDF uniquement",
    description: "Formats acceptés: PDF seulement",
    allowedExtensions: [".pdf"],
    maxFileSize: 10 * 1024 * 1024, // 10MB
  },
};

export const Images: Story = {
  args: {
    name: "images_1",
    label: "Photos et images",
    description: "Formats d'images acceptés",
    allowedExtensions: [".jpg", ".jpeg", ".png", ".gif", ".webp"],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    multiple: true,
    maxFiles: 10,
  },
};

export const Required: Story = {
  args: {
    name: "file_2",
    label: "Pièce jointe obligatoire",
    description: "Ce fichier est requis",
    required: true,
    allowedExtensions: [".pdf", ".doc", ".docx"],
    maxFileSize: 2 * 1024 * 1024, // 2MB
  },
};
