"use client";

import { Dialog, DialogOverlay, DialogPortal, DialogTrigger, DialogTriggerProps } from "@radix-ui/react-dialog";
import clsx from "clsx";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import styles from "./SlideInModal.module.scss";

export default function useSlideInModal(modal: React.ReactNode) {
  const [open, setOpen] = useState(false);

  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    setOpen(false);
  }, [pathname, searchParams]);

  function Trigger({ ...props }: DialogTriggerProps) {
    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild aria-expanded={undefined} aria-controls={undefined} {...props} />
        <DialogPortal>
          <DialogOverlay className={styles.overlay} />
          <div className={clsx(styles.modalContainer, open && styles.active)}>{modal}</div>
        </DialogPortal>
      </Dialog>
    );
  }

  return { Trigger };
}
