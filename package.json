{"name": "citeopolis-monorepo", "private": true, "scripts": {"changeset": "changeset", "docs": "hugo server -D -s docs", "predocs": "node scripts/docs-preflight.mjs", "prepare": "husky"}, "devDependencies": {"@changesets/cli": "^2.27.11", "@commitlint/cli": "^19", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "execa": "^9.5.2", "fs-extra": "^11.3.0", "hugo-extended": "^0.142.0", "husky": "^9.1.7", "lint-staged": "^15.4.3"}, "repository": {"type": "git", "url": "https://code.stratis.fr/citeopolis-5/citeopolis-frontend"}, "commitlint": {"extends": ["./packages/commitlint-config/commitlint.config"]}, "packageManager": "pnpm@10.14.0", "engines": {"node": "22.x || >=23.10"}}