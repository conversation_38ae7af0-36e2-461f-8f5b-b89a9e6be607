import { OEmbedResourceType } from "@/generated/graphql/graphql";
import type { Meta, StoryObj } from "@storybook/nextjs";
import Embed from "./Embed";

const meta: Meta<typeof Embed> = {
  title: "Blocks/Embed",
  component: Embed,
  tags: ["autodocs"],
};

export default meta;

type Story = StoryObj<typeof Embed>;

export const Youtube: Story = {
  args: {
    type: OEmbedResourceType.VIDEO,
    html: '<iframe src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>',
  },
};

export const Vimeo: Story = {
  args: {
    type: OEmbedResourceType.VIDEO,
    html: '<iframe src="https://player.vimeo.com/video/451331621" allowfullscreen></iframe>',
  },
};

export const Caption: Story = {
  args: {
    caption: "Légende de la vidéo",
    type: OEmbedResourceType.VIDEO,
    html: '<iframe src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>',
  },
};

export const Transcription: Story = {
  args: {
    type: OEmbedResourceType.VIDEO,
    html: '<iframe src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ" allowfullscreen></iframe>',
    transcription: `Never gonna give you up, never gonna let you down\nNever gonna run around and desert you\nNever gonna make you cry, never gonna say goodbye\nNever gonna tell a lie and hurt you`,
  },
};

export const WithMetadata: Story = {
  args: {
    caption: "Vidéo de démonstration RGESN",
    type: OEmbedResourceType.VIDEO,
    html: '<iframe src="https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>',
    videoDuration: 600,
    videoFormats: [
      { name: "480p", weight: 20_971_520 },
      { name: "720p", weight: 52_428_800 },
      { name: "1080p", weight: 209_715_200 },
      { name: "4K", weight: 880_803_840 },
    ],
  },
};
