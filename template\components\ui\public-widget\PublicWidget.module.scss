@use "@/styles/lib/variables.scss" as *;
@use "@/styles/lib/mixins.scss" as *;
@use "@/styles/lib/helpers.scss" as *;

.publicWidget {
  container: widget-public / inline-size;

  @extend %text-wrap;
}

.title {
  display: flex;
  gap: 6px;
  align-items: center;
  margin-bottom: 24px;
  font-size: 1.8rem;
  font-weight: $fw-bold;
  color: $color-primary-500;

  @include breakpoint(medium up) {
    font-size: 2rem;
  }

  @include breakpoint(large up) {
    margin-bottom: 32px;
    font-size: 3.2rem;
  }

  i {
    min-width: 32px;
    margin-right: 6px;
    font-size: 2.4rem;
    color: $color-secondary-500;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;

  @container (min-width: 720px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }

  @include breakpoint(large up) {
    gap: 32px;
  }
}

.audienceList {
  @include restore-list-styles;

  padding-left: 24px;
  font-size: 1.6rem;
  line-height: 110%;

  @include breakpoint(medium up) {
    font-size: 1.8rem;
  }
}
